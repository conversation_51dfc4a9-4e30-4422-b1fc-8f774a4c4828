package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.CafeLookUpService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Test class to demonstrate the new closure reporting functionality
 */
@SpringBootTest
public class CafeLookUpServiceImplTest {

    private static final Logger LOG = LoggerFactory.getLogger(CafeLookUpServiceImplTest.class);

    @Autowired
    private CafeLookUpService cafeLookUpService;

    /**
     * Test method to generate and display Swiggy closure report
     */
    @Test
    public void testGenerateSwiggyClosureReport() {
        try {
            String closureReport = cafeLookUpService.generateSwiggyClosureReport();
            LOG.info("Swiggy Closure Report:\n{}", closureReport);
            
            // You can also send this to GChat or Slack
            // Example: Send to notification service
            // notificationService.sendToGChat(closureReport);
            
        } catch (Exception e) {
            LOG.error("Error testing closure report generation", e);
        }
    }
}
