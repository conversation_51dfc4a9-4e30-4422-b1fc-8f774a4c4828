package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.github.javaparser.utils.Log;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ZomatoError;
import com.stpl.tech.kettle.channelpartner.core.service.CafeLookUpService;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.SwiggyServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.core.util.ZomatoServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.domain.model.CafeActivationTokenInfo;
import com.stpl.tech.kettle.channelpartner.domain.model.CafeStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOfflineDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitChannelPartnerStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerOfflineStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCafeStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOfflineReasons;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCafeStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoOutletDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerCafeStatusHistoryDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.SwiggyCafeStatusDataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.ZomatoCafeStatusDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
public class CafeLookUpServiceImpl implements CafeLookUpService {

    private static final Logger LOG = LoggerFactory.getLogger(CafeLookUpServiceImpl.class);

    @Autowired
    private ZomatoCafeStatusDao zomatoCafeStatusDao;

    @Autowired
    private SwiggyCafeStatusDataDao swiggyCafeStatusDataDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private PartnerMetadataManagementService partnerMetadataManagementService;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerCafeStatusHistoryDao partnerCafeStatusHistoryDao;

    @Autowired
    private TokenService<CafeActivationTokenInfo> tokenService;

    @Autowired
    private CafeStatusChannelPartnerService cafeStatusChannelPartnerService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void getSwiggyCafeStatus() {
        try {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
            Map<RestaurantPartnerKey,UnitPartnerBrandMappingData> mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2();

            ExecutorService taskExecutor = Executors.newFixedThreadPool(10);
            for(Map.Entry<RestaurantPartnerKey, UnitPartnerBrandMappingData> entry: mapping.entrySet()){
                try{
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = mapping.get(entry.getKey());
                    UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitPartnerBrandMappingData.getUnitId());
                    UnitHours unitHourDetail = masterDataCache.getUnit(unitPartnerBrandMappingData.getUnitId())
                            .getOperationalHours().get(ChannelPartnerUtils.getWeekDayNumber(AppUtils.getBusinessDate())-1);
                    if (UnitCategory.CAFE.equals(unit.getCategory()) && unitPartnerBrandMappingData.getPartnerId().equals(partnerId) &&
                            unit.isLive() && UnitStatus.ACTIVE.equals(unit.getStatus())) {
                        taskExecutor.execute(() -> {
                            updateSwiggyCafeStatus(unitPartnerBrandMappingData, unit, unitHourDetail);
                        });
                    }
                }catch (Exception e){
                    LOG.error("Exception Caught While Checking Cafe Status for Unit" + entry.getKey().getRestaurantId(),e);
                }
            }
            taskExecutor.shutdown();
            try {
                if(!taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS)){
                    LOG.error("Error in completion of thread");
                }
            } catch (InterruptedException e) {
                LOG.error("Error in completion of  threads", e);
                Thread.currentThread().interrupt();
            }
        } catch (Exception e) {
            LOG.info("Error in finding Swiggy status", e);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void getZomatoCafeStatus() {
        try {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
            Map<RestaurantPartnerKey,UnitPartnerBrandMappingData> mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2();
            ExecutorService taskExecutor = Executors.newFixedThreadPool(10);
            for(Map.Entry<RestaurantPartnerKey, UnitPartnerBrandMappingData> entry: mapping.entrySet()){
                try{
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = mapping.get(entry.getKey());
                    UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitPartnerBrandMappingData.getUnitId());
                    if (UnitCategory.CAFE.equals(unit.getCategory()) && unitPartnerBrandMappingData.getPartnerId().equals(partnerId) &&
                            unit.isLive() && UnitStatus.ACTIVE.equals(unit.getStatus())) {
                        taskExecutor.execute(() -> {
                            ZomatoCafeStatusRequest zomatoCafeStatusRequest = new ZomatoCafeStatusRequest();
                            zomatoCafeStatusRequest.setOutletId(unitPartnerBrandMappingData.getRestaurantId());
                            try {
                                setZomatoCafeStatusData(zomatoCafeStatusRequest);
                            } catch (ParseException | IOException e) {
                                LOG.error("Error while setting Zomato Cafe Status Data", e);
                            }
                        });
                    }
                }catch (Exception e){
                    LOG.error("Exception Caught While Checking Cafe Status for Unit " + entry.getKey().getRestaurantId(),e);
                }
            }
            taskExecutor.shutdown();
            try {
                if(!taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS)){
                    LOG.error("Error in completion of thread");
                }
            } catch (InterruptedException e) {
                LOG.error("Error in completion of  threads", e);
                Thread.currentThread().interrupt();
            }
        } catch (Exception e) {
            LOG.info("Error in finding Zomato status", e);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void getSwiggyCafeStatus_() {
        try {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
            Map<RestaurantPartnerKey,UnitPartnerBrandMappingData> mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2();
            ExecutorService taskExecutor = Executors.newFixedThreadPool(10);
            for(Map.Entry<RestaurantPartnerKey, UnitPartnerBrandMappingData> entry: mapping.entrySet()){
                try{
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = mapping.get(entry.getKey());
                    UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitPartnerBrandMappingData.getUnitId());
                    UnitHours unitHourDetail = masterDataCache.getUnit(unitPartnerBrandMappingData.getUnitId())
                            .getOperationalHours().get(ChannelPartnerUtils.getWeekDayNumber(AppUtils.getBusinessDate())-1);
                    if (UnitCategory.CAFE.equals(unit.getCategory()) && unitPartnerBrandMappingData.getPartnerId().equals(partnerId) &&
                            unit.isLive() && UnitStatus.ACTIVE.equals(unit.getStatus())) {
                        taskExecutor.execute(() -> {
                            updateSwiggyCafeStatus_(unitPartnerBrandMappingData, unit, unitHourDetail);
                        });
                    }
                }catch (Exception e){
                    LOG.error("Exception Caught While Checking Cafe Status for Unit " + entry.getKey().getRestaurantId(),e);
                }
            }
            taskExecutor.shutdown();
            try {
                if(!taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS)){
                    LOG.error("Error in completion of thread");
                }
            } catch (InterruptedException e) {
                LOG.error("Error in completion of  threads", e);
                Thread.currentThread().interrupt();
            }
        } catch (Exception e) {
            LOG.info("Error in finding status", e);

        }
    }

    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateSwiggyCafeStatus(UnitPartnerBrandMappingData unitPartnerBrandMappingData, UnitBasicDetail unit, UnitHours unitHourDetail) {
        SwiggyCafeStatusData swiggyCafeStatusData=null;
        try {
            swiggyCafeStatusData = swiggyCafeStatusDataDao.findByUnitIdAndBrandId(unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
        }catch (Exception e){
            LOG.info("No data found for Swiggy for unit:{} amd brand:{}", unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
        }
        if (swiggyCafeStatusData==null){
            swiggyCafeStatusData = new SwiggyCafeStatusData();
            swiggyCafeStatusData.setUnitId(unit.getId());
            swiggyCafeStatusData.setCity(unit.getCity());
            swiggyCafeStatusData.setRegion(unit.getRegion());
            swiggyCafeStatusData.setName(unit.getName());
            swiggyCafeStatusData.setCafeLive(unit.isLive());
            swiggyCafeStatusData.setBrandId(unitPartnerBrandMappingData.getBrandId());
        }
        Map<String, String> pathVariables = new HashMap<>();
        pathVariables.put("id", String.valueOf(unitPartnerBrandMappingData.getRestaurantId()));
        LOG.info("units details are: {}", unit.getName());
        LOG.info("id is {}", unit.getId());
        try {
            SwiggyCafeStatusResponse response = webServiceHelper.callSwiggyApi(environmentProperties,
                    SwiggyServiceEndpoints.RESTAURANT_LOOKUP, HttpMethod.GET, SwiggyCafeStatusResponse.class, pathVariables);
            String status = Boolean.TRUE.equals((swiggyCafeStatusData.getPartnerStatus())) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE;
            if(!(swiggyCafeStatusData.getPartnerStatus().equals(response.getData().getOpen()))){
                swiggyCafeStatusData.setLastUpdatedTimeIST(AppUtils.getCurrentTimestamp().toString());
            }
            Log.info("Unit Closure Notification Sending Swiggy");
            if (response != null) {
                boolean statusFlag=false;
                if(swiggyCafeStatusData.getPartnerStatus()!=null){
                    statusFlag=swiggyCafeStatusData.getPartnerStatus();
                }
                swiggyCafeStatusData.setPartnerStatus(response.getData().getOpen());
                swiggyCafeStatusData.setUnitId(unit.getId());
                swiggyCafeStatusData.setCafeLive(unit.isLive());
                swiggyCafeStatusData.setUnitStatus(unit.getStatus());
                swiggyCafeStatusData.setSwiggyResponse(response);
                if (Objects.nonNull(swiggyCafeStatusData.getLastUpdatedTime()) && swiggyCafeStatusData.getPartnerStatus().equals(!statusFlag)) {
                    swiggyCafeStatusData.setLastUpdatedTime(AppUtils.getCurrentTimestamp().toString());
                }
                swiggyCafeStatusData.setUpdationRequest(false);
                swiggyCafeStatusData.setLastSyncTime(ChannelPartnerUtils.getCurrentTimestamp());
                saveChannelPartnerStatusHistory(swiggyCafeStatusData.getUnitId(), swiggyCafeStatusData.getName(),
                        ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID, ChannelPartnerServiceConstants.SWIGGY_ROOT_CONTEXT,
                        swiggyCafeStatusData.getPartnerStatus(), swiggyCafeStatusData.getCafeLive(), swiggyCafeStatusData.getUnitStatus(),
                        false, response.getData().getDispositionName());
                swiggyCafeStatusDataDao.save(swiggyCafeStatusData);
                LOG.info("Api Is hitting");
                if (!statusFlag==response.getData().getOpen()
                        /*&& AppUtils.checkDayCloseTime(AppUtils.getCurrentTimestamp(),
                        unitHourDetail.getDineInOpeningTime().toString(), unitHourDetail.getDineInClosingTime().toString())*/) {

                   // String activationUrl = getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY);
                   if(status.equals(AppConstants.IN_ACTIVE)) {
                       String brandName = masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName();
                       CafeStatusChannelPartner cafeStatusChannelPartner = getCafeStatusFromChannelPartnerSwiggy(swiggyCafeStatusData, response, brandName, status, unitPartnerBrandMappingData, unit, AppConstants.SWIGGY);
                       cafeStatusChannelPartnerService.saveCafeStatus(cafeStatusChannelPartner);
                   }
                    // Give Slack Notification
                    String message = ChannelPartnerUtils.getMessage( "Swiggy Status Changed : " + status , "::: Unit :::" + unit.getName() + "("
                                    + masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName() + ") \n" +
                                    "AM/DAM " + masterDataCache.getUnit(unit.getId()).getCafeManager().getName() + "/" +
                                    masterDataCache.getUnit(unit.getId()).getUnitManager().getName() +"\n"+
                                    getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY));

//                    String message = "::: Unit :::" + unit.getName() + "("
//                            + masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName() + ") \n" +
//                            "AM/DAM " + masterDataCache.getUnit(unit.getId()).getCafeManager().getName() + "/" +
//                            masterDataCache.getUnit(unit.getId()).getUnitManager().getName() + " Swiggy Status Changed : " + status +"\n"+
//                            getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY);
                    LOG.info("Message::::{}",message);
                    slackToManagerId(unit, message);

                }
            }
        } catch (Exception e) {
            LOG.error("Error in fetching status for unit id : {}", unit.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateSwiggyCafeStatus_(UnitPartnerBrandMappingData unitPartnerBrandMappingData, UnitBasicDetail unit, UnitHours unitHourDetail) {
        SwiggyCafeStatusData swiggyCafeStatusData=null;
        try {
            swiggyCafeStatusData = swiggyCafeStatusDataDao.findByUnitIdAndBrandId(unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
        }catch (Exception e){
            LOG.info("No data found for Swiggy for unit:{} amd brand:{}", unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
        }
        if (swiggyCafeStatusData==null){
            swiggyCafeStatusData = new SwiggyCafeStatusData();
            swiggyCafeStatusData.setUnitId(unit.getId());
            swiggyCafeStatusData.setCity(unit.getCity());
            swiggyCafeStatusData.setRegion(unit.getRegion());
            swiggyCafeStatusData.setName(unit.getName());
            swiggyCafeStatusData.setCafeLive(unit.isLive());
            swiggyCafeStatusData.setBrandId(unitPartnerBrandMappingData.getBrandId());
        }
        Map<String, String> pathVariables = new HashMap<>();
        pathVariables.put("id", String.valueOf(unitPartnerBrandMappingData.getRestaurantId()));
        LOG.info("units details are: {}", unit.getName());
        LOG.info("id is {}", unit.getId());
        try {
            SwiggyCafeStatusResponse response = webServiceHelper.callSwiggyApi(environmentProperties,
                    SwiggyServiceEndpoints.RESTAURANT_LOOKUP, HttpMethod.GET, SwiggyCafeStatusResponse.class, pathVariables);
            String status = Boolean.TRUE.equals((swiggyCafeStatusData.getPartnerStatus())) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE;
            if (response != null) {
                boolean statusFlag=false;
                if(swiggyCafeStatusData.getPartnerStatus()!=null){
                    statusFlag=swiggyCafeStatusData.getPartnerStatus();
                }
                swiggyCafeStatusData.setPartnerStatus(response.getData().getOpen());
                swiggyCafeStatusData.setSwiggyResponse(response);
                swiggyCafeStatusData.setUnitId(unit.getId());
                swiggyCafeStatusData.setLastUpdatedTime(AppUtils.getCurrentTimestamp().toString());
                swiggyCafeStatusData.setCafeLive(unit.isLive());
                swiggyCafeStatusData.setUnitStatus(unit.getStatus());
                swiggyCafeStatusData.setPartnerStatus(response.getData().getOpen());
                swiggyCafeStatusData.setSwiggyResponse(response);
                swiggyCafeStatusData.setLastUpdatedTime(AppUtils.getCurrentTimestamp().toString());
                swiggyCafeStatusData.setUpdationRequest(false);
                saveChannelPartnerStatusHistory(swiggyCafeStatusData.getUnitId(), swiggyCafeStatusData.getName(),
                        ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID, ChannelPartnerServiceConstants.SWIGGY_ROOT_CONTEXT,
                        swiggyCafeStatusData.getPartnerStatus(), swiggyCafeStatusData.getCafeLive(), swiggyCafeStatusData.getUnitStatus(),
                        false, response.getData().getDispositionName());
                swiggyCafeStatusDataDao.save(swiggyCafeStatusData);
                LOG.info("Api Is hitting");
                    // String activationUrl = getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY);
                    if(status.equals(AppConstants.IN_ACTIVE)) {
                        String brandName = masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName();
                        CafeStatusChannelPartner cafeStatusChannelPartner = getCafeStatusFromChannelPartnerSwiggy(swiggyCafeStatusData, response, brandName, status, unitPartnerBrandMappingData, unit, AppConstants.SWIGGY);
                        cafeStatusChannelPartnerService.saveCafeStatus(cafeStatusChannelPartner);
                    }
                    // Give Slack Notification
                    String message = ChannelPartnerUtils.getMessage( "Swiggy Status Changed : " + status , "::: Unit :::" + unit.getName() + "("
                            + masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName() + ") \n" +
                            "AM/DAM " + masterDataCache.getUnit(unit.getId()).getCafeManager().getName() + "/" +
                            masterDataCache.getUnit(unit.getId()).getUnitManager().getName() +"\n"+
                            getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY));

//                    String message = "::: Unit :::" + unit.getName() + "("
//                            + masterDataCache.getBrandMetaData().get(unitPartnerBrandMappingData.getBrandId()).getBrandName() + ") \n" +
//                            "AM/DAM " + masterDataCache.getUnit(unit.getId()).getCafeManager().getName() + "/" +
//                            masterDataCache.getUnit(unit.getId()).getUnitManager().getName() + " Swiggy Status Changed : " + status +"\n"+
//                            getActivationLink(unitPartnerBrandMappingData, status, swiggyCafeStatusData.getId(),unit, AppConstants.SWIGGY);
                    LOG.info("Message::::{}",message);
//                    slackToManagerId(unit, message);

                }

        } catch (Exception e) {
            LOG.error("Error in fetching status for unit id : {}", unit.getId());
        }
    }

    private CafeStatusChannelPartner getCafeStatusFromChannelPartnerSwiggy(SwiggyCafeStatusData swiggyCafeStatusData,SwiggyCafeStatusResponse response,String brandName,String status,UnitPartnerBrandMappingData unitPartnerBrandMappingData, UnitBasicDetail unit,String channelPartner){
        CafeStatusChannelPartner data=new  CafeStatusChannelPartner();
        //data.setId();
        data.setUnitId(unit.getId());
        data.setUnitName(unit.getName());
        data.setUnitRegion(unit.getRegion());
        data.setUnitCity(unit.getCity());
        data.setCafeLive(swiggyCafeStatusData.getCafeLive() ? "YES":"NO");
        data.setLastUpdatedTime(swiggyCafeStatusData.getLastUpdatedTime());
        data.setChannelPartner(channelPartner);
        data.setBrandName(brandName);
        data.setStatusCode(response.getStatusCode());
        data.setBrandId(swiggyCafeStatusData.getBrandId());
        data.setEmpId(unit.getUnitManagerId());
        data.setChannelRestaurantId(unitPartnerBrandMappingData.getRestaurantId());
        data.setStatusMessage(response.getStatusMessage());
        data.setStatusUpdate(status);
        data.setPartnerId(unitPartnerBrandMappingData.getPartnerId());
        data.setApplicationSource(AppConstants.INTERNAL_SOURCE);
        //data.setUpdationRequest();
        return data;
    }

    private void publishEventOnKnock(UnitChannelPartnerStatus unitChannelPartnerStatus) {
        webServiceHelper.postWithAuth_(environmentProperties.getKnockNotificationUrl(),environmentProperties.getKnockMasterToken(),unitChannelPartnerStatus, Boolean.class);
    }

    private String getActivationLink(UnitPartnerBrandMappingData unitPartnerBrandMappingData, String status,
                                     String requestId, UnitBasicDetail unit, String partnerName) throws IOException {
        if (status.equals(AppConstants.IN_ACTIVE)) {
            String activationLink = "";
            CafeActivationTokenInfo cafeActivationTokenInfo = new CafeActivationTokenInfo(requestId,
                    unitPartnerBrandMappingData.getPartnerId(), AppUtils.getCurrentTimestamp());
            String jwtToken = tokenService.createToken(cafeActivationTokenInfo, 3600000);
            activationLink = environmentProperties.getChannelPartnerBasePath()
                    + ChannelPartnerClientEndpoints.CHANNEL_PARTNER_CAFE_ACTIVATION + "/" + jwtToken;
            try {
                if(!AppUtils.isActive(status)){
                    publishEventOnKnock(new UnitChannelPartnerStatus(unit.getId(), unit.getName(), unitPartnerBrandMappingData.getBrandId(),
                            masterDataCache.getUnit(unit.getId()).getCafeManager().getId(), masterDataCache.getUnit(unit.getId()).getUnitManager().getId(),
                            AppUtils.isActive(status), unitPartnerBrandMappingData.getPartnerId(), partnerName,"Cafe is Offline on " + partnerName,activationLink));
                }
            } catch (Exception e){
                LOG.error("Exception Faced While Processing notification for Unit ::{}  ::::::: ",unit.getId(),e);
            }
            try {
                return SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(activationLink).getUrl();
            } catch (Exception e) {
                LOG.error("Error Caught While Generating Short Url", e);
                return activationLink;
            }
        }
        return "";

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ZomatoCafeStatusData> getZomatoCafeStatusData() {
        return zomatoCafeStatusDao.findAll();
    }

    private void resendUnitClosureRequestAfterMenuPush(ZomatoCafeStatusRequest request){
        try {
            Integer unitId = ChannelPartnerUtils.getUnitIdFromStoreId(request.getOutletId());
            Integer brandId = ChannelPartnerUtils.getBrandIdFromStoreId(request.getOutletId());
            Boolean isActiveClosureRequestExist = cafeStatusChannelPartnerService.checkIfActiveCafeClosureRequestExists(unitId,brandId,
                    AppConstants.CHANNEL_PARTNER_ZOMATO);
            if(Boolean.TRUE.equals(isActiveClosureRequestExist)){
                ZomatoDeliveryChangeRequest zomatoDeliveryChangeRequest = new ZomatoDeliveryChangeRequest();
                zomatoDeliveryChangeRequest.setOutletId(request.getOutletId());
                zomatoDeliveryChangeRequest.setOutletDeliveryStatusUpdateReason("Cafe is Offline");
                zomatoDeliveryChangeRequest.setOutletDeliveryStatus(0);
                try {
                    LOG.error("Auto Disable Partner Status for zomato for unit :{}", request.getOutletId());
                    partnerMetadataManagementService.updateZomatoOutletStatusByRestaurantId(brandId, zomatoDeliveryChangeRequest);
                } catch (ChannelPartnerException e) {
                    LOG.error("Error in updating cafe status on zomato for unit :{}", request.getOutletId());
                }
            }
        }catch (Exception e){
            LOG.error("Error while resend unit closure request for store id : {} ", request.getOutletId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setZomatoCafeStatusData(ZomatoCafeStatusRequest request) throws ParseException, IOException {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();

        //hitting get api for zomato cafe status
        boolean getApiZomatoResponse = false;
        ZomatoOutletDeliveryStatusRequest getRequest = new ZomatoOutletDeliveryStatusRequest();
        getRequest.setOutletId(request.getOutletId());

        ZomatoStatusResponse getResponse = null;
        try {
            getResponse = webServiceHelper.callZomatoApi(environmentProperties,
                ZomatoServiceEndpoints.GET_DELIVERY_STATUS, HttpMethod.POST,
                getRequest, ZomatoStatusResponse.class, 1);
            LOG.info("zomato cafe status response from get api for outletId {} is {}", request.getOutletId(), getResponse.getData().getOutletDeliveryStatus());
            if (getResponse.getData() != null && getResponse.getData().getOutletDeliveryStatus() == 1) {
                getApiZomatoResponse = true;
                resendUnitClosureRequestAfterMenuPush(request);
            }
        } catch (HttpStatusCodeException e) {
            LOG.info(e.getResponseBodyAsString());
            ZomatoError error = new Gson().fromJson(e.getResponseBodyAsString(), ZomatoError.class);
            LOG.info("zomato cafe status code is {} message is {} status {}",error.getCode(),error.getMessage(), error.getStatus());
        }

        RestaurantPartnerKey restaurantPartnerKey = new RestaurantPartnerKey(request.getOutletId(), partnerId);
        UnitPartnerBrandMappingData mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(restaurantPartnerKey);
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(mapping.getUnitId());
        UnitHours unitHourDetail = masterDataCache.getUnit(mapping.getUnitId())
            .getOperationalHours().get(ChannelPartnerUtils.getWeekDayNumber(AppUtils.getBusinessDate()));
        String status = Boolean.TRUE.equals(request.getZomatoOnlineOrderStatus()) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE;
        if (unit != null) {
            ZomatoCafeStatusData zomatoCafeStatusData = null;
            try {
                zomatoCafeStatusData = zomatoCafeStatusDao.findByUnitIdAndBrandId(mapping.getUnitId(), mapping.getBrandId());
            } catch (Exception e) {
                LOG.info("no data found for zomato for unit:{} amd brand:{}", mapping.getUnitId(), mapping.getBrandId());
            }
            if(Objects.requireNonNull(zomatoCafeStatusData).getPartnerStatus().equals(getApiZomatoResponse)){
                zomatoCafeStatusData.setLastUpdatedTimeIST(AppUtils.getCurrentTimestamp().toString());
            }
            boolean notifySlack = false;
            if (zomatoCafeStatusData == null) {
                zomatoCafeStatusData = new ZomatoCafeStatusData();
                zomatoCafeStatusData.setUnitId(unit.getId());
                zomatoCafeStatusData.setCity(unit.getCity());
                zomatoCafeStatusData.setRegion(unit.getRegion());
                zomatoCafeStatusData.setName(unit.getName());
                zomatoCafeStatusData.setBrandId(mapping.getBrandId());
                if (ChannelPartnerUtils.checkDayCloseTime(ChannelPartnerUtils.getCurrentTimestamp(),
                    unitHourDetail.getDineInOpeningTime().toString(), unitHourDetail.getDineInClosingTime().toString())) {
                    notifySlack = true;
                }
            } else if (!zomatoCafeStatusData.getPartnerStatus().equals(request.getZomatoOnlineOrderStatus()) &&
                ChannelPartnerUtils.checkDayCloseTime(ChannelPartnerUtils.getCurrentTimestamp(),
                    unitHourDetail.getDineInOpeningTime().toString(), unitHourDetail.getDineInClosingTime().toString())) {
                notifySlack = true;
            }
            zomatoCafeStatusData.setPartnerStatus(getApiZomatoResponse);
            zomatoCafeStatusData.setCafeLive(unit.isLive());
            zomatoCafeStatusData.setUnitStatus(unit.getStatus());
            zomatoCafeStatusData.setZomatoResponse(request);
            if (Objects.nonNull(zomatoCafeStatusData.getLastUpdatedTime()) && zomatoCafeStatusData.getPartnerStatus().equals(!getApiZomatoResponse)) {
                zomatoCafeStatusData.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimestamp());
                zomatoCafeStatusData.setLastUpdatedTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
            }
            zomatoCafeStatusData.setZomatoStatusFromGetApi(getApiZomatoResponse);
            zomatoCafeStatusData.setUpdationRequest(false);
            zomatoCafeStatusData.setLastSyncTime(ChannelPartnerUtils.getCurrentTimestamp());
            saveChannelPartnerStatusHistory(zomatoCafeStatusData.getUnitId(),
                zomatoCafeStatusData.getName(), zomatoCafeStatusData.getBrandId(),
                ChannelPartnerServiceConstants.ZOMATO_ROOT_CONTEXT, zomatoCafeStatusData.getPartnerStatus(),
                zomatoCafeStatusData.getCafeLive(), zomatoCafeStatusData.getUnitStatus(),getApiZomatoResponse, request.getReason());
            LOG.info("persisting zomato response data in db for unit {} : {}", zomatoCafeStatusData.getUnitId(), zomatoCafeStatusData);
            zomatoCafeStatusDao.save(zomatoCafeStatusData);
//            if(status.equals(AppConstants.IN_ACTIVE)) {
//                String brandName = masterDataCache.getBrandMetaData().get(mapping.getBrandId()).getBrandName();
//                CafeStatusChannelPartner cafeStatusChannelPartner = getCafeStatusFromChannelPartnerZomato(zomatoCafeStatusData, brandName, status, mapping, unit, AppConstants.ZOMATO);
//                cafeStatusChannelPartnerService.saveCafeStatus(cafeStatusChannelPartner);
//            }
//            if (notifySlack) {
//                String message = "::: Unit(Brand) :::" + unit.getName() + "("
//                        + masterDataCache.getBrandMetaData().get(mapping.getBrandId()).getBrandName() + ") \n" +
//                        "AM/DAM " + masterDataCache.getUnit(unit.getId()).getCafeManager().getName() + "/" +
//                        masterDataCache.getUnit(unit.getId()).getUnitManager().getName() + " Zomato Status Changed : " + status + " \n" +
//                        zomatoCafeStatusData.getZomatoResponse().getReason() + " \n" +
//                        getActivationLink(mapping, status, zomatoCafeStatusData.getId(), unit,AppConstants.ZOMATO);
//                slackToManagerId(unit, message);
//            }
            if(Objects.nonNull(zomatoCafeStatusData.getZomatoResponse()) &&
                    Objects.nonNull(zomatoCafeStatusData.getZomatoResponse().getReason())){
                ZomatoOfflineReasons reasonForOfflineCafe= ZomatoOfflineReasons.getReason(zomatoCafeStatusData.getZomatoResponse().getReason());
                if (!zomatoCafeStatusData.getPartnerStatus() &&
                        reasonForOfflineCafe.isAutoSwitchOn()) {
                    ZomatoDeliveryChangeRequest zomatoDeliveryChangeRequest = new ZomatoDeliveryChangeRequest();
                    zomatoDeliveryChangeRequest.setOutletId(getRequest.getOutletId());
                    zomatoDeliveryChangeRequest.setOutletDeliveryStatusUpdateReason("Cafe is Online");
                    zomatoDeliveryChangeRequest.setOutletDeliveryStatus(1);
                    try {
                        LOG.error("Auto Enable Partner Status for zomato for unit :{}", getRequest.getOutletId());
                        Integer brandId = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(new RestaurantPartnerKey(getRequest.getOutletId(), partnerId)).getBrandId();
                        partnerMetadataManagementService.updateZomatoOutletStatusByRestaurantId(brandId, zomatoDeliveryChangeRequest);
                    } catch (ChannelPartnerException e) {
                        LOG.error("Error in updating cafe status on zomato for unit :{}", zomatoCafeStatusData.getUnitId());
                    }
                }

            }
            return true;
        }
        return false;
    }

    public CafeStatusChannelPartner getCafeStatusFromChannelPartnerZomato(ZomatoCafeStatusData zomatoCafeStatusData,String brandName,String status,UnitPartnerBrandMappingData mapping,UnitBasicDetail unit,String channelPartner){

        CafeStatusChannelPartner data=new  CafeStatusChannelPartner();
        //data.setId();
        data.setUnitId(unit.getId());
        data.setUnitName(unit.getName());
        data.setUnitRegion(unit.getRegion());
        data.setUnitCity(unit.getCity());
        data.setCafeLive(zomatoCafeStatusData.getCafeLive() ? "YES":"NO");
        data.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimeISTString());
        data.setChannelPartner(channelPartner);
        data.setBrandName(brandName);
        //data.setStatusCode();
        data.setBrandId(mapping.getBrandId());
        data.setEmpId(unit.getUnitManagerId());
        data.setChannelRestaurantId(mapping.getRestaurantId());
        data.setStatusMessage(zomatoCafeStatusData.getZomatoResponse().getReason());
        data.setStatusUpdate(status);
        data.setPartnerId(mapping.getPartnerId());
        data.setApplicationSource(AppConstants.INTERNAL_SOURCE);
        //data.setUpdationRequest();
        return data;
    }

    @Override
    public CafeStatus getCafeStatusForUnit(Integer unitId, Integer brandId) {
        Unit unit = masterDataCache.getUnit(unitId);
        ZomatoCafeStatusData zomato = null;
        try {
            zomato = zomatoCafeStatusDao.findByUnitIdAndBrandId(unitId, brandId);
        } catch (Exception e) {
            LOG.info("no data found for zomato for unit:{} amd brand:{}", unitId, brandId, e);
        }
        SwiggyCafeStatusData swiggy = swiggyCafeStatusDataDao.findByUnitIdAndBrandId(unitId, brandId);
        CafeStatus cafeStatus = new CafeStatus();
        if (zomato != null) {
            cafeStatus.setZomatoStatus(zomato.getPartnerStatus());
            cafeStatus.setCity(zomato.getCity());
            cafeStatus.setZomatoStatusFromGetApi(zomato.isZomatoStatusFromGetApi());
        }
        if (swiggy != null) {
            cafeStatus.setSwiggyStatus(swiggy.getPartnerStatus());
        }
        if(Objects.nonNull(unit)) {
            cafeStatus.setRegion(unit.getRegion());
            cafeStatus.setName(unit.getName());
            cafeStatus.setCafeLive(unit.isLive());
            cafeStatus.setUnitStatus(unit.getStatus());
        }
        return cafeStatus;
    }

    @Override
    public void updateSwiggyCafeStatus(String outletId,int unitId) {
        Map<RestaurantPartnerKey,UnitPartnerBrandMappingData> mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2();
        RestaurantPartnerKey restaurantPartnerKey = new RestaurantPartnerKey(outletId, AppConstants.CHANNEL_PARTNER_SWIGGY);
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        UnitHours unitHourDetail = masterDataCache.getUnit(unitId)
                .getOperationalHours().get(ChannelPartnerUtils.getWeekDayNumber(AppUtils.getBusinessDate()));
        updateSwiggyCafeStatus(mapping.get(restaurantPartnerKey), unit, unitHourDetail);
    }

    @Override
    public List<UnitPartnerOfflineStatus> getUnitCafesOfflineOnPartner(List<Integer> unitIds){
        Integer swiggyId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        Integer zomatoId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
        Map<Integer, Set<Integer>> partnerActiveUnitsMap = new HashMap<>();
        masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
            if (unitIds.contains(unitChannelPartnerMapping.getUnit().getId())) {
                getPartnerUnit(partnerActiveUnitsMap, unitChannelPartnerMapping);
            }
        });
        return getUnitPartnerOfflineStatuses(swiggyId, zomatoId, partnerActiveUnitsMap);
    }
    @Override
    public List<UnitPartnerOfflineStatus> getCafesOfflineOnPartner(){
        Integer swiggyId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        Integer zomatoId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
        Map<Integer, Set<Integer>> partnerActiveUnitsMap = new HashMap<>();
//        masterDataCache.getUnits(UnitCategory.CAFE);
        masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                getPartnerUnit(partnerActiveUnitsMap, unitChannelPartnerMapping);
        });
        return getUnitPartnerOfflineStatuses(swiggyId, zomatoId, partnerActiveUnitsMap);
    }

    private void getPartnerUnit(Map<Integer, Set<Integer>> partnerActiveUnitsMap, UnitChannelPartnerMapping unitChannelPartnerMapping) {
        Integer partnerId = unitChannelPartnerMapping.getChannelPartner().getId();
        if(!partnerActiveUnitsMap.containsKey(partnerId)
                || partnerActiveUnitsMap.get(partnerId) == null) {
            partnerActiveUnitsMap.put(partnerId, new HashSet<>());
        }
        partnerActiveUnitsMap.get(partnerId).add(unitChannelPartnerMapping.getUnit().getId());
    }

    private List<UnitPartnerOfflineStatus> getUnitPartnerOfflineStatuses(Integer swiggyId, Integer zomatoId, Map<Integer, Set<Integer>> partnerActiveUnitsMap) {
        List<SwiggyCafeStatusData> swiggyStatusData = swiggyCafeStatusDataDao.findAllByUnitIdInAndPartnerStatusAndBrandIdExists(partnerActiveUnitsMap.get(swiggyId), false, true);
        List<ZomatoCafeStatusData> zomatoStatusData = zomatoCafeStatusDao.findAllByUnitIdInAndPartnerStatusAndBrandIdExists(partnerActiveUnitsMap.get(zomatoId), false, true);

        Map<Integer, Map<Integer, List<PartnerOfflineDetails>>> offlineDetailsMap = new HashMap<>();
        swiggyStatusData.forEach(swiggyCafeStatusData -> {
            Unit unit = masterDataCache.getUnit(swiggyCafeStatusData.getUnitId());
            if (unit != null && unit.getStatus().equals(UnitStatus.ACTIVE)) {
                if(!offlineDetailsMap.containsKey(swiggyCafeStatusData.getUnitId())) {
                    offlineDetailsMap.put(swiggyCafeStatusData.getUnitId(), new HashMap<>());
                }
                if(!offlineDetailsMap.get(swiggyCafeStatusData.getUnitId()).containsKey(swiggyCafeStatusData.getBrandId())) {
                    offlineDetailsMap.get(swiggyCafeStatusData.getUnitId()).put(swiggyCafeStatusData.getBrandId(), new ArrayList<>());
                }
                offlineDetailsMap.get(swiggyCafeStatusData.getUnitId()).get(swiggyCafeStatusData.getBrandId()).add(
                        new PartnerOfflineDetails(swiggyId, "SWIGGY", swiggyCafeStatusData.getPartnerStatus(),
                                swiggyCafeStatusData.getSwiggyResponse().getData().getDispositionName(),
                                AppUtils.parseDate(swiggyCafeStatusData.getLastUpdatedTime(), new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy"))
                        )
                );
            }
        });

        zomatoStatusData.forEach(zomatoCafeStatusData -> {
            Unit unit = masterDataCache.getUnit(zomatoCafeStatusData.getUnitId());
            if (unit != null && unit.getStatus().equals(UnitStatus.ACTIVE)) {
                if (!offlineDetailsMap.containsKey(zomatoCafeStatusData.getUnitId())) {
                    offlineDetailsMap.put(zomatoCafeStatusData.getUnitId(), new HashMap<>());
                }
                if (!offlineDetailsMap.get(zomatoCafeStatusData.getUnitId()).containsKey(zomatoCafeStatusData.getBrandId())) {
                    offlineDetailsMap.get(zomatoCafeStatusData.getUnitId()).put(zomatoCafeStatusData.getBrandId(), new ArrayList<>());
                }
                offlineDetailsMap.get(zomatoCafeStatusData.getUnitId()).get(zomatoCafeStatusData.getBrandId()).add(
                        new PartnerOfflineDetails(zomatoId, "ZOMATO", zomatoCafeStatusData.getPartnerStatus(),
                                zomatoCafeStatusData.getZomatoResponse().getReason(),
                                zomatoCafeStatusData.getLastUpdatedTime()
                        )
                );
            }
        });

        List<UnitPartnerOfflineStatus> cafeStatusList = new ArrayList<>();
        offlineDetailsMap.forEach((unitId, brandStatusesMap) -> {
                brandStatusesMap.forEach((brandId, partnerOfflineDetails) -> {
                    cafeStatusList.add(new UnitPartnerOfflineStatus(unitId, masterDataCache.getUnitBasicDetail(unitId).getName(),
                    brandId, masterDataCache.getBrandMetaData().get(brandId).getBrandName(), partnerOfflineDetails,
                            partnerOfflineDetails.get(0).getOfflineTime()));
                });
        });

        cafeStatusList.sort((cafeStatus1, cafeStatus2) -> {
            Date date1 = cafeStatus1.getLatestUpdationDate();
            Date date2 = cafeStatus2.getLatestUpdationDate();
            if (date1 == null) {
                return -1;
            }
            if (date2 == null) {
                return 1;
            }
            return cafeStatus1.getLatestUpdationDate().compareTo(cafeStatus2.getLatestUpdationDate());
        });

        // Filtering Offline Partners till one month before from current date

        Date oneMonthBeforeDate = new Date();
        oneMonthBeforeDate.setMonth(oneMonthBeforeDate.getMonth() - 1);
        List<UnitPartnerOfflineStatus> filteredCafeStatusList = cafeStatusList.stream().filter(
                (cafeStatus) -> Objects.nonNull(cafeStatus.getLatestUpdationDate()) && cafeStatus.getLatestUpdationDate().compareTo(oneMonthBeforeDate) >=0)
                .collect(Collectors.toList());

//        LOG.info("cafeStatusList is ::: {}", new Gson().toJson(cafeStatusList));
        return filteredCafeStatusList;
    }

    private void saveChannelPartnerStatusHistory(Integer unitId, String name, Integer brandId, String partnerName,
                                                 Boolean partnerStatus, Boolean cafeLive, UnitStatus unitStatus,
                                                 boolean getAPiStatusResponse, String reason) {
        PartnerCafeStatusHistory partnerCafeStatusHistory = new PartnerCafeStatusHistory(name, unitId, brandId, partnerName,
            partnerStatus, cafeLive, unitStatus, ChannelPartnerUtils.getCurrentTimestamp(), ChannelPartnerUtils.getCurrentTimeISTString(),getAPiStatusResponse, reason);
        partnerCafeStatusHistoryDao.save(partnerCafeStatusHistory);
    }

	private void slackToManagerId(UnitBasicDetail ubd, String message) {
		try {
			SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
					"Channel Partner", SlackNotification.CHANNEL_PARTNER_STATUS, message);
			if (ubd.getUnitManagerId() != null) {
				SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getUnitManagerId() + "_notify"
										: ubd.getUnitManagerId() + "_notify",
								message);
			}
			if (ubd.getCafeManagerId() != null) {
				SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getCafeManagerId() + "_notify"
										: ubd.getCafeManagerId() + "_notify",
								message);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing slack for channel partner status event", e);
		}
	}

    // sending closure report to gchat UNIT_CLOSURE
    public void sendClosureReportToGChat(String message) {
        try {
            SlackNotificationService.getInstance().sendNotification(EnvType.valueOf("PROD"),
                    "Channel Partner", SlackNotification.UNIT_CLOSURE, message);
            LOG.info("Closure report sent to GChat {} for notification type {} ", message, SlackNotification.UNIT_CLOSURE);
        } catch (Exception e) {
            LOG.error("Error sending closure report to GChat", e);
        }
    }

    public void updatePartnerStatusAfterChangingCafeStatus(Integer unitId, Boolean status, Integer brandId, Integer partnerId) {
        switch (partnerId) {
            case AppConstants.CHANNEL_PARTNER_ZOMATO:
                updateZomatoStatusAfterChangingCafeStatus(unitId, status, brandId);
                break;
            case AppConstants.CHANNEL_PARTNER_SWIGGY:
                updateSwiggyStatusAfterChangingCafeStatus(unitId, status, brandId);
                break;
        }
    }

    private void updateZomatoStatusAfterChangingCafeStatus(Integer unitId, Boolean status, Integer brandId) {
        ZomatoCafeStatusData zomatoCafeStatusData = zomatoCafeStatusDao.findByUnitIdAndBrandId(unitId, brandId);
        zomatoCafeStatusData.setPartnerStatus(status);
        zomatoCafeStatusData.setZomatoStatusFromGetApi(status);
        zomatoCafeStatusData.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimestamp());
        zomatoCafeStatusData.setLastUpdatedTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
    }

    private void updateSwiggyStatusAfterChangingCafeStatus(Integer unitId, Boolean status, Integer brandId) {
        SwiggyCafeStatusData swiggyCafeStatusData = swiggyCafeStatusDataDao.findByUnitIdAndBrandId(unitId, brandId);
        swiggyCafeStatusData.setPartnerStatus(status);
        swiggyCafeStatusData.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimeISTString());
    }

    public void unitClosureNotify(Integer partnerId){
        try{
            Integer swiggyPartnerId = channelPartnerDataCache.getPartnerCache().get(ChannelPartnerServiceConstants.SWIGGY).getKettlePartnerId();
            Integer zomatoPartnerId = channelPartnerDataCache.getPartnerCache().get(ChannelPartnerServiceConstants.ZOMATO).getKettlePartnerId();
            // Maps to collect all closed units for each partner
            Map<Integer, String> closedSwiggyUnitIds = new HashMap<>();
            Map<Integer, String> closedZomatoUnitIds = new HashMap<>();
            Map<RestaurantPartnerKey,UnitPartnerBrandMappingData> mapping = masterDataCache.getUnitPartnerBrandMappingMetaData2();
            // Iterate through all units and collect closed ones
            for(Map.Entry<RestaurantPartnerKey, UnitPartnerBrandMappingData> entry: mapping.entrySet()){
                try{
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = mapping.get(entry.getKey());
                    UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitPartnerBrandMappingData.getUnitId());
                    // Skip if unit is not a cafe or not live or not active
                    if (!UnitCategory.CAFE.equals(unit.getCategory()) || !unit.isLive() || !UnitStatus.ACTIVE.equals(unit.getStatus())) {
                        continue;
                    }
                    // Check Swiggy units
                    if (unitPartnerBrandMappingData.getPartnerId().equals(swiggyPartnerId)) {
                        SwiggyCafeStatusData swiggyCafeStatusData = swiggyCafeStatusDataDao.findByUnitIdAndBrandId(unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
                        if (swiggyCafeStatusData != null && Boolean.FALSE.equals(swiggyCafeStatusData.getPartnerStatus())) {
                            closedSwiggyUnitIds.put(unitPartnerBrandMappingData.getUnitId(), swiggyCafeStatusData.getLastUpdatedTimeIST());
                        }
                    }
                    // Check Zomato units
                    if (unitPartnerBrandMappingData.getPartnerId().equals(zomatoPartnerId)) {
                        ZomatoCafeStatusData zomatoCafeStatusData = zomatoCafeStatusDao.findByUnitIdAndBrandId(unitPartnerBrandMappingData.getUnitId(), unitPartnerBrandMappingData.getBrandId());
                        if (zomatoCafeStatusData != null && Boolean.FALSE.equals(zomatoCafeStatusData.getPartnerStatus())) {
                            closedZomatoUnitIds.put(unitPartnerBrandMappingData.getUnitId(), zomatoCafeStatusData.getLastUpdatedTimeIST());
                        }
                    }
                }catch (Exception e){
                    LOG.error("Exception Caught While Checking Cafe Status for Unit{}", entry.getKey().getRestaurantId(), e);
                }
            }
            if(closedSwiggyUnitIds.isEmpty()){
                generateUnitClosureMessage(closedSwiggyUnitIds, ChannelPartnerServiceConstants.SWIGGY);
            }
            if(!closedZomatoUnitIds.isEmpty()){
                generateUnitClosureMessage(closedZomatoUnitIds, ChannelPartnerServiceConstants.ZOMATO);
            }
        }catch (Exception e){
            LOG.error("Error while sending closure notification for partner ::{}  ::::::: ",partnerId,e);
        }
    }

    public void generateUnitClosureMessage(Map<Integer, String> closedUnitIdToTimestampMap,String partnerName) {
        boolean hasRows = false;
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("#### UNIT CLOSURE REPORT#### \n");
        messageBuilder.append("####"+ partnerName +"####" + "CHAAYOS" + "####\n");
        messageBuilder.append("----------------------------------------------------------------------------------------\n");
        messageBuilder.append(String.format("%-10s | %-25s | %-15s | %-15s | %-15s\n", "Unit ID", "Unit Name", "Status",
                                            "Closed For","Last Updated Time"));
        try {
            for (Map.Entry<Integer, String> entry : closedUnitIdToTimestampMap.entrySet()) {
                Integer unitId = entry.getKey();
                String lastUpdatedStr = entry.getValue();
                Date lastUpdated = AppUtils.parseDate(lastUpdatedStr, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));
                Date now = AppUtils.getCurrentTimestamp();
                long diffMillis = now.getTime() - lastUpdated.getTime();
                long diffMinutes = diffMillis / 60000;
                if (diffMinutes > 1440) {
                    continue;
                }
                SimpleDateFormat sdf = new SimpleDateFormat("MMM dd HH:mm:ss");
                String formattedTime = sdf.format(now);
                // Fetch unit name
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
                String unitName = unit != null ? unit.getName() : "Unknown";
                // Append formatted row
                messageBuilder.append(String.format(
                        "%-10s | %-25s | %-15s | %-15s | %-15s\n",
                        unitId,
                        unitName,
                        "CLOSED",
                        diffMinutes + " minutes",
                        formattedTime
                ));
                hasRows = true;
            }
        } catch (Exception e) {
            LOG.error("Error while generating closure message", e);
        }
        if(!messageBuilder.isEmpty() && hasRows) {
            sendClosureReportToGChat(messageBuilder.toString());
        }
    }

}
