package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuAuditHistory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerMenuAuditDao extends MongoRepository<PartnerMenuAuditHistory, String> {

	List<PartnerMenuAuditHistory> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(Integer unitId, Integer partnerId,
			Integer brandId, String menuType);

	List<PartnerMenuAuditHistory> findAllByUnitIdAndPartnerIdAndBrandIdAndStatus(Integer unitId,
			Integer kettlePartnerId, Integer brandId, String status);

    List<PartnerMenuAuditHistory> findTop15ByUnitIdAndPartnerIdAndBrandIdAndMenuTypeOrderByAddTimeISTDesc(Integer unitId,Integer partnerId,Integer brandId,String menuType);

}
