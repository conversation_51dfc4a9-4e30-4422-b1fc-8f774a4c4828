package com.stpl.tech.kettle.channelpartner.core.notification.email;

import com.stpl.tech.kettle.channelpartner.core.notification.email.templates.OrderMismatchNotificationTemplate;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class OrderMismatchNotification extends EmailNotification {

    private OrderMismatchNotificationTemplate orderMismatchNotificationTemplate;
    private EnvType envType;
    private String subjectOfEmail;

    public OrderMismatchNotification() {
    }

    public OrderMismatchNotification(OrderMismatchNotificationTemplate orderMismatchNotificationTemplate,
                                     EnvType envType) {
        this.orderMismatchNotificationTemplate = orderMismatchNotificationTemplate;
        this.envType = envType;
        //this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        if (ChannelPartnerUtils.isDev(getEnvironmentType())) {
            return new String[]{"<EMAIL>"};
        } else {
            Set<String> mails = new HashSet<>();
            mails.add("<EMAIL>");
            mails.add("<EMAIL>");
            String[] simpleArray = new String[mails.size()];
            return mails.toArray(simpleArray);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        subjectOfEmail = String.format("Order mismatch event for %s order Id %s for %s",
                orderMismatchNotificationTemplate.getOrder().getPartnerName(),
                orderMismatchNotificationTemplate.getPartnerOrderId(),
                ChannelPartnerUtils.getFormattedTime(orderMismatchNotificationTemplate.getOrder().getAddTime(), "EEE dd MMM yyyy"));
        if (ChannelPartnerUtils.isDev(getEnvironmentType())) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return orderMismatchNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }


    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
