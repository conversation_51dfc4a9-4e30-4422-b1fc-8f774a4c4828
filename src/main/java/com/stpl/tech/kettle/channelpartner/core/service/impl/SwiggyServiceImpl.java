package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.amazonaws.regions.Regions;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.SyncLock;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.converters.SwiggyConverters;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.exceptions.SwiggyError;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.CommissionService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.RedisCacheService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.task.MarkStockInOOSItemsTask;
import com.stpl.tech.kettle.channelpartner.core.task.SwiggyOrderTask;
import com.stpl.tech.kettle.channelpartner.core.util.CRMServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.SwiggyServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.ActionCategory;
import com.stpl.tech.kettle.channelpartner.domain.model.CODCustomerLoginData;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.DayUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.RateLimitApi;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.Slot;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyAddOnStockRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyMarkFoodReadyRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyOrderRejectionCodes;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyPartnerSupportRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyPartnerSupportResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRequestMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderArrivalResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyStockRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyStockUpdateItemResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyStockUpdateResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyUnitAvailableRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyVariantStockRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.Addon;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.AddonGroup;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.Entity;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.Item;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.SwiggyMenuRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.SwiggyMenuResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.SwiggyMenuTrackResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.Variant;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.VariantGroup;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.ConnectCustomerObject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.ReasonMetaData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.RejectionMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyCustomerConnect;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderReject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyRejectOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.SwiggyCafeStatusDataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.SwiggyHolidaySlotDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuAuditHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyHolidaySlotData;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.SwiggyOrderOutOfStockDataDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerStockSchedulerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackStatusDao;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpStatusCodeException;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SwiggyServiceImpl implements SwiggyService {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyServiceImpl.class);
    @Autowired
    CommissionService commissionService;
    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private TrackService trackService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private OrderValidationService orderValidationService;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private SwiggyHolidaySlotDao swiggyHolidaySlotDao;

    @Autowired
    private SwiggyCafeStatusDataDao swiggyCafeStatusDataDao;

    @Autowired
    private PartnerMenuService partnerMenuService;

    @Autowired
    private PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;

    @Autowired
    private PartnerOrderService partnerOrderService;
    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    private RedisCacheService redisCacheService;

    @Autowired
    private SwiggyOrderOutOfStockDataDao swiggyOrderOutOfStockDataDao;

    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;


    @Autowired
    private CafeStatusChannelPartnerDao partnerOrderDataDao;

    @Autowired
    private PartnerStockSchedulerDao partnerStockSchedulerDao;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private SyncLock lock;

    @Autowired
    private CafeStatusChannelPartnerService cafeStatusChannelPartnerService;

    @Autowired
    CafeLookUpServiceImpl cafeLookUpService;

    @Autowired
    private PartnerOrderFallbackStatusDao partnerOrderFallbackStatusDao;

    @Autowired
    private PartnerOrderServiceImpl partnerOrderServiceImpl;


    private static final String PARTNER_NAME = "SWIGGY";

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean callSwiggyPartnerSupport(PartnerOrderDetail partnerOrderDetail) {
        SwiggyPartnerSupportRequest swiggyPartnerSupportRequest = new SwiggyPartnerSupportRequest();
        swiggyPartnerSupportRequest.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
        swiggyPartnerSupportRequest.setSwiggyOrderId(Long.parseLong(partnerOrderDetail.getPartnerOrderId()));
        swiggyPartnerSupportRequest.setTimestamp(
                ChannelPartnerUtils.getFormattedTime(partnerOrderDetail.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
        swiggyPartnerSupportRequest.setTimestampOutlet(
                ChannelPartnerUtils.getFormattedTime(partnerOrderDetail.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
        SwiggyPartnerSupportResponse response;
        try {
            response = webServiceHelper.callSwiggyApi(environmentProperties,
                    SwiggyServiceEndpoints.CALL_PARTNER_SUPPORT, HttpMethod.POST, swiggyPartnerSupportRequest,
                    SwiggyPartnerSupportResponse.class);
            if (response != null && response.getStatusCode() == 0) {
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(
                        PartnerActionCode.SWIGGY_CALL_INITIATED, "Requested swiggy support call!", null));
                trackService.updatePartnerOrder(partnerOrderDetail);
                return true;
            } else {
                return false;
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Error calling Swiggy call support API:: ");
        }
        return false;
    }

    private void filterStockOutProductIdsForStockIn(UnitProductsStockEvent event, UnitPartnerBrandKey unitPartnerBrandKey){
        try{
            List<Integer> productIds = event.getProductIds().stream().map(Integer::parseInt).toList();
            List<String> filteredProductIds = new ArrayList<>();
            Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
                    getUnitPartnerBrandMappingMetaData().get(unitPartnerBrandKey).getPriceProfileUnitId());
            for(Integer productId : productIds){
                if(AppUtils.isCombo(masterDataCache.getProduct(productId).getTaxCode())){
                    Set<Integer> comboItems = map.get(productId);
                    comboItems = comboItems.stream().filter(id-> masterDataCache.getProduct(id).isInventoryTracked()).
                            collect(Collectors.toSet());
                    Map productStock = orderValidationService.getUnitProductInventoryByProducts(unitPartnerBrandKey.getUnitId(), comboItems.stream().toList());
                    LOG.info("Inventory map for unit Id  :::: {} :::: Partner :::: {}  Time ::::: {}   ::::::  {} ", unitPartnerBrandKey.getUnitId() , PARTNER_NAME,
                            AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
                    Boolean comboStockOut = comboItems.stream().anyMatch(comboItemId -> (!productStock.containsKey(comboItemId.toString())
                            || (Integer) productStock.get(comboItemId.toString()) <= 0));
                    if(!comboStockOut){
                        filteredProductIds.add(String.valueOf(productId));
                    }else{
                        LOG.info("Combo Id : {} for unit Id : {} is stock out during scheduled stock in event : combo Items : {} ",productId,
                                event.getUnitId(),comboItems);
                    }
                }else{
                    Map productStock = orderValidationService.getUnitProductInventoryByProducts(unitPartnerBrandKey.getUnitId(), Collections.singletonList(productId));
                    LOG.info("Inventory map for unit Id  :::: {} :::: Partner :::: {}  Time ::::: {}   ::::::  {} ", unitPartnerBrandKey.getUnitId() , PARTNER_NAME,
                            AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
                    Boolean productStockOut = productStock.containsKey(productId.toString()) && ((Integer) productStock.get(productId.toString()) <= 0);
                    if(!productStockOut){
                        filteredProductIds.add(String.valueOf(productId));
                    }else{
                        LOG.info("Product Id : {} for unit Id : {} is stock out during scheduled stock in event  ",productId,
                                event.getUnitId());
                    }
                }
            }
            event.setProductIds(filteredProductIds);
        }catch (Exception e){
            LOG.info("Error while filtering stock out products for swiggy : {} ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateSwiggyStock(UnitProductsStockEvent event) {
        if (event != null && event.getUnitId() != null && masterDataCache.getUnit(event.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById()
                    .get(channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId());
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerDetail.getKettlePartnerId()
                            && unitChannelPartnerMapping.getUnit().getId() == event.getUnitId());
            Unit unit = masterDataCache.getUnit(event.getUnitId());
            if (partnerDetail != null && partnerDetail.getPartnerStatus().equals(PartnerStatus.ACTIVE) && mappingValid &&
                    UnitStatus.ACTIVE.equals(unit.getStatus()) && unit.isLive()) {
                List<Integer> brandId = new ArrayList<>();
                if (Objects.nonNull(event.getBrandId())) {
                    brandId = Arrays.asList(event.getBrandId());
                } else {
                    brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID, AppConstants.GNT_BRAND_ID,AppConstants.DOHFUL_BRAND_ID);
                }
                for (Integer brand : brandId) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(event.getUnitId(),
                            brand, partnerDetail.getKettlePartnerId());
                    if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                        if(event.getStatus().equals(StockStatus.STOCK_IN) && Boolean.TRUE.equals(event.getForceStockOut())){
                            filterStockOutProductIdsForStockIn(event,key);
                        }
                        processUnitPartnerBrandMappingEvents(event, key);
                    }
                }
//                for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
//                    if (key.getPartnerId().equals(partnerDetail.getKettlePartnerId()) && key.getUnitId().equals(event.getUnitId())) {
//                        processUnitPartnerBrandMappingEvents(event, key);
//                    }
//                }
            }
        } else {
            String eventJson = new Gson().toJson(event);
            String message= ChannelPartnerUtils.getMessage("Incorrect Event Data",eventJson);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
            LOG.error("incorrect event data:::::{}", message);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateSwiggyStockForScheduledStockUpdate(UnitProductsStockEvent event , PartnerStockUpdateSchedule partnerStockUpdateSchedule) {
        if (event != null && event.getUnitId() != null && masterDataCache.getUnit(event.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById()
                    .get(channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId());
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerDetail.getKettlePartnerId()
                            && unitChannelPartnerMapping.getUnit().getId() == event.getUnitId());
            Unit unit = masterDataCache.getUnit(event.getUnitId());
            if (partnerDetail != null && partnerDetail.getPartnerStatus().equals(PartnerStatus.ACTIVE) && mappingValid &&
                    UnitStatus.ACTIVE.equals(unit.getStatus()) && unit.isLive()) {
                List<Integer> brandId = new ArrayList<>();
                if (Objects.nonNull(event.getBrandId())) {
                    brandId = Arrays.asList(event.getBrandId());
                } else {
                    brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID, AppConstants.GNT_BRAND_ID);
                }
                for (Integer brand : brandId) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(event.getUnitId(),
                            brand, partnerDetail.getKettlePartnerId());
                    if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                        if(event.getStatus().equals(StockStatus.STOCK_IN)){
                            filterStockOutProductIdsForStockIn(event,key);
                        }
                        processUnitPartnerBrandMappingEventsForScheduleStockUpdate(event, key,partnerStockUpdateSchedule);
                    }
                }
//                for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
//                    if (key.getPartnerId().equals(partnerDetail.getKettlePartnerId()) && key.getUnitId().equals(event.getUnitId())) {
//                        processUnitPartnerBrandMappingEvents(event, key);
//                    }
//                }
            }
        } else {
            String eventJson = new Gson().toJson(event);
            String message= ChannelPartnerUtils.getMessage("Incorrect Event Data",eventJson);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
            LOG.error("incorrect event data:::::{}", message);
        }
    }
    private boolean processUnitPartnerBrandMappingEvents(UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
        if (stockEvent == null) {
            LOG.info("Error cloning swiggy stock event:::::");
            return true;
        }
        List<Integer> knockAppStockOutProducts = cafeStatusChannelPartnerService.setKnockAppStockOutProducts(event, key);
        if(event.getStatus().equals(StockStatus.STOCK_IN)){
            filterProductsScheduledForStockUpdate(stockEvent,knockAppStockOutProducts);
        }
        Map<Integer,Boolean> knockAppStockOutMap = knockAppStockOutProducts.stream().collect(Collectors.toMap(productId -> productId,productId -> true,(a,b) -> a));
        filterBrandLevelProductsUsingPricingUnit(stockEvent, key);
        addNormalComboItemsToStockEvent(stockEvent, key,knockAppStockOutMap);
        String stockEventJson = new Gson().toJson(stockEvent);
        LOG.info("Stock ON/OFF API call for brand : {} : {}", key.getBrandId(),stockEventJson);
        Set<String> partnerProductIds = new HashSet<>();
        PartnerUpsellingSuperCombosProdIdDetail unitProductMappingIds = channelPartnerDataCache
                .getPartnerUnitUpsellingCombosProductMappings().get(key);
        if(Boolean.TRUE.equals(event.getForceStockOut())){
            updateHeroComboVariantsStock(event, key, unitProductMappingIds,knockAppStockOutMap);
        }
        Map<Integer, Product> productMap = getProductMap(key);
        if (unitProductMappingIds != null) {
            if(unitProductMappingIds.getProductVariantsMap() != null && !unitProductMappingIds.getProductVariantsMap().isEmpty()) {
                updateDimensionLevelStock(stockEvent, key, unitProductMappingIds);
            }
            if(unitProductMappingIds.getUpsellingProdIds() != null && !unitProductMappingIds.getUpsellingProdIds().isEmpty()) {
                updateUpsellingAndHeroProducts(unitProductMappingIds.getUpsellingProdIds(), stockEvent, key);
            }
            if (unitProductMappingIds.getAddOnProductIds()!=null && !unitProductMappingIds.getAddOnProductIds().isEmpty()) {
                updateUpsellingAndHeroProducts(unitProductMappingIds.getAddOnProductIds(), stockEvent, key);
            }
            if(!CollectionUtils.isEmpty(unitProductMappingIds.getSuperCombosProdIds())){
                updateSuperComboProducts(unitProductMappingIds,stockEvent, key,productMap);
            }
            if (unitProductMappingIds.getProductsIds() != null && !unitProductMappingIds.getProductsIds().isEmpty()) {
                List<String> unitProductIds = getUnitProductsIds(stockEvent.getProductIds(), unitProductMappingIds.getProductsIds());
                if (!unitProductIds.isEmpty()) {
                    partnerProductIds.addAll(unitProductIds);
                }
            }
            try {
                if(Objects.nonNull(unitProductMappingIds.getProductVariantsMap())) {
                    List<String> comboIds = unitProductMappingIds.getProductVariantsMap().keySet().stream().filter(variantMap ->
                            variantMap.contains(ChannelPartnerServiceConstants.HERO_COMBO_IDENTIFIER)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(comboIds)) {
                        Map<String, List<String>> stockMap = partnerMenuService.checkForStockInAndOutByRecipeMinAndMax(event, unitProductMappingIds, key,
                                comboIds,knockAppStockOutMap);
                        if (!org.apache.commons.collections.CollectionUtils.isEmpty(stockMap.get(stockEvent.getStatus().name()))) {
                            partnerProductIds.addAll(stockMap.get(stockEvent.getStatus().name()));
                        }
                    }
                }
            }catch (Exception e){
                LOG.info("Error while updating Stock Event for combo ::::::::::::::: {}",e);
            }
        }
        if(!partnerProductIds.isEmpty()) {
            Integer noOfDays = Boolean.TRUE.equals(event.getForceStockOut()) ? 1 : 2;
            SwiggyStockRequest swiggyStockRequest = new SwiggyStockRequest();
            swiggyStockRequest.setEnable(stockEvent.getStatus().getEnable());
            swiggyStockRequest.setExternalItemIds(partnerProductIds.stream().toList());
            swiggyStockRequest.setFromTime(ChannelPartnerUtils
                    .getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
            swiggyStockRequest.setToTime(ChannelPartnerUtils
                    .getFormattedTime(ChannelPartnerUtils.getDayBeforeOrAfterCurrentDay(noOfDays), "yyyy-MM-dd HH:mm:ss"));
            swiggyStockRequest.setRestaurantId(
                    masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            prepareSwiggyStockRequest(swiggyStockRequest, stockEvent);
        }

        return false;
    }


    private List<String> filterProductsScheduledForStockUpdate(UnitProductsStockEvent event , List<Integer> knockAppStockOutProducts){
        try{
            List<PartnerStockUpdateSchedule> partnerStockSchedulerList = (List<PartnerStockUpdateSchedule>)
                    partnerStockSchedulerDao.findAll();

            List<String> originalSwiggyProductIds = event.getProductIds();
            List<String> forceStockOutProductIds = new ArrayList<>(knockAppStockOutProducts.stream().map(String::valueOf).toList());
            if(!org.apache.commons.collections.CollectionUtils.isEmpty(partnerStockSchedulerList)){
                List<String> scheduledProductIds = new ArrayList<>();
                Date currentTime  =  AppUtils.getCurrentTimestamp();
                for(PartnerStockUpdateSchedule partnerStockUpdateSchedule : partnerStockSchedulerList){
                    if(partnerStockUpdateSchedule.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)
                             && partnerStockUpdateSchedule.getPartnerId().equals(6)
                            && Objects.nonNull(partnerStockUpdateSchedule.getLastExecutionTime())  &&
                            (currentTime.after(partnerStockUpdateSchedule.getStockOutStartTime())
                                    && currentTime.before(partnerStockUpdateSchedule.getStockOutEndTime())
                            )){
                        if(partnerStockUpdateSchedule.getUnitIds().contains(String.valueOf(event.getUnitId()))){
                            List<String> tempList = Arrays.stream(partnerStockUpdateSchedule.getProductIds().split(",")).toList();
                            scheduledProductIds.addAll(tempList);
                            tempList.stream().map(Integer::parseInt).forEach(knockAppStockOutProducts::add);
                        }
                    }
                }
                forceStockOutProductIds.addAll(scheduledProductIds);
            }
            if(!CollectionUtils.isEmpty(forceStockOutProductIds)){
                List<String> filteredProductIds = new ArrayList<>();
                for(String productId : event.getProductIds()){
                    if(!forceStockOutProductIds.contains(productId)){
                        filteredProductIds.add(productId);
                    }
                }
                if(originalSwiggyProductIds.size() != filteredProductIds.size()){
                    LOG.info("original Swiggy Product Ids for Unit Id : {} ::::: {} ",event.getUnitId(),originalSwiggyProductIds);
                    LOG.info("filtered Swiggy Product Ids for Unit Id : {} ::::: {} ",event.getUnitId(),filteredProductIds);
                }
                event.setProductIds(filteredProductIds);
                return forceStockOutProductIds;
            }
        }catch (Exception e){
            LOG.info("Error while Filtering Scheduled Products For Stock in ::::: {} ", e);
        }
        return new ArrayList<>();


    }

    private boolean processUnitPartnerBrandMappingEventsForScheduleStockUpdate(UnitProductsStockEvent event, UnitPartnerBrandKey key
            ,PartnerStockUpdateSchedule partnerStockUpdateSchedule) {
        UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
        if (stockEvent == null) {
            LOG.info("Error cloning swiggy stock event:::::");
            return true;
        }
        event.setForceStockOut(Boolean.TRUE);
        Map<Integer,Boolean> forceStockOutMap = event.getProductIds().stream().map(productId -> Integer.parseInt(productId)).
                collect(Collectors.toMap(productId -> productId,productId -> true,(a,b) -> a));
        //filterBrandLevelProductsUsingPricingUnit(stockEvent, key);
        addNormalComboItemsToStockEvent(stockEvent, key,forceStockOutMap);
        String stockEventJson = new Gson().toJson(stockEvent);
        LOG.info("Stock ON/OFF Scheduled call: {}", stockEventJson);
        Set<String> partnerProductIds = new HashSet<>();
        PartnerUpsellingSuperCombosProdIdDetail unitProductMappingIds = channelPartnerDataCache
                .getPartnerUnitUpsellingCombosProductMappings().get(key);
        Map<Integer, Product> productMap = getProductMap(key);
        if (unitProductMappingIds != null) {
            /*if(unitProductMappingIds.getProductVariantsMap() != null && !unitProductMappingIds.getProductVariantsMap().isEmpty()) {
                updateDimensionLevelStock(stockEvent, key, unitProductMappingIds);
            }*/
            if(unitProductMappingIds.getUpsellingProdIds() != null && !unitProductMappingIds.getUpsellingProdIds().isEmpty()) {
                updateUpsellingAndHeroProducts(unitProductMappingIds.getUpsellingProdIds(), stockEvent, key);
            }
            if (unitProductMappingIds.getAddOnProductIds()!=null && !unitProductMappingIds.getAddOnProductIds().isEmpty()) {
                updateUpsellingAndHeroProducts(unitProductMappingIds.getAddOnProductIds(), stockEvent, key);
            }
            if(!CollectionUtils.isEmpty(unitProductMappingIds.getSuperCombosProdIds())){
                updateSuperComboProducts(unitProductMappingIds,stockEvent, key,productMap);
            }
            if (unitProductMappingIds.getProductsIds() != null && !unitProductMappingIds.getProductsIds().isEmpty()) {
                List<String> unitProductIds = getUnitProductsIds(stockEvent.getProductIds(), unitProductMappingIds.getProductsIds());
                if (!unitProductIds.isEmpty()) {
                    partnerProductIds.addAll(unitProductIds);
                }
            }
        }
        if(!partnerProductIds.isEmpty()) {
            String dateForScheduleStockUpdate = ChannelPartnerUtils
                    .getFormattedTime(AppUtils.addDays(partnerStockUpdateSchedule.getStockOutEndTime(),1)
                            , "yyyy-MM-dd HH:mm:ss");
            Integer noOfDays = Boolean.TRUE.equals(event.getForceStockOut()) ? 1 : 2;
            SwiggyStockRequest swiggyStockRequest = new SwiggyStockRequest();
            swiggyStockRequest.setEnable(stockEvent.getStatus().getEnable());
            swiggyStockRequest.setExternalItemIds(partnerProductIds.stream().toList());
            swiggyStockRequest.setFromTime(ChannelPartnerUtils
                    .getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
            swiggyStockRequest.setToTime(dateForScheduleStockUpdate);
            swiggyStockRequest.setRestaurantId(
                    masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            prepareSwiggyStockRequest(swiggyStockRequest, stockEvent);
        }

        return true;
    }

    public Map<Integer, Product> getProductMap(UnitPartnerBrandKey partnerBrandKey){
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData().get(partnerBrandKey);
        Map<Integer, Product> products = new HashMap<>();
        if(Objects.nonNull(data)){
            masterDataCache.getUnitProductDetails(data.getPriceProfileUnitId()).forEach(product -> products.put(product.getId(), product));
        }
        return products;
    }

    //Dimension level stock fundamentals for Swiggy system:
    //If product has multiple dimensions then send only variants while stock update
    //If product has only single dimension then send only product while stock update
    private void updateDimensionLevelStock(UnitProductsStockEvent event,
                                           UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if ((key.getBrandId().equals(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID) || AppConstants.DOHFUL_BRAND_ID.equals(key.getBrandId()))
                && unitUpsellCombosMapping != null &&
                unitUpsellCombosMapping.getProductVariantsMap() != null && !unitUpsellCombosMapping.getProductVariantsMap().isEmpty()) {
            List<String> productIds = new ArrayList<>();
            List<String> variantIds = new ArrayList<>();
            Map<String, InventoryInfo> inventory = webServiceHelper.callInternalApi(
                    environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE_WEB,
                    environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Map.class, event.getUnitId(), null);
            if (inventory != null && !inventory.keySet().isEmpty()) {
                for (String productId : event.getProductIds()) {
                    Product product = masterDataCache.getProduct(Integer.valueOf(productId));
                    if((Objects.nonNull(product) && ChannelPartnerServiceConstants.COMBO.equalsIgnoreCase(product.getTaxCode()))
                            || Boolean.TRUE.equals(event.getForceStockOut())){
                        productIds.add(productId);
                        continue;
                    }
                    if(inventory.containsKey(productId)) {
                        InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                        LOG.info("Inventory Info For Product Id : {} :::: {} ",productId,new Gson().toJson(inventoryInfo));
                        if (inventoryInfo.getDim() == null || inventoryInfo.getDim().isEmpty()) {
                            productIds.add(productId);
                        } else {
                            addDimensionsWithMatchingStockState(inventoryInfo, event, productIds, variantIds, productId,
                                    unitUpsellCombosMapping);
                        }
                    } else {
                        LOG.info("inventory not found for product id {}", productId);
                        LOG.info(new Gson().toJson(inventory.get(productId)));
                    }
                }
            }
            event.setProductIds(productIds);
            pushDimensionLevelStock(variantIds, event, key, unitUpsellCombosMapping);
        }
    }

    private Map<String,List<String>> updateSuperComboStock(UnitProductsStockEvent event, UnitPartnerBrandKey key,
                                               Set<String> superComboIds, Map<Integer, Product> productMap) {
        Map<String, List<String>> eventMap = new HashMap<>();
        try {
            List<String> idsToStockOut = new ArrayList<>();
            List<String> idsToStockIn = new ArrayList<>();
            Map<String, Map<Integer, List<String>>> productIdsToCheckForInventory = new HashMap<>();
            Map<String, InventoryInfo> inventory = webServiceHelper.callInternalApi(
                    environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE_WEB,
                    environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Map.class, event.getUnitId(), null);
            if (!CollectionUtils.isEmpty(superComboIds)) {
                getProductMapForAllSuperCombos(superComboIds, productMap, productIdsToCheckForInventory);
            }
            if (StockStatus.STOCK_OUT.equals(event.getStatus())) {
                checkingInventoryForStockOutEvent(inventory, superComboIds, productIdsToCheckForInventory, idsToStockOut);
            }
            if (StockStatus.STOCK_IN.equals(event.getStatus())) {
                checkingInventoryForStockInEvent(inventory, superComboIds, productIdsToCheckForInventory, idsToStockIn);
            }
            if (!CollectionUtils.isEmpty(idsToStockOut)) {
                eventMap.put(StockStatus.STOCK_OUT.name(), idsToStockOut);
            }
            if (!CollectionUtils.isEmpty(idsToStockIn)) {
                eventMap.put(StockStatus.STOCK_IN.name(), idsToStockIn);
            }
        }catch (Exception e){
            LOG.info("Error while updating super combo stock ::::: {}",e);
        }
        return eventMap;
    }

    private void getProductMapForAllSuperCombos(Set<String> superComboIds,Map<Integer, Product> productMap,
                                                Map<String, Map<Integer, List<String>>> productIdsToCheckForInventory){
        for (String comboId : superComboIds) {
            Product comboProduct = productMap.get(Integer.valueOf(comboId));
            RecipeDetail recipeDetail = recipeCache.getRecipe(Integer.valueOf(comboId), comboProduct.getPrices().get(0).getDimension(),
                    String.valueOf(comboProduct.getPrices().get(0).getRecipe().getProfile()));
            productIdsToCheckForInventory.put(comboId, new HashMap<>());
            Map<Integer, List<String>> listMap = new HashMap<>();
            int index = 1;
            if(Objects.nonNull(recipeDetail.getIngredient().getCompositeProduct()) && Objects.nonNull(recipeDetail.getIngredient().getCompositeProduct().getDetails())) {
                for (CompositeIngredientData productDetail : recipeDetail.getIngredient().getCompositeProduct().getDetails()) {
                    for (IngredientProductDetail ingredientProductDetail : productDetail.getMenuProducts()) {
                        if (!listMap.containsKey(index)) {
                            listMap.put(index, new ArrayList<>(Arrays.asList(String.valueOf(ingredientProductDetail.getProduct().getProductId()))));
                        }else {
                            listMap.get(index).add(String.valueOf(ingredientProductDetail.getProduct().getProductId()));
                        }
                    }
                    index++;
                }
                productIdsToCheckForInventory.put(comboId,listMap);
            }
        }
    }

    private void checkingInventoryForStockOutEvent(Map<String, InventoryInfo> inventory,Set<String> superComboIds,
                                                   Map<String, Map<Integer, List<String>>> productIdsToCheckForInventory,
                                                   List<String> idsToStockOut){
        if (Objects.nonNull(inventory) && !CollectionUtils.isEmpty(inventory.keySet())) {
            for (String comboId : superComboIds) {
                Map<Integer, List<String>> items = productIdsToCheckForInventory.get(comboId);
                for (int i = 1; i <= items.size(); i++) {
                    List<String> productIds = new ArrayList<>();
                    for (String productId : items.get(i)) {
                        if (inventory.containsKey(productId)) {
                            InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                            if (inventoryInfo.getQuantity() < 1) {
                                productIds.add(String.valueOf(productId));
                            }
                        } else {
                            LOG.info("inventory not found for product id {}", productId);
                            LOG.info(new Gson().toJson(inventory.get(productId)));
                        }
                    }
                    if (productIds.size() == items.get(i).size()) {
                        idsToStockOut.add(comboId);
                        break;
                    }
                }
            }
        }
    }

    private void checkingInventoryForStockInEvent(Map<String, InventoryInfo> inventory,Set<String> superComboIds,
                                                   Map<String, Map<Integer, List<String>>> productIdsToCheckForInventory,
                                                   List<String> idsToStockIn){
        if (Objects.nonNull(inventory) && !CollectionUtils.isEmpty(inventory.keySet())) {
            for (String comboId : superComboIds) {
                Map<Integer,Boolean> map = new HashMap<>();
                for (Map.Entry<Integer, List<String>> items : productIdsToCheckForInventory.get(comboId).entrySet()) {
                    map.put(items.getKey(),false);
                    for (String productId : items.getValue()) {
                        if (inventory.containsKey(productId)) {
                            InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                            if (inventoryInfo.getQuantity() > 0) {
                                map.put(items.getKey(),true);
                            }
                        } else {
                            if(Objects.nonNull(masterDataCache.getProduct(Integer.valueOf(productId)))
                                    && !masterDataCache.getProduct(Integer.valueOf(productId)).isInventoryTracked()){
                                map.put(items.getKey(),true);
                            }else {
                                LOG.info("inventory not found for product id {}", productId);
                                LOG.info(new Gson().toJson(inventory.get(productId)));
                            }
                        }
                    }
                }
                boolean isAtleastOneProductActive = true;
                for(Map.Entry<Integer,Boolean> indexFlagMap : map.entrySet()){
                    if(indexFlagMap.getValue() == false){
                        isAtleastOneProductActive = false;
                    }
                }
                if(isAtleastOneProductActive){
                    idsToStockIn.add(comboId);
                }
                LOG.info("Index map :::: {}", new Gson().toJson(map));
            }
        }
    }

    private void addDimensionsWithMatchingStockState(InventoryInfo inventoryInfo, UnitProductsStockEvent event, List<String> productIds,
                                                     List<String> variantIds, String productId,
                                                     PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        List<String> dimensions = new ArrayList<>();
        if (StockStatus.STOCK_IN.equals(event.getStatus())) {
            //productIds.add(productId);
            if (everyDimensionInSameStockState(inventoryInfo)) {
                dimensions.addAll(inventoryInfo.getDim().keySet());
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() > 0)
                        .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        } else {
            // Updating Inventory to cancel out the diff between inventory and event (Only for stock out event)
            updatingInventoryAsPerEvent(inventoryInfo,event);
            if (everyDimensionInSameStockState(inventoryInfo)) {
                //productIds.add(productId);
                dimensions.addAll(inventoryInfo.getDim().keySet());
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() <= 0)
                        .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        }
        dimensions.forEach(dimension -> {
            if (unitUpsellCombosMapping.getProductVariantsMap().containsKey(productId)) {
                List<String> ids = unitUpsellCombosMapping.getProductVariantsMap().get(productId).
                        stream().filter(variantVendorEntityId ->
                                variantVendorEntityId.toLowerCase().contains(dimension.toLowerCase())
                        ).collect(Collectors.toList());
                variantIds.addAll(ids);
            }
        });
    }

    private void updatingInventoryAsPerEvent(InventoryInfo inventoryInfo, UnitProductsStockEvent event) {
        //Creating a Set for quick lookup of product names that match the id
        try {
            if(!CollectionUtils.isEmpty(inventoryInfo.getDim()) && !CollectionUtils.isEmpty(event.getProductDimensions())) {
                Set<String> eventProductNames = event.getProductDimensions().stream()
                        .filter(eventProduct -> eventProduct.getId() == inventoryInfo.getId())
                        .map(eventProduct -> eventProduct.getName().toLowerCase())
                        .collect(Collectors.toSet());
                inventoryInfo.getDim().replaceAll((key, value) ->
                        eventProductNames.contains(key.toLowerCase()) ? 0 : value);
            }
        }catch (Exception e){
            LOG.info("Error while updating dimension level stock in inventory::::: {}",e);
        }
    }

    private boolean everyDimensionInSameStockState(InventoryInfo inventoryInfo) {
        if (inventoryInfo.getDim().keySet().size() == 1) {
            return true;
        }
        Iterator<Map.Entry<String, Integer>> itr = inventoryInfo.getDim().entrySet().iterator();
        Boolean initialStatus = itr.next().getValue() > 0;
        while (itr.hasNext()) {
            boolean currentStatus = itr.next().getValue() > 0;
            if (currentStatus != Boolean.TRUE.equals(initialStatus)) {
                return false;
            }
        }
        return true;
    }

    private void prepareSwiggyStockRequest(SwiggyStockRequest swiggyStockRequest, UnitProductsStockEvent stockEvent) {
        if (!swiggyStockRequest.getExternalItemIds().isEmpty()) {
            List<String> itemIds = new ArrayList<>(swiggyStockRequest.getExternalItemIds());
            int cap = 40;
            int count = itemIds.size() / cap;
            for (int i = 0; i < count + 1; i++) {
                List<String> externalIds = new ArrayList<>();
                swiggyStockRequest.setExternalItemIds(new ArrayList<String>());
                for (int j = (i * cap); j < (i * cap) + cap && j < itemIds.size(); j++) {
                    externalIds.add(itemIds.get(j));
                }
                if(externalIds.isEmpty()){
                    continue;
                }
                swiggyStockRequest.setExternalItemIds(externalIds);
                executeSwiggyStockRequest(swiggyStockRequest, stockEvent);
            }
        }
    }

    /*private void updateComboProducts(List<String> superCombosProdIds, UnitProductsStockEvent event, List<String> menuProductIds, UnitPartnerBrandKey key) {
        Set<String> comboProductIds = new HashSet<>();
        for (String product : event.getProductIds()) {
            for (String comboIds : superCombosProdIds) {
                String[] ids = comboIds.split("_");
                if (product.equalsIgnoreCase(ids[0])) {
                    comboProductIds.add(comboIds);
                }
            }
        }
        for (String id : comboProductIds) {
            SwiggyVariantStockRequest swiggyVarStockRequest = new SwiggyVariantStockRequest();
            swiggyVarStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            swiggyVarStockRequest.setInStock(event.getStatus().getEnable());
            swiggyVarStockRequest.setExternalVariantId(id);
            swiggyVarStockRequest.setExternalVariantGroupId(getExternalVariantId(id));
            swiggyVarStockRequest.setExternalItemId(getItemId(id, menuProductIds));
            executeSwiggyVariantStockRequest(swiggyVarStockRequest);
        }
    }*/

    private void publishVariantStock(List<String> variantIds, UnitProductsStockEvent event,
                                         UnitPartnerBrandKey key,String parentItemId ) {
        Set<String> republishVariantIds = new HashSet<>();
        for (String id : variantIds) {
            SwiggyVariantStockRequest swiggyVarStockRequest = new SwiggyVariantStockRequest();
            swiggyVarStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            swiggyVarStockRequest.setInStock(event.getStatus().getEnable());
            swiggyVarStockRequest.setExternalItemId(parentItemId);
            swiggyVarStockRequest.setExternalVariantId(id);
            //swiggyVarStockRequest.setExternalVariantGroupId(id.split("_")[0] + "_size");
            executeSwiggyVariantStockRequest(swiggyVarStockRequest,republishVariantIds);
        }
        if(!republishVariantIds.isEmpty()){
            LOG.info("Republishing Variant Toggle event as API rate limit was hit:::{}", new Gson().toJson(event));
            PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
            partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
            event.setProductIds(republishVariantIds.stream().toList());
            partnerActionEvent.setEventData(event);
            redisPublisher.publish(PARTNER_NAME,partnerActionEvent);
        }
    }

    private void updateHeroComboVariantsStock(UnitProductsStockEvent event,
                                              UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping ,
                                              Map<Integer,Boolean> forceStockUpdateMap){
        Map<String,List<String>> productVariantMap = unitUpsellCombosMapping.getProductVariantsMap();
        List<String>  stockUpdateVariantIds =  new ArrayList<>();
        if(StockStatus.STOCK_IN.equals(event.getStatus())){
            event.getProductIds().forEach(pId -> forceStockUpdateMap.put(Integer.parseInt(pId),Boolean.TRUE));
        }
        List<String> heroComboIds = unitUpsellCombosMapping.getProductVariantsMap().entrySet().stream().filter(
                        entry -> entry.getKey().split("_").length == 1 && entry.getValue().size()>0 &&  entry.getValue().stream().anyMatch(variantId ->
                                variantId.contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) && entry.getValue().stream().anyMatch(variantId ->
                                forceStockUpdateMap.containsKey(Integer.parseInt(variantId.split("_")[0]))))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        LOG.info("hero combo Ids : {} " , heroComboIds);
        Map<String,Boolean> heroComboIdsMap = heroComboIds.stream().collect(Collectors.toMap(heroComboId -> heroComboId,heroComboId -> true));
        Map<String,String> catalogueItemIdMap = unitUpsellCombosMapping.getProductsIds().stream()
                .filter(productId -> heroComboIdsMap.containsKey(productId.split("_")[0]))
                .collect(Collectors.toMap(productId -> productId.split("_")[0],productId -> productId));

        for(String heroComboId : heroComboIds){
            try{
                List<String>  variantIds = productVariantMap.get(heroComboId).stream()
                        .filter(variantId -> forceStockUpdateMap.containsKey(Integer.parseInt(variantId.split("_")[0]))).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(variantIds)){
                    if(catalogueItemIdMap.containsKey(heroComboId)){
                        String heroComboCatalogueId = catalogueItemIdMap.get(heroComboId);
                        publishVariantStock(variantIds,event,key,heroComboCatalogueId);
                        stockUpdateVariantIds.addAll(variantIds);
                    }
                }
            }catch (Exception e){
                LOG.info("Exception while parsing hero combo sub item for force stock update");
            }
        }
        if(StockStatus.STOCK_IN.equals(event.getStatus())){
            event.getProductIds().forEach(pId -> forceStockUpdateMap.remove(Integer.parseInt(pId)));
        }
        LOG.info("STOCK out variants: {]" , stockUpdateVariantIds);
    }

    private void pushDimensionLevelStock(List<String> variantIds, UnitProductsStockEvent event,
                                         UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        Set<String> republishVariantIds = new HashSet<>();
        for (String id : variantIds) {
            AtomicReference<String> itemId = new AtomicReference<>();
            unitUpsellCombosMapping.getProductsIds().forEach(productId -> {
                if(productId.split("_")[0].equalsIgnoreCase(id.split("_")[0])) {
                    itemId.set(productId);
                }
            });
            SwiggyVariantStockRequest swiggyVarStockRequest = new SwiggyVariantStockRequest();
            swiggyVarStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            swiggyVarStockRequest.setInStock(event.getStatus().getEnable());
            swiggyVarStockRequest.setExternalItemId(itemId.get());
            swiggyVarStockRequest.setExternalVariantId(id);
            swiggyVarStockRequest.setExternalVariantGroupId(id.split("_")[0] + "_size");
            executeSwiggyVariantStockRequest(swiggyVarStockRequest,republishVariantIds);
        }
        if(!republishVariantIds.isEmpty()){
                LOG.info("Republishing Variant Toggle event as API rate limit was hit:::{}", new Gson().toJson(event));
                PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
                partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
                event.setProductIds(republishVariantIds.stream().toList());
                partnerActionEvent.setEventData(event);
                redisPublisher.publish(PARTNER_NAME,partnerActionEvent);
        }
    }

    private void updateUpsellingAndHeroProducts(List<String> upsellingProdIds, UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        Set<String> upsellingProductIds = new HashSet<>();
        Set<String> republishProductIds = new HashSet<>();
        UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
        for (String product : event.getProductIds()) {
            for (String upsellId : upsellingProdIds) {
                String[] ids = upsellId.split("_");
                if (product.equalsIgnoreCase(ids[0])) {
                    upsellingProductIds.add(upsellId);
                }
            }
        }
        for (String id : upsellingProductIds) {
            SwiggyAddOnStockRequest swiggyAddonStockRequest = new SwiggyAddOnStockRequest();
            swiggyAddonStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            swiggyAddonStockRequest.setExternalAddonId(id);
            swiggyAddonStockRequest.setInStock(event.getStatus().getEnable());
            executeSwiggyAddOnStockRequest(swiggyAddonStockRequest, republishProductIds);
        }
        if (!republishProductIds.isEmpty()) {
            LOG.info("Republishing Addon stock event as API rate limit was hit:::{}", new Gson().toJson(stockEvent));
            PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
            partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
            stockEvent.setProductIds(republishProductIds.stream().toList());
            partnerActionEvent.setEventData(stockEvent);
            redisPublisher.publish(PARTNER_NAME, partnerActionEvent);
        }
    }

    private void updateSuperComboProducts(PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping, UnitProductsStockEvent event,
                                           UnitPartnerBrandKey key,Map<Integer, Product> productMap) {
        try {
            Set<String> comboProductIds = new HashSet<>();
            Set<String> republishProductIds = new HashSet<>();
            Set<String> superComboIds = new HashSet<>();
            Map<String, String> mapOfOriginalIds = new HashMap<>();
            UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
            for (String product : event.getProductIds()) {
                for (Map.Entry<String, List<String>> map : unitUpsellCombosMapping.getProductVariantsMap().entrySet()) {
                    String[] ids = map.getKey().split("_");
                    if (product.equalsIgnoreCase(ids[0]) && !CollectionUtils.isEmpty(map.getValue()) && Objects.nonNull(map.getValue().get(0))
                            && map.getValue().get(0).contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)) {
                        comboProductIds.add(map.getKey());
                        superComboIds.add(ids[2]);
                    }
                }
            }
            for (String comboId : superComboIds) {
                for (String id : unitUpsellCombosMapping.getProductsIds()) {
                    String[] idArray = id.split("_");
                    if (comboId.equalsIgnoreCase(idArray[0])) {
                        mapOfOriginalIds.put(comboId, id);
                    }
                }
            }
            for (String id : comboProductIds) {
                SwiggyAddOnStockRequest swiggyAddonStockRequest = new SwiggyAddOnStockRequest();
                swiggyAddonStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
                swiggyAddonStockRequest.setExternalAddonId(id);
                swiggyAddonStockRequest.setInStock(event.getStatus().getEnable());
                executeSwiggyAddOnStockRequest(swiggyAddonStockRequest, republishProductIds);
            }
            if (!CollectionUtils.isEmpty(superComboIds)) {
                Map<String, List<String>> superComboStockMap = updateSuperComboStock(event, key, superComboIds, productMap);
                if (superComboStockMap.containsKey(StockStatus.STOCK_OUT.name())) {
                    List<String> list = getListOfkey(superComboStockMap.get(StockStatus.STOCK_OUT.name()), mapOfOriginalIds);
                    createSwiggyStockEventForSuperCombo(list, event, key);
                }
                if (superComboStockMap.containsKey(StockStatus.STOCK_IN.name())) {
                    List<String> list = getListOfkey(superComboStockMap.get(StockStatus.STOCK_IN.name()), mapOfOriginalIds);
                    createSwiggyStockEventForSuperCombo(list, event, key);
                }
            }
            if (!republishProductIds.isEmpty()) {
                LOG.info("Republishing Addon stock event as API rate limit was hit:::{}", new Gson().toJson(stockEvent));
                PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
                partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
                stockEvent.setProductIds(republishProductIds.stream().toList());
                partnerActionEvent.setEventData(stockEvent);
                redisPublisher.publish(PARTNER_NAME, partnerActionEvent);
            }
        }catch (Exception e){
            LOG.error("Error While Updating Super Combo Stock For Swiggy ::: unit Id ::: {} event :: {}" , event.getUnitId() , event.getProductIds());
        }
    }

    private List<String> getListOfkey(List<String> comboIds ,Map<String,String> mapOfOriginalIds ){
        List<String> list = new ArrayList<>();
        for(String comboId : comboIds){
            if(Objects.nonNull(mapOfOriginalIds.get(comboId))) {
                list.add(mapOfOriginalIds.get(comboId));
            }
        }
        return list;
    }

    private void createSwiggyStockEventForSuperCombo(List<String> stockProductIds, UnitProductsStockEvent event,
                                                     UnitPartnerBrandKey key) {
        Integer noOfDays = Boolean.TRUE.equals(event.getForceStockOut()) ? 1 : 2;
        if (!CollectionUtils.isEmpty(stockProductIds)) {
            SwiggyStockRequest swiggyStockRequest = new SwiggyStockRequest();
            swiggyStockRequest.setEnable(event.getStatus().getEnable());
            swiggyStockRequest.setExternalItemIds(stockProductIds.stream().toList());
            swiggyStockRequest.setFromTime(ChannelPartnerUtils
                    .getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
            swiggyStockRequest.setToTime(ChannelPartnerUtils
                    .getFormattedTime(ChannelPartnerUtils.getDayBeforeOrAfterCurrentDay(noOfDays), "yyyy-MM-dd HH:mm:ss"));
            swiggyStockRequest.setRestaurantId(
                    masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
            prepareSwiggyStockRequest(swiggyStockRequest, event);
        }
    }

    private void executeSwiggyAddOnStockRequest(SwiggyAddOnStockRequest swiggyAddonStockRequest, Set<String> republishIds) {
        String requestJson = new Gson().toJson(swiggyAddonStockRequest);
        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            swiggyAddonStockRequest.setRestaurantId(ChannelPartnerUtils.getSwiggyTestOutletId());
        }
        try {
            SwiggyCancelResponse response = webServiceHelper.callSwiggyApiStockAPI(environmentProperties,
                    SwiggyServiceEndpoints.ADDON_AVAILABLE, HttpMethod.POST, swiggyAddonStockRequest,
                    SwiggyCancelResponse.class, RateLimitApi.SWIGGY_ADDON_TOGGLE.value());
            LOG.info("Swiggy add-on stock API request:::::{}", requestJson);
            String responseJson = new Gson().toJson(response);
            LOG.info("Swiggy add-on stock API response:::::{}", responseJson);
            if (response == null || response.getStatusCode() != 0) {
                String message = ChannelPartnerUtils.getMessage("Swiggy stock API returned incorrect response", requestJson);
                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION, message);
            }
        } catch (ChannelPartnerException e) {
            if (e.getMessage().equals("RATE_LIMIT_EXCEEDED") || e.getMessage().equals("API rate limit exceeded")) {
                republishIds.add(swiggyAddonStockRequest.getExternalAddonId().split("_")[0]);
            }else {
                catchAndLogException(e, "Swiggy add-on stock stock API returned incorrect response:::::");
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Swiggy add-on stock stock API returned incorrect response:::::");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating add-on stock stock to Swiggy:::::\nrequest::::"
                    + requestJson);
        }
    }


    private void executeSwiggyVariantStockRequest(SwiggyVariantStockRequest swiggyVarStockRequest, Set<String> republishVariantIds) {
        String requestJson = new Gson().toJson(swiggyVarStockRequest);
        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            swiggyVarStockRequest.setRestaurantId(ChannelPartnerUtils.getSwiggyTestOutletId());
        }
        try {
            SwiggyCancelResponse response = webServiceHelper.callSwiggyApiStockAPI(environmentProperties,
                    SwiggyServiceEndpoints.VARIANT_AVAILABLE, HttpMethod.POST, swiggyVarStockRequest,
                    SwiggyCancelResponse.class,RateLimitApi.SWIGGY_VARIANT_TOGGLE.value());
            LOG.info("Swiggy variant stock API request::{}", requestJson);
            String responseJson = new Gson().toJson(response);
            LOG.info("Swiggy variant stock API response:: {}", responseJson);
            if (response == null || response.getStatusCode() != 0) {
                String message= ChannelPartnerUtils.getMessage("Swiggy variant stock API returned incorrect response","::::REQUEST::::"+requestJson+"\n"+"::::RESPONSE::::"+responseJson);
                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
            }
        }catch (ChannelPartnerException e){
            if(e.getMessage().equals("RATE_LIMIT_EXCEEDED") || e.getMessage().equals("API rate limit exceeded")) {
                republishVariantIds.add(swiggyVarStockRequest.getExternalVariantId().split("_")[0]);
            }
        }
        catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Swiggy variant stock stock API returned incorrect response:::::");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating variant stock stock to Swiggy:::::" + "\nrequest::::"
                    + requestJson);
        }
    }

    private void executeSwiggyStockRequest(SwiggyStockRequest swiggyStockRequest, UnitProductsStockEvent event) {
        String requestJson = new Gson().toJson(swiggyStockRequest);
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        try {
            if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                swiggyStockRequest.setRestaurantId(ChannelPartnerUtils.getSwiggyTestOutletId());
            }
            SwiggyCancelResponse response = webServiceHelper.callSwiggyApiStockAPI(environmentProperties,
                    SwiggyServiceEndpoints.ITEM_AVAILABLE, HttpMethod.POST, swiggyStockRequest, SwiggyCancelResponse.class, RateLimitApi.SWIGGY_ITEM_TOGGLE.value());
            String responseJson = new Gson().toJson(response);
            LOG.info("STOCK API REQUEST PROCESSED :::: (SWIGGY) :::: REQUEST :::: {} :::: RESPONSE :::: {}",requestJson,responseJson);
            if (response == null || response.getStatusCode() != 0) {
                String message = ChannelPartnerUtils.getMessage("Swiggy stock API returned incorrect response", "::::REQUEST::::"+requestJson+"\n"+"::::RESPONSE::::"+responseJson);
                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
            } else {
                logStockUpdateSnapshot(swiggyStockRequest, event,response);
            }
        } catch (ChannelPartnerException e) {
            if(e.getMessage().equals("RATE_LIMIT_EXCEEDED") || e.getMessage().equals("API rate limit exceeded")) {
                LOG.info("Republishing stock event as API rate limit was hit:::{}", new Gson().toJson(event));
                PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
                partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
                partnerActionEvent.setEventData(event);
                redisPublisher.publish(PARTNER_NAME,partnerActionEvent);
            } else {
                partnerOrderService.logStockRefreshEvent(event.getUnitId(),partnerId,event.getBrandId(),
                        event.getStatus().name(),AppConstants.FAILED);
                catchAndLogException(e, "Swiggy stock API returned incorrect response:::::");
            }
        } catch (HttpStatusCodeException e) {
            SwiggyError error = new Gson().fromJson(e.getResponseBodyAsString(), SwiggyError.class);
            if(error!=null && "API rate limit exceeded".equals(error.getMessage())){
                LOG.info("Republishing stock event as API rate limit was hit:::{}", new Gson().toJson(event));
                PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
                partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
                partnerActionEvent.setEventData(event);
                redisPublisher.publish(PARTNER_NAME,partnerActionEvent);
            }else{
                partnerOrderService.logStockRefreshEvent(event.getUnitId(),partnerId,event.getBrandId(),
                        event.getStatus().name(),AppConstants.FAILED);
                catchAndLogSwiggyException(e, "Swiggy stock API returned incorrect response:::::");
            }

        } catch (Exception e) {
            partnerOrderService.logStockRefreshEvent(event.getUnitId(), partnerId, event.getBrandId(),
                    event.getStatus().name(), AppConstants.FAILED);
            catchAndLogException(e, "Error updating stock to Swiggy:::::" + "\nrequest::::"
                    + requestJson);
        }
    }

    private List<String> getUnitProductsIds(List<String> productIds, List<String> unitProductMappingIds) {
        List<String> productsNewIds = new ArrayList<>();
        for (String prodId : productIds) {
            for (String unitProdIds : unitProductMappingIds) {
                String[] ids = unitProdIds.split("_");
                if (prodId.equalsIgnoreCase(ids[0])) {
                    productsNewIds.add(unitProdIds);
                }
            }
        }
        return productsNewIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitInventory(List<Integer> unitIds) {
        for (Integer unitId : unitIds) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            partnerOrderService.logStockRefreshEvent(unitId,partnerId,null,"REFRESH_START",AppConstants.STATUS_SUCCESSFUL);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            Unit unit = masterDataCache.getUnit(unitId);
            if (masterDataCache.getUnits().containsKey(unitId) && mappingValid && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()) {
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
                stockOut.setStatus(StockStatus.STOCK_OUT);
                stockOut.setUnitId(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
                stockIn.setStatus(StockStatus.STOCK_IN);
                stockIn.setUnitId(unitId);
                orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
                if (stockOut.getProductIds() != null && !stockOut.getProductIds().isEmpty()) {
                    updateSwiggyStock(stockOut);
                }
                if (stockIn.getProductIds() != null && !stockIn.getProductIds().isEmpty()) {
                    updateSwiggyStock(stockIn);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitInventory(List<Integer> unitIds,Integer brandId) {
        for (Integer unitId : unitIds) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            partnerOrderService.logStockRefreshEvent(unitId,partnerId,brandId,"REFRESH_START",AppConstants.STATUS_SUCCESSFUL);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            Unit unit = masterDataCache.getUnit(unitId);
            if (masterDataCache.getUnits().containsKey(unitId)
                    && mappingValid && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()) {
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
                stockOut.setStatus(StockStatus.STOCK_OUT);
                stockOut.setUnitId(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
                stockIn.setStatus(StockStatus.STOCK_IN);
                stockIn.setUnitId(unitId);
                orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
                if (Objects.nonNull(brandId)) {
                    stockOut.setBrandId(brandId);
                    stockIn.setBrandId(brandId);
                }
                updateSwiggyStock(stockOut);
                updateSwiggyStock(stockIn);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean setUnitAvailability(List<Integer> unitIds, Boolean status, Date startDate, Date endDate, Integer brandId)
            throws ChannelPartnerException {
        for (Integer unitId : unitIds) {
            if (masterDataCache.getUnit(unitId) != null) {
                Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
                boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                        unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
                if (mappingValid) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
                    String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                    SwiggyUnitAvailableRequest swiggyRequest = new SwiggyUnitAvailableRequest();
                    swiggyRequest.setPartnerId(restaurantId);
                    swiggyRequest.setRequestedToOpen(status);
                    if (startDate != null) {
                        swiggyRequest
                                .setFromTime(ChannelPartnerUtils.getFormattedTime(startDate, "yyyy-MM-dd HH:mm:ss"));
                    }
                    if (endDate != null) {
                        swiggyRequest.setToTime(ChannelPartnerUtils.getFormattedTime(endDate, "yyyy-MM-dd HH:mm:ss"));
                    }
                    SwiggyCancelResponse response;
                    try {
                        response = webServiceHelper.callSwiggyApi(environmentProperties,
                                SwiggyServiceEndpoints.UNIT_AVAILABLE, HttpMethod.POST, swiggyRequest,
                                SwiggyCancelResponse.class);
                        if (response == null || response.getStatusCode() != 0) {
                            String responseJson = new Gson().toJson(response);
                            LOG.error("Error in updating unit status on SWIGGY:::: {}", responseJson);
                            String message = ChannelPartnerUtils.getMessage("Error in updating unit status on SWIGGY", "::::REQUEST::::"+new Gson().toJson(swiggyRequest)+"\n"+"::::RESPONSE::::"+new Gson().toJson(response));

                            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                    message);
                            throw new ChannelPartnerException("Error updating unit status.");
                        } else {
                            cafeLookUpService.updatePartnerStatusAfterChangingCafeStatus(unitId, status, brandId, AppConstants.CHANNEL_PARTNER_SWIGGY);
                            LOG.info("Swiggy Status Updated After Changing Cafe Status");
                        }
                    } catch (HttpStatusCodeException e) {
                        catchLogAndThrowSwiggyException(e, "Error in updating unit status on SWIGGY:::: ");
                    }
                }
            }
        }
        return true;
    }

    private OrderDeliveryStatusUpdate getOrderDeliveryStatusUpdate(PartnerOrderDetail partnerOrderDetail,SwiggyRiderStatusRequest  request) {
        Date billingServerTime = AppUtils.parseDate(partnerOrderDetail.getAddTime());
        if(Objects.nonNull(partnerOrderDetail.getKettleOrder())){
                Order order = (Order) partnerOrderDetail.getKettleOrder();
                billingServerTime = order.getBillingServerTime();
        }
        return OrderDeliveryStatusUpdate.builder().orderId(partnerOrderDetail.getKettleOrderId()).unitId(partnerOrderDetail.getUnitId())
                .status(request.getStatus()).partnerName(partnerOrderDetail.getPartnerName()).partnerOrderId(partnerOrderDetail.getPartnerOrderId())
                .addTime(ChannelPartnerUtils.getCurrentTimestamp()).riderName(request.getRiderDetail().getName()).riderContact(request.getRiderDetail().getContactNumber())
                .billingServerTime(billingServerTime).build();
    }

    private void publishToOrderDeliveryStatusQueue(PartnerOrderDetail partnerOrderDetail ,SwiggyRiderStatusRequest request) throws JMSException {
        OrderDeliveryStatusUpdate orderDeliveryStatusUpdate = getOrderDeliveryStatusUpdate(partnerOrderDetail, request);
        sqsNotificationService.publishToSQSFifo(environmentProperties.getEnvType().name(),orderDeliveryStatusUpdate,
                    "_PARTNER_ORDER_DELIVERY_STATUS.fifo", Regions.valueOf(environmentProperties.getAwsQueueRegion()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SwiggyRiderStatusResponse updateSwiggyOrderDeliveryStatus(SwiggyRiderStatusRequest request) {
        List<PartnerOrderDetail> partnerOrderDetails = trackService
                .getPartnerOrderByPartnerOrderId(request.getOrderId().toString());
        SwiggyRiderStatusResponse response = new SwiggyRiderStatusResponse();
        response.setTimestamp(
                ChannelPartnerUtils.getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
        response.setSwiggyOrderId(request.getOrderId());
        if (partnerOrderDetails != null && !partnerOrderDetails.isEmpty()) {
            partnerOrderDetails.forEach(partnerOrderDetail -> {
                PartnerDeliveryStatus partnerDeliveryStatus = new PartnerDeliveryStatus();
                if (request.getRiderDetail() != null) {
                    partnerDeliveryStatus.setRiderName(request.getRiderDetail().getName());
                    partnerDeliveryStatus.setRiderContact(request.getRiderDetail().getContactNumber());
                    if (request.getRiderDetail().getAltContactNumber() != null) {
                        partnerDeliveryStatus.setRiderAltContact(request.getRiderDetail().getAltContactNumber());
                    }
                }
                partnerDeliveryStatus.setStatus(request.getStatus());
                partnerDeliveryStatus.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
                partnerDeliveryStatus.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
                partnerOrderDetail.getDeliveryStatuses().add(partnerDeliveryStatus);
                trackService.updatePartnerOrder(partnerOrderDetail);
                try {
                    if(Objects.nonNull(partnerOrderDetail.getPartnerOrderStatus()) && !PartnerOrderStatus.CHECKED.
                            equals(partnerOrderDetail.getPartnerOrderStatus())){
                        publishToOrderDeliveryStatusQueue(partnerOrderDetail,request);
                    }
                }catch (Exception e) {
                    LOG.info("Error While Publishing Partner Delivery Order Picked Up Event " +
                            "for Swiggy Order Id :::::: {} , {} ", request.getOrderId(), e);
                }
            });
            response.setStatusCode(200);
            response.setStatusMessage("SUCCESS");
        } else {
            response.setStatusCode(-1);
            response.setStatusMessage("Swiggy order id does not exist");
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SwiggyCancelResponse addSwiggyHolidaySlot(SwiggyHolidaySlotData request) {
        try {
            request = swiggyHolidaySlotDao.save(request);
            SwiggyCancelResponse response = new SwiggyCancelResponse();
            if (request != null) {
                String message = ChannelPartnerUtils.getMessage("New holiday slot received from SWIGGY","::::REQUEST::::"+new Gson().toJson(request));
                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), "Orders",
                        message);
                response.setStatusCode(0);
                response.setStatusMessage("Holiday slot saved.");
                return response;
            }
        } catch (Exception e) {
            LOG.error("Error in updating holiday slot from SWIGGY:::: {}", new Gson().toJson(request), e);
            String message = ChannelPartnerUtils.getMessage("Error in updating holiday slot from SWIGGY", "::::REQUEST::::" + new Gson().toJson(request));
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), "Orders",
                    message);
        }
        SwiggyCancelResponse response = new SwiggyCancelResponse();
        response.setStatusCode(-1);
        response.setStatusMessage("Error saving holiday slot.");
        return response;
    }

    private Set<Integer> getKettleProductIdsForOOSItems(SwiggyOrderOutOfStockData swiggyOrderOutOfStockData,UnitPartnerBrandKey key){
        Set<Integer> kettleProductIds = new HashSet<>();
        Map<Integer, Set<Integer>> comboProductMappings = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
                getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
        if (Objects.nonNull(swiggyOrderOutOfStockData.getAddOnIds()) && !swiggyOrderOutOfStockData.getAddOnIds().isEmpty()) {
            for (String id : swiggyOrderOutOfStockData.getAddOnIds()) {
                Integer kettleProductId = Integer.parseInt(id.split("_")[0]);
                kettleProductIds.add(kettleProductId);
                if(comboProductMappings.containsKey(kettleProductId)){
                    Set<Integer> comboProductIds = comboProductMappings.get(kettleProductId);
                    kettleProductIds.addAll(comboProductIds);
                }
            }
        }
        if (Objects.nonNull(swiggyOrderOutOfStockData.getItemIds()) && !swiggyOrderOutOfStockData.getItemIds().isEmpty()) {
            for (String id : swiggyOrderOutOfStockData.getItemIds()) {
                Integer kettleProductId = Integer.parseInt(id.split("_")[0]);
                kettleProductIds.add(kettleProductId);
                if(comboProductMappings.containsKey(kettleProductId)){
                    Set<Integer> comboProductIds = comboProductMappings.get(kettleProductId);
                    kettleProductIds.addAll(comboProductIds);
                }
            }
        }
        if (Objects.nonNull(swiggyOrderOutOfStockData.getVariantIds()) && !swiggyOrderOutOfStockData.getVariantIds().isEmpty()) {
            for (String id : swiggyOrderOutOfStockData.getVariantIds()) {
                Integer kettleProductId = Integer.parseInt(id.split("_")[0]);
                kettleProductIds.add(kettleProductId);
                if(comboProductMappings.containsKey(kettleProductId)){
                    Set<Integer> comboProductIds = comboProductMappings.get(kettleProductId);
                    kettleProductIds.addAll(comboProductIds);
                }
            }
        }
        return  kettleProductIds;
    }

    @Override
    public Boolean markOOSItemsStockIn(String partnerOrderid, UnitPartnerBrandKey key) {
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        LOG.info("Mark Stock In OOS Started For Partner Order Id :::: {} ",partnerOrderid);
        stopwatch.start();
        Optional<SwiggyOrderOutOfStockData> swiggyOrderOutOfStockDataOptional = redisCacheService.getSwiggyOrderOutOfStockData(partnerOrderid);
        SwiggyOrderOutOfStockData swiggyOrderOutOfStockData;
        if (swiggyOrderOutOfStockDataOptional.isPresent()) {
            swiggyOrderOutOfStockData = swiggyOrderOutOfStockDataOptional.get();
        } else {
            return true;
        }
        try {
            Map<Integer, Set<Integer>> productComboMappings = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                    getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
            Set<Integer> kettleProductIds = getKettleProductIdsForOOSItems(swiggyOrderOutOfStockData,key);
            Map<String, InventoryInfo> inventoryInfoMap = new HashMap<>();
            ObjectMapper mapper = new ObjectMapper();
            if(!kettleProductIds.isEmpty()){
                Map inventoryInfoMapTemp = orderValidationService.getUnitProductLiveInventoryByProducts(key.getUnitId(), new ArrayList<>(kettleProductIds));
                String mapString = new Gson().toJson(inventoryInfoMapTemp);
                inventoryInfoMap = new Gson().fromJson(
                        mapString, new TypeToken<HashMap<String, InventoryInfo>>() {}.getType()
                );
            }
            Set<String> stockInProducts = new HashSet<>();
            Set<String> stockInDimensions = new HashSet<>();

            if (Objects.isNull(inventoryInfoMap)) {
                swiggyOrderOutOfStockDataDao.delete(swiggyOrderOutOfStockData);
                return true;
            }
            for (String productId : inventoryInfoMap.keySet()) {
                InventoryInfo inventoryInfo = inventoryInfoMap.get(productId);
                if (inventoryInfo!=null && inventoryInfo.getQuantity() > 0) {
                    stockInProducts.add(productId);
                    if(productComboMappings.containsKey(Integer.valueOf(productId))){
                        Set<String> combos = productComboMappings.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                        stockInProducts.addAll(combos);
                    }

                }
                if (inventoryInfo!=null && inventoryInfo.getDim() != null) {
                    for (String dim : inventoryInfo.getDim().keySet()) {
                        if (inventoryInfo.getDim().get(dim) != null && inventoryInfo.getDim().get(dim) > 0) {
                            stockInDimensions.add(productId + "_" + dim);
                        }
                    }
                }
            }
            LOG.info("Stocked In Product Ids For OOS Marked Swiggy Order :::: {} ",new Gson().toJson(stockInProducts));
            LOG.info("Stocked In Product Dimensions  For OOS Marked Swiggy Order :::: {} ",new Gson().toJson(stockInDimensions));
            Map<String, Boolean> stockInMap = stockInProducts.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                return true;
            }));
            Map<String, Boolean> stockInDimensionMap = stockInDimensions.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                return true;
            }));

            if (Objects.nonNull(swiggyOrderOutOfStockData.getAddOnIds()) && !swiggyOrderOutOfStockData.getAddOnIds().isEmpty()) {
                for (String id : swiggyOrderOutOfStockData.getAddOnIds()) {
                    if (!stockInMap.containsKey(id.split("_")[0])) {
                        continue;
                    }
                    SwiggyAddOnStockRequest swiggyAddonStockRequest = new SwiggyAddOnStockRequest();
                    swiggyAddonStockRequest.setRestaurantId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
                    swiggyAddonStockRequest.setExternalAddonId(id);
                    swiggyAddonStockRequest.setInStock(true);
                    executeSwiggyAddOnStockRequest(swiggyAddonStockRequest,new HashSet<>());
                }
            }

            if (Objects.nonNull(swiggyOrderOutOfStockData.getItemIds()) && !swiggyOrderOutOfStockData.getItemIds().isEmpty()) {
                List<String> stockInItemIds = swiggyOrderOutOfStockData.getItemIds().stream().filter(id -> stockInMap.containsKey(id.split("_")[0])).
                        collect(Collectors.toList());
                swiggyOrderOutOfStockData.setItemIds(stockInItemIds);
                SwiggyStockRequest swiggyStockRequest = new SwiggyStockRequest();
                swiggyStockRequest.setEnable(true);
                swiggyStockRequest.setExternalItemIds(swiggyOrderOutOfStockData.getItemIds());
                swiggyStockRequest.setFromTime(ChannelPartnerUtils
                        .getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
                swiggyStockRequest.setToTime(ChannelPartnerUtils
                        .getFormattedTime(ChannelPartnerUtils.getDayBeforeOrAfterCurrentDay(2), "yyyy-MM-dd HH:mm:ss"));
                swiggyStockRequest.setRestaurantId(
                        masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());

                UnitProductsStockEvent stockEvent = new UnitProductsStockEvent(key.getUnitId(), StockStatus.STOCK_IN);
                stockEvent.setBrandId(key.getBrandId());
                stockEvent.setPartnerId(key.getPartnerId());
                stockEvent.setProductIds(swiggyOrderOutOfStockData.getItemIds());
                prepareSwiggyStockRequest(swiggyStockRequest, stockEvent);
            }

            if (Objects.nonNull(swiggyOrderOutOfStockData.getVariantIds()) && !swiggyOrderOutOfStockData.getVariantIds().isEmpty()) {
                PartnerUpsellingSuperCombosProdIdDetail unitProductMappingIds = channelPartnerDataCache
                        .getPartnerUnitUpsellingCombosProductMappings().get(key);
                List<String> stockInVariantIds = swiggyOrderOutOfStockData.getVariantIds().stream().filter(stockInDimensionMap::containsKey)
                        .collect(Collectors.toList());
                swiggyOrderOutOfStockData.setVariantIds(stockInVariantIds);
                List<String> variantIds = new ArrayList<>();
                UnitProductsStockEvent stockEvent = new UnitProductsStockEvent(key.getUnitId(), StockStatus.STOCK_IN);
                stockEvent.setBrandId(key.getBrandId());
                stockEvent.setPartnerId(key.getPartnerId());
                stockEvent.setProductIds(new ArrayList<>());
                variantIds.addAll(swiggyOrderOutOfStockData.getVariantIds());
                if (unitProductMappingIds != null) {
                    if (unitProductMappingIds.getProductVariantsMap() != null && !unitProductMappingIds.getProductVariantsMap().isEmpty()) {
                        pushDimensionLevelStock(variantIds, stockEvent, key, unitProductMappingIds);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error While Stocking in OOS Marked Products Of Partner Order Id :::: {} :::: {}", partnerOrderid, e);
            return false;
        }
        swiggyOrderOutOfStockDataDao.delete(swiggyOrderOutOfStockData);
        LOG.info("Mark Stock In Completed For Order Id :::: {} in :::: {} ms ",partnerOrderid,stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return true;


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SwiggyCancelResponse cancelSwiggyOrder(SwiggyCancelRequest request, boolean isManual,String employeeId) {
        SwiggyCancelResponse swiggyCancelResponse = new SwiggyCancelResponse();
        if (request != null) {
            List<PartnerOrderDetail> partnerOrderDetails = trackService
                    .getPartnerOrderByPartnerOrderId(Long.toString(request.getSwiggyOrderId()));
            partnerOrderDetails.forEach(partnerOrderDetail -> {
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(
                        PartnerActionCode.CANCEL_REQUESTED, "Order cancellation requested by SWIGGY", null));
                partnerOrderDetail.getOrderStateLogs()
                        .add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                                PartnerOrderStatus.CANCEL_REQUESTED, isManual, null));
                if (partnerOrderDetail.getKettleOrder() != null) {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCEL_REQUESTED);
                    Integer approvedBy = ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID;
                    orderValidationService.cancelOrder(partnerOrderDetail, isManual,request.getCancellationReason(), approvedBy);
                } else {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCELLED);
                    partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(PartnerOrderStatus.CANCEL_REQUESTED,
                            PartnerOrderStatus.CANCELLED, false, null));
                    trackService.updatePartnerOrder(partnerOrderDetail);
                }
                swiggyCancelResponse.setStatusCode(0);
                swiggyCancelResponse.setStatusMessage("order has been successfully cancelled");
                PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
                if(Objects.nonNull(statusLogPrev) ){
                    partnerOrderServiceImpl.logFallbackData(partnerOrderDetail, String.valueOf(PartnerActionCode.CANCELLED),"Swiggy Order Cancelled ",ActionCategory.SWIGGY_ORDER_CANCELLED.name(),statusLogPrev,employeeId);
                }
            });

        }
        return swiggyCancelResponse;
    }

    private Boolean isOrderEligibleForEdit(List<PartnerOrderDetail> partnerOrderDetailList){
        Boolean isEligible = true;
        if(partnerOrderDetailList.size()>1){
            PartnerOrderDetail originalPartnerOrder = partnerOrderDetailList.get(0);
            if(originalPartnerOrder.getPartnerOrderStatus().equals(PartnerOrderStatus.EDIT_CANCEL_REQUESTED)
            || originalPartnerOrder.getPartnerOrderStatus().equals(PartnerOrderStatus.EDIT_CANCELLED)){
                  isEligible = false;
            }
        }
        return isEligible;
    }

    private void cancelPreviousKettleOrders(List<PartnerOrderDetail> previousOrdersList , Boolean isManual){
        List<PartnerOrderStatus> nonEligibleCancellationStatus = new ArrayList<>(Arrays.asList(PartnerOrderStatus.EDIT_CANCELLED,
                PartnerOrderStatus.EDIT_CANCEL_REQUESTED
        ,PartnerOrderStatus.CANCELLED));
          previousOrdersList.forEach(partnerOrderDetail -> {
              try{
                  PartnerOrderStatus currentStatus = partnerOrderDetail.getPartnerOrderStatus();
                  if(!nonEligibleCancellationStatus.contains(partnerOrderDetail.getPartnerOrderStatus())){
                      if (partnerOrderDetail.getKettleOrder() != null) {
                          partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.EDIT_CANCEL_REQUESTED);
                          partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(
                                  PartnerActionCode.EDIT_CANCEL_REQUESTED, "Order edit requested", null));
                          Integer approvedBy = ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID;
                          orderValidationService.cancelOrder(partnerOrderDetail, isManual,null, approvedBy);
                      }else{
                          partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.EDIT_CANCELLED);
                          partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(
                                  PartnerActionCode.EDIT_CANCELLED, "Order cancelled due to edit request", null));
                      }
                      partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(currentStatus,
                              partnerOrderDetail.getPartnerOrderStatus(), isManual, null));
                      partnerOrderDetail.setBeingProcessed(false);
                      partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                  }
              }catch (Exception e){
                  LOG.error("Error while cancelling previous order in edit order : {}  ",e);
              }
          });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public SwiggyOrderResponse  addSwiggyOrder(SwiggyOrderRequest request, boolean isManual) {
        if (request.isOrderEdit()) {
            List<PartnerOrderDetail> pods = trackService.getPartnerOrderByPartnerOrderId(request.getOrderId());
            if (pods != null && !pods.isEmpty()) {
                Collections.sort(pods, Comparator.comparing(PartnerOrderDetail::getAddTime).reversed());
                if(Boolean.TRUE.equals(isOrderEligibleForEdit(pods))) {
                    for (PartnerOrderDetail pod : pods) {
                        if (pod != null && pod.getLinkedOrderId() == null) {
                            int minutes = ChannelPartnerUtils.getSecondsDiff(pod.getAddTime(),
                                    ChannelPartnerUtils.getCurrentTimestamp()) / 60;
                            if (minutes <= 45) {
                                cancelPreviousKettleOrders(pods,isManual);
                                return addSwiggyOrder(request, pod.getPartnerOrderId(), isManual);
                            }
                        }
                    }
                }

            }
            SwiggyOrderResponse response = new SwiggyOrderResponse();
            response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
            response.setSwiggyOrderId(Long.valueOf(request.getOrderId()));
            response.setExternalOrderId(request.getOrderId());
            response.setStatusMessage("Swiggy Order id is not valid or order time exceeded 45 minutes");
            response.setStatusCode(0);
            return response;
        } else {
            return addSwiggyOrder(request, null, isManual);
        }
    }

    @Override
    public void runOOSItemsStockInTask(String partnerOrderId , UnitPartnerBrandKey key){
        if(redisCacheService.getSwiggyOrderOutOfStockData(partnerOrderId).isPresent()){
            MarkStockInOOSItemsTask stockInOOSItemsTask = MarkStockInOOSItemsTask.builder().partnerOrderId(partnerOrderId)
                    .key(key).swiggyService(this).build();
            scheduledThreadPoolExecutor.schedule(stockInOOSItemsTask,5,TimeUnit.MINUTES);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException {
        if (partnerOrderDetail != null) {
            Order order = null;
            SwiggyOrderRequest request = null;
            partnerOrderDetail.setBeingProcessed(true);
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
            Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
            if (partnerOrderDetail != null) {

                try {
                    switch (partnerOrderDetail.getPartnerOrderStatus()) {
                        case RECEIVED:
                            order = convertOrder(partnerOrderDetail, true, partnerProductTaxMap);
                            checkOrder(partnerOrderDetail, order, partnerProductTaxMap, true);
                            request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
                            setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck, request);
                            break;
                        case CHECKED:
                            if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null && partnerOrderDetail.getToBeProcessed())) {
                                order = convertOrder(partnerOrderDetail, true, partnerProductTaxMap);
                                request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
                                setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck, request);
                            }
                            break;
                        case FAILED:
                            order = convertOrder(partnerOrderDetail, true, partnerProductTaxMap);
                            request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
                            setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck, request);
                            break;
                        case PLACED:
                            request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
                            if (!request.isOrderEdit()) {
                                notifyOrder(partnerOrderDetail, true);
                            }
                            break;
                        default:
                            partnerOrderDetail.setBeingProcessed(false);
                            trackService.updatePartnerOrder(partnerOrderDetail);
                    }
                } catch (Exception e) {
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    LOG.error("Error in manual order process:", e);
                }
            } else {
                throw new ChannelPartnerException("Error in processing the order!");
            }
        } else {
            throw new ChannelPartnerException("Please provide proper order details for processing!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void confirmSwiggyOrder(SwiggyPartnerSupportRequest request, boolean isManual) {
        long partnerOrderId = request.getSwiggyOrderId();
        List<PartnerOrderDetail> partnerOrderDetails = trackService
                .getPartnerOrderByPartnerOrderId(Long.toString(partnerOrderId));
        if (partnerOrderDetails != null && !partnerOrderDetails.isEmpty()) {
            for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetails) {
                if (partnerOrderDetail != null && !partnerOrderDetail.isNotified()) {
                    try {
                        SwiggyPartnerSupportResponse response = webServiceHelper.callSwiggyApi(
                                environmentProperties, SwiggyServiceEndpoints.CONFIRM_ORDER, HttpMethod.POST,
                                request, SwiggyPartnerSupportResponse.class);
                        String responseJson = new Gson().toJson(response);
                        LOG.info("SWIGGY notification response:::::::::::::::::{}", responseJson);
                        orderValidationService.updatePartnerNotificationStatus(partnerOrderDetail,
                                response != null && response.getStatusCode() == 0, isManual);
                        if (response != null && response.getStatusCode() != 0) {
                            String message = ChannelPartnerUtils.getMessage("Error in confirming order status on SWIGGY","::::REQUEST::::"+new Gson().toJson(request)+"\n"+"::::RESPONSE::::"+new Gson().toJson(response));
                            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                    message);
                        }
                    } catch (HttpStatusCodeException e) {
                        LOG.error("Error confirming Swiggy order:: {}", e.getResponseBodyAsString());
                        String message = ChannelPartnerUtils.getMessage("Error in confirming order status on SWIGGY","::::::::REQUEST::::::::"+new Gson().toJson(request)+"\n"+":::::::RESPONSE:::::::"+e.getResponseBodyAsString());
                        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                message);
                        LOG.error("Error confirming order:", e);
                    } catch (Exception e) {
                        LOG.error("Error confirming Swiggy order:: ", e);
                        String message = ChannelPartnerUtils.getMessage("Error in confirming order status on SWIGGY","::::::::REQUEST::::::::"+new Gson().toJson(request)+"\n"+":::::::RESPONSE:::::::"+e.getMessage());
                        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                message);
                        LOG.error("Error confirming order:", e);
                    }
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                }
            }
        }
    }

    @Override
    public Integer getRiderTimeOfArrival(String orderId, PartnerOrderDetail partnerOrderDetail)
            throws ChannelPartnerException {
        try {
            LOG.info("Getting time to arrive from SWIGGY::::::::::::::::::::");
            Map<String, String> uriVariables = new HashMap<>();
            uriVariables.put("orderId", partnerOrderDetail.getPartnerOrderId());
            SwiggyRiderArrivalResponse response = webServiceHelper.callSwiggyApi(environmentProperties,
                    SwiggyServiceEndpoints.RIDER_TIME_TO_ARRIVE, HttpMethod.GET, null, SwiggyRiderArrivalResponse.class,
                    uriVariables);
            if (response != null && response.getStatusCode() == 0) {
                return response.getData().getTimeToArrive();
            }
        } catch (HttpStatusCodeException e) {
            catchLogAndThrowSwiggyException(e, "Error getting rider time of arrival:");
        } catch (Exception e) {
            catchAndLogException(e, "Error getting rider time of arrival:");
        }
        throw new ChannelPartnerException("Invalid response",
                "Error getting rider arrival time. Please call Swiggy support.");
    }


    @Override
    public void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) {
        switch (request.getState()) {
            case PREPARED:
                markFoodReadyStatusUpdate(partnerOrderDetail);
                break;
            case ASSIGNED:
                break;
            case PICKEDUP:
                break;
            case DELIVERED:
                break;
            default:
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void savePartnerOrderDataMysql(PartnerOrderDetail partnerOrderDetail , Integer kettleOrderId){
        try {
            SwiggyOrderRequest swiggyOrderRequest = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
            PartnerOrderData partnerOrderData = new PartnerOrderData();
            partnerOrderData.setPartnerId(AppConstants.CHANNEL_PARTNER_SWIGGY);
            partnerOrderData.setPartnerOrderId(swiggyOrderRequest.getOrderId());
            partnerOrderData.setKettleOrderId(kettleOrderId);
            partnerOrderData.setCustomerId(swiggyOrderRequest.getCustomerId());
            partnerOrderData.setCustomerName(swiggyOrderRequest.getCustomerName());
            partnerOrderData.setCartGst(BigDecimal.valueOf(swiggyOrderRequest.getCartGst()));
            partnerOrderData.setOrderPackagingCharges(BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingCharges()));
            partnerOrderData.setRestaurantGrossBill(BigDecimal.valueOf(swiggyOrderRequest.getRestaurantGrossBill()));
            partnerOrderData.setRestaurantDiscount(BigDecimal.valueOf(swiggyOrderRequest.getRestaurantDiscount()));
            partnerOrderData.setIsPriortizedOrder(ChannelPartnerUtils.isPriortizedOrder(swiggyOrderRequest.getTags(),
                    environmentProperties.getSwiggyBoltIdentifier()));
            partnerOrderDataDao.add(partnerOrderData);
        }catch (Exception e){
            LOG.error("Error While Saving partner Order Data in Mysql");
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual,
                           boolean skipInventoryCheck) {
        SwiggyOrderRequest request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Stopwatch watch = Stopwatch.createUnstarted();
        orderValidationService.tagOrder(partnerOrderDetail, order);
        if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null && partnerOrderDetail.getToBeProcessed())) {
            // Sending order to KETTLE
            String orderJson = new Gson().toJson(order);
            LOG.info(orderJson);
            Map<String, Object> uriVariables = new HashMap<>();
            uriVariables.put("addMetadata", true);
            boolean logFailure = false;
            try {
                try {
                    if (Objects.nonNull(order.getPartnerCustomerId()) && order.getPartnerCustomerId().length() < 4) {
                        int partnerDiff = 6 - order.getPartnerCustomerId().length();
                        order.setPartnerCustomerId(String.join("", Collections.nCopies(partnerDiff, "0")) + order.getPartnerCustomerId());
                    }
                }catch (Exception e){
                    LOG.info("Exception Faced While generating partner customer id");
                }
                order = webServiceHelper.callInternalApi(
                        environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_ORDER,
                        environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Order.class, order,
                        uriVariables);
                if(order!=null){
                    watch.start();
                    commissionService.commissionCalculation(order,partnerOrderDetail);
                    LOG.info("{},SWIGGY STEP ,Commission Calculation",
                            watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
                if (order != null) {
                    partnerOrderDetail.setKettleOrder(order);
                    partnerOrderDetail.setKettleOrderId(order.getOrderId().toString());
                    partnerOrderDetail.getOrderStateLogs()
                            .add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                                    PartnerOrderStatus.PLACED, isManual, currentTime));
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.PLACED);
                    if (request.isOrderEdit()) {
                        partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(
                                PartnerOrderStatus.PLACED, PartnerOrderStatus.NOTIFIED, isManual, currentTime));
                        partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.NOTIFIED);
                    }
                    savePartnerOrderDataMysql(partnerOrderDetail,order.getOrderId());
                    partnerOrderDetail.getOrderActions().add(
                            trackService.generatePartnerOrderAction(PartnerActionCode.PLACED, "Order placed!", null));
                    if (request.isOrderEdit()) {
                        partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(
                                PartnerActionCode.ORDER_CONFIRMED, "Confirmed swiggy order!", null));
                    }
                    partnerOrderDetail.setBeingProcessed(false);
                    partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                } else {
                    logFailure = true;
                }
            } catch (Exception e) {
                LOG.error("Swiggy order exception {}", partnerOrderDetail.getPartnerOrderId(), e);
                logFailure = true;
            }
            if (logFailure || environmentProperties.isTestingModeforNotification()) {
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(
                        partnerOrderDetail.getPartnerOrderStatus(), PartnerOrderStatus.FAILED, isManual, currentTime));
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.FAILED);
                partnerOrderDetail.getOrderActions().add(trackService
                        .generatePartnerOrderAction(PartnerActionCode.FAILED, "Order failed to punch!", null));
                partnerOrderDetail.setBeingProcessed(false);
                trackService.updatePartnerOrder(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
            }
        } else {
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
            partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
            partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual) {
        // Add to queue for notification
        SwiggyPartnerSupportRequest swiggyPartnerSupportRequest = new SwiggyPartnerSupportRequest();
        swiggyPartnerSupportRequest.setTimestamp(
                ChannelPartnerUtils.getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
        swiggyPartnerSupportRequest.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
        swiggyPartnerSupportRequest.setSwiggyOrderId(Long.parseLong(partnerOrderDetail.getPartnerOrderId()));
        swiggyPartnerSupportRequest.setTimestampOutlet(swiggyPartnerSupportRequest.getTimestamp());
        SwiggyOrderRequest request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
        if(request.getPrepTimeDetails().getPredictedPrepTime() > 0) {
            SwiggyRequestMetadata metadata = new SwiggyRequestMetadata();
            metadata.setUpdatedPrepTime(request.getPrepTimeDetails().getPredictedPrepTime());
            swiggyPartnerSupportRequest.setMetadata(metadata);
        }
        confirmSwiggyOrder(swiggyPartnerSupportRequest, isManual);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateSwiggyMenu(Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer kettlePartnerId, Integer employeeId) throws ChannelPartnerException {
        LOG.info("Enter updateSwiggyMenu ");
        Map<String, String> pathVariables = new HashMap<>();
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
        if (mappingValid) {
            UnitMenuAddVO request = createSwiggyMenuRequestObj(partnerId, unitId, eventType, brandId, employeeId);
            SwiggyMenuRequest menuRequest = (SwiggyMenuRequest) request.getMenuRequest();
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
            String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
            pathVariables.put("id", restaurantId);
            if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                pathVariables.put("id", ChannelPartnerUtils.getSwiggyTestOutletId());
            }
            updateInStockValueForMenuPush(menuRequest, request.getUnitId(), request.getBrandId());
            callSwiggyMenuPush(menuRequest, pathVariables, request);
        }
    }

    private void updateSwiggyUnitProductsMappings(SwiggyMenuRequest menuRequest, UnitMenuAddVO request) {
        List<String> productsIds = new ArrayList<>();
        Set<String> upsellingProdIds = new HashSet<>();
        List<String> addOnProductIds = new ArrayList<>();
        Set<String> superCombosProductIds = new HashSet<>();
        Map<String, List<String>> productVariantsMap = new HashMap<>();
        for (Item item : menuRequest.getEntity().getItems()) {
            productsIds.add(item.getId());
            for (AddonGroup addOnGroup : item.getAddonGroups()) {
//                if (addOnGroup.getId().contains(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER) ||
//                        addOnGroup.getId().contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                if(addOnGroup.getId().contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)){
                    String[] addOnGroupId = addOnGroup.getId().split("_");
                    superCombosProductIds.add(addOnGroupId[0]);
                }
                for (Addon addOn : addOnGroup.getAddons()) {
                    try {
                        if (addOn.getId().contains(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER) ||
                                addOnGroup.getId().contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                            upsellingProdIds.add(addOn.getId());
                        } else if (addOn.getId().contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)) {
                            productVariantsMap.put(addOn.getId(), Collections.singletonList(addOn.getId()));
                        } else if (masterDataCache.getProduct(Integer.parseInt(addOn.getId())).isInventoryTracked()) {
                            addOnProductIds.add(addOn.getId());
                        }
                    } catch (NumberFormatException ignored) {

                    }
//                    }
                }
            }
            List<String> variantIds = new ArrayList<>();
            for (VariantGroup variantGroup : item.getVariantGroups()) {
                for (Variant variant : variantGroup.getVariants()) {
                    variantIds.add(variant.getId());
                }
            }
            String productId = item.getId().split("_")[0];
            if(!superCombosProductIds.contains(productId)) {
                productVariantsMap.put(productId, variantIds);
            }
        }
        if (!productsIds.isEmpty()) {
            partnerMenuService.addPartnerUnitMenuMappingForUpsellingProds(productsIds, request, new ArrayList<>(upsellingProdIds),
                    new ArrayList<>(superCombosProductIds), productVariantsMap, addOnProductIds, null, null);
            //channelPartnerDataCache.loadSwiggyUnitProductMappings();
            channelPartnerDataCache.loadUnitUpsellingSuperComboMapping(request.getKettlePartnerId(), request.getUnitId(), request.getBrandId());
        }
    }

    /*private void fixMenuRequest(SwiggyMenuRequest menuRequest) {
        if (menuRequest != null && menuRequest.getEntity() != null && menuRequest.getEntity().getItems() != null) {
            for (Item item : menuRequest.getEntity().getItems()) {
                if (item.getPrice() == 0.0) {
                    item.setPrice(50.50);
                }
                if (CollectionUtils.isNotEmpty(item.getVariantGroups())) {
                    for (VariantGroup variantGroup : item.getVariantGroups()) {
                        if (CollectionUtils.isNotEmpty(variantGroup.getVariants())) {
                            for (Variant variant : variantGroup.getVariants()) {
                                if (variant != null && variant.getPrice() == null) {
                                    variant.setPrice(50.50);
                                }
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(item.getAddonGroups())) {
                    for (AddonGroup addonGroup : item.getAddonGroups()) {
                        if (CollectionUtils.isNotEmpty(addonGroup.getAddons())) {
                            for (Addon addon : addonGroup.getAddons()) {
                                if (addon != null && addon.getPrice() == null) {
                                    addon.setPrice(50.50);
                                }
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(item.getPricingCombinations())) {
                    for (PricingCombination pricingCombination : item.getPricingCombinations()) {
                        if (pricingCombination != null && pricingCombination.getPrice() == null) {
                            pricingCombination.setPrice(50.50);
                        }
                    }
                }
            }
        }
    }*/

    @Override
    public UnitMenuAddVO createSwiggyMenuRequestObj(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) throws ChannelPartnerException {
        LOG.info("Enter createSwiggyMenuRequestObj where partnerId is {} unitId is {}", partnerId, unitId);
        UnitMenuAddVO request = new UnitMenuAddVO();
        request.setKettlePartnerId(partnerId);
        request.setEmployeeId(employeeId);
        request.setBrandId(brandId);
        request.setUnitId(unitId);
        request.setRegion(masterDataCache.getUnit(unitId).getRegion());
        Object menuData = null;
        if (PartnerActionEventType.UPDATE_MENU.equals(eventType)) {
            menuData = partnerMenuService.getPartnerMenuDetail(request).getMenuData();
        } else if (PartnerActionEventType.UPDATE_UNIT_MENU.equals(eventType)) {
            menuData = partnerMenuService.getActivePartnerUnitMenuDetail(partnerId, unitId, brandId).getMenuData();
        }
        if (menuData != null) {
            SwiggyMenuRequest menuRequest = new SwiggyMenuRequest();
            menuRequest.setEntity(ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), Entity.class));
            request.setMenuRequest(menuRequest);
        }
        LOG.info("Exit createSwiggyMenuRequestObj");
        return request;
    }

    private void markFoodReadyStatusUpdate(PartnerOrderDetail partnerOrderDetail) {
        SwiggyMarkFoodReadyRequest swiggyRequest = new SwiggyMarkFoodReadyRequest();
        swiggyRequest.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
        swiggyRequest.setSwiggyOrderId(Long.parseLong(partnerOrderDetail.getPartnerOrderId()));
        swiggyRequest.setTimestamp(
                ChannelPartnerUtils.getFormattedTime(ChannelPartnerUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss"));
        SwiggyCancelResponse response;
        try {
            response = webServiceHelper.callSwiggyApi(environmentProperties,
                    SwiggyServiceEndpoints.MARK_FOOD_READY, HttpMethod.POST, swiggyRequest, SwiggyCancelResponse.class);
            if (response != null && response.getStatusCode() == 0) {
                String responseJson = new Gson().toJson(response);
                LOG.info("MARK_FOOD_READY_RESPONSE for order id:::: {} response::: {}", swiggyRequest.getSwiggyOrderId(), responseJson);
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Error updating mark food ready to Swiggy:");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating mark food ready to Swiggy:\n request: " + new Gson().toJson(swiggyRequest));
        }
    }

    private SwiggyOrderResponse addSwiggyOrder(SwiggyOrderRequest swiggyOrderRequest, String linkedOrderId,
                                               boolean isManual) {
        String partnerOrderId = swiggyOrderRequest.getOrderId();
        SwiggyOrderResponse response = new SwiggyOrderResponse();
        SwiggyOrderResponse result ;
        Stopwatch watch1 = Stopwatch.createUnstarted();
        watch1.start();
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        LOG.info("{},SWIGGY STEP 0,Start Processing,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        result = lock.syncLock(partnerOrderId,null, () -> {
            boolean processOrder;
            if (swiggyOrderRequest.isOrderEdit()) {
                processOrder = true;
            } else {
                PartnerOrderCacheDetail partnerOrderCacheDetail = channelPartnerDataCache.getPartnerOrderCache()
                        .get(partnerOrderId);
                processOrder = (partnerOrderCacheDetail == null
                        || !partnerOrderCacheDetail.getPartnerName().equalsIgnoreCase(PARTNER_NAME));
            }
            Pair<String, String> unit = ChannelPartnerUtils.parseUnitId(swiggyOrderRequest.getOutletId());
            LOG.info("{},SWIGGY STEP 1,Start Process Order,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
            if (processOrder && unit != null) {
                watch.start();
                RestaurantPartnerKey key = new RestaurantPartnerKey(unit.getKey(), channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId());
                UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
                LOG.info("{},SWIGGY STEP 2,Get Partner Mapping Data,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                PartnerOrderDetail partnerOrderDetail = receiveOrder(swiggyOrderRequest, linkedOrderId, isManual, response);
                LOG.info("{},SWIGGY STEP 3,Receive Order,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
                if (partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeProcessed())
                        && Boolean.FALSE.equals(partnerOrderDetail.getToBeRejected())) {
                    watch.start();
                    trackService.addPartnerOrderToCache(partnerOrderDetail.getPartnerOrderId(),
                            partnerOrderDetail.getPartnerName(), data.getUnitId());
                    LOG.info("{},SWIGGY STEP 4,Add Partner Order to cache,{}", partnerOrderId,
                            watch.stop().elapsed(TimeUnit.MILLISECONDS));
                    try {
                        watch.start();
                        Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
                        Order order = convertOrder(partnerOrderDetail, isManual, partnerProductTaxMap);
                        LOG.info("{},SWIGGY STEP 5,Convert To Kettle Order,{}", partnerOrderId,
                                watch.stop().elapsed(TimeUnit.MILLISECONDS));
                        if (order != null) {
                            watch.start();
                            checkOrder(partnerOrderDetail, order, partnerProductTaxMap, isManual);
                            LOG.info("{},SWIGGY STEP 6,Validate Order,{}", partnerOrderId,
                                    watch.stop().elapsed(TimeUnit.MILLISECONDS));

                            if (partnerOrderDetail.getToBeRejected() == null || !partnerOrderDetail.getToBeRejected()) {
                                watch.start();
                                setOrderProcessingThread(partnerOrderDetail, order, isManual, null, swiggyOrderRequest);
                                LOG.info("{},SWIGGY STEP 7,Process Order In Kettle,{}", partnerOrderId,
                                        watch.stop().elapsed(TimeUnit.MILLISECONDS));
                                watch.start();
                                response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
                                response.setSwiggyOrderId(Long.valueOf(partnerOrderDetail.getPartnerOrderId()));
                                response.setStatusMessage("Received new order from swiggy");
                                response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                                response.setStatusCode(200);
                                LOG.info("{},SWIGGY STEP 9,Order Processed Successfully,{}", partnerOrderId,
                                        watch.stop().elapsed(TimeUnit.MILLISECONDS));
                            } else {
                                watch.start();
                                Set<String> errorCodes = new HashSet<>();
                                if (partnerOrderDetail.getOrderErrors() != null
                                        && partnerOrderDetail.getOrderErrors().size() > 0) {
                                    for (PartnerOrderError error : partnerOrderDetail.getOrderErrors()) {
                                        if (error.getErrorCode().isToBeRejected()) {
                                            errorCodes.add(error.getErrorCode().name());
                                        }
                                    }
                                }
                                UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(partnerOrderDetail.getUnitId());
                                LOG.error("Rejecting order for partner {} for cafe {} for partnerOrder Id {} with reasons {} ",
                                        partnerOrderDetail.getPartnerName(), ubd.getName(),
                                        partnerOrderDetail.getPartnerOrderId(), StringUtils.join(errorCodes, ","));
                                partnerOrderDetail.setBeingProcessed(false);
                                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                                partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                                partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
                                response.setStatusMessage("Stock Not Sufficient");
                                response.setStatusCode(101);
                                response.setExternalOrderId(swiggyOrderRequest.getOrderId());
                                response.setSwiggyOrderId(Long.valueOf(swiggyOrderRequest.getOrderId()));
                                response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
                                LOG.info("{},SWIGGY STEP 8 Fail End,Order Rejected,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
                                return response;

                            }
                        }
                    } catch (Exception e) {
                        LOG.error("Swiggy order processing error: ", e);
                        partnerOrderDetail.setBeingProcessed(false);
                        partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                        partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                        partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
                        if (response.getStatusCode() == null) {
                            response.setStatusMessage("Error in receiving order");
                            response.setStatusCode(0);
                        }
                        response.setExternalOrderId(swiggyOrderRequest.getOrderId());
                        response.setSwiggyOrderId(Long.valueOf(swiggyOrderRequest.getOrderId()));
                        response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
                        return response;
                    }

                } else if (partnerOrderDetail != null && Boolean.FALSE.equals(partnerOrderDetail.getToBeProcessed())
                        && Boolean.FALSE.equals(partnerOrderDetail.getToBeRejected())) {
                    LOG.info("{},SWIGGY STEP 4 Fail Start,Order Not Processed", partnerOrderId);
                    watch.start();
                    if (response.getStatusCode() == null) {
                        response.setStatusMessage("Error in receiving order");
                        response.setStatusCode(0);
                    }
                    response.setExternalOrderId(swiggyOrderRequest.getOrderId());
                    response.setSwiggyOrderId(Long.valueOf(swiggyOrderRequest.getOrderId()));
                    response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
                    LOG.info("{},SWIGGY STEP 4 Fail End,Order Not Processed,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
                } else if (partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeRejected())) {
                    LOG.info("{},SWIGGY STEP 5 Fail Start,Order Rejected,{}", partnerOrderId);
                    watch.start();
                    response.setStatusMessage("Order Cannot Be Fullfilled");
                    response.setStatusCode(101);
                    response.setExternalOrderId(swiggyOrderRequest.getOrderId());
                    response.setSwiggyOrderId(Long.valueOf(swiggyOrderRequest.getOrderId()));
                    response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
                    LOG.info("{},SWIGGY STEP 5 Fail End,Order Rejected,{}", partnerOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
                if (partnerOrderDetail != null) {
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                }
                LOG.info("{},SWIGGY STEP FINAL,Full Order Time, {}", partnerOrderId, watch1.stop().elapsed(TimeUnit.MILLISECONDS));
                return response;
            }
            return getSwiggyDuplicateOrderResponse(response, partnerOrderId);
        }, !swiggyOrderRequest.isOrderEdit());
        return Objects.isNull(result) ? getSwiggyDuplicateOrderResponse(response, partnerOrderId) : result;
    }

    private Order convertOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual,
                               Map<Integer, StateTaxVO> partnerProductTaxMap) {
        SwiggyOrderRequest request = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
        if (isManual) {
            updateMissingData(partnerOrderDetail, request);
        }
        Pair<String, String> unitKey = ChannelPartnerUtils.parseUnitId(request.getOutletId());
        Order order = null;
        if (unitKey != null) {
            Customer customer = new Customer();
            if(!environmentProperties.isDefaultSwiggyCustomerFlow()){
                customer = getSwiggyCustomer();
            }else{
                customer.setId(environmentProperties.getSwiggyCustomerId());
                customer.setFirstName(ChannelPartnerUtils.SWIGGY);
            }
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId();
            RestaurantPartnerKey key = new RestaurantPartnerKey(unitKey.getKey(), partnerId);
            UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
            order = SwiggyConverters.convertOrder(request,customer, masterDataCache, data,environmentProperties.getSwiggyBoltIdentifier());
            Map<String, TaxDataVO> taxMap = orderValidationService.getUnitProductTaxCodeMap(data).getTaxMap();
            Map<Integer, Product> products = new HashMap<>();
            masterDataCache.getUnitProductDetails(data.getPriceProfileUnitId()).forEach(product -> products.put(product.getId(), product));
            Map<Integer, Map<String, BigDecimal>> pricingMap = channelPartnerDataCache.getPartnerUnitProductPricing(new UnitPartnerBrandKey(data.getUnitId(),
                    data.getBrandId(), data.getPartnerId()));
            Map<Integer, Product> cafeProducts = new HashMap<>();
            masterDataCache.getUnitProductDetails(data.getUnitId()).forEach(product ->
                    cafeProducts.put(product.getId(), product)
            );
            Map<String, DesiChaiCustomProfiles> chaiProfiles = channelPartnerDataCache.getDesiChaiCustomProfilesMap();
            SwiggyConverters.addItemsToOrder(order, request, masterDataCache, partnerOrderDetail, taxMap, products,
                    partnerProductTaxMap, data.getUnitId(), data.getPriceProfileUnitId(), pricingMap, environmentProperties, chaiProfiles,cafeProducts);
            SwiggyConverters.setTransactionDetail(order, request);
        }
        return order;
    }

    private void updateMissingData(PartnerOrderDetail partnerOrderDetail, SwiggyOrderRequest request) {
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME);
        SwiggyOrderResponse response = new SwiggyOrderResponse();
        if (partnerOrderDetail != null) {
            // Primary validations like unit and item
            partnerOrderDetail = primaryChecks(request, partnerOrderDetail, response, partner.getKettlePartnerId());
        }
        boolean tobeProcessed = partnerOrderDetail != null && partnerOrderDetail.getToBeProcessed() != null && partnerOrderDetail.getToBeProcessed();
        if (tobeProcessed && (partnerOrderDetail.getUnitId() == null || partnerOrderDetail.getBrandId() == null)) {
            Pair<String, String> unitData = ChannelPartnerUtils.parseUnitId(request.getOutletId());
            if (unitData != null) {
                RestaurantPartnerKey key = new RestaurantPartnerKey(unitData.getKey(), partner.getKettlePartnerId());
                UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
                partnerOrderDetail.setUnitId(data.getUnitId());
                partnerOrderDetail.setBrandId(data.getBrandId());
                partnerOrderDetail.setRestaurantId(data.getRestaurantId());
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                if (partnerOrderDetail != null && partnerOrderDetail.getToBeProcessed()) {
                    trackService.addPartnerOrderToCache(partnerOrderDetail.getPartnerOrderId(),
                            partnerOrderDetail.getPartnerName(), data.getUnitId());
                }
            }
        }
    }

    private void checkOrder(PartnerOrderDetail partnerOrderDetail, Order order,
                            Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(partnerOrderDetail.getUnitId(), partnerOrderDetail.getBrandId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        boolean inventoryCheckPasses = orderValidationService.runCommonValidations(order, partnerOrderDetail, partnerProductTaxMap, data, masterDataCache.getUnit(key.getUnitId()),this);
        LOG.info("Status of Inventory check {} for partner {} for partnerOrder Id {} ", inventoryCheckPasses,
                partnerOrderDetail.getPartnerName(), partnerOrderDetail.getPartnerOrderId());
        // Validate transaction data
        checkTransactionalData(order, partnerOrderDetail);
        orderValidationService.updateCheckOrderStatus(partnerOrderDetail, isManual);
    }

    private void checkTransactionalData(Order order, PartnerOrderDetail partnerOrderDetail) {
        TransactionDetail td1 = order.getTransactionDetail();
        SwiggyOrderRequest swiggyOrderRequest = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
        partnerOrderDetail.setBillDifference(BigDecimal.ZERO);
        partnerOrderDetail.setBillPercentageDifference(BigDecimal.ZERO);
        /*BigDecimal paidAmount = BigDecimal
            .valueOf(swiggyOrderRequest.getRestaurantGrossBill() - swiggyOrderRequest.getRestaurantDiscount());*/
        BigDecimal paidAmount = ChannelPartnerUtils.add(calculateFinalPayableAmount(swiggyOrderRequest), BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingCharges()));
        if (!orderValidationService.isValidData(td1.getCollectionAmount(), paidAmount)) {
            orderValidationService.checkPaidAmount(order, partnerOrderDetail, td1, paidAmount);
        }
        BigDecimal taxableAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item item : swiggyOrderRequest.getItems()) {
            taxableAmount = taxableAmount.add(BigDecimal.valueOf(item.getFinalSubTotal()));
            totalAmount = totalAmount.add(BigDecimal.valueOf(item.getSubtotal()));
        }
        if (swiggyOrderRequest.getOrderPackingCharges() > 0) {
            taxableAmount = taxableAmount.add(BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingCharges()));
            totalAmount = totalAmount.add(BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingCharges()));
        }
        if (!orderValidationService.isValidData(td1.getTaxableAmount(), taxableAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TAXABL", null,
                    td1.getTaxableAmount().floatValue(), taxableAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                    "Taxable Amount our/partner " + td1.getTaxableAmount() + "/" + taxableAmount);
        }
        if (!orderValidationService.isValidData(td1.getTotalAmount(), totalAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL", null,
                    td1.getTotalAmount().floatValue(), totalAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                    "Total Amount our/partner " + td1.getTotalAmount() + "/" + totalAmount);
        }
        BigDecimal tax = BigDecimal.valueOf(swiggyOrderRequest.getCartGst());
        if (!orderValidationService.isValidData(td1.getTax(), tax)) {
            orderValidationService.logMetadataDifference(order, "CART", "TAX", null, td1.getTax().floatValue(),
                    tax.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                    "Tax our/partner " + td1.getTax() + "/" + tax);
        }
        BigDecimal totalDiscount = BigDecimal.valueOf(swiggyOrderRequest.getRestaurantDiscount());
        if (!orderValidationService.isValidData(td1.getDiscountDetail().getTotalDiscount(), totalDiscount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL_DISC", null,
                    td1.getDiscountDetail().getTotalDiscount().floatValue(), totalDiscount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                    "Total Discount our/partner " + td1.getDiscountDetail().getTotalDiscount() + "/" + totalDiscount);
        }
        checkCartTaxes(td1,swiggyOrderRequest,partnerOrderDetail,order);
        /*for (TaxDetail txd1 : td1.getTaxes()) {
            if (txd1.getCode().equalsIgnoreCase("CGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail,
                    BigDecimal.valueOf(swiggyOrderRequest.getCartCgstPercent()),
                    BigDecimal.valueOf(swiggyOrderRequest.getCartCgst()));
            }
            if (txd1.getCode().equalsIgnoreCase("SGST/UTGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail,
                    BigDecimal.valueOf(swiggyOrderRequest.getCartSgstPercent()),
                    BigDecimal.valueOf(swiggyOrderRequest.getCartSgst()));
            }
            if (txd1.getCode().equalsIgnoreCase("IGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail,
                    BigDecimal.valueOf(swiggyOrderRequest.getCartIgstPercent()),
                    BigDecimal.valueOf(swiggyOrderRequest.getCartIgst()));
            }
        }*/
    }

    private BigDecimal calculateFinalPayableAmount(SwiggyOrderRequest swiggyOrderRequest) {
        BigDecimal bearableTax = BigDecimal.ZERO;
        BigDecimal taxableAmt = BigDecimal.ZERO;
        BigDecimal itemPackagingSum=BigDecimal.ZERO;
        for (com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item item : swiggyOrderRequest.getItems()){
            if(Objects.nonNull(item.getGstLiability()) && !TaxPayingEntity.PARTNER.getSwiggyNew().equalsIgnoreCase(item.getGstLiability())){
                BigDecimal totalGstPerItem = ChannelPartnerUtils.add(BigDecimal.valueOf(item.getCgst()), BigDecimal.valueOf(item.getSgst()));
                bearableTax= ChannelPartnerUtils.add(bearableTax,totalGstPerItem);
            }
            itemPackagingSum= ChannelPartnerUtils.add(itemPackagingSum, BigDecimal.valueOf(item.getPackingCharges()));
            taxableAmt=ChannelPartnerUtils.add(taxableAmt,BigDecimal.valueOf(item.getFinalSubTotal()));
        }
        /*This additional check is added to ensure order error in case of orders with fixed order packaging strategy */
        if(BigDecimal.ZERO.compareTo(itemPackagingSum)>=0){
            bearableTax=ChannelPartnerUtils.add(bearableTax, BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingChargesGst()));
        }
        return ChannelPartnerUtils.add(bearableTax,taxableAmt);
    }

    private void checkCartTaxes(TransactionDetail td1, SwiggyOrderRequest swiggyOrderRequest, PartnerOrderDetail partnerOrderDetail, Order order) {
        Map<TaxDetailKey, TaxDetail> taxPercentMap = calculatePartnerTaxPercentBifurcation(swiggyOrderRequest);
        Map<String, BigDecimal> taxValueMap = calculateOurCartTaxValues(td1);
        for (TaxDetail txd1 : td1.getTaxes()) {
            TaxDetailKey newKey = new TaxDetailKey(txd1.getType(), txd1.getCode(), txd1.getPercentage());
            BigDecimal partnerTaxPercent = BigDecimal.ZERO;
            if (taxPercentMap.containsKey(newKey)) {
                partnerTaxPercent = taxPercentMap.get(newKey).getPercentage();
            }
            if (txd1.getCode().equalsIgnoreCase("CGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail, partnerTaxPercent,
                        BigDecimal.valueOf(swiggyOrderRequest.getCartCgst()), taxValueMap);
            }
            if (txd1.getCode().equalsIgnoreCase("SGST/UTGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail, partnerTaxPercent,
                        BigDecimal.valueOf(swiggyOrderRequest.getCartSgst()), taxValueMap);
            }
            if (txd1.getCode().equalsIgnoreCase("IGST")) {
                checkCartTaxes(txd1, td1, order, partnerOrderDetail, partnerTaxPercent,
                        BigDecimal.valueOf(swiggyOrderRequest.getCartIgst()), taxValueMap);
            }
        }
    }

    private Map<String, BigDecimal> calculateOurCartTaxValues(TransactionDetail td1) {
        Map<String,BigDecimal> aggregatedTaxValueMap = new HashMap<>();
        for(TaxDetail txd : td1.getTaxes()){
            if(Objects.nonNull(txd.getCode()) && aggregatedTaxValueMap.size()>0 && aggregatedTaxValueMap.containsKey(txd.getCode())){
                BigDecimal val =aggregatedTaxValueMap.get(txd.getCode());
                aggregatedTaxValueMap.put(txd.getCode(),ChannelPartnerUtils.add(val,txd.getValue()));
            }else if(!aggregatedTaxValueMap.containsKey(txd.getCode())){
                aggregatedTaxValueMap.put(txd.getCode(),txd.getValue());
            }
        }
        return aggregatedTaxValueMap;
    }

    private Map<TaxDetailKey , TaxDetail> calculatePartnerTaxPercentBifurcation(SwiggyOrderRequest swiggyOrderRequest) {
//        Map<String,BigDecimal> map = new HashMap<>();
        Map<TaxDetailKey,TaxDetail> taxMap = new HashMap<>();
        //packaging charges are exclusive of taxes for swiggy
        /*if(Objects.nonNull(swiggyOrderRequest.getOrderPackingChargesGst()) && swiggyOrderRequest.getOrderPackingChargesGst()==0){
            BigDecimal applicableAmt = ChannelPartnerUtils.subtract(ChannelPartnerUtils.subtract(BigDecimal.valueOf(swiggyOrderRequest.getRestaurantGrossBill()),BigDecimal.valueOf(swiggyOrderRequest.getRestaurantDiscount())),BigDecimal.valueOf(swiggyOrderRequest.getOrderPackingCharges()));
            BigDecimal cartCgstPercentage = BigDecimal.valueOf(swiggyOrderRequest.getCartCgst()).divide(applicableAmt, 10, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            BigDecimal cartSgstPercentage = BigDecimal.valueOf(swiggyOrderRequest.getCartSgst()).divide(applicableAmt, 10, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            BigDecimal cartIgstPercentage = BigDecimal.valueOf(swiggyOrderRequest.getCartIgst()).divide(applicableAmt, 10, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            map.put("CGST",cartCgstPercentage);
            map.put("SGST",cartSgstPercentage);
            map.put("IGST",cartIgstPercentage);
        }else{
            map.put("CGST", BigDecimal.valueOf(swiggyOrderRequest.getCartCgstPercent()));
            map.put("SGST",BigDecimal.valueOf(swiggyOrderRequest.getCartSgstPercent()));
            map.put("IGST",BigDecimal.valueOf(swiggyOrderRequest.getCartIgstPercent()));
        }*/
        if(Objects.nonNull(swiggyOrderRequest.getOrderPackingChargesGst()) && swiggyOrderRequest.getOrderPackingChargesGst()==0){
            for( com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item item:swiggyOrderRequest.getItems()){
                if(item.getCgstPercent()>0){
                    calculatePartnerItemLevelTaxBifurcation(TaxType.CGST.getKey(), taxMap, item.getSubtotal(), item.getFinalSubTotal() , item.getCgstPercent(),item.getCgst());
                }
                if(item.getSgstPercent()>0){
                    calculatePartnerItemLevelTaxBifurcation(TaxType.SGST.getKey(), taxMap, item.getSubtotal(), item.getFinalSubTotal(), item.getSgstPercent(),item.getSgst());
                }
                if(item.getIgstPercent()>0){
                    calculatePartnerItemLevelTaxBifurcation(TaxType.IGST.getKey(), taxMap, item.getSubtotal(), item.getFinalSubTotal(), item.getIgstPercent(),item.getIgst());
                }
            }
        }
//        Check if Packaging Charge Taxes Applicable
        if(Objects.nonNull(swiggyOrderRequest.getOrderPackingChargesGst()) && swiggyOrderRequest.getOrderPackingChargesGst()>0){
            if(swiggyOrderRequest.getOrderPackingChargesCgstPercent()>0){
                calculatePartnerItemLevelTaxBifurcation(TaxType.CGST.getKey(), taxMap,swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingChargesCgstPercent(), swiggyOrderRequest.getOrderPackingChargesCgst());
            }
            if(swiggyOrderRequest.getOrderPackingChargesSgstPercent()>0){
                calculatePartnerItemLevelTaxBifurcation(TaxType.SGST.getKey(), taxMap,swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingChargesSgstPercent(), swiggyOrderRequest.getOrderPackingChargesSgst());
            }
            if(swiggyOrderRequest.getOrderPackingChargesIgstPercent()>0){
                calculatePartnerItemLevelTaxBifurcation(TaxType.IGST.getKey(), taxMap,swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingCharges(),swiggyOrderRequest.getOrderPackingChargesIgstPercent(), swiggyOrderRequest.getOrderPackingChargesIgst());
            }
        }
        return  taxMap;
    }

    private void calculatePartnerItemLevelTaxBifurcation(String taxType, Map<TaxDetailKey, TaxDetail> taxMap, float subtotal, float finalSubTotal, float taxPercent, float taxValue) {
        TaxDetailKey key = new TaxDetailKey(TaxType.GST.name(), taxType, BigDecimal.valueOf(taxPercent));
        TaxDetail taxd = taxMap.get(key);
        if (taxd == null) {
            TaxDetail taxDetail = new TaxDetail();
            taxDetail.setTotal(BigDecimal.valueOf(subtotal));
            taxDetail.setTaxable(BigDecimal.valueOf(finalSubTotal));
            taxDetail.setType(TaxType.GST.getKey());
            taxDetail.setCode(taxType);
            taxDetail.setPercentage(BigDecimal.valueOf(taxPercent));
            taxDetail.setValue(BigDecimal.valueOf(taxValue));
            taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
        } else {
            taxd.setTotal(taxd.getTotal().add(BigDecimal.valueOf(subtotal)));
            taxd.setTaxable(ChannelPartnerUtils.add(taxd.getTaxable(),BigDecimal.valueOf(finalSubTotal)));
        }
        taxMap.put(key,taxd);
    }

    private void checkCartTaxes(TaxDetail txd1, TransactionDetail td1, Order order,
                                PartnerOrderDetail partnerOrderDetail, BigDecimal partnerTaxPercent, BigDecimal value, Map<String, BigDecimal> taxValueMap) {
        if (txd1.getPercentage().compareTo(partnerTaxPercent) == 0) {
            if (!orderValidationService.isValidData(taxValueMap.get(txd1.getCode()), value)) {
                orderValidationService.logMetadataDifference(order, "CART", txd1.getCode(), null,
                        td1.getTax().floatValue(), value.floatValue());
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TAX_MISMATCH,
                        "Cart Tax mismatch " + txd1.getCode() + " our/partner  " + taxValueMap.get(txd1.getCode()) + "/" + value);
            }
        } else {
            float percetageDiff = ChannelPartnerUtils.subtract(partnerTaxPercent,txd1.getPercentage()).floatValue();
            if(percetageDiff>0.1){
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TAX_MISMATCH,
                        "Cart Tax percent mismatch " + txd1.getCode() + " our/partner  " + txd1.getPercentage() + "/"
                                + partnerTaxPercent);
            }
        }
    }

    private void setOrderProcessingThread(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual,
                                          Boolean skipInventoryCheck, SwiggyOrderRequest request) {
        SwiggyOrderTask swiggyOrderTask = new SwiggyOrderTask();
        swiggyOrderTask.setSwiggyService(this);
        swiggyOrderTask.setManual(isManual);
        swiggyOrderTask.setOrder(order);
        swiggyOrderTask.setPartnerOrderDetail(partnerOrderDetail);
        swiggyOrderTask.setSkipInventoryCheck(skipInventoryCheck);
        swiggyOrderTask.setSwiggyOrderRequest(request);
        swiggyOrderTask.setRequestId(MDC.get("request.id"));
        threadPoolTaskExecutor.execute(swiggyOrderTask);
    }

    private PartnerOrderDetail receiveOrder(SwiggyOrderRequest request, String linkedOrderId, boolean isManual,
                                            SwiggyOrderResponse response) {
        // Log order data
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME);

        PartnerOrderDetail partnerOrderDetail = trackService.trackPartnerOrder(request.getOrderId(),
                partner.getPartnerId(), partner.getPartnerName(), partner.getPartnerCode(), request, linkedOrderId,
                isManual, "V1");
        if (partnerOrderDetail != null) {
            // Primary validations like unit and item
            partnerOrderDetail = primaryChecks(request, partnerOrderDetail, response, partner.getKettlePartnerId());
        }
        if (partnerOrderDetail != null) {
            Pair<String, String> unitData = ChannelPartnerUtils.parseUnitId(request.getOutletId());
            if (unitData != null) {
                RestaurantPartnerKey key = new RestaurantPartnerKey(unitData.getKey(), partner.getKettlePartnerId());
                UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
                partnerOrderDetail.setUnitId(data.getUnitId());
                partnerOrderDetail.setBrandId(data.getBrandId());
                partnerOrderDetail.setRestaurantId(data.getRestaurantId());
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
            }
        }
        return partnerOrderDetail;
    }

    private PartnerOrderDetail primaryChecks(SwiggyOrderRequest request, PartnerOrderDetail partnerOrderDetail,
                                             SwiggyOrderResponse response, Integer kettlePartnerId) {
        try {
            String restaurantId = null;
            if (StringUtils.isBlank(request.getOutletId())) {
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_MISSING,
                        "Unit Id is missing in the order.");
                response.setStatusCode(SwiggyOrderRejectionCodes.OUTLET_ID_NOT_EXIST.getCode());
                response.setStatusMessage(SwiggyOrderRejectionCodes.OUTLET_ID_NOT_EXIST.getMessage());
            } else {
                restaurantId = parseUnitId(request, partnerOrderDetail, response);
            }
            if (restaurantId != null) {
                RestaurantPartnerKey key = new RestaurantPartnerKey(restaurantId, kettlePartnerId);
                UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
                Integer unitId = data.getUnitId();
                Unit unit = null;
                if (unitId != null) {
                    unit = masterDataCache.getUnit(unitId);
                }
                validateUnit(unit, partnerOrderDetail, response, unitId);
                if (unit != null) {
                    Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
                    boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                            unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
                    if (!mappingValid) {
                        orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID,
                                "Unit Id: " + unitId + " is not mapped.");
                        response.setStatusCode(SwiggyOrderRejectionCodes.OTHER_ERROR.getCode());
                        response.setStatusMessage("Unit Id is not mapped to partner");
                    }
                    if (request.getItems() == null || request.getItems().isEmpty()) {
                        orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.EMPTY_ORDER,
                                "No items in the order.");
                        response.setStatusCode(SwiggyOrderRejectionCodes.OTHER_ERROR.getCode());
                        response.setStatusMessage("Order has no items");
                    }
                }
            }
            partnerOrderDetail.setToBeProcessed(partnerOrderDetail.getOrderErrors().isEmpty());
            partnerOrderDetail.setToBeRejected(!partnerOrderDetail.getOrderErrors().isEmpty());
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
        }
        return trackService.updatePartnerOrder(partnerOrderDetail);
    }

    private String parseUnitId(SwiggyOrderRequest request, PartnerOrderDetail partnerOrderDetail,
                               SwiggyOrderResponse response) {
        String restaurantId = null;
        try {
            Pair<String, String> unitData = ChannelPartnerUtils.parseUnitId(request.getOutletId());
            if (unitData == null) {
                throw new NumberFormatException("Unit id not valid");
            }
            restaurantId = unitData.getKey();

        } catch (NumberFormatException e) {
            String missingUnit = "Unit Id is missing";
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_MISSING,
                    missingUnit);
            response.setStatusCode(SwiggyOrderRejectionCodes.OTHER_ERROR.getCode());
            response.setStatusMessage(missingUnit);
        }
        return restaurantId;
    }

    private void validateUnit(Unit unit, PartnerOrderDetail partnerOrderDetail, SwiggyOrderResponse response, Integer unitId) {
        String unitIdString = "Unit Id ";
        if (unit == null) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID,
                    unitIdString + unitId + " is not valid.");
            response.setStatusCode(SwiggyOrderRejectionCodes.OTHER_ERROR.getCode());
            response.setStatusMessage("Unit id is not valid");
        }
        if (unit != null && unit.getStatus().equals(UnitStatus.IN_ACTIVE)) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID,
                    unitIdString + unitId + " is not active.");
            response.setStatusCode(SwiggyOrderRejectionCodes.RESTAURANT_CLOSED.getCode());
            response.setStatusMessage(SwiggyOrderRejectionCodes.RESTAURANT_CLOSED.getMessage());
        }
        if (unit != null && !unit.isLive()) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID,
                    unitIdString + unitId + " is not live.");
            response.setStatusCode(SwiggyOrderRejectionCodes.RESTAURANT_CLOSED.getCode());
            response.setStatusMessage(SwiggyOrderRejectionCodes.RESTAURANT_CLOSED.getMessage());
        }
    }

    private Customer getSwiggyCustomer() {
        if (channelPartnerDataCache.getSwiggyCustomer() != null
                && channelPartnerDataCache.getSwiggyCustomer().getId() != 0) {
            return channelPartnerDataCache.getSwiggyCustomer();
        } else {
            CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
            codCustomerLoginData.setContactNumber(environmentProperties.getSwiggyCustomerContact());
            CODCustomerLoginData customer = webServiceHelper.postWithAuth(environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP,
                    environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
                    CODCustomerLoginData.class);
            channelPartnerDataCache.setSwiggyCustomer(customer.getCustomer());
            LOG.info("Printing Cod Customer Details ::::::::::{}", new Gson().toJson(customer));
            return customer.getCustomer();
        }
    }

    private void catchAndLogSwiggyException(HttpStatusCodeException e, String title) {
        try {
            String errorBody = e.getResponseBodyAsString();
            LOG.error(title, errorBody);
            String message = ChannelPartnerUtils.getMessage(title, errorBody);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
        } catch (Exception ex) {
            LOG.error("Error in logging Swiggy exception", ex);
        }
    }

    private void catchLogAndThrowSwiggyException(HttpStatusCodeException e, String title) throws ChannelPartnerException {
        String errorBody = e.getResponseBodyAsString();
        LOG.error(title, errorBody);
        String message = ChannelPartnerUtils.getMessage(title, errorBody);
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                message);
        throw new ChannelPartnerException(errorBody);
    }

    private void catchAndLogException(Exception e, String title) {
        LOG.error(title, e);
        String message = ChannelPartnerUtils.getMessage(title, e.getMessage());
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                message);
    }

    // this function only adds normal combo ids and does not include super combo or hero combo ids
    private void addNormalComboItemsToStockEvent(UnitProductsStockEvent event, UnitPartnerBrandKey key , Map<Integer,Boolean> knockAppStockOutMap) {
        List<String> productIds = new ArrayList<>(event.getProductIds());
        if (StockStatus.STOCK_OUT.equals(event.getStatus())) {
            Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                    getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
            Set<String> comboIds = new HashSet<>();
            for (String productId : event.getProductIds()) {
                // Finding combo mappings for normal products only
                if (map != null && map.containsKey(Integer.parseInt(productId)) && map.get(Integer.parseInt(productId)) != null) {
                    map.get(Integer.parseInt(productId)).stream().filter(comboId ->
                            !productIds.contains(comboId.toString())
                    ).forEach(comboId -> comboIds.add(comboId.toString()));
                }
            }
            productIds.addAll(comboIds);
        } else {
            addNormalComboItemsToStockInEvent(event, key, productIds , knockAppStockOutMap);
        }
        event.setProductIds(productIds);
    }

    private void addNormalComboItemsToStockInEvent(UnitProductsStockEvent event, UnitPartnerBrandKey key,
                                                   List<String> productIds , Map<Integer,Boolean> kncokAppStockOutMap) {
        Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
                getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
        if (map != null) {
            Set<Integer> comboItems = new HashSet<>();
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                boolean setCombo = entry.getValue().stream().anyMatch(productId -> event.getProductIds().contains(productId.toString()));
                if (setCombo) {
                    comboItems.addAll(entry.getValue());
                }
            }
            if(!CollectionUtils.isEmpty(comboItems)){
                LOG.info("Product Ids in Combo for Partner  : {}  To Check For Stock In  :::: {} ",PARTNER_NAME,comboItems);
            }
            Map productStock = orderValidationService.getUnitProductInventoryByProducts(key.getUnitId(), new ArrayList<>(comboItems));
            LOG.info("Inventory map for unit Id  :::: {} :: Partner :::: {}  Time ::::: {}  ::::::  {} ", key.getUnitId() ,PARTNER_NAME , AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
            Boolean printEvent = false;
            for(String productId : event.getProductIds()){
                if((productStock.get(productId) !=null &&
                        (Integer)productStock.get(productId) <= 0)){
                    LOG.info("Got Event For Stock In But Got Inventory 0 on refetch !! for Partner : {} , Brand Id : {}   for product Id :: {} ",PARTNER_NAME ,key.getBrandId(),productId);
                    productStock.put(productId,1);
                    printEvent = true;
                }
            }
            if(Boolean.TRUE.equals(printEvent)){
                LOG.info("stock event : {} ", new Gson().toJson(event));
            }
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                Set<Integer> productIdset = entry.getValue();
                boolean addCombo = productIdset.stream().noneMatch(productId -> (masterDataCache.getProduct(productId).isInventoryTracked()
                        && (productStock.get(productId.toString()) == null || (Integer) productStock.get(productId.toString()) <= 0)) ||
                        kncokAppStockOutMap.containsKey(productId) );
                if (addCombo && !productIds.contains(entry.getKey().toString())) {
                    productIds.add(entry.getKey().toString());
                }
            }
        }
    }

    private void logStockUpdateSnapshot(SwiggyStockRequest swiggyStockRequest, UnitProductsStockEvent event,SwiggyCancelResponse swiggyResponse) throws ChannelPartnerException {
        LOG.info("Updating Swiggy unit product stock snapshot for Unit Id :::: {}",swiggyStockRequest.getRestaurantId());
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        Set<Integer> productIds = new HashSet<>();
        for (String productId : swiggyStockRequest.getExternalItemIds()) {
            if (productId.contains("_")) {
                productIds.add(Integer.parseInt(productId.substring(0, productId.indexOf('_'))));
            } else {
                productIds.add(Integer.parseInt(productId));
            }
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            SwiggyStockUpdateResponse stockUpdateResponse = mapper.convertValue(swiggyResponse.getData(),
                    new TypeReference<SwiggyStockUpdateResponse>() {});
            for(SwiggyStockUpdateItemResponse item : stockUpdateResponse.getItemToggleStatus()){
                if("fail".equalsIgnoreCase(item.getStatus())){
                    LOG.error("Swiggy Stock Update Failed For Id :::: {} ",item.getId());
                    String title = "############ Swiggy Item Stock Update Failure ###############";
                    String errorMsg = "Item Update Failure For Item Id ::::: " + item.getId()  + "\n" +
                            "Reason ::::  " + item.getMessage();
                    String message = ChannelPartnerUtils.getMessage(title, errorMsg);
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                            message);
                }
            }
        }catch (Exception e){
            LOG.error("Error While Validating Stock update Response From Swiggy ::::");
        }
        for (Integer productId : productIds) {
            PartnerUnitProductStockSnapshot snapshot = partnerUnitStockSnapshotDao
                    .findByUnitIdAndPartnerIdAndProductId(swiggyStockRequest.getRestaurantId(), partnerId, productId);
            if (snapshot == null) {
                snapshot = new PartnerUnitProductStockSnapshot();
                snapshot.setPartnerId(partnerId);
                snapshot.setUnitId(swiggyStockRequest.getRestaurantId());
                snapshot.setProductId(productId);
                snapshot.setProductStockSnapshot(new ArrayList<>());
            }
            ProductStockSnapshot stockSnapshot = new ProductStockSnapshot();
            stockSnapshot.setStockStatus(event.getStatus());
            stockSnapshot.setUpdateTime(ChannelPartnerUtils.getFormattedTime(currentTime, "yyyy-MM-dd HH:mm:ss"));
            snapshot.getProductStockSnapshot().add(stockSnapshot);
            snapshot = partnerUnitStockSnapshotDao.save(snapshot);
            if (snapshot == null) {
                throw new ChannelPartnerException("Error updating stock snapshot!");
            } else {
                LOG.info("Updated Swiggy unit product stock snapshot");
            }
        }
    }


    @Override
    public SwiggyOrderResponse addSwiggyOrderInDev(Object request) {
        return webServiceHelper.postWithAuth(environmentProperties.getDevUrl() +
                ChannelPartnerClientEndpoints.CHANNEL_PARTNER_TEST_ORDER, environmentProperties.getChannelPartnerRedirectToken(), request, SwiggyOrderResponse.class);
    }

    @Override
    public void pushMenuToUnits(List<Integer> unitIdsForMenu, Integer brandId, Integer employeeId,
                                Integer kettlePartnerId, MenuType menuType) {
        LOG.info("Enter updateSwiggyMenu ");
        for (Integer unitId : unitIdsForMenu) {
            Map<String, String> pathVariables = new HashMap<>();
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            if (mappingValid) {
                UnitMenuAddVO request = createSwiggyMenu(kettlePartnerId, unitId, brandId, employeeId,
                        menuType.toString());
                if (request.getMenuRequest() != null) {
                    SwiggyMenuRequest menuRequest = (SwiggyMenuRequest) request.getMenuRequest();
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
                    String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                    pathVariables.put("id", restaurantId);
                    if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                        pathVariables.put("id", ChannelPartnerUtils.getSwiggyTestOutletId());
                    }
                    updateInStockValueForMenuPush(menuRequest, request.getUnitId(), request.getBrandId());
                    callSwiggyMenuPush(menuRequest, pathVariables, request);
                }
            }
        }
    }

    private UnitMenuAddVO createSwiggyMenu(Integer partnerId, Integer unitId, Integer brandId, Integer employeeId,
                                           String menuType) {
        LOG.info("Enter createSwiggyMenuRequestObj where partnerId is {} unitId is {}", partnerId, unitId);
        UnitMenuAddVO request = new UnitMenuAddVO();
        request.setKettlePartnerId(partnerId);
        request.setBrandId(brandId);
        request.setUnitId(unitId);
        request.setEmployeeId(employeeId);
        request.setNew(true);
        request.setMenuType(MenuType.fromValue(menuType));
        request.setRegion(masterDataCache.getUnit(unitId).getRegion());
        Object menuData = null;
        PartnerUnitMenuDetail partnerUnitMenuData;
        PartnerUnitMenuVersionMapping partnerUnitMenuVersionMapping = partnerMenuService
                .getPartnerUnitVersionData(partnerId, unitId, brandId, menuType);
        if (partnerUnitMenuVersionMapping == null) {
            partnerUnitMenuVersionMapping = partnerMenuService
                    .getPartnerUnitVersionData(partnerId, unitId, brandId, MenuType.DEFAULT.name());
            request.setMenuType(MenuType.DEFAULT);
        }
        if (partnerUnitMenuVersionMapping != null) {
            partnerUnitMenuData = partnerMenuService.getPartnerUnitVersionMenuDetail(partnerId, unitId, brandId,
                    partnerUnitMenuVersionMapping.getMenuType().name(), partnerUnitMenuVersionMapping.getVersion());
            if (partnerUnitMenuData != null) {
                menuData = partnerUnitMenuData.getMenuData();
                if (menuData != null) {
                    SwiggyMenuRequest menuRequest = new SwiggyMenuRequest();
                    menuRequest.setEntity(ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), Entity.class));
                    request.setMenuRequest(menuRequest);
                    request.setVersion(partnerUnitMenuVersionMapping.getVersion());
                } else {
                    LOG.info("No Menu Mapping found for Unit: {} with brandId: {} for {}", unitId, brandId, PARTNER_NAME);
                }
            }
        }
        LOG.info("Exit createSwiggyMenuRequestObj");
        return request;

    }

    private SwiggyMenuRequest updateInStockValueForMenuPush(SwiggyMenuRequest newMenu, Integer unitId, Integer brandId) {
        try {
            if(Objects.isNull(brandId)){
                return newMenu;
            }
            LOG.info("Trying To update In Stock Value IN Menu For Unit Id :::: {} , Brand Id :::: {} ",unitId, brandId);
            List<String> forceStockOutProductIds  = filterProductsScheduledForStockUpdate(new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT),new ArrayList<>());
            Boolean isUnitEnabledForStockInFilter = environmentProperties.isStockInProductsDuringMenuUnitIds(unitId);
            LOG.info("Stock In Filter Check Status For Unit Id : {}  is :::: {} ",unitId,isUnitEnabledForStockInFilter);
            UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
            stockOut.setStatus(StockStatus.STOCK_OUT);
            stockOut.setUnitId(unitId);
            UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
            stockIn.setStatus(StockStatus.STOCK_IN);
            stockIn.setUnitId(unitId);
            orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
            stockOut.getProductIds().addAll(forceStockOutProductIds);
            Set<String> stockOutProductIds = new HashSet<>();
            Set<String> stockInProductIds = new HashSet<>();

            UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, 6);

            LOG.info("Stock Out products :::: {}",new Gson().toJson(stockOut.getProductIds()));

            for (String productId : stockOut.getProductIds()) {
                Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                        getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
                if (map.containsKey(Integer.valueOf(productId))) {
                    Set<String> comboIds = map.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                    if (!CollectionUtils.isEmpty(comboIds)) {
                        stockOutProductIds.addAll(comboIds);
                    }
                }
            }
            stockOutProductIds.addAll(stockOut.getProductIds());
            if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                for (String productId : stockIn.getProductIds()) {
                    Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                            getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
                    if (map.containsKey(Integer.valueOf(productId))) {
                        Set<String> comboIds = map.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                        if (!CollectionUtils.isEmpty(comboIds)) {
                            stockInProductIds.addAll(comboIds);
                        }
                    }
                }
                stockInProductIds.addAll(stockIn.getProductIds());
            }


            LOG.info("Stock Out Products For Unit Id : {} During Menu Push :::: {}",unitId,new Gson().toJson(stockOutProductIds));

            Map<String, Boolean> stockOuts = stockOutProductIds.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                return true;
            }));
            Map<String, Boolean> stockIns = new HashMap<>();
            if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                stockIns = stockInProductIds.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                    return true;
                }));
            }

            for (Item item : newMenu.getEntity().getItems()) {
                String productKey = item.getId().split("_")[0];
                if (stockOuts.containsKey(productKey)) {
                    item.setInStock(0);
                    continue;
                }
                if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                    if(!stockIns.containsKey(productKey) && masterDataCache.getProduct(Integer.parseInt(productKey)).isInventoryTracked()){
                        item.setInStock(0);
                        continue;
                    }
                }
                for (AddonGroup addOnGroup : item.getAddonGroups()) {
                    for (Addon addOn : addOnGroup.getAddons()) {
                        try {
                            String addOnKey = addOn.getId().split("_")[0];
                            if (stockOutProductIds.contains(addOnKey)) {
                                addOn.setInStock(0);
                            }
                            /*if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled())){
                                if (!stockInProductIds.contains(addOnKey)) {
                                    addOn.setInStock(0);
                                }
                            }*/
                        } catch (NumberFormatException ignored) {

                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error While Updating Stock Out Items During Menu Push :::: ", e);
        }
        return newMenu;
    }

    @Override
    public void scheduledMenuPush(List<Integer> unitIdsForMenu, MenuType menuType ,Integer brandId) throws IOException {
        List<Integer> menuPushUnitIds = new ArrayList<>();
        brandId = Objects.isNull(brandId) ? AppConstants.CHAAYOS_BRAND_ID : brandId;

        for (Integer unitId : unitIdsForMenu) {
            try {
                if (masterDataCache.getUnit(unitId) != null) {
                    Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
                    boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(
                            unitChannelPartnerMapping -> unitChannelPartnerMapping.getChannelPartner().getId() == partnerId
                                    && unitChannelPartnerMapping.getUnit().getId() == unitId);
                    if (mappingValid) {
                    Map<Integer, Map<Integer, CafeMenuAutoPush>> menuAutoPushMap = partnerMenuService.getMapValue(unitIdsForMenu);
                    for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
                        if (key.getPartnerId().equals(partnerId) && key.getUnitId().equals(unitId)
                        && brandId.equals(key.getBrandId())) {
                            Map<Integer, CafeMenuAutoPush> flagDetail = menuAutoPushMap.get(key.getUnitId());
                                if (Objects.nonNull(flagDetail)) {
                                CafeMenuAutoPush cafeAutoPush = flagDetail.get(key.getBrandId());
                                if (Objects.nonNull(cafeAutoPush) && Objects.nonNull(cafeAutoPush.getSwiggyMenu()) && Boolean.TRUE.equals(cafeAutoPush.getSwiggyMenu())) {
                                    UnitMenuAddVO request = createSwiggyMenu(partnerId, unitId, key.getBrandId(), AppConstants.SYSTEM_EMPLOYEE_ID,
                                            menuType.toString());
                                    if (request.getMenuRequest() != null) {
                                        Map<String, String> pathVariables = new HashMap<>();
                                        SwiggyMenuRequest menuRequest = (SwiggyMenuRequest) request.getMenuRequest();
                                        updateInStockValueForMenuPush(menuRequest, request.getUnitId(), request.getBrandId());
                                        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                                        pathVariables.put("id", restaurantId);
                                        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                                            pathVariables.put("id", ChannelPartnerUtils.getSwiggyTestOutletId());
                                        }
                                        if (partnerMenuService.checkVersionMenuAuditData(request, PARTNER_NAME)) {
                                            callSwiggyMenuPush(menuRequest, pathVariables, request);
                                            menuPushUnitIds.add(unitId);
                                        }
                                    }
                                } else {
                                    LOG.info("Menu is not Push for " + PARTNER_NAME + " unit is " + key.getUnitId() + " and brand is " + key.getBrandId());
                                }
                            }
                        }
                    }
                    }
                }
            } catch (Exception e) {
                LOG.error("Error in pushing scheduled Swiggy Menu for " + PARTNER_NAME + " unit is " + unitId, e);
                e.printStackTrace();
            }
        }
        if(Boolean.TRUE.equals(environmentProperties.toSendStockOutReportAfterMenuPush())){
            partnerMenuService.sendStockOutReport(menuPushUnitIds,menuType,PARTNER_NAME);
        }
    }

    private Map<Integer, Map<Integer, CafeMenuAutoPush>> getMapOfUnitAndBrand(List<CafeMenuAutoPush> menuData, List<Integer> unitIdsForMenu) {
        Map<Integer, Map<Integer, CafeMenuAutoPush>> mapOfUnitAndBrand = new HashMap<>();
        for (Integer unit : unitIdsForMenu) {
            Map<Integer, CafeMenuAutoPush> mapOfBrand = new HashMap<>();
            boolean flag = false;
            for (CafeMenuAutoPush menuAutoPush : menuData) {
                if (unit.equals(menuAutoPush.getUnitId())) {
                    mapOfBrand.put(menuAutoPush.getBrandId(), menuAutoPush);
                    flag = true;
                }
            }
            if (flag) {
                mapOfUnitAndBrand.put(unit, mapOfBrand);
            }
        }
        return mapOfUnitAndBrand;
    }

    private void callSwiggyMenuPush(SwiggyMenuRequest menuRequest, Map<String, String> pathVariables, UnitMenuAddVO request) {
        try {
            SwiggyMenuResponse response = webServiceHelper.swiggyFullMenuCreateCall(environmentProperties,
                    SwiggyServiceEndpoints.MENU_ADD_V1, HttpMethod.POST, SwiggyMenuResponse.class, menuRequest, pathVariables);
            String responseJson = new Gson().toJson(response);
            LOG.info("Response received from swiggy {}", responseJson);
            menuPushPostProcessing(menuRequest, response, request);
            /*if (response != null && String.valueOf(response.getStatusCode()).equalsIgnoreCase("202")) {
                partnerMenuService.updatePartnerMenuAuditData(request.getKettlePartnerId(), request.getUnitId(),
                    request.getBrandId(), request.getEmployeeId(), request.getMenuType().toString(),
                    request.getVersion(), PARTNER_NAME, response);
                updateSwiggyUnitProductsMappings(menuRequest, request);
                updatePartnerUnitProductPricing(request.getKettlePartnerId(), request.getUnitId(), request.getBrandId(),
                    request.getEmployeeId(), menuRequest);
                LOG.info("Product Data is saved and cache is updated:::::");
            }*/
            slackAndLogNotification("Swiggy menu added response:::::\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                            + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n" + responseJson,
                    SlackNotification.PARTNER_MENU);
        } catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Error Adding menu to Swiggy:::::\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                    + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n");
        } catch (Exception e) {
            catchAndLogException(e, "Error Adding menu to Swiggy:::::\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                    + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n");
        }
    }

    private void menuPushPostProcessing(SwiggyMenuRequest menuRequest, SwiggyMenuResponse response, UnitMenuAddVO request) {
        if (response != null && String.valueOf(response.getStatusCode()).equalsIgnoreCase("202")) {
            partnerMenuService.updatePartnerMenuAuditData(request.getKettlePartnerId(), request.getUnitId(),
                    request.getBrandId(), request.getEmployeeId(), request.getMenuType().toString(),
                    request.getVersion(), PARTNER_NAME, response);
            updateSwiggyUnitProductsMappings(menuRequest, request);
            updatePartnerUnitProductPricing(request.getKettlePartnerId(), request.getUnitId(), request.getBrandId(),
                    request.getEmployeeId(), menuRequest);
            channelPartnerDataCache.loadUnitProductComboMappings(masterDataCache.getUnitBasicDetail(request.getUnitId()));
            LOG.info("Product Data is saved and cache is updated:::::");
        } else {
            LOG.error("error adding swiggy menu : {} ",new Gson().toJson(response));
            slackAndLogNotification("ERROR::: Swiggy menu push response:\n" + new Gson().toJson(response));
        }
    }

    private void filterBrandLevelProductsUsingPricingUnit(UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        List<String> matchingProductsIds = new ArrayList<>();
        List<IdName> matchingProductDimensions = new ArrayList<>();
        masterDataCache.getUnitProductDetails(brandMappingData.getPriceProfileUnitId()).forEach(product -> {
            if (product.isInventoryTracked() || key.getBrandId().equals(AppConstants.DOHFUL_BRAND_ID) || Boolean.TRUE.equals(event.getForceStockOut())) {
                List<String> matchingIds = event.getProductIds().stream().filter(s -> Integer.parseInt(s) == product.getId()).collect(Collectors.toList());
                if (!matchingIds.isEmpty()) {
                    matchingProductsIds.addAll(matchingIds);
                }
                if (event.getProductDimensions() != null) {
                    List<IdName> productDimensions = event.getProductDimensions().stream().filter(idName ->
                            idName.getId() == product.getId()).collect(Collectors.toList());
                    if (!productDimensions.isEmpty()) {
                        matchingProductDimensions.addAll(productDimensions);
                    }
                }
            }
        });
        event.setProductIds(matchingProductsIds);
        event.setProductDimensions(matchingProductDimensions);
    }

    private void updatePartnerUnitProductPricing(Integer kettlePartnerId, Integer unitId, Integer brandId, Integer employeeId, SwiggyMenuRequest menuRequest) {
        Map<Integer, Map<String, BigDecimal>> pricing = new HashMap<>();
        Integer pricingUnit = masterDataCache.getUnitPartnerBrandMappingMetaData().get(new UnitPartnerBrandKey(unitId, brandId,
                kettlePartnerId)).getPriceProfileUnitId();
        Collection<Product> products = masterDataCache.getUnitProductDetails(pricingUnit);
        menuRequest.getEntity().getItems().forEach(item -> {
            Integer productId = item.getId().contains("_") ?
                    Integer.parseInt(item.getId().split("_")[0]) : Integer.parseInt(item.getId());
            if (masterDataCache.getProduct(productId) != null && ProductClassification.MENU.equals(masterDataCache.getProduct(productId).getClassification())) {
                if (!pricing.containsKey(productId)) {
                    pricing.put(productId, new HashMap<>());
                }
                BigDecimal basePrice = BigDecimal.valueOf(item.getPrice());
                boolean dimensionsFound = false;
                if (item.getVariantGroups() != null && !item.getVariantGroups().isEmpty()) {
                    for (VariantGroup variantGroup : item.getVariantGroups()) {
                        if (variantGroup.getName().equalsIgnoreCase("Size")) {
                            dimensionsFound = true;
                            variantGroup.getVariants().forEach(variant -> {
                                String dimension = ChannelPartnerUtils.getDimensionFromSwiggyMenuVariant(variant.getName());
                                pricing.get(productId).put(dimension, ChannelPartnerUtils.add(basePrice, BigDecimal.valueOf(variant.getPrice())));
                            });
                        }
                    }
                }
                if (!dimensionsFound) {
                    products.forEach(product -> {
                        if (product.getId() == productId) {
                            if (product.getPrices().size() == 1) {
                                pricing.get(productId).put(product.getPrices().get(0).getDimension(), basePrice);
                            } else {
                                product.getPrices().forEach(productPrice -> {
                                    if (productPrice.getPrice().compareTo(basePrice) == 0) {
                                        pricing.get(productId).put(productPrice.getDimension(), basePrice);
                                    }
                                });
                            }
                        }
                    });
                }
            }
        });
        PartnerUnitProductPricingDetail pricingDetail = partnerMenuService.updatePartnerUnitProductPricing(kettlePartnerId, unitId, brandId,
                employeeId, null, PARTNER_NAME);
        channelPartnerDataCache.loadPartnerUnitProductPricingMap(pricingDetail);
    }

    @Override
    public SwiggyMenuTrackResponse trackSwiggyMenuPushStatus(Integer unitId, Integer partnerId, Integer brandId) {
        List<PartnerMenuAuditHistory> partnerAuditHistory = partnerMenuService.getPartnerMenuHistoryByStatus(partnerId, unitId, brandId, AppConstants.ACTIVE);
        LOG.info("PartnerMenuAuditHistory found ::::::::: {}", partnerAuditHistory == null ? -1 : partnerAuditHistory.size());
        LOG.info("PartnerMenuAuditHistory requestId ::::::::: {}", partnerAuditHistory == null ? -1 : partnerAuditHistory.get(0).getMenuResponse());
        if (partnerAuditHistory != null && !partnerAuditHistory.isEmpty() && partnerAuditHistory.get(0).getMenuResponse() != null) {
            SwiggyMenuResponse response = new Gson().fromJson(new Gson().toJson(partnerAuditHistory.get(0).getMenuResponse()), SwiggyMenuResponse.class);
            Map<String, String> uriVars = new HashMap<>();
            uriVars.put("request_id", response.getRequestId());
            try {
                Object result = webServiceHelper.swiggyFullMenuCreateCall(environmentProperties,
                        SwiggyServiceEndpoints.TRACK_MENU, HttpMethod.GET, Object.class, null, uriVars);
                if (result != null) {
                    String responseJson = new Gson().toJson(result);
                    LOG.info("TRACK_MENU response::: {}", responseJson);
                    return new Gson().fromJson(responseJson, SwiggyMenuTrackResponse.class);
                }
            } catch (HttpStatusCodeException e) {
                catchAndLogSwiggyException(e, "Error getting menu push status Swiggy \n request: unit " + unitId + " partner " + partnerId + " brand " + brandId);
            } catch (Exception e) {
                catchAndLogException(e, "Error updating mark food ready to Swiggy:\n request: unit " + unitId + " partner " + partnerId + " brand " + brandId);
            }
        }
        return null;
    }

    private void slackAndLogNotification(String message) {
        slackAndLogNotification(message, SlackNotification.PARTNER_INTEGRATION);
    }

    private void slackAndLogNotification(String message, SlackNotification channel) {
        String notification = ChannelPartnerUtils.getMessage(message ,"");
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), channel,
                notification);
        LOG.info(message);
    }


    @Override
    public boolean updateCafeDeliveryTimeSwiggy(CafeTimingChangeRequest cafeTimingChangeRequest){
        String [] daysOfWeek = cafeTimingChangeRequest.getDayOfWeekText().split(",");
        ArrayList<DayUpdate> deliverTimeUpdate = new ArrayList<>();
        for(String days : daysOfWeek){
            DayUpdate obj = new DayUpdate();
            obj.setDay(days);
            obj.setSlots(new ArrayList<>());
            Slot slot = new Slot();
            slot.setOpen_time(cafeTimingChangeRequest.getDeliveryOpeningTime());
            slot.setClose_time(cafeTimingChangeRequest.getDeliveryClosingTime());
            obj.getSlots().add(slot);
            deliverTimeUpdate.add(obj);
        }
        String request = new Gson().toJson(deliverTimeUpdate);
        LOG.info("Value of Swiggy request is::{} ",request);
        Map<String, String> pathVariables = new HashMap<>();
        Integer swiggyId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        UnitPartnerBrandKey unitPartnerBrandKey = new UnitPartnerBrandKey();
        unitPartnerBrandKey.setUnitId(cafeTimingChangeRequest.getUnitId());
        unitPartnerBrandKey.setBrandId(cafeTimingChangeRequest.getBrandId()!=null?cafeTimingChangeRequest.getBrandId():1);
        unitPartnerBrandKey.setPartnerId(swiggyId);
        String partnerid = masterDataCache.getUnitPartnerBrandMappingMetaData().get(unitPartnerBrandKey).getRestaurantId();
        SwiggyCancelResponse response;
        String requestJson = new Gson().toJson(deliverTimeUpdate);
        try{
            pathVariables.put("partnerid", partnerid);
            response = webServiceHelper.callSwiggyApi(environmentProperties,SwiggyServiceEndpoints.DELIVERY_TIMING_UPDATE,HttpMethod.PATCH,deliverTimeUpdate,SwiggyCancelResponse.class,null,pathVariables,null);
            LOG.info("Swiggy Delivery Business Hours Updated for Unit:::::{}",cafeTimingChangeRequest.getUnitId());

            if (response == null || response.getStatusCode() != 0) {
                LOG.info("Swiggy Delivery Status Response  Message::::{}",cafeTimingChangeRequest.getUnitId(),response.getStatusMessage(),response.getStatusCode());
                String responseJson = new Gson().toJson(response);
                String message= ChannelPartnerUtils.getMessage("Swiggy Update Delivery Time  API returned incorrect response","::::REQUEST::::"+requestJson+"\n"+"::::RESPONSE::::"+responseJson);
                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KNOCK_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
            }
            if(response.getStatusCode()==0){
                LOG.info("Successfully Updated Delivery Time on Swiggy for Unit::{}",cafeTimingChangeRequest.getUnitId());
                return true;
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogSwiggyException(e, "Swiggy updateCafeDeliveryTime API returned incorrect response:::::");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating Delivery Time on Swiggy:::::" + "\nrequest::::"+ requestJson);
        }
        return false;
    }

    @Override
    public SwiggyOrderReject orderReject(SwiggyRejectOrderDetail swiggyRejectOrderDetail,RejectionMetadata rejectionMetadata,ReasonMetaData reasonMetaData) {
        setOrderRejectionMetadata(swiggyRejectOrderDetail,rejectionMetadata,reasonMetaData);
        SwiggyOrderReject response = webServiceHelper.callSwiggyApi(environmentProperties,SwiggyServiceEndpoints.ORDER_REJECT,HttpMethod.POST,swiggyRejectOrderDetail,SwiggyOrderReject.class );
        if(Objects.isNull(response)|| response.getStatusCode() != 0){
          LOG.error("Error in rejecting order for orderId:{}",swiggyRejectOrderDetail.getReferenceId());
        }
        return response;
    }

    private void setOrderRejectionMetadata( SwiggyRejectOrderDetail swiggyRejectOrderDetail,RejectionMetadata rejectionMetadata,ReasonMetaData reasonMetaData) {
        Object metaData;
        switch (rejectionMetadata) {
            case ITEMS_OUT_OF_STOCK:
                metaData = reasonMetaData.getOutOfStockItems();
                swiggyRejectOrderDetail.getMetadata().setRejectionMetadata(metaData);
                break;

            case RESTAURANT_CLOSED:
            case KITCHEN_STRESS:
            case MERCHANT_DEVICE_ISSUE:
            case NO_DELIVERY_PARTNER_AVAILABLE:

                metaData = reasonMetaData.getNextAvailableTimeEpoch();
                swiggyRejectOrderDetail.getMetadata().setRejectionMetadata(metaData);
                break;

            case CUSTOMER_OUTSIDE_DELIVERY_RADIUS:
                swiggyRejectOrderDetail.getMetadata().setRejectionMetadata(null);
                break;
            default:
                LOG.error("No rejection reason selected");
        }
    }


    @Override
    public String getRestaurantId(String orderId) {
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(orderId);
        String restaurantId = partnerOrderDetail.getRestaurantId();
        return restaurantId;
    }

    @Override

    public SwiggyCustomerConnect connectWithCustomer(ConnectCustomerObject connectCustomerObject) {
        Map<String, String> ids = new HashMap<>();
        Map<String,String> uri = new HashMap<>();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(connectCustomerObject.getUnitId(), connectCustomerObject.getBrandId(),connectCustomerObject.getPartnerId());
        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
        ids.put("external_restaurant_id", restaurantId);
        ids.put("order_id",connectCustomerObject.getOrderId());
        uri.put("order_id",connectCustomerObject.getOrderId());
        SwiggyCustomerConnect response = webServiceHelper.callSwiggyApi(environmentProperties, SwiggyServiceEndpoints.CUSTOMER_CONNECT, HttpMethod.GET,connectCustomerObject,SwiggyCustomerConnect.class,uri,ids,null);
        logFallbackData(connectCustomerObject.getOrderId());
        if (Objects.nonNull(response) && response.getStatusCode() == 0) {
            return response;
        } else {
            LOG.error("Error in Connecting With Customer for orderId:{}", connectCustomerObject.getOrderId());
        }
        return response;
    }

    private  void  logFallbackData(String orderId){
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByPartnerOrderId(orderId).get(0);
        PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId(partnerOrderDetail.getOrderId() );
        String prevEmployeeId = (statusLogPrev == null || "system".equals(statusLogPrev.getEmployeeId()))
                ? "system"
                : statusLogPrev.getEmployeeId();
        PartnerOrderFallbackStatus statusLog = partnerOrderService.addFallbackOrderStatusLog(partnerOrderDetail,statusLogPrev);
        partnerOrderService.addFallbackOrderLog(partnerOrderDetail,String.valueOf(PartnerActionCode.CALL_SWIGGY_CUSTOMER),"Call Swiggy Customer",statusLog, ActionCategory.BUTTON_CLICKED.name(),prevEmployeeId);
    }

    public SwiggyOrderResponse getSwiggyDuplicateOrderResponse(SwiggyOrderResponse swiggyOrderResponse, String orderId) {
        LOG.info("SWIGGY duplicate order received for order id:::::::::::::::::::: {}", orderId);
        swiggyOrderResponse.setStatusCode(106);
        swiggyOrderResponse.setExternalOrderId(orderId);
        swiggyOrderResponse.setStatusMessage(String.format("%s already exist", orderId));
        if (environmentProperties.slackDuplicateOrders()) {
            String message = ChannelPartnerUtils.getMessage("Swiggy duplicate order received!" + orderId, "");
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
        }
        return swiggyOrderResponse;
    }

}
