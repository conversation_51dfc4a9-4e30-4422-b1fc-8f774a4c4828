package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "message_order",
        "color",
        "penalty_amount",
        "message",
        "message_exp_time"
})
public class ZomatoOrderMessagesV3 {
	
	@JsonProperty("message_order")
    private Integer messageOrder;
    @JsonProperty("color")
    private String color;
    @JsonProperty("penalty_amount")
    private Float panaltyAmount;
    @JsonProperty("message")
    private String message;
    @JsonProperty("message_exp_time")
    private Integer messageExpTime;
    
    @JsonProperty("message_order")
	public Integer getMessageOrder() {
		return messageOrder;
	}
    
    @JsonProperty("message_order")
	public void setMessageOrder(Integer messageOrder) {
		this.messageOrder = messageOrder;
	}
    
    @JsonProperty("color")
	public String getColor() {
		return color;
	}
    
    @JsonProperty("color")
	public void setColor(String color) {
		this.color = color;
	}
    
    @JsonProperty("penalty_amount")
	public Float getPanaltyAmount() {
		return panaltyAmount;
	}
    
    @JsonProperty("penalty_amount")
	public void setPanaltyAmount(Float panaltyAmount) {
		this.panaltyAmount = panaltyAmount;
	}
    
    @JsonProperty("message")
	public String getMessage() {
		return message;
	}
    
    @JsonProperty("message")
	public void setMessage(String message) {
		this.message = message;
	}
    
    @JsonProperty("message_exp_time")
	public Integer getMessageExpTime() {
		return messageExpTime;
	}
    
    @JsonProperty("message_exp_time")
	public void setMessageExpTime(Integer messageExpTime) {
		this.messageExpTime = messageExpTime;
	}

	@Override
	public String toString() {
		return "ZomatoOrderMessagesV3 [messageOrder=" + messageOrder + ", color=" + color + ", panaltyAmount="
				+ panaltyAmount + ", message=" + message + ", messageExpTime=" + messageExpTime + "]";
	}
    
	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(messageOrder).append(color).append(panaltyAmount).append(message).append(messageExpTime).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderMessagesV3) == false) {
            return false;
        }
        ZomatoOrderMessagesV3 rhs = ((ZomatoOrderMessagesV3) other);
        return new EqualsBuilder().append(messageOrder, rhs.messageOrder).append(color, rhs.color).append(panaltyAmount, rhs.panaltyAmount).append(message, rhs.message).append(messageExpTime, rhs.messageExpTime).isEquals();
    }

}
