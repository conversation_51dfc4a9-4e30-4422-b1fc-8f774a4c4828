package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "variant_id", "catalogue_id", "catalogue_name", "properties", "unit_cost", "modifier_groups", "quantity" })

public class ZomatoOrderCompSelectionEntityV3 {
	
	@JsonProperty("variant_id")
	private String variantId;
	@JsonProperty("catalogue_id")
	private String catalogueId;
	@JsonProperty("catalogue_name")
	private String catalogueName;
	@JsonProperty("properties")
	private List<ZomatoOrderCompositionPropV3> properties;
	@JsonProperty("unit_cost")
	private Float unitCost;
	@JsonProperty("modifier_groups")
	private List<ZomatoOrderModifierGroupsV3> modifierGroups;
	@JsonProperty("quantity")
	private Integer quantity;
	
	public String getVariantId() {
		return variantId;
	}
	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}
	public String getCatalogueId() {
		return catalogueId;
	}
	public void setCatalogueId(String catalogueId) {
		this.catalogueId = catalogueId;
	}
	public String getCatalogueName() {
		return catalogueName;
	}
	public void setCatalogueName(String catalogueName) {
		this.catalogueName = catalogueName;
	}
	public List<ZomatoOrderCompositionPropV3> getProperties() {
		return properties;
	}
	public void setProperties(List<ZomatoOrderCompositionPropV3> properties) {
		this.properties = properties;
	}
	public Float getUnitCost() {
		return unitCost;
	}
	public void setUnitCost(Float unitCost) {
		this.unitCost = unitCost;
	}
	public List<ZomatoOrderModifierGroupsV3> getModifierGroups() {
		return modifierGroups;
	}
	public void setModifierGroups(List<ZomatoOrderModifierGroupsV3> modifierGroups) {
		this.modifierGroups = modifierGroups;
	}
	public Integer getQuantity() {
		return quantity;
	}
	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}
	@Override
	public String toString() {
		return "ZomatoOrderCompSelectionEntityV3 [variantId=" + variantId + ", catalogueId=" + catalogueId
				+ ", catalogueName=" + catalogueName + ", properties=" + properties + ", unitCost=" + unitCost
				+ ", modifierGroups=" + modifierGroups + ", quantity=" + quantity + "]";
	}
	
	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(variantId).append(catalogueId).append(catalogueName).append(properties)
				.append(unitCost).append(modifierGroups).append(quantity).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderCompSelectionEntityV3) == false) {
			return false;
		}
		ZomatoOrderCompSelectionEntityV3 rhs = ((ZomatoOrderCompSelectionEntityV3) other);
		return new EqualsBuilder().append(variantId, rhs.variantId).append(catalogueId, rhs.catalogueId)
				.append(catalogueName, rhs.catalogueName).append(properties, rhs.properties)
				.append(unitCost, rhs.unitCost).append(modifierGroups, rhs.modifierGroups)
				.append(quantity, rhs.quantity).isEquals();
	}
}
