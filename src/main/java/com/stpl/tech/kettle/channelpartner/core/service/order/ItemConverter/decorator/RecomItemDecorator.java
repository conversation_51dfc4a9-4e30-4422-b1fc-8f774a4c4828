package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator;

import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderItemDecoratorBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderItemFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class RecomItemDecorator extends BaseAddonItemDecorator {

    private BaseAddonItemDecorator baseAddonItemDecorator;

    private BaseOrderItemDecorator baseOrderItemDecorator;

    public RecomItemDecorator(OrderItemDecoratorBuilder orderItemDecoratorBuilder, BaseAddonItemDecorator baseAddonItemDecorator, BaseOrderItemDecorator baseOrderItemDecorator) {
        super(orderItemDecoratorBuilder);
        this.baseAddonItemDecorator = baseAddonItemDecorator;
        this.baseOrderItemDecorator = baseOrderItemDecorator;
    }

    @Override
    public <R, I> Pair<PartnerItemData, List<OrderItem>> processItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts, int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) {
        Optional<Pair<PartnerItemData, List<OrderItem>>> itemDataListPair = Optional.ofNullable(this.baseAddonItemDecorator.processItem(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap, cafeProducts));
        itemDataListPair.ifPresent(pair -> {
            if (!CollectionUtils.isEmpty(pair.getKey().getRecommendedProducts())) {
                addRecommendedItems(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap,
                        cafeProducts, priceProfileProducts.get(pair.getKey().getProductId()), pair.getKey(), pair.getValue());
            }
            orderItemDecoratorBuilder.discountStrategy.applyDiscount(request, item, pair.getValue(), pair.getKey());
            orderItemDecoratorBuilder.taxStrategy.addItemTax(item, pair.getValue(), taxMap);
        });
        return itemDataListPair.orElse(null);
    }


    private <I, R> void addRecommendedItems(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail,
                                            Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                            int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap,
                                            Map<Integer, Product> cafeProducts, Product product, PartnerItemData partnerItemData,
                                            List<OrderItem> orderItemList) {
        for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getRecommendedProducts()) {
            PartnerItemData recommendeditemPartnerData = OrderItemFactory.getPartnerItemDataForRecommendedItem(partnerOrderDetail.getPartnerName(),
                    partnerItemAddonData);
            Product recommendedProduct = priceProfileProducts.get(recommendeditemPartnerData.getProductId());
            I recommendedPartnerItem = OrderItemFactory.getPartnerItemForRecommendationProduct(partnerOrderDetail.getPartnerName(),
                    item, partnerItemAddonData, partnerItemData);
            this.baseOrderItemDecorator.addMenuItem(order, request, recommendedPartnerItem, partnerOrderDetail, taxMap, priceProfileProducts,
                    pricingUnitId, pricingMap, cafeProducts, recommendedProduct, recommendeditemPartnerData, false, null,
                    orderItemList);
        }
    }

}
