package com.stpl.tech.kettle.channelpartner.core.util;

import java.time.Instant;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class TokenBucket {
    private int capacity;
    private int tokens;
    private Instant lastRefillTime;
    private Lock lock;

    public TokenBucket(int capacity) {
        this.capacity = capacity;
        this.tokens = capacity;
        this.lastRefillTime = Instant.now();
        this.lock = new ReentrantLock();
    }

    public boolean tryConsume() {
        lock.lock();
        try {
            refillTokens();
            if (tokens > 0) {
                tokens--;
                return true;
            }
            return false;
        } finally {
            lock.unlock();
        }
    }

//    public synchronized boolean tryConsume() {
//        refillTokens();
//        if (tokens > 0) {
//            tokens--;
//            return true;
//        }
//        return false;
//    }

    private void refillTokens() {
        Instant now = Instant.now();
        long timeElapsed = now.toEpochMilli() - lastRefillTime.toEpochMilli();
        int newTokens = (int) (timeElapsed * capacity / 60000);
        tokens = Math.min(tokens + newTokens, capacity);
        lastRefillTime = now;
    }
}
