package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Log4j2
public class PartnerEventSlackNotificationService implements CommonPartnerEventNotificationService<String , SlackNotification, Boolean, String> {
    
    @Autowired
    private EnvironmentProperties environmentProperties ;
    @Override
    public void publishPartnerEventNotificationToChannel(String message, SlackNotification channel, Boolean success, String partnerName, String applicationName) {
        if(Objects.nonNull(success)&& success ){
            publishSucessNotification(message, channel,partnerName,applicationName);
        }else{
            publishErrorNotification(message,channel,partnerName,applicationName);
        }
    }

    private void publishErrorNotification(String message, SlackNotification channel, String partnerName, String applicationName) {
        try {
            log.info(message);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    applicationName, channel,
                    message);
        } catch (Exception ex) {
            log.error("Error in logging exception for partner ::::{}",partnerName, ex);
        }
    }

    private void publishSucessNotification(String message, SlackNotification channel, String partnerName, String applicationName) {
        String notification = ChannelPartnerUtils.getMessage(message ,"");
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                applicationName, channel,
                notification);
        log.info(message);
    }

    @Override
    public <Q, R> void publishPartnerOrderEventNotificationToUser(String message, Boolean success, Q user, R directUsr) {
        String message1 = ChannelPartnerUtils.getMessage("Order Rejections", message);
        SlackNotificationService.getInstance()
                .sendNotification(environmentProperties.getEnvType(), "Channel Partner", (String) directUsr,
                        !AppUtils.isProd(environmentProperties.getEnvType())
                                ? environmentProperties.getEnvType().name().toLowerCase() + "_"
                                + user
                                : (String) user,
                        message1);
    }
}
