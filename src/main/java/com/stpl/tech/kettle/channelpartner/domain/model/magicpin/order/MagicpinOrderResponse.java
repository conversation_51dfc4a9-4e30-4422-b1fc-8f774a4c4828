package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "responseCode",
        "responseMessage",
        "thirdPartyOrderId",
        "deliverySlots"
})
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MagicpinOrderResponse {
    private Integer responseCode;
    private String responseMessage;
    private String thirdPartyOrderId;

}
