package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ZomatoTakeawayChangeRequest {

    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("outlet_takeaway_status")
    private Integer outletTakeawayStatus;
    @JsonProperty("status_update_reason")
    private String statusUpdateReason;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("outlet_takeaway_status")
    public Integer getOutletTakeawayStatus() {
        return outletTakeawayStatus;
    }

    @JsonProperty("outlet_takeaway_status")
    public void setOutletTakeawayStatus(Integer outletTakeawayStatus) {
        this.outletTakeawayStatus = outletTakeawayStatus;
    }

    @JsonProperty("status_update_reason")
    public String getStatusUpdateReason() {
        return statusUpdateReason;
    }

    @JsonProperty("status_update_reason")
    public void setStatusUpdateReason(String statusUpdateReason) {
        this.statusUpdateReason = statusUpdateReason;
    }
}