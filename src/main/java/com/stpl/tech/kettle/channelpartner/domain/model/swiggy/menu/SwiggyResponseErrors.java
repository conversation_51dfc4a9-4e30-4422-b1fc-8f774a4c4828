package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "error_field",
        "rejected_value",
        "message"
})
public class SwiggyResponseErrors {

    @JsonProperty("error_field")
    private String errorField;
    @JsonProperty("rejected_value")
    private String rejectedValue;
    @JsonProperty("message")
    private String message;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("error_field")
    public String getErrorField() {
        return errorField;
    }

    @JsonProperty("error_field")
    public void setErrorField(String errorField) {
        this.errorField = errorField;
    }

    @JsonProperty("rejected_value")
    public String getRejectedValue() {
        return rejectedValue;
    }

    @JsonProperty("rejected_value")
    public void setRejectedValue(String rejectedValue) {
        this.rejectedValue = rejectedValue;
    }

    @JsonProperty("message")
    public String getMessage() {
        return message;
    }

    @JsonProperty("message")
    public void setMessage(String message) {
        this.message = message;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
