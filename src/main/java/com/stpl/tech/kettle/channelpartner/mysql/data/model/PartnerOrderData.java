package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "PARTNER_ORDER_DETAIL")
public class PartnerOrderData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "PARTNER_ORDER_ID")
    private String partnerOrderId;

    @Column(name = "KETTLE_ORDER_ID")
    private Integer kettleOrderId;

    @Column(name = "PARTNER_ID")
    private Integer partnerId;

    @Column(name = "CUSTOMER_ID")
    private String customerId;

    @Column(name = "CUSTOMER_NAME")
    private String customerName;

    @Column(name = "RESTAURANT_DISCOUNT")
    private BigDecimal restaurantDiscount;

    @Column(name = "RESTAURANT_GROSS_BILL")
    private BigDecimal restaurantGrossBill;

    @Column(name = "ORDER_PACKAGING_CHARGES")
    private BigDecimal orderPackagingCharges;

    @Column(name = "CART_GST")
    private BigDecimal cartGst;

    @Column(name = "NET_AMOUNT")
    private BigDecimal netAmount;

    @Column(name = "GROSS_AMOUNT")
    private BigDecimal grossAmount;

    @Column(name = "AMOUNT_PAID")
    private BigDecimal amountPaid;

    @Column(name = "SALT_DISCOUNT")
    private BigDecimal saltDiscount;

    @Column(name = "ORDER_LEVEL_DISCOUNT")
    private BigDecimal orderLevelDiscount;

    @Column(name = "ORDER_TAX")
    private BigDecimal orderTax;

    @Column(name = "IS_PRIORTIZED")
    private String isPriortizedOrder = "N";


}
