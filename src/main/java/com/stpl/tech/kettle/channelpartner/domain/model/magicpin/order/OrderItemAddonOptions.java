package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"id", "name", "mrp", "sell_price", "quantity", "tax", "third_party_id", "primary_type","option_groups"})
public class OrderItemAddonOptions {

    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;

    @JsonProperty("selected")
    private boolean selected;
    @JsonProperty("mrp")
    private Float mrp;
    @JsonProperty("sell_price")
    private Float sellPrice;
    @JsonProperty("quantity")
    private Float quantity;
    @JsonProperty("tax")
    private Float tax;
    @JsonProperty("third_party_id")
    private String thirdPartyId;
    @JsonProperty("primary_type")
    private String primaryType;

    @JsonProperty("discount")
    private Float discount;

    @JsonProperty("parentItemID")
    private String parentItemId;


    @JsonProperty("option_groups")
    private List<MagicpinOrderItemAddons> optionGroups;
}
