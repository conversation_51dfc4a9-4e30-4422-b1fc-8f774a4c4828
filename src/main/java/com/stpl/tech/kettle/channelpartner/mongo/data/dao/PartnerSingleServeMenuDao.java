package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerSingleServeMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;

@Repository
public interface PartnerSingleServeMenuDao extends MongoRepository<PartnerSingleServeMenuDetail, String> {
	
	public List<PartnerSingleServeMenuDetail> findAllByPartnerIdAndUnitId(Integer partnerId, Integer unitId);
	
	public List<PartnerSingleServeMenuDetail> findAllByActiveAndPartnerIdAndUnitId(Boolean active, Integer partnerId, Integer unitId);

	public List<PartnerSingleServeMenuDetail> findAllByActiveAndPartnerIdAndUnitIdAndBrandId(Boolean active, Integer partnerId, Integer unitId, Integer brandId);


}
