package com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.MagicpinError;
import com.stpl.tech.kettle.channelpartner.core.service.CommissionService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderConverterFactory;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.task.MagicpinOrderTask;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.MagicpinServiceEndPoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerAddressData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.MagicpinNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinChargesDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItems;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinAddressDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinTaxDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTaxDetailsV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Qualifier("magicpinOrderService")
@Log4j2
public class MagicPinOrderManagementService extends PartnerOrderManagementServiceImpl {

    @Autowired
    OrderValidationService orderValidationService;

    @Autowired
    PartnerOrderConverterDependency partnerOrderConverterDependency;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private TrackService trackService;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    private EnvironmentProperties environmentProperties;


    @Autowired
    private CommissionService commissionService;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private PartnerOrderService partnerOrderService;


    @Autowired
    public MagicPinOrderManagementService(OrderValidationService orderValidationService, ChannelPartnerDataCache channelPartnerDataCache,
                                          MasterDataCache masterDataCache) {
        super(orderValidationService, channelPartnerDataCache,
                masterDataCache);
    }


    @Override
    public void checkOrder(PartnerOrderDetail partnerOrderDetail, Order order, Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual, PartnerPrimaryData partnerPrimaryData) {
        super.checkOrder(partnerOrderDetail, order, partnerProductTaxMap, isManual, partnerPrimaryData);
        // Validate transaction data
        checkTransactionalData(order, partnerOrderDetail);
        orderValidationService.updateCheckOrderStatus(partnerOrderDetail, isManual);
    }

    @Override
    public void setOrderProcessingThread(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck) {
        MagicpinOrderTask magicpinOrderTask = new MagicpinOrderTask();
        magicpinOrderTask.setMagicpinService(this);
        magicpinOrderTask.setIsManual(isManual);
        magicpinOrderTask.setOrder(order);
        magicpinOrderTask.setPartnerOrderDetail(partnerOrderDetail);
        magicpinOrderTask.setSkipInventoryCheck(skipInventoryCheck);
        magicpinOrderTask.setRequestId(MDC.get("request.id"));
        threadPoolTaskExecutor.execute(magicpinOrderTask);
    }

    @Override
    public <R, Q> PartnerOrderDetail primaryChecks(R request, PartnerOrderDetail partnerOrderDetail, Q response, Integer kettlePartnerId, PartnerPrimaryData primaryData) {
        partnerOrderDetail = super.primaryChecks(request, partnerOrderDetail, response, kettlePartnerId, primaryData);
        //MagicPin Specific Checks
        return partnerOrderDetail;
    }

    @Override
    public <R> PartnerPrimaryData getPrimaryData(R request) {
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        return PartnerPrimaryData.builder().orderId(String.valueOf(magicpinOrderRequest.getOrderId()))
                .outletId(magicpinOrderRequest.getMagicpinMerchantData().getClientId()).partnerName(ChannelPartnerServiceConstants.MAGICPIN)
                .customerName(magicpinOrderRequest.getUserName()).orderType(magicpinOrderRequest.getOrderType())
                .customerPhoneNumber(magicpinOrderRequest.getPhoneNo()).orderInstructions(magicpinOrderRequest.getNote())
                .paymentMode(magicpinOrderRequest.getPaymentMode())
                .cashToCollect(BigDecimal.ZERO).orderInstructions(((MagicpinOrderRequest) request).getNote())
                .partnerCustomerData(getPartnerCustomerData(request))
                .build();
    }


    @Override
    public <R, T> T handlesDuplicateOrder(R request, T response, PartnerPrimaryData partnerPrimaryData) {
        log.info((String) super.handlesDuplicateOrder(request, response, partnerPrimaryData));
        MagicpinOrderResponse magicPinOrderResponse = getMagicPinOrderResponse(PartnerOrderStates.DUPLICATE, null, PartnerOrderStates.DUPLICATE.getResponseMessage(), partnerPrimaryData);
        return (T) magicPinOrderResponse;
    }

    @Override
    public <R, T> T handlesFailedOrder(R request, T response, PartnerOrderDetail partnerOrderDetail) {
        Object res = super.handlesRejectedOrder(request, partnerOrderDetail, PartnerOrderStates.FAILED);
        MagicpinOrderResponse magicPinOrderResponse = getMagicPinOrderResponse(PartnerOrderStates.FAILED, partnerOrderDetail, (String) res, null);
        return (T) magicPinOrderResponse;
    }

    @Override
    public <R, T> T handlesRejectedOrder(R request, PartnerOrderDetail partnerOrderDetail, PartnerOrderStates rejected) {
        try {
            Object res = super.handlesRejectedOrder(request, partnerOrderDetail, rejected);
            MagicpinOrderResponse magicpinOrderResponse = getMagicPinOrderResponse(PartnerOrderStates.REJECTED, partnerOrderDetail, (String) res, null);
            return (T) magicpinOrderResponse;
        } catch (ClassCastException classCastException) {
            log.error("Exception caught during casting object while handling reject order ::::::::", classCastException);
        } catch (Exception e) {
            log.error("Exception caught during handling reject order :::::::::::::: ", e);
        }
        return null;
    }

    @Override
    public <R, T> T handlesSuccessFullOrder(R request, PartnerOrderDetail partnerOrderDetail) {
        MagicpinOrderResponse responseObject =(MagicpinOrderResponse) getResponseObject();
        responseObject.setResponseCode(200);
        responseObject.setThirdPartyOrderId(partnerOrderDetail.getExternalOrderId());
        responseObject.setResponseMessage("Success");
        return (T) responseObject;
    }

    public  <R> PartnerCustomerData getPartnerCustomerData(R request){
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        MagicpinAddressDetail shippingAddress = magicpinOrderRequest.getShippingAddress();
        return PartnerCustomerData.builder().customerName(shippingAddress.getName()).contactNumber(shippingAddress.getContactNumbers().get(0))
                .partnerCustomerAddressData(getPartnerCustomerAddressData(request))
                .build();
    }

    private <R>PartnerCustomerAddressData getPartnerCustomerAddressData(R request){
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        MagicpinAddressDetail magicpinAddressDetail = magicpinOrderRequest.getShippingAddress();
        return PartnerCustomerAddressData.builder().addressLine1(magicpinAddressDetail.getAddressLine1()).addressLine2(magicpinAddressDetail.getAddressLine2())
                .city(magicpinAddressDetail.getCity()).state(magicpinAddressDetail.getState()).country(magicpinAddressDetail.getCountry())
                .latitude(Objects.nonNull(magicpinAddressDetail.getLat()) ? magicpinAddressDetail.getLat().toString()
                        : null).longitude(Objects.nonNull(magicpinAddressDetail.getLon()) ? magicpinAddressDetail.getLon().toString() : null)
                .locality(magicpinAddressDetail.getLocalityName()).zipcode(magicpinAddressDetail.getPinCode()).build();
    }

    //@Override

    @Override
    public <R, T> T handlesExceptionInOrder(R request, T response, PartnerOrderDetail partnerOrderDetail, Exception e, PartnerPrimaryData partnerPrimaryData) {
        String msg = PartnerOrderStates.EXCEPTION.getResponseMessage()+ " for partnerName" + partnerOrderDetail.getPartnerName() +" and partner order id ::::"+ partnerOrderDetail.getPartnerOrderId();
        log.error(msg, e);
        super.commonHandlerForAnyErrorInOrder(partnerOrderDetail);
        return (T) getMagicPinOrderResponse(PartnerOrderStates.EXCEPTION, partnerOrderDetail, msg,partnerPrimaryData);
    }

    @Override
    public <T> T getResponseObject() {
        return (T) new MagicpinOrderResponse();
    }

    @Override
    public Order convertOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual,
                              PartnerPrimaryData partnerPrimaryData) {
        return OrderConverterFactory.getInstance(partnerOrderDetail.getPartnerName(),
                partnerOrderConverterDependency).convertOrder(partnerOrderDetail, isManual,
                partnerPrimaryData);

    }


    private void checkTransactionalData(Order order, PartnerOrderDetail partnerOrderDetail) {
        TransactionDetail td1 = order.getTransactionDetail();
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) partnerOrderDetail.getPartnerOrder();
        partnerOrderDetail.setBillDifference(BigDecimal.ZERO);
        partnerOrderDetail.setBillPercentageDifference(BigDecimal.ZERO);
        BigDecimal paidAmount = BigDecimal.valueOf(magicpinOrderRequest.getAmount());
        BigDecimal discountAmount  = BigDecimal.valueOf(magicpinOrderRequest.getMerchantFundedDiscount());
        BigDecimal ourPaidAmount = calculateOurPaidAmount(td1);
        BigDecimal taxableAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal taxPaidByPartner = BigDecimal.ZERO;

        for (MagicpinOrderItems item : magicpinOrderRequest.getMagicpinOrderItems()) {
            boolean isTaxPaidByPartner = TaxPayingEntity.PARTNER.getMagicPin().equalsIgnoreCase(item.getTaxLiability());
            BigDecimal total = BigDecimal.valueOf(item.getAmount());

            taxableAmount = taxableAmount.add(total);
            taxableAmount = taxableAmount.subtract(BigDecimal.valueOf(item.getDiscount()));

            for (MagicpinChargesDetails magicpinChargesDetails : item.getMagicpinChargesDetails()) {
                taxableAmount = taxableAmount.add(BigDecimal.valueOf(magicpinChargesDetails.getAmount()));
                total = total.add(BigDecimal.valueOf(magicpinChargesDetails.getAmount()));

                if (isTaxPaidByPartner) {
                    addTaxPaidByPartner( magicpinChargesDetails.getMagicpinTaxDetails(), taxPaidByPartner);
                }
            }

            if (isTaxPaidByPartner) {
                addTaxPaidByPartner(item.getTaxCharges(), taxPaidByPartner);
            }

            totalAmount = totalAmount.add(total);
        }

        ourPaidAmount = ChannelPartnerUtils.subtract(ourPaidAmount, taxPaidByPartner);
        paidAmount = ChannelPartnerUtils.subtract(paidAmount, taxPaidByPartner);
        paidAmount = ChannelPartnerUtils.subtract(paidAmount,discountAmount);

        validateAndLogDifferences(order, partnerOrderDetail, td1, paidAmount, taxableAmount, totalAmount,
                BigDecimal.valueOf(magicpinOrderRequest.getMerchantFundedDiscount()));
    }

    private void addTaxPaidByPartner(List<MagicpinTaxDetails> magicpinItemTaxDetails, BigDecimal taxPaidByPartner) {
        for (MagicpinTaxDetails taxDetail : magicpinItemTaxDetails) {
            taxPaidByPartner = ChannelPartnerUtils.add(taxPaidByPartner, BigDecimal.valueOf(taxDetail.getAmount()));
        }
    }

    private BigDecimal calculateOurPaidAmount(TransactionDetail td1) {
        BigDecimal roundOffValue = td1.getRoundOffValue();
        return (roundOffValue.negate().compareTo(BigDecimal.ZERO) > 0) ?
                ChannelPartnerUtils.add(td1.getPaidAmount(), roundOffValue) :
                ChannelPartnerUtils.subtract(td1.getPaidAmount(), roundOffValue);
    }

    private void validateAndLogDifferences(Order order, PartnerOrderDetail partnerOrderDetail, TransactionDetail td1,
                                           BigDecimal paidAmount, BigDecimal taxableAmount, BigDecimal totalAmount, BigDecimal totalDiscount) {
        if (!orderValidationService.isValidData(td1.getCollectionAmount(), paidAmount)) {
            orderValidationService.checkPaidAmount(order, partnerOrderDetail, td1, paidAmount);
        }

        validateAndLogDifference(order, partnerOrderDetail, td1.getTaxableAmount(), taxableAmount, "CART", "TAXABL");
        validateAndLogDifference(order, partnerOrderDetail, td1.getTotalAmount(), totalAmount, "CART", "TOTAL");
        validateAndLogDifference(order, partnerOrderDetail, td1.getDiscountDetail().getTotalDiscount(), totalDiscount, "CART", "TOTAL_DISC");
    }

    private void validateAndLogDifference(Order order, PartnerOrderDetail partnerOrderDetail, BigDecimal actual, BigDecimal expected, String category, String type) {
        if (!orderValidationService.isValidData(actual, expected)) {
            orderValidationService.logMetadataDifference(order, category, type, null, actual.floatValue(), expected.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                    type + " our/partner " + actual + "/" + expected);
        }
    }


    private MagicpinOrderResponse getMagicPinOrderResponse(PartnerOrderStates partnerOrderError , PartnerOrderDetail partnerOrderDetail, String message, PartnerPrimaryData partnerPrimaryData){
        MagicpinOrderResponse responseObject = getResponseObject();
        responseObject.setResponseCode(partnerOrderError.getResponseCode());
        responseObject.setThirdPartyOrderId(Objects.nonNull(partnerOrderDetail.getExternalOrderId()) ? partnerOrderDetail.getExternalOrderId() : partnerPrimaryData.getOrderId());
        responseObject.setResponseMessage(message);
        return responseObject;
    }

    @Override
    public void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Stopwatch watch = Stopwatch.createUnstarted();
        orderValidationService.tagOrder(partnerOrderDetail, order);
        if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null && partnerOrderDetail.getToBeProcessed())) {
            //Sending order to KETTLE
            Map<String, Object> uriVariables = new HashMap<>();
            uriVariables.put("addMetadata", true);
            boolean logFailure = false;
            try {
                order = webServiceHelper.callInternalApi(environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_ORDER,
                        environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Order.class, order, uriVariables);
                if (order != null) {
                    watch.start();
                    commissionService.commissionCalculation(order, partnerOrderDetail);
                    log.info("{},MAGICPIN STEP ,Commission Calculation",
                            watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
                if (order != null) {
                    partnerOrderDetail.setKettleOrder(order);
                    partnerOrderDetail.setKettleOrderId(order.getOrderId().toString());
                    partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                            PartnerOrderStatus.PLACED, isManual, currentTime));
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.PLACED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.PLACED, "Order placed!", null));
//                    savePartnerOrderDataMysql(partnerOrderDetail,order.getOrderId()); Todo need to implement after object declaration
                    partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                } else {
                    logFailure = true;
                }
            } catch (Exception e) {
                log.error("Magicpin order exception {}", partnerOrderDetail.getPartnerOrderId(), e);
                logFailure = true;
            }
            if (logFailure) {
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                        PartnerOrderStatus.FAILED, isManual, currentTime));
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.FAILED);
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.FAILED, "Order failed to punch!", null));
                partnerOrderDetail.setBeingProcessed(false);
                trackService.updatePartnerOrder(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
            }
        } else {
            // send slack notification here
            partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
            partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
        }
    }

    @Override
    public void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual) {
        MagicpinOrderUpdate magicpinOrderUpdate = new MagicpinOrderUpdate();
        magicpinOrderUpdate.setOrderId(Long.valueOf(partnerOrderDetail.getPartnerOrderId()));
        magicpinOrderUpdate.setStatus(partnerOrderDetail.getPartnerOrderStatus().ACCEPTED.toString());
//       Dont  Run order confirmation checks only in Testing env
        if (AppUtils.isProd(environmentProperties.getEnvType()) || !environmentProperties.isTestingModeEnabled()) {
            confirmMagicpinOrder(magicpinOrderUpdate, isManual);
        }
    }

    @Override
    public void confirmMagicpinOrder(MagicpinOrderUpdate magicpinOrderUpdate, boolean isManual) {
        log.info("Inside Magicpin Confirmation Call for {}", magicpinOrderUpdate.getOrderId());
        String partnerOrderId = String.valueOf(magicpinOrderUpdate.getOrderId());
        List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(partnerOrderId);
        if (partnerOrderDetails.isEmpty()) {
            log.error("No order found for confirmation call Magicpin: {}", magicpinOrderUpdate.getOrderId());
        }
        for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetails) {
            if (!partnerOrderDetail.isNotified()) {
                try {
                    MagicpinNotificationResponse response = webServiceHelper.callMagicpinApi(environmentProperties,
                            MagicpinServiceEndPoints.ORDER_STATUS_UPDATE, HttpMethod.POST,
                            magicpinOrderUpdate, MagicpinNotificationResponse.class, partnerOrderDetail.getBrandId());
                    String responseJson = new Gson().toJson(response);
                    log.info("Magicpin order confirmation response:: {}", responseJson);
                    orderValidationService.updatePartnerNotificationStatus(partnerOrderDetail,
                            response != null , isManual);
                } catch (HttpStatusCodeException e) {
                    String errorBody = e.getResponseBodyAsString();
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    /*MagicpinError error = new Gson().fromJson(e.getResponseBodyAsString(), MagicpinError.class);
                    if (e.getResponseBodyAsString() != null) {
                        errorBody = e.getResponseBodyAsString();
                    }*/
                    slackAndLogNotification("Error confirming Magicpin order:-\n" + errorBody);
                } catch (Exception e) {
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    slackAndLogNotification("Error confirming Magicpin order:\n" + e);
                }
            }

        }
    }

    private void slackAndLogNotification(String message) {
        slackAndLogNotification(message, SlackNotification.PARTNER_INTEGRATION);
    }

    private void slackAndLogNotification(String message, SlackNotification channel) {
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                message);
        log.error(message);
    }

}
