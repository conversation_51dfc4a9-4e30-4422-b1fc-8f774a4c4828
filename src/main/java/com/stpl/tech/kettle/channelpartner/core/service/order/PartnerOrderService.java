package com.stpl.tech.kettle.channelpartner.core.service.order;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderProcessingFactory;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Log4j2
public class PartnerOrderService extends PartnerAbstractOrderService {
    private final PartnerOrderConverterDependency partnerOrderConverterDependency ;

    public PartnerOrderService(TrackService trackService, ChannelPartnerDataCache channelPartnerCacheService,
                               PartnerOrderManagementService partnerOrderManagementService, PartnerOrderConverterDependency partnerOrderConverterDependency) {
        super(trackService, channelPartnerCacheService, partnerOrderManagementService);
        this.partnerOrderConverterDependency = partnerOrderConverterDependency;
    }

    @Override
    public <R, T> T addOrder(R request, boolean isManual) throws ChannelPartnerException {
        PartnerPrimaryData partnerPrimaryData = partnerOrderManagementService.getPrimaryData(request);
        T response = partnerOrderManagementService.getResponseObject();
        OrderProcessingFactory.getInstance(partnerPrimaryData.getPartnerName(), partnerOrderConverterDependency)
                .process(request,isManual, response, partnerPrimaryData, null );
        return response;
    }

    private PartnerOrderDetail enrichPartnerOrderDetail(PartnerDetail partner, PartnerOrderDetail partnerOrderDetail, String outletId) {
        if (partnerOrderDetail != null) {
            UnitPartnerBrandMappingData data = channelPartnerDataCache.getUnitPartnerBrandMappingData(outletId, partner.getKettlePartnerId());
            partnerOrderDetail.setRestaurantId(data.getRestaurantId());
            partnerOrderDetail.setUnitId(data.getUnitId());
            partnerOrderDetail.setBrandId(data.getBrandId());
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        }
        return partnerOrderDetail;
    }

    @Override
    public void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException {
        if (partnerOrderDetail == null) {
            throw new ChannelPartnerException("Please provide proper order details for processing!");
        }
        partnerOrderDetail.setBeingProcessed(true);
        partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        if (partnerOrderDetail == null) {
            throw new ChannelPartnerException("Error in processing order for orderId {}", partnerOrderDetail.getPartnerOrderId());
        }
        Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
        PartnerPrimaryData partnerPrimaryData = partnerOrderManagementService.getPrimaryData(partnerOrderDetail.getPartnerOrder());
        try {
            switch (partnerOrderDetail.getPartnerOrderStatus()) {
                case RECEIVED:
                    manualProcessReceivedStatus(partnerOrderDetail, skipInventoryCheck, partnerProductTaxMap, partnerPrimaryData);
                    break;
                case CHECKED:
                    manualProcessCheckedStatus(partnerOrderDetail, skipInventoryCheck, partnerPrimaryData);
                    break;
                case FAILED:
                    manualProcessFailedStatus(partnerOrderDetail, skipInventoryCheck, partnerPrimaryData);
                    break;
                case PLACED:
                    partnerOrderManagementService.notifyOrder(partnerOrderDetail, true);
                    break;
                default:
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
            }
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
            log.error("Error in manual processing order for orderId {} ", partnerOrderDetail.getPartnerOrderId(), e);
        }
    }

    private <R, Q> void updateMissingData(PartnerOrderDetail partnerOrderDetail, R request,
                                          PartnerPrimaryData partnerPrimaryData) {
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(partnerPrimaryData.getPartnerName());
        if (partnerOrderDetail != null) {
            //Primary validations like unit and item
            Q response = partnerOrderManagementService.getResponseObject();
            partnerOrderDetail = partnerOrderManagementService.primaryChecks(request,
                    partnerOrderDetail, response, partner.getKettlePartnerId(), partnerPrimaryData);
        }
        boolean tobeProcessed = partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeProcessed());
        if (tobeProcessed && (partnerOrderDetail.getUnitId() == null || partnerOrderDetail.getBrandId() == null)) {
            enrichPartnerOrderDetail(partner, partnerOrderDetail, partnerPrimaryData.getOutletId());
        }
    }

    private void manualProcessReceivedStatus(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck, Map<Integer, StateTaxVO> partnerProductTaxMap,
                                             PartnerPrimaryData partnerPrimaryData) {
        updateMissingData(partnerOrderDetail, partnerOrderDetail.getPartnerOrder(), partnerPrimaryData);
        Order order = partnerOrderManagementService.convertOrder(partnerOrderDetail, true, partnerPrimaryData);
        partnerOrderManagementService.checkOrder(partnerOrderDetail, order, partnerProductTaxMap, true,
                partnerPrimaryData);
        partnerOrderManagementService.setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
    }

    private void manualProcessCheckedStatus(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck, PartnerPrimaryData partnerPrimaryData) {
        if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null &&
                partnerOrderDetail.getToBeProcessed())) {
            updateMissingData(partnerOrderDetail, partnerOrderDetail.getPartnerOrder(), partnerPrimaryData);
            Order order = partnerOrderManagementService.convertOrder(partnerOrderDetail, true, partnerPrimaryData);
            partnerOrderManagementService.setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
        } else {
            log.error("Don't have required data to manual process order for orderId {} ", partnerOrderDetail.getPartnerOrderId());
        }
    }

    private void manualProcessFailedStatus(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck, PartnerPrimaryData partnerPrimaryData) {
        updateMissingData(partnerOrderDetail, partnerOrderDetail.getPartnerOrder(), partnerPrimaryData);
        Order order = partnerOrderManagementService.convertOrder(partnerOrderDetail, true, partnerPrimaryData);
        partnerOrderManagementService.setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
    }


}
