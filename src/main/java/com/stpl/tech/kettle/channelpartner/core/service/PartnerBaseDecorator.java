package com.stpl.tech.kettle.channelpartner.core.service;


import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

import java.util.List;

public abstract class PartnerBaseDecorator {

    protected RedisPublisher redisPublisher;
    protected ChannelPartnerDataCache channelPartnerDataCache;
    protected MasterDataCache masterDataCache;
    protected EnvironmentProperties environmentProperties;

    public PartnerBaseDecorator(PartnerMetadataBuilder partnerMetadataBuilder) {
        this.redisPublisher = partnerMetadataBuilder.redisPublisher;
        this.channelPartnerDataCache = partnerMetadataBuilder.channelPartnerDataCache;
        this.masterDataCache = partnerMetadataBuilder.masterDataCache;
        this.environmentProperties = partnerMetadataBuilder.environmentProperties;
    }

    public abstract <T, R> R preProcessData(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds, Class<R> returnType) throws ChannelPartnerException;
}
