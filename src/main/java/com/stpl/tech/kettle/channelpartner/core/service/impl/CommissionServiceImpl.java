package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.channelpartner.config.PartnerDroolConfig;
import com.stpl.tech.kettle.channelpartner.core.PartnerDroolFileType;
import com.stpl.tech.kettle.channelpartner.core.mapper.MonthlyAOVDetailMapper;
import com.stpl.tech.kettle.channelpartner.core.service.CommissionService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerDroolService;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.OrderCommissionData;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerAOVRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.BrandMonthYearKey;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MonthlyAOVDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.MonthlyAOVMetadataDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.OrderCommissionDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.MonthlyAOVMetadata;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.OrderComission;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.MasterObjectFactory;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import org.kie.api.runtime.KieSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class CommissionServiceImpl implements CommissionService {
    private static final Logger LOG = LoggerFactory.getLogger(CommissionServiceImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;
    private static final MasterObjectFactory masterFactory = new MasterObjectFactory();
    @Autowired
    private PartnerDroolConfig partnerDroolConfig;
    @Autowired
    private OrderCommissionDao dao;

    @Autowired
    private MonthlyAOVMetadataDao aovDao;
    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private EnvironmentProperties environmentProperties;
    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private PartnerDroolService partnerDroolService;

   private static final HashMap<BrandMonthYearKey,BigDecimal> localAovCache = new HashMap<>();

   private OrderComission orderConverter(OrderCommissionData orderCommissionData){
       OrderComission orderComission = new OrderComission();
       orderComission.setUnitId(orderCommissionData.getUnitId());
       orderComission.setPartnerOrderId(orderCommissionData.getPartnerOrderId());
       orderComission.setKettleOrderId(orderCommissionData.getKettleOrderId());
       orderComission.setSubTotal(orderCommissionData.getSubTotal());
       orderComission.setPackagingCharges(orderCommissionData.getPackagingCharges());
       orderComission.setDiscount(orderCommissionData.getDiscount());
       orderComission.setNetAmount(orderCommissionData.getNetAmount());
       orderComission.setAov(orderCommissionData.getAov());
       orderComission.setCommissionRate(orderCommissionData.getCommissionRate());
       orderComission.setExtraCommissionRate(orderCommissionData.getExtraCommissionRate());
       orderComission.setCommissionAmount(orderCommissionData.getCommissionAmount());
       orderComission.setGstRate(orderCommissionData.getGstRate());
       orderComission.setFinalCommissionAmount(orderCommissionData.getFinalCommissionAmount());
       orderComission.setBrand(orderCommissionData.getBrand());
       orderComission.setPartnerName(orderCommissionData.getPartnerName());
       orderComission.setUnitAge(orderCommissionData.getUnitAge());
       orderComission.setUnitLiveDate(orderCommissionData.getUnitLiveDate());
       orderComission.setSwiggyCloudKitchen(orderCommissionData.getSwiggyCloudKitchen());
       orderComission.setRuleParams(orderCommissionData.getRuleParams());
       orderComission.setOrderDate(orderCommissionData.getOrderDate());
       orderComission.setRestaurantGrossBill(orderCommissionData.getRestaurantGrossBill());
       orderComission.setStatus(orderCommissionData.getStatus());
       return orderComission;
   }
    private OrderCommissionData setProps(Order order) {
        int unitId = order.getUnitId();
        int brandId = order.getBrandId();
        int partnerId = order.getChannelPartner();
        int channelPartnerId = order.getChannelPartner();

        UnitPartnerBrandMappingData data = getUnitBrandData(unitId,brandId,partnerId);
        OrderCommissionData obj = new OrderCommissionData();
          obj.setPartnerOrderId(order.getSourceId());
          obj.setKettleOrderId(order.getOrderId());
          obj.setUnitId(unitId);
          setCommissionTransactionalData(order,obj);
          if(channelPartnerId==AppConstants.CHANNEL_PARTNER_SWIGGY){
              obj.setPartnerName(AppConstants.SWIGGY);
          } else if (channelPartnerId==AppConstants.CHANNEL_PARTNER_ZOMATO) {
              obj.setPartnerName(AppConstants.ZOMATO);
          }
          obj.setBrand(brandId);
          obj.setBrandName(brandId==AppConstants.GNT_BRAND_ID?AppConstants.GNT_BRAND:AppConstants.CHAAYOS_BRAND);
          obj.setSwiggyCloudKitchen(data.getSwiggyCloudKitchen());
          obj.setGstRate(18.0);

        if(brandId==AppConstants.CHAAYOS_BRAND_ID && channelPartnerId==AppConstants.CHANNEL_PARTNER_SWIGGY){
         setLiveUnitDate(data,obj);
        }
        obj.setOrderDate(order.getBillingServerTime());
        obj.setStatus("ACTIVE");
        return obj;
    }
    @Override
    @Transactional(rollbackFor = Exception.class,value="CPDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public void commissionCalculation(Order order, PartnerOrderDetail partnerOrderDetail) throws IOException {
       try{
       Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        OrderCommissionData orderComm = setProps(order);
        LOG.info("Step 1 : Order Commission Object prop set : {}ms",  watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        BigDecimal amount = AppUtils.add(orderComm.getSubTotal(),orderComm.getPackagingCharges());
        BigDecimal netAmount = AppUtils.subtract(amount,orderComm.getDiscount());
        orderComm.setNetAmount(netAmount);
        if(order.getChannelPartner()==AppConstants.CHANNEL_PARTNER_SWIGGY){
            orderComm.setAov(netAmount);
            SwiggyOrderRequest partnerOrder = (SwiggyOrderRequest)partnerOrderDetail.getPartnerOrder();
            orderComm.setRestaurantGrossBill(BigDecimal.valueOf(partnerOrder.getRestaurantGrossBill()));
        }else if (order.getChannelPartner()==AppConstants.CHANNEL_PARTNER_ZOMATO){
            orderComm.setAov(getZomatoAov(order.getBillingServerTime(),order.getBrandId()));
            ZomatoOrderRequestV3 partnerOrder = (ZomatoOrderRequestV3)partnerOrderDetail.getPartnerOrder();
            orderComm.setRestaurantGrossBill(BigDecimal.valueOf(partnerOrder.getGrossAmount()));
        }else {
            return;
        }

        LOG.info("Step 2 : Set net amount : {}ms",  watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        if(!partnerDroolService.isDroolContainerInitializeForCommissionMatrix()){
            partnerDroolService.initailizeDroolContainer(PartnerDroolFileType.COMMISSION_MATRIX.getFileName());
        }
        KieSession kieSession =  partnerDroolConfig.getKieContainerForCommissionMatrix().newKieSession();
        kieSession.insert(orderComm);
        kieSession.fireAllRules();
        kieSession.destroy();
        setCommissionData(orderComm,netAmount);
        LOG.info("Step 3 : Commission set from drool : {}ms",  watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
           OrderComission orderObj = orderConverter(orderComm);
            dao.add(orderObj);
        LOG.info("Step 4 : Store order commission object : {}ms",  watch.stop().elapsed(TimeUnit.MILLISECONDS));
       } catch (Exception e){
        LOG.error("Exception in commissionCalcuation function : {}",e);
    }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,value="CPDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public void calculateMonthWiseAOV(PartnerAOVRequest partnerAOVRequest) {
        PartnerAOVRequest aovRequest = setBusineessDatesForAOVCalculation(partnerAOVRequest);
        List<MonthlyAOVDetail> monthlyAOVDetailList = new ArrayList<>();
        Stopwatch watch = Stopwatch.createUnstarted();
        LOG.info("&&& Step 1 ::: Sending Request to calculate aov for Orders from :::{} till ::{} for AOV Calculation for partnerId ::{} and brandId ::{}", aovRequest.getStartDate(), aovRequest.getEndDate(), aovRequest.getPartnerId(), aovRequest.getBrandId());
        watch.start();
        String endPoint = environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.CALCULATE_AOV;
        List<?> list = webServiceHelper.callInternalApi(endPoint,
                environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, List.class, aovRequest, null);
        LOG.info("&&& Completed Request to calculate aov for Orders from :::{} till ::{} for AOV Calculation for partnerId ::{} and brandId ::{} in :::{}", aovRequest.getStartDate(), aovRequest.getEndDate(), aovRequest.getPartnerId(), aovRequest.getBrandId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        convertToZomatoAOVDetail(aovRequest, list, monthlyAOVDetailList);
        LOG.info("&&& Step 2 ::: Sending Request to save or update aov values  from :::{} till ::::{} ",aovRequest.getStartDate(), aovRequest.getEndDate());
        watch.start();
        saveOrUpdateAOVValues(monthlyAOVDetailList,aovRequest);
    }

    private void saveOrUpdateAOVValues(List<MonthlyAOVDetail> monthlyAOVDetailList, PartnerAOVRequest aovRequest) {
       List<MonthlyAOVMetadata> monthlyAOVMetadataList = aovDao.getBrandWisePreviousMonthAov(AppUtils.getYear(aovRequest.getStartDate()),AppUtils.getMonth(aovRequest.getStartDate()), aovRequest.getBrandId());
       if(Objects.nonNull(monthlyAOVDetailList) && !monthlyAOVDetailList.isEmpty() && Objects.nonNull(monthlyAOVMetadataList) && !monthlyAOVMetadataList.isEmpty()){
           monthlyAOVMetadataList.stream().forEach(monthlyAOVMetadata -> {
               Optional<MonthlyAOVDetail> obj = monthlyAOVDetailList.stream().filter(monthlyAOVDetail -> monthlyAOVDetail.getBrandId().equals(monthlyAOVMetadata.getBrandId())).findAny();
               if(obj.isPresent()){
                   try {
                       aovDao.updateAovValues(monthlyAOVMetadata, obj);
                   } catch (DataUpdationException e) {
                       throw new RuntimeException(e);
                   }
               }
           });
       }else {
           try{
               aovDao.addAll(MonthlyAOVDetailMapper.INSTANCE.toDtoList(monthlyAOVDetailList));
               synchronized (this) {
                   for (MonthlyAOVDetail obj : monthlyAOVDetailList) {
                       BrandMonthYearKey brandMonthYearKey = BrandMonthYearKey.builder().brandId(obj.getBrandId()).month(obj.getMonth()).year(obj.getYear()).build();
                       localAovCache.put(brandMonthYearKey, obj.getAov());
                   }
               }
           }catch(Exception e ){
               LOG.error("Exception while saving aov Values  ::::", e );
           }
       }
        LOG.info("&&& Completed  to save aov values  from :::{} till ::::{} ",aovRequest.getStartDate(), aovRequest.getEndDate());
    }

    private PartnerAOVRequest setBusineessDatesForAOVCalculation(PartnerAOVRequest partnerAOVRequest) {
        if (Objects.isNull(partnerAOVRequest)) {
            Date endDate = AppUtils.getPreviousDate(AppUtils.getCurrentTimestamp());
            int month = AppUtils.getMonth(endDate);
            int year  = AppUtils.getYear(endDate);
            Date startDate = AppUtils.getStartDateOfMonth(year,month);
            return PartnerAOVRequest.builder().endDate(endDate).startDate(startDate).brandId(new ArrayList<>(Arrays.asList(AppConstants.CHAAYOS_BRAND_ID, AppConstants.GNT_BRAND_ID))).partnerId(AppConstants.CHANNEL_PARTNER_ZOMATO).build();
        }
        return partnerAOVRequest;
    }

    private List<MonthlyAOVDetail> convertToZomatoAOVDetail(PartnerAOVRequest partnerAOVRequest, List<?> list, List<MonthlyAOVDetail> monthlyAOVDetailList) {
       try{
           if(Objects.nonNull(list) && !list.isEmpty()){
               GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
               list.forEach(p -> {
                   Gson gson = gSonBuilder.create();
                   String str = gson.toJson(p);
                   MonthlyAOVDetail monthlyAOVDetail = gson.fromJson(str, MonthlyAOVDetail.class);
                   monthlyAOVDetailList.add(monthlyAOVDetail);
               });
           }
       }catch(Exception e ){
          LOG.error("Exception while converting to monthly aov detail list :::{}", monthlyAOVDetailList);
       }
       return monthlyAOVDetailList;
    }


    private long getCafeBrandAge(Date liveDate){
     String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
     String openDate =  new SimpleDateFormat("yyyy-MM-dd").format(liveDate);
     return ChronoUnit.MONTHS.between(
                LocalDate.parse(openDate),
                LocalDate.parse(currentDate));
    }
    private UnitPartnerBrandMappingData getUnitBrandData(int unitId,int brandId,int partnerId){
        UnitPartnerBrandKey key = masterFactory.createUnitPartnerData();
        key.setUnitId(unitId);
        key.setBrandId(brandId);
        key.setPartnerId(partnerId);
        return  masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
    }
     private void setCommissionTransactionalData(Order order,OrderCommissionData obj){
       BigDecimal packagingCharges = BigDecimal.valueOf(0);
       OrderItem lastItem = order.getOrders().get(order.getOrders().size() - 1);
       if(lastItem.getProductId()==1043){
               packagingCharges = lastItem.getTotalAmount();
       }
       if(order.getOrders().size()>=2) {
           OrderItem secondLastItem = order.getOrders().get(order.getOrders().size() - 2);
           if (secondLastItem.getProductId() == 1043) {
               packagingCharges = AppUtils.add(packagingCharges, secondLastItem.getTotalAmount());
           }
       }
           obj.setSubTotal(AppUtils.subtract(order.getTransactionDetail().getTotalAmount(),packagingCharges));
           obj.setDiscount(order.getTransactionDetail().getDiscountDetail().getTotalDiscount());
           obj.setPackagingCharges(packagingCharges);
     }
     private void setLiveUnitDate(UnitPartnerBrandMappingData data, OrderCommissionData obj){
         Date liveDate = data.getLiveDate();
         if(liveDate != null){
             long unitAge = getCafeBrandAge(liveDate);
             obj.setUnitLiveDate(liveDate);
             obj.setUnitAge(unitAge);
         }else{
             obj.setUnitAge(6);
         }
     }

     private  void setCommissionData(OrderCommissionData orderComm,BigDecimal netAmount){
         double comm = orderComm.getCommissionRate();
         if(orderComm.getExtraCommissionRate()!=null){
             comm += orderComm.getExtraCommissionRate();
         }
         BigDecimal commissionAmount = AppUtils.multiply(netAmount,BigDecimal.valueOf(comm/100));
         orderComm.setCommissionAmount(commissionAmount);
         double gst = orderComm.getGstRate()/100;
         BigDecimal finalCommissionAmount = AppUtils.add(commissionAmount,AppUtils.multiply(commissionAmount,BigDecimal.valueOf(gst)));
         orderComm.setFinalCommissionAmount(finalCommissionAmount);
     }

    private BigDecimal getZomatoAov(Date orderDate,int brandId){

        int month = AppUtils.getPreviousMonthFromDate(orderDate);
        int year = AppUtils.getPreviousYearIfChanged(orderDate);
        BrandMonthYearKey brandMonthYearKey = BrandMonthYearKey.builder().month(month).year(year).brandId(brandId).build();
        BigDecimal aov = BigDecimal.ZERO;
       synchronized (this) {
           aov = localAovCache.getOrDefault(brandMonthYearKey, BigDecimal.valueOf(-1));
       }
        if(Objects.equals(aov, BigDecimal.valueOf(-1))){
            MonthlyAOVMetadata zomatoAov = aovDao.getPreviousMonthAov(year,month,brandId);
            if(zomatoAov==null || zomatoAov.getAov()==null){
                return BigDecimal.valueOf(0);
            }
            synchronized (this) {
                localAovCache.put(brandMonthYearKey, zomatoAov.getAov());
            }
            return zomatoAov.getAov();
        }
        return aov;
    }
    @Override
    @Transactional(rollbackFor = Exception.class,value="CPDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public void startOrderCommissionCalculationForPrevDate(String startDate, String endDate){

        String endpoint =  KettleServiceClientEndpoints.GET_MISSED_ORDER;
        if(startDate != null || endDate != null ){
               endpoint = KettleServiceClientEndpoints.GET_MISSED_ORDER+"?startDate="+startDate+"&endDate="+endDate;
        }

        LOG.info("################# 1. Starting commission Calculation for Previous Orders, between {} and {} date. #####################",startDate,endDate);
        List<?> orders = webServiceHelper.callInternalApi(
                environmentProperties.getKettleServiceBasePath() + endpoint,
                environmentProperties.getChannelPartnerClientToken(), HttpMethod.GET, List.class, null,
                null);
        LOG.info("################ 2. Get {} missed orders for commission calculation ###################",orders.size());
        GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
        AtomicInteger orderCount = new AtomicInteger();
        orders.forEach(e->{
            Gson gson = gSonBuilder.create();
            String str = gson.toJson(e);
            Order order = gson.fromJson(str, Order.class);

            List<PartnerOrderDetail> partnerOrderDetailList =  partnerOrderDao.searchByPartnerOrderId(order.getSourceId());
            if(e!=null && order.getOrderId()!=null && partnerOrderDetailList.size()>0){
                try {
                    commissionCalculation(order,partnerOrderDetailList.get(partnerOrderDetailList.size()-1));
                    orderCount.getAndIncrement();
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            }else{
                LOG.info("######## Partner Order Detail Not found for order id :{}",order.getOrderId());
            }

        });
        LOG.info("################ 3. Completed commission calculation for {}  orders ###################",orderCount);

    }

}
