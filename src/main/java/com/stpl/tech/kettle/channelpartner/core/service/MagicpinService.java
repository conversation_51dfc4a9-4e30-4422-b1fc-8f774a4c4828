package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicPinStatusUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinDeliveryUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;

import java.util.List;

public interface MagicpinService {
    public MagicpinOrderResponse addMagicpinOrder(MagicpinOrder orderRequest);

    public void updateDeliveryStatus(MagicpinDeliveryUpdate deliveryUpdate);

    public boolean setUnitDeliveryStatus(List<Integer> unitIds, Boolean status, Integer brandId) throws ChannelPartnerException;

    void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    public void cancelOrder(MagicPinStatusUpdate magicPinStatusUpdate);
}
