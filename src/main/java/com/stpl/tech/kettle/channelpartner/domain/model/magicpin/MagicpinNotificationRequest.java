package com.stpl.tech.kettle.channelpartner.domain.model.magicpin;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "external_order_id",
        "delivery_time",
        "prep_time"
})
public class MagicpinNotificationRequest {

    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("external_order_id")
    private String externalOrderId;
    @JsonProperty("delivery_time")
    private Integer deliveryTime;
    @JsonProperty("prep_time")
    private Integer prepTime;

}
