package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerLocalityDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;

public interface PartnerLocalityService {
    PartnerLocalityDetail getLocality(Integer kettlePartnerId, String locality, String city, String state, String country, String pinCode);

    PartnerLocalityDetail addLocality(PartnerLocalityDetail partnerLocalityDetail);

    UnitPartnerLocalityMapping getPartnerLocalityMapping(PartnerLocalityDetail partnerLocalityDetail);

    UnitPartnerLocalityMapping addMapping(UnitPartnerLocalityMapping mapping);
}
