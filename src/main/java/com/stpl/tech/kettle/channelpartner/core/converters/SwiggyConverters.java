package com.stpl.tech.kettle.channelpartner.core.converters;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxType;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Addon;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Variant;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.OptionData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.math3.analysis.function.Add;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class SwiggyConverters {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyConverters.class);
    private static final String REGULAR_SUGAR = "Regular Sugar";


    public static Order convertOrder(SwiggyOrderRequest request, Customer customer, MasterDataCache masterDataCache,
                                     UnitPartnerBrandMappingData data , String priortizationKey) {
        Pair<String, String> unitDetail = ChannelPartnerUtils.parseUnitId(request.getOutletId());
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Order order = new Order();
        //order.setOrderId();
        order.setGenerateOrderId(ChannelPartnerUtils.generateRandomOrderId());
        //order.setExternalOrderId(externalOrderId);
        //order.setOptionResultEventId();
        //order.setUnitOrderId();
        //order.setCampaignId();
        order.setCustomerId(customer.getId());
        order.setEmployeeId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
        order.setPointsRedeemed(0);
        order.setSource("COD");
        order.setSourceId(request.getOrderId());
        order.setHasParcel(true);
        order.setStatus(OrderStatus.CREATED);
        //order.setApplication();
        //order.setEnquiryItems();
        //setTransactionDetail(order, request);
        //order.setPrintCount(1);
        order.setSettlementType(SettlementType.DEBIT);
        order.setUnitId(data.getUnitId());
        if (unitDetail != null) {
            order.setOrderAttribute(unitDetail.getValue());
        }
        order.setUnitName(masterDataCache.getUnit(order.getUnitId()).getName());
        order.setTerminalId(1);
        //order.setTableNumber();
        order.setBillStartTime(currentTime);
        order.setBillCreationTime(currentTime);
        order.setBillCreationSeconds(0);
        order.setBillingServerTime(currentTime);
        order.setChannelPartner(6);
        order.setDeliveryPartner(5); //PARTNER ID 5 FOR PICKUP
        //order.setSubscriptionDetail();
        //order.setOfferCode();
        //order.setReprints();
        //order.setCancellationDetails();
        if (request.getInstructions() != null) {
            order.setOrderRemark(request.getInstructions());
        }
        order.setDeliveryAddress(0); // SWIGGY orders are PICK UP orders
        order.setCustomerName(customer.getFirstName());
        order.setContainsSignupOffer(false);
        //order.setEmployeeMeal();
        //order.setEmployeeIdForMeal();
        //order.setTempCode();
        //order.setMetadataList();
        order.setNewCustomer(false);
        //order.setPendingCash();
        //order.setTokenNumber();
        //order.setLinkedOrderId();
        //order.setPaymentDetailId();
        //order.setAwardLoyalty();
        order.setOrderType("order");
        //order.setBillBookNo();
        order.setBrandId(data.getBrandId());
        order.setPartnerCustomerId(request.getCustomerId());
        order.setPrioritizedOrder(AppUtils.getStatus(ChannelPartnerUtils.isPriortizedOrder(request.getTags(),priortizationKey)));
        return order;
    }

    public static void addItemsToOrder(Order order, SwiggyOrderRequest request, MasterDataCache masterDataCache, PartnerOrderDetail partnerOrderDetail,
                                       Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, Map<Integer, StateTaxVO> partnerProductTaxMap,
                                       int unitId, int priceProfileUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, EnvironmentProperties environmentProperties,
                                       Map<String, DesiChaiCustomProfiles> chaiProfiles,Map<Integer, Product> cafeProducts) {
        boolean isInterState = false;
        BigDecimal discountPercent = getDiscountValues(request).getPercentage();
        updateDesiChaiProductIds(request);
        boolean packagingCheckForSuperCombo = false;
        List<Item> recommendedOrderItems = new ArrayList<Item>();
        List<Item> paidAddonList = new ArrayList<>();
        Map<String, Float> parentTaxMap = new HashMap<>();
        for (Item item : request.getItems()) {
            String itemId = item.getId().replaceAll("(_[A-Za-z0-9]*)", "");
            boolean productMatched = false;
            if (products.containsKey(Integer.valueOf(itemId))) {
                productMatched = true;
                Product product = products.get(Integer.valueOf(itemId));
                OrderItem orderItem = convertOrderItemNew(product, item, taxMap, products, discountPercent, null, BigDecimal.ZERO,
                    request, 1, masterDataCache, partnerOrderDetail.getBrandId(), recommendedOrderItems, unitId, pricingMap,
                        false,parentTaxMap,paidAddonList,
                        request.getInstructions(), environmentProperties, chaiProfiles, false,cafeProducts);
                order.getOrders().add(orderItem);
                StateTaxVO stateTaxVO = new StateTaxVO();
                stateTaxVO.setCgst(BigDecimal.valueOf(item.getCgstPercent()));
                stateTaxVO.setSgst(BigDecimal.valueOf(item.getSgstPercent()));
                stateTaxVO.setIgst(BigDecimal.valueOf(item.getIgstPercent()));
                partnerProductTaxMap.put(product.getId(), stateTaxVO);
                if (product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
                    packagingCheckForSuperCombo = true;
                }
            }
            prepareOrderError(productMatched, partnerOrderDetail, item);
        }
        if (!recommendedOrderItems.isEmpty() || !paidAddonList.isEmpty()) {
            createRecommendedAndPaidAddonsProductsData(order, recommendedOrderItems, products, partnerOrderDetail, isInterState, taxMap,
                    discountPercent, masterDataCache, request, partnerProductTaxMap, unitId, pricingMap, parentTaxMap, paidAddonList, environmentProperties,
                    chaiProfiles,cafeProducts);
        }
        /*BigDecimal cartValueSourceTax = BigDecimal.ZERO;
        BigDecimal cartValueDefaultTax = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            if(Boolean.TRUE.equals(item.getTaxDeductedByPartner())) {
                cartValueSourceTax = cartValueSourceTax.add(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
            } else {
                cartValueDefaultTax = cartValueDefaultTax.add(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
            }
        }
        if (request.getOrderPackingCharges() > 0 || packagingCheckForSuperCombo) {
            if (products.containsKey(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID)) {
                Product product = products.get(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID);
                //BigDecimal discountPercentage = BigDecimal.ZERO;
                if(cartValueSourceTax.compareTo(BigDecimal.ZERO) > 0) {
                    OrderItem orderItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                            masterDataCache, isInterState, taxMap, false, discountPercent, "SWIGGY",
                            product.getPrices().get(0).getDimension(), priceProfileUnitId, cartValueSourceTax, true, TaxPayingEntity.PARTNER.getSwiggyNew());
                    order.getOrders().add(orderItem);
                }
                if(cartValueDefaultTax.compareTo(BigDecimal.ZERO) > 0) {
                    OrderItem orderItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                            masterDataCache, isInterState, taxMap, false, discountPercent, "SWIGGY",
                            product.getPrices().get(0).getDimension(), priceProfileUnitId, cartValueDefaultTax, false, TaxPayingEntity.SELF.getSwiggyNew());
                    order.getOrders().add(orderItem);
                }
            }
        }*/
        if(request.getOrderPackingCharges()>0 || packagingCheckForSuperCombo){
            setPackagingChargesOrderItem(request,order,masterDataCache,isInterState,taxMap,products,unitId, "SWIGGY");
        }
        try {
            addCondimentsForGheeAndTurmeric(order, masterDataCache, products, environmentProperties, priceProfileUnitId);
        } catch (Exception e) {
            LOG.error("Exception Caught While Adding GnT Chutney::::", e);
        }
        //setOrderLevelRemarks(order,request);
    }

    private static void setOrderLevelRemarks(Order order, SwiggyOrderRequest request) {
        try {
            Map<String, String> orderItemRemarkMap = ChannelPartnerUtils.getOrderItemRemarkMap();
            if (!orderItemRemarkMap.isEmpty()) {
                if (Objects.nonNull(order) && Objects.nonNull(request) && Objects.nonNull(request.getInstructions()) && request.getInstructions().length() > 0 && orderItemRemarkMap.containsKey(request.getInstructions().trim().toLowerCase())) {
                    order.setOrderRemark(orderItemRemarkMap.get(request.getInstructions().trim().toLowerCase()));
                }
            }
        } catch (Exception e) {
            LOG.error("Exception ehile setting order remarks ::::::::::", e);
        }
    }

    private static void setPackagingChargesOrderItem(SwiggyOrderRequest request, Order order, MasterDataCache masterDataCache, boolean isInterState, Map<String, TaxDataVO> taxMap, Map<Integer, Product> products,
                                                     int pricingUnitId, String discountName) {
        BigDecimal sourcePackaging = BigDecimal.ZERO;
        BigDecimal defaultPackaging = BigDecimal.ZERO;
        BigDecimal sourcePackagingWithQuantity = BigDecimal.ZERO;
        BigDecimal defaultPackagingWithQuantity = BigDecimal.ZERO;
        BigDecimal orderPackagingCharges = BigDecimal.ZERO;
        Boolean taxDeductedByPartner = false;
        for (Item item : request.getItems()) {
            if (item.getPackingCharges() > 0) {
                if (TaxPayingEntity.PARTNER.getSwiggyNew().equalsIgnoreCase(item.getGstLiability())) {
                    sourcePackaging = AppUtils.add(sourcePackaging, BigDecimal.valueOf(item.getPackingCharges()));
                    sourcePackagingWithQuantity = AppUtils.add(sourcePackagingWithQuantity, AppUtils.multiply(BigDecimal.valueOf(item.getPackingCharges()), BigDecimal.valueOf(item.getQuantity())));
                } else {
                    defaultPackaging = AppUtils.add(defaultPackaging, BigDecimal.valueOf(item.getPackingCharges()));
                    defaultPackagingWithQuantity = AppUtils.add(defaultPackagingWithQuantity, AppUtils.multiply(BigDecimal.valueOf(item.getPackingCharges()), BigDecimal.valueOf(item.getQuantity())));
                }
            }
            taxDeductedByPartner = TaxPayingEntity.PARTNER.getSwiggyNew().equalsIgnoreCase(item.getGstLiability());
        }
        boolean isPackingChargeWithQuantity = false;
        if(AppUtils.add(sourcePackaging,defaultPackaging).compareTo(BigDecimal.valueOf(request.getOrderPackingCharges())) == 0){
            isPackingChargeWithQuantity = false;
        } else if (AppUtils.add(sourcePackagingWithQuantity,defaultPackagingWithQuantity).compareTo(BigDecimal.valueOf(request.getOrderPackingCharges())) == 0) {
            isPackingChargeWithQuantity = true;
        }
        if(BigDecimal.ZERO.compareTo(BigDecimal.valueOf(request.getOrderPackingCharges())) < 0
                && BigDecimal.ZERO.compareTo(sourcePackaging) >= 0
                && BigDecimal.ZERO.compareTo(defaultPackaging) >= 0) {
            defaultPackaging = AppUtils.add(defaultPackaging, BigDecimal.valueOf(request.getOrderPackingCharges()));
            if(Objects.nonNull(request.getGstBreakup()) && Objects.nonNull(request.getGstBreakup().getSwiggy())
                    && request.getGstBreakup().getSwiggy().getPackagingChargeGst() > 0){
                taxDeductedByPartner = true;
            }
        }
        if (BigDecimal.ZERO.compareTo(sourcePackaging) < 0) {
            if(!isPackingChargeWithQuantity) {
                addPackagingChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                        pricingUnitId, discountName, BigDecimal.ZERO, true, sourcePackaging, TaxPayingEntity.PARTNER.getSwiggyNew());
            }
            else{
                addPackagingChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                        pricingUnitId, discountName, BigDecimal.ZERO, true, sourcePackagingWithQuantity, TaxPayingEntity.PARTNER.getSwiggyNew());
            }
        }
        if (BigDecimal.ZERO.compareTo(defaultPackaging) < 0) {
            if(!isPackingChargeWithQuantity) {
                addPackagingChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                        pricingUnitId, discountName, BigDecimal.ZERO, taxDeductedByPartner, defaultPackaging, TaxPayingEntity.SELF.getSwiggyNew());
            }
            else{
                addPackagingChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                        pricingUnitId, discountName, BigDecimal.ZERO, taxDeductedByPartner, defaultPackagingWithQuantity, TaxPayingEntity.SELF.getSwiggyNew());
            }
        }
    }

    private static void addPackagingChargeItemsToOrder(Order order, MasterDataCache masterDataCache, boolean isInterState, Map<String, TaxDataVO> taxMap,
                                                       Map<Integer, Product> products, int pricingUnitId, String discountName, BigDecimal discountPercent,
                                                       boolean taxDeductedByPartner, BigDecimal price, String partnerTaxType) {
        Product product = products.get(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID);
        OrderItem orderItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                masterDataCache, isInterState, taxMap, false, discountPercent, discountName,
                product.getPrices().get(0).getDimension(), pricingUnitId, BigDecimal.ZERO, taxDeductedByPartner, partnerTaxType);
        orderItem.getTaxes().clear();
        orderItem.setPrice(price);
        orderItem.setAmount(price);
        orderItem.setTotalAmount(price);
        BigDecimal totalItemTax = BigDecimal.ZERO;
        orderItem.setTax(totalItemTax);
        AbstractConverters.setOrderItemTaxes(product,orderItem,taxMap, orderItem.getDiscountDetail(), orderItem.getQuantity());
        if(Objects.nonNull(orderItem.getTaxes()) && !orderItem.getTaxes().isEmpty()){
            for(TaxDetail taxDetail : orderItem.getTaxes()){
                if(Objects.nonNull(taxDetail) && Objects.nonNull(taxDetail.getPercentage())) {
                    taxDetail.setPercentage(taxDetail.getPercentage().setScale(2));
                }
            }
        }
        order.getOrders().add(orderItem);
    }

    private static void addCondimentsForGheeAndTurmeric(Order order, MasterDataCache masterDataCache, Map<Integer, Product> products,
                                                        EnvironmentProperties environmentProperties, int pricingUnitId) {
        LOG.info("Brand Id of Order is : " + order.getBrandId());
        LOG.info("Channel Partner Condiment flag is " + environmentProperties.getCpCondimentFlag());
        // Ghee and turmeric Product Addition
        if (ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID.equals(order.getBrandId()) && environmentProperties.getCpCondimentFlag()) {
            BigDecimal gntCartValue = BigDecimal.ZERO;
            for (OrderItem item : order.getOrders()) {
                Integer prodCatId = item.getProductCategory().getId();
                boolean catValid = Arrays.stream(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_CATEGORIES).anyMatch(value -> value == prodCatId);
                if (catValid && item.getProductId() != 1043) {
                    gntCartValue = ChannelPartnerUtils.subtract(ChannelPartnerUtils.add(ChannelPartnerUtils.multiply(item.getPrice(),
                        new BigDecimal(item.getQuantity())), gntCartValue), item.getDiscountDetail().getTotalDiscount());
                }
            }
            LOG.info("Brand Id is matched with ghee turmeric brand");
            if (gntCartValue.compareTo(BigDecimal.ZERO) > 0) {
                gntCartValue = gntCartValue.add(getPackaginPriceForCondimentOrderV3(gntCartValue, masterDataCache, pricingUnitId));
                Map<TaxDetailKey, TaxDetail> mapTax = new HashMap<>();
                gntCartValue = gntCartValue.add(AbstractConverters.aggregateTaxesFromOrderItems(order.getOrders(), mapTax));
                if (products.containsKey(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_ID)) {
                    Product product = products.get(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_ID);
                    LOG.info("Adding Product " + product.getId() + " to Order");
                    OrderItem orderItem = getCondimentProductV3(product, masterDataCache,
                        product.getPrices().get(0).getDimension(), gntCartValue);
                    order.getOrders().add(orderItem);
                    LOG.info("Product Chutney Box to Order is added successfully");
                }
            }
        }
    }

    private static BigDecimal getPackaginPriceForCondimentOrderV3(BigDecimal gntCartValue, MasterDataCache masterDataCache, int unitId) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
            totalPrice = ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(masterDataCache.getUnit(unitId).getPackagingValue(), new BigDecimal(100)), gntCartValue);
        } else if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("FIXED")) {
            totalPrice = masterDataCache.getUnit(unitId).getPackagingValue();
        }
        return totalPrice;
    }

    private static OrderItem getCondimentProductV3(Product product, MasterDataCache masterDataCache, String dimension, BigDecimal cartValue) {
        LOG.info("Entering getCondimentProduct Method :");
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setProductName(product.getName());
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
        orderItem.setQuantity(getCondimentQuantityV3(cartValue));
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        orderItem.setCode(product.getTaxCode());
        orderItem.setBookedWastage(false);
        orderItem.setTakeAway(false);

        // Finding dimension for product
        ProductPrice productPrice = AbstractConverters.setOrderItemPrice(dimension, product, orderItem);
        // Adding Product Price
        if (productPrice == null) {
            productPrice = product.getPrices().get(0);
        }
        orderItem.setPrice(BigDecimal.ZERO);
        RecipeDetail recipeDetail = productPrice.getRecipe();
        if (recipeDetail != null) {
            orderItem.setRecipeId(recipeDetail.getRecipeId());
        }
        orderItem.setTotalAmount(BigDecimal.ZERO);
        orderItem.setTax(BigDecimal.ZERO);
        LOG.info("Exiting getCondimentProduct Method :");
        //orderItem.setItemCode();
        //orderItem.setReasonId();
        //orderItem.setCardType();
        return orderItem;
    }

    private static int getCondimentQuantityV3(BigDecimal cartValue) {
        BigDecimal quantity = (cartValue.setScale(0, BigDecimal.ROUND_DOWN)).divide(ChannelPartnerServiceConstants.CHUTNEY_BOX_THRESHOLD_PRICE, 0);
        return (quantity.setScale(0, BigDecimal.ROUND_DOWN)).intValue();
    }

    private static void prepareOrderError(boolean productMatched, PartnerOrderDetail partnerOrderDetail, Item item) {
        if (!productMatched) {
            PartnerOrderError partnerOrderError = new PartnerOrderError();
            partnerOrderError.setErrorCode(PartnerOrderErrorCode.PRODUCT_MISMATCH);
            partnerOrderError.setErrorDescription("Product " + item.getName() + " with id " + item.getId() + " not found.");
            partnerOrderDetail.getOrderErrors().add(partnerOrderError);
        }
    }

    private static void createRecommendedAndPaidAddonsProductsData(Order order, List<Item> recommendedOrderItems, Map<Integer, Product> products,
                                                                   PartnerOrderDetail partnerOrderDetail, boolean isInterState, Map<String, TaxDataVO> taxMap,
                                                                   BigDecimal discountPercent, MasterDataCache masterDataCache, SwiggyOrderRequest request,
                                                                   Map<Integer, StateTaxVO> partnerProductTaxMap, int unitId, Map<Integer, Map<String, BigDecimal>> pricingMap
            , Map<String, Float> parentTaxMap, List<Item> paidAddonList, EnvironmentProperties environmentProperties, Map<String, DesiChaiCustomProfiles> chaiProfiles,
                                                                   Map<Integer, Product> cafeProducts) {
        if (Objects.nonNull(recommendedOrderItems) && !recommendedOrderItems.isEmpty()) {
            createRecommendedAndPaidAddonsProductsData(recommendedOrderItems, products, taxMap, discountPercent, masterDataCache, request, partnerOrderDetail, unitId, order, pricingMap, parentTaxMap,
                    partnerProductTaxMap, environmentProperties, chaiProfiles,true,cafeProducts);
        }
        if (Objects.nonNull(paidAddonList) && !paidAddonList.isEmpty()) {
            createRecommendedAndPaidAddonsProductsData(paidAddonList, products, taxMap, discountPercent, masterDataCache, request, partnerOrderDetail, unitId, order, pricingMap,
                    parentTaxMap, partnerProductTaxMap, environmentProperties, chaiProfiles,false,cafeProducts);
        }
        /*for (Item item : recommendedOrderItems) {
            String itemId = item.getId().replaceAll("(_[A-Za-z0-9]*)", "");
            boolean productMatched = false;
            if (products.containsKey(Integer.valueOf(itemId))) {
                Product product = products.get(Integer.valueOf(itemId));
                productMatched = true;
                OrderItem orderItem = new OrderItem();
                orderItem = convertOrderItemNew(product, item, taxMap, products, discountPercent, null, BigDecimal.ZERO,
                    request, 1, masterDataCache, partnerOrderDetail.getBrandId(), recommendedOrderItems, unitId, pricingMap, false, parentTaxMap, paidAddonList);
                order.getOrders().add(orderItem);
                StateTaxVO stateTaxVO = new StateTaxVO();
                stateTaxVO.setCgst(BigDecimal.valueOf(item.getCgstPercent()));
                stateTaxVO.setSgst(BigDecimal.valueOf(item.getSgstPercent()));
                stateTaxVO.setIgst(BigDecimal.valueOf(item.getIgstPercent()));
                partnerProductTaxMap.put(product.getId(), stateTaxVO);
            }
            prepareOrderError(productMatched, partnerOrderDetail, item);
        }*/
    }
    private static void createRecommendedAndPaidAddonsProductsData(List<Item> list, Map<Integer, Product>products, Map<String, TaxDataVO> taxMap, BigDecimal discountPercent
            , MasterDataCache masterDataCache, SwiggyOrderRequest request, PartnerOrderDetail partnerOrderDetail, int unitId, Order order, Map<Integer, Map<String, BigDecimal>> pricingMap
            , Map<String, Float> parentTaxMap, Map<Integer, StateTaxVO> partnerProductTaxMap, EnvironmentProperties environmentProperties, Map<String, DesiChaiCustomProfiles> chaiProfiles,
                                                                   boolean isRecommendedProduct,Map<Integer, Product> cafeProducts){
        for (Item item : list) {
            String itemId = item.getId().replaceAll("(_[A-Za-z0-9]*)", "");
            boolean productMatched = false;
            if (products.containsKey(Integer.valueOf(itemId))) {
                Product product = products.get(Integer.valueOf(itemId));
                productMatched = true;
                OrderItem orderItem = new OrderItem();
                orderItem = convertOrderItemNew(product, item, taxMap, products, discountPercent, null, BigDecimal.ZERO,
                        request, 1, masterDataCache, partnerOrderDetail.getBrandId(), list, unitId, pricingMap, false,
                        parentTaxMap, null, request.getInstructions(), environmentProperties, chaiProfiles,isRecommendedProduct,cafeProducts);
                orderItem.setIsBestPairedItem(AppUtils.setStatus(isRecommendedProduct));
                order.getOrders().add(orderItem);
                StateTaxVO stateTaxVO = new StateTaxVO();
                stateTaxVO.setCgst(BigDecimal.valueOf(item.getCgstPercent()));
                stateTaxVO.setSgst(BigDecimal.valueOf(item.getSgstPercent()));
                stateTaxVO.setIgst(BigDecimal.valueOf(item.getIgstPercent()));
                partnerProductTaxMap.put(product.getId(), stateTaxVO);
            }
            prepareOrderError(productMatched, partnerOrderDetail, item);
        }
    }

    public static void setTransactionDetail(Order order, SwiggyOrderRequest request) {
        TransactionDetail transactionDetail = new TransactionDetail();
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal taxable = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            total = ChannelPartnerUtils.add(total, item.getTotalAmount());
            taxable = ChannelPartnerUtils.add(taxable, item.getAmount());
        }
        transactionDetail.setTotalAmount(total);
        transactionDetail.setTaxableAmount(taxable);
        BigDecimal discountValue = BigDecimal.ZERO;
        BigDecimal discountPercent = BigDecimal.ZERO;
        BigDecimal discountPromotional = BigDecimal.ZERO;
        DiscountDetail discountDetail = new DiscountDetail();
        /*if (request.getRestaurantDiscount() > 0) {
            PercentageDetail percentageDetail = getDiscountValues(request);
            discountValue = percentageDetail.getValue();
            discountPercent = percentageDetail.getPercentage();
            discountDetail.setDiscountReason("SWIGGY");
            discountDetail.setDiscountCode(2004);
        }*/
        for(OrderItem orderItem :order.getOrders()){
            BigDecimal orderItemDiscountValue = BigDecimal.ZERO;
            BigDecimal percentage = BigDecimal.ZERO;
            BigDecimal promotionalOffer = BigDecimal.ZERO;
            if (Objects.nonNull(orderItem.getDiscountDetail())) {
                /*orderItemDiscountValue = orderItem.getDiscountDetail().getDiscount().getValue();
                percentage = orderItem.getDiscountDetail().getDiscount().getPercentage();*/
                promotionalOffer = orderItem.getDiscountDetail().getPromotionalOffer();
            }
//           BigDecimal percentage = orderItemDiscountValue.divide(orderItem.getTotalAmount(), 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            discountValue = discountValue.add(orderItemDiscountValue);
            discountPercent = discountPercent.add(percentage);
            discountPromotional = discountPromotional.add(promotionalOffer);
        }
        if (request.getRewardType() != null) {
            String rewardType = request.getRewardType().replaceAll("[^A-Za-z0-9. %#*&$@!-]", "");
            discountDetail.setDiscountReason(discountDetail.getDiscountReason() != null ?
                (discountDetail.getDiscountReason() + "+" + rewardType) : rewardType);
        }
        discountDetail.setDiscountReason("SWIGGY");
        discountDetail.setDiscountCode(2004);
        discountDetail.setDiscount(new PercentageDetail());
        discountDetail.getDiscount().setValue(discountValue);
        discountDetail.getDiscount().setPercentage(discountPercent);
        discountDetail.setPromotionalOffer(discountPromotional);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getDiscount().getValue(),
            discountDetail.getPromotionalOffer()));
        checkFreebie(request, discountDetail);
        transactionDetail.setDiscountDetail(discountDetail);

        /*transactionDetail.setTaxableAmount(ChannelPartnerUtils.subtract(transactionDetail.getTotalAmount(),
                transactionDetail.getDiscountDetail().getTotalDiscount()));*/

        Map<TaxDetailKey, TaxDetail> taxMap = new HashMap<>();
        boolean isTaxOnPackagingChargesApplicable = false;
        if(Objects.nonNull(request) && request.getOrderPackingChargesGst()>0){
            isTaxOnPackagingChargesApplicable=true;
        }
        BigDecimal totalTax = aggregateTaxesFromOrderItems(order.getOrders(), taxMap,isTaxOnPackagingChargesApplicable);
        transactionDetail.getTaxes().addAll(taxMap.values());
        transactionDetail.setTax(totalTax);

        Map<TaxDetailKey, TaxDetail> collectionTaxMap = new HashMap<>();
//        BigDecimal collectionTotalTax = AbstractConverters.aggregateCollectionTaxesFromOrderItems(order.getOrders(), collectionTaxMap);
        BigDecimal collectionTotalTax = aggregateCollectionTaxesFromOrderItems(order.getOrders(),collectionTaxMap,isTaxOnPackagingChargesApplicable);
        transactionDetail.setCollectionTax(collectionTotalTax);
        BigDecimal collectionTotalAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(), transactionDetail.getCollectionTax());

        BigDecimal paidAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(), transactionDetail.getTax());
        transactionDetail.setPaidAmount(paidAmount.setScale(0, RoundingMode.HALF_UP));
        transactionDetail.setCollectionAmount(collectionTotalAmount.setScale(5, RoundingMode.HALF_UP));
        transactionDetail.setRoundOffValue(ChannelPartnerUtils.subtract(transactionDetail.getPaidAmount(), paidAmount));
        AbstractConverters.calculateSaving(transactionDetail, order);
        order.setTransactionDetail(transactionDetail);

        Settlement settlement = new Settlement();
        settlement.setAmount(order.getTransactionDetail().getPaidAmount());
        settlement.setMode(6); //FOR Credit
        ExternalSettlement externalSettlement = new ExternalSettlement();
        externalSettlement.setAmount(order.getTransactionDetail().getPaidAmount());
        externalSettlement.setExternalTransactionId(Integer.valueOf(3).toString()); //Swiggy credit account id
        settlement.getExternalSettlements().add(externalSettlement);
        order.getSettlements().add(settlement);
    }

    private static BigDecimal aggregateCollectionTaxesFromOrderItems(List<OrderItem> orderItems, Map<TaxDetailKey, TaxDetail> taxMap, boolean isTaxOnPackagingChargesApplicable) {
        BigDecimal totalTax = BigDecimal.ZERO;
        for (OrderItem item : orderItems) {
            boolean sourceTaxflag = true;
            if (Objects.nonNull(item.getPartnerTaxType())) {
                if (item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getZomato())) {
                    sourceTaxflag = false;
                } else if (item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getSwiggyNew())) {
                    sourceTaxflag = false;
                }
            }
            if (sourceTaxflag) {
                if (item.getProductId() != AppConstants.PACKAGING_PRODUCT_ID) {
                    if (item.getCode().equalsIgnoreCase("COMBO")) {
                        AbstractConverters.calculateTaxesForComboItems(item, taxMap);
                    } else {
                        AbstractConverters.calculateTaxesForNonComboItems(item, taxMap);
                    }
                } else {
                    if (isTaxOnPackagingChargesApplicable) {
                        AbstractConverters.calculateTaxesForNonComboItems(item, taxMap);
                    }
                }
            }
        }
        totalTax= AbstractConverters.calculateTotalTaxFromTaxMap(taxMap);
        return totalTax;
    }

    private static BigDecimal aggregateTaxesFromOrderItems(List<OrderItem> orderItems, Map<TaxDetailKey, TaxDetail> taxMap, boolean isTaxOnPackagingChargesApplicable){
        BigDecimal totalTax = BigDecimal.ZERO;
        for (OrderItem item : orderItems) {
            if(item.getProductId()!= AppConstants.PACKAGING_PRODUCT_ID){
                if(item.getCode().equalsIgnoreCase("COMBO")){
                    AbstractConverters.calculateTaxesForComboItems(item,taxMap);
                }
                    AbstractConverters.calculateTaxesForNonComboItems(item,taxMap);
            }else{
                if(isTaxOnPackagingChargesApplicable){
                    AbstractConverters.calculateTaxesForNonComboItems(item,taxMap);
                }
            }
        }
        totalTax=AbstractConverters.calculateTotalTaxFromTaxMap(taxMap);
        return totalTax;
    }

    private static void updateDesiChaiProductIds(SwiggyOrderRequest request) {
        request.getItems().forEach(item -> {
            String itemId = item.getId().replaceAll("(_[A-Za-z0-9]*)", "");
            if (Integer.valueOf(itemId) == 10) {
                item.getVariants().forEach(variant -> {
                    if (ChannelPartnerUtils.getDesiChaiMilkMap().get(variant.getName().trim()) != null) {
                        item.setId(ChannelPartnerUtils.getDesiChaiMilkMap().get(variant.getName().trim()).toString());
                        //item.setName(ChannelPartnerUtils.getDesiChaiProductName().get(Integer.parseInt(item.getId())));
                        String name = item.getName();
                        if (item.getName().contains("(")) {
                            name = item.getName().split("\\(")[0];
                        }
                        name += " " + variant.getName().trim();
                        item.setName(name);
                    }
                });
                if(!CollectionUtils.isEmpty(item.getAddons())){
                    item.getAddons().forEach(addon -> {
                        if (ChannelPartnerUtils.getDesiChaiMilkMap().get(addon.getName().trim()) != null) {
                            item.setId(ChannelPartnerUtils.getDesiChaiMilkMap().get(addon.getName().trim()).toString());
                            //item.setName(ChannelPartnerUtils.getDesiChaiProductName().get(Integer.parseInt(item.getId())));
                            String name = item.getName();
                            if (item.getName().contains("(")) {
                                name = item.getName().split("\\(")[0];
                            }
                            name += " " + addon.getName().trim();
                            item.setName(name);
                        }
                    });
                }
            }
            if (Integer.valueOf(itemId) == 1282) {
                item.getVariants().forEach(variant -> {
                    if (ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(variant.getName().trim()) != null) {
                        item.setId(ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(variant.getName().trim()).toString());
                        //item.setName(ChannelPartnerUtils.getDesiChaiProductName().get(Integer.parseInt(item.getId())));
                        String name = item.getName();
                        if (item.getName().contains("(")) {
                            name = item.getName().split("\\(")[0];
                        }
                        name += " " + variant.getName().trim();
                        item.setName(name);
                    }
                });
                if(!CollectionUtils.isEmpty(item.getAddons())){
                    item.getAddons().forEach(addon -> {
                        if (ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(addon.getName().trim()) != null) {
                            item.setId(ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(addon.getName().trim()).toString());
                            //item.setName(ChannelPartnerUtils.getDesiChaiProductName().get(Integer.parseInt(item.getId())));
                            String name = item.getName();
                            if (item.getName().contains("(")) {
                                name = item.getName().split("\\(")[0];
                            }
                            name += " " + addon.getName().trim();
                            item.setName(name);
                        }
                    });
                }
            }
        });
    }

    private static OrderItem convertOrderItemNew(Product product, Item item, Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, BigDecimal discountPercent,
                                                 String dimension, BigDecimal comboDiscountPercent, SwiggyOrderRequest request, Integer parentQuantity, MasterDataCache masterDataCache,
                                                 Integer brandId, List<Item> recommendedOrderItems, int unitId, Map<Integer, Map<String, BigDecimal>> pricingMap, boolean isConstituent,
                                                 Map<String, Float> parentTaxMap, List<Item> paidAddonList, String instructions, EnvironmentProperties environmentProperties,
                                                 Map<String, DesiChaiCustomProfiles> chaiProfiles, boolean isRecommendedProduct,Map<Integer, Product> cafeProducts) {
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setProductName(product.getName());
        orderItem.setItemName(item.getName());
        /*EntityAliasKey key = new EntityAliasKey(product.getId(), "PRODUCT", brandId);
        EntityAliasMappingData data = masterDataCache.getEntityAliasMappingData().get(key);
        if (data != null) {
            orderItem.setProductName(data.getAlias());
        }*/
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));

        //Setting Item Source Category Name and Subcategory Name
        if (Objects.nonNull(item.getId())) {
            String[] splitId = item.getId().split("_");
            if (splitId.length > 2) {
                orderItem.setSourceCategory(splitId[splitId.length - 2]);
                orderItem.setSourceSubCategory(splitId[splitId.length - 1]);
            }

        }

        orderItem.setQuantity(item.getQuantity());
        AtomicBoolean isRecommendedDish= new AtomicBoolean(false);
        DiscountDetail recomDiscountDetail = null;
        boolean superCombo = product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID
            || product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID;
        boolean fixedMealCombo = product.getSubType() == ChannelPartnerServiceConstants.FIXED_MEAL_COMBO_SUBCATEGORY_ID;
        BigDecimal recommendedDishCost = BigDecimal.ZERO;
        if (!superCombo) {
            recomDiscountDetail = new DiscountDetail();
            recommendedDishCost=getRecommendedProductExtraction(item, recommendedOrderItems, unitId, masterDataCache, products,parentTaxMap,isRecommendedDish, recomDiscountDetail);
        }
        LOG.info("Printing recommmended Dish Cost for item :::::{}:::{}", item.getName(), recommendedDishCost.toString());
        orderItem.setPrice(BigDecimal.valueOf(item.getPrice()).setScale(2, RoundingMode.HALF_UP));
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        //orderItem.setComposition();
        //orderItem.setItemCode();
        orderItem.setCode(product.getTaxCode());
        //orderItem.setReasonId();
        orderItem.setBookedWastage(false);
        //orderItem.setCardType();
        orderItem.setTakeAway(false);
        orderItem.setTaxDeductedByPartner(TaxPayingEntity.PARTNER.getSwiggy().equalsIgnoreCase(item.getGstLiability()) || TaxPayingEntity.PARTNER.getSwiggyNew().equalsIgnoreCase(item.getGstLiability()));

        orderItem.setPartnerTaxType(item.getGstLiability());
        ProductPrice productPrice = null;

        if (dimension == null) {
            if (product.getPrices().size() == 1) {
                productPrice = product.getPrices().get(0);
            } else if (product.getPrices().size() > 1) {
                for (Variant variant : item.getVariants()) {
                    for (ProductPrice productPrice1 : product.getPrices()) {
                        if (variant.getId().toLowerCase().replace(" ", "").contains(productPrice1.getDimension().toLowerCase())) {
                            productPrice = productPrice1;
                        }
                    }
                }
            }
            if (productPrice == null) {
                BigDecimal price = BigDecimal.valueOf(item.getPrice());
                for (Variant variant : item.getVariants()) {
                    if (variant.getPrice() > 0) {
                        price = ChannelPartnerUtils.add(price, BigDecimal.valueOf(variant.getPrice()));
                    }
                }
                //this is not needed as it caused dimension mismatch issues because when upselling or hero items are there
                //then this gets added to price value leading to higher price than actual and caused higher dimension to be picked
                //during price matching for dimension
                /*for (Addon addOn : item.getAddons()) {
                    if (addOn.getPrice() > 0) {
                        price = ChannelPartnerUtils.add(price, BigDecimal.valueOf(addOn.getPrice()));
                    }
                }*/
                TreeMap<BigDecimal, ProductPrice> priceDifference = new TreeMap<>();
                for (ProductPrice pPrice : product.getPrices()) {
                    priceDifference.put(ChannelPartnerUtils.subtract(price, pPrice.getPrice()).abs(), pPrice);
                }
                productPrice = priceDifference.get(priceDifference.firstKey());
            }
        } else {
            for (ProductPrice pPrice : product.getPrices()) {
                if (pPrice.getDimension().equalsIgnoreCase(dimension)) {
                    productPrice = pPrice;
                }
            }
            if (productPrice == null) {
                productPrice = product.getPrices().get(0);
            }
        }
        // Original price of product from pricing unit this can be different from partner price
        // because of dynamic price which is reduced from original price
        if (product.getSubType() != ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
            product.getSubType() != ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
            orderItem.setOriginalPrice(productPrice.getPrice());
        }
        int productId = product.getId();
        if (ChannelPartnerUtils.getDesiChaiVariantMap().containsKey(productId)) {
            productId = ChannelPartnerUtils.getDesiChaiVariantMap().get(productId);
        }
        if (pricingMap != null && pricingMap.containsKey(productId) && pricingMap.get(productId).containsKey(productPrice.getDimension())) {
            productPrice.setPrice(pricingMap.get(productId).get(productPrice.getDimension()));
        }
        if (product.getSubType() != ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
            product.getSubType() != ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
            if(Objects.nonNull(item) && Objects.nonNull(item.getPrice())){
                orderItem.setPrice(BigDecimal.valueOf(item.getPrice()));
            }else {
                orderItem.setPrice(productPrice.getPrice());
            }
        }
        orderItem.setDimension(productPrice.getDimension());

        RecipeDetail recipeDetail = (cafeProducts.containsKey(product.getId()) && product.getPrices().size() == 1
                 && !CollectionUtils.isEmpty(cafeProducts.get(product.getId()).getPrices())
                && AppConstants.CHAAYOS_BRAND_ID == brandId) ? cafeProducts.get(product.getId()).getPrices().get(0).getRecipe()
                :productPrice.getRecipe();
        if (superCombo) { // Fetching Price for Hero and Super Combo
            if(product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID){
                orderItem.setPrice(setUnitPriceForHeroCombo(products,recipeDetail,item,product));
            }else{
                orderItem.setPrice(setUnitPriceForCombo(products, recipeDetail, item, product));
            }
        }

        //LOG.info(new Gson().toJson(recipeDetail));
        orderItem.setRecipeId(recipeDetail.getRecipeId());

        AtomicBoolean containsPaidAddon= new AtomicBoolean(false);
        BigDecimal totalAmount = getParentProductPriceWithPaidAddon(orderItem,item,containsPaidAddon,recommendedDishCost,isRecommendedDish,products);
        LOG.info("Printing total amount for item ::::{} ::: {}", item.getName(), totalAmount);
        orderItem.setTotalAmount(totalAmount);//item.getSubtotal is exclusive of discounts
        LOG.info("Printing containsPaidAddon value :::{}", containsPaidAddon.get());

        //Setting discount data
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        discountDetail.setDiscount(new PercentageDetail());
        BigDecimal orderItemLevelDiscountValue = BigDecimal.ZERO;
        if(Objects.nonNull(item.getItemRestaurantOfferDiscount()) && item.getItemRestaurantOfferDiscount() > 0) {
            orderItemLevelDiscountValue = BigDecimal.valueOf(item.getItemRestaurantOfferDiscount());
        }
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setValue(BigDecimal.ZERO);
        if(!isRecommendedDish.get()){
            if (isConstituent) {
                percentageDetail.setPercentage(comboDiscountPercent);
                percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(percentageDetail.getPercentage(),
                        orderItem.getTotalAmount()));
            } else {
                if (orderItemLevelDiscountValue.compareTo(BigDecimal.ZERO) > 0) {
                    discountDetail.setPromotionalOffer(orderItemLevelDiscountValue);
                }
            }
        }else{
            if(Objects.nonNull(recomDiscountDetail)){
                orderItemLevelDiscountValue = ChannelPartnerUtils.percentageOfWithScale10(recomDiscountDetail.getDiscount().getPercentage(),orderItem.getTotalAmount());
                discountDetail.setPromotionalOffer(orderItemLevelDiscountValue);
            }
        }
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), discountDetail.getDiscount().getValue()));

        if (discountPercent.compareTo(BigDecimal.ZERO) > 0 && comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("SWIGGY+COMBO");
        } else if (discountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("SWIGGY");
        } else if (comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("COMBO");
        }
        if (request.getRewardType() != null) {
            String rewardType = request.getRewardType().replaceAll("[^A-Za-z0-9. %#*&$@!-]", "");
            discountDetail.setDiscountReason(discountDetail.getDiscountReason() != null ?
                (discountDetail.getDiscountReason() + "+" + rewardType) : rewardType);
        }
        orderItem.setDiscountDetail(discountDetail);
        BigDecimal itemFinalAmt = BigDecimal.ZERO;
        /*if((Objects.isNull(recommendedOrderItems) || recommendedOrderItems.isEmpty()) && !isRecommendedProduct && !containsPaidAddon.get()){
                if(item.getFinalSubTotal()>0){
                    itemFinalAmt = BigDecimal.valueOf(item.getFinalSubTotal());
                }else if (item.getSubtotal() > 0 && BigDecimal.ZERO.compareTo(orderItemLevelDiscountValue) < 0){
                    itemFinalAmt= AppUtils.subtract(BigDecimal.valueOf(item.getSubtotal()),orderItemLevelDiscountValue);
                }
            }
        else{
            if(BigDecimal.ZERO.compareTo(totalAmount)<0){
                itemFinalAmt=AppUtils.add(itemFinalAmt,totalAmount);
            }
            if(BigDecimal.ZERO.compareTo(orderItemLevelDiscountValue)<0){
                itemFinalAmt=AppUtils.subtract(totalAmount, orderItemLevelDiscountValue);
            }
        }*/
        itemFinalAmt=AppUtils.subtract(totalAmount,orderItemLevelDiscountValue);
        LOG.info("Printing item amount for item ::::{} ::: {}", item.getName(), totalAmount);
        orderItem.setAmount(itemFinalAmt);
        AtomicBoolean isIncludesPaidAddon = new AtomicBoolean(false);

        //Setting addons and Paid Addons
		OrderItemComposition orderItemComposition = new OrderItemComposition();
		if (item.getAddons() != null && item.getAddons().size() > 0) {
			for (Addon addon : item.getAddons()) {
				Integer id = 0;
				String addonId = addon.getId();
                if(Objects.nonNull(addonId) && !addonId.contains("PAIDADDON")){
                    id =findAddonId(addonId, false);
                    for (IngredientProductDetail ingredientProductDetail : recipeDetail.getAddons()) {
                        if ((id > 0 && ingredientProductDetail.getProduct().getProductId() == id) || ingredientProductDetail
                                .getProduct().getName().equalsIgnoreCase(addon.getName().trim())) {
                            orderItemComposition.getAddons().add(ingredientProductDetail);
                        }
                    }
                }else{
                    id = findAddonId(addonId,true);
                    //        Converting Paid Addons To Item
                    if(Objects.nonNull(paidAddonList)&& paidAddonList.size() >=0){
                        createPaidAddonItem(id , products, paidAddonList, item, addon,null,true);
                    }
                    ProductPrice finalProductPrice = productPrice;
                    List<ProductPrice> matchedPriceObj = cafeProducts.get(productId).getPrices().stream().filter(price ->
                            price.getDimension().equals(finalProductPrice.getDimension())).toList();
                    if(!matchedPriceObj.isEmpty()){
                        RecipeDetail originalProductRecipe = matchedPriceObj.get(0).getRecipe();
                        if(Objects.nonNull(originalProductRecipe.getOptions())){
                            for(OptionData optionData :originalProductRecipe.getOptions()){
                                if(id>0 && Objects.equals(optionData.getId(), id) || optionData.getName().equalsIgnoreCase(addon.getName().trim())){
                                    orderItemComposition.getOptions().add(optionData.getName().trim());
                                }
                            }
                        }
                    }
                    isIncludesPaidAddon.set(true);
                }
			}
		}
        //Check for Paid Addon in Variant
        checkForPaidAddonInVariants(item,orderItemComposition,recipeDetail,paidAddonList,products,productPrice,productId, isIncludesPaidAddon);

        //Adding addons from desi chai custom profiles
        DesiChaiCustomProfiles desiChaiProfile = null;
        if(!item.getVariants().isEmpty()) {
            Variant variant = item.getVariants().get(0);
            if(Objects.nonNull(variant.getId())){
                String[] variantIdSplit = item.getVariants().get(0).getId().split("_");
                LOG.info("variantIdSplit:::::::");
                Arrays.stream(variantIdSplit).forEach(LOG::info);
                if(chaiProfiles.containsKey(variantIdSplit[variantIdSplit.length-1])) {
                    desiChaiProfile = chaiProfiles.get(variantIdSplit[variantIdSplit.length-1]);
                }
                LOG.info("desiChaiProfile::::::: {}", Objects.nonNull(desiChaiProfile) ? desiChaiProfile.getProfileName():"no matched");
                if(Objects.nonNull(desiChaiProfile)) {
                    List<Integer> addonIds = orderItemComposition.getAddons().stream().
                            map(ingredientProductDetail -> ingredientProductDetail.getProduct().getProductId()).toList();
                    desiChaiProfile.getAddons().forEach(idName -> {
                        if(!addonIds.contains(idName.getId())) {
                            recipeDetail.getAddons().stream().
                                    filter(ingredientProductDetail -> ingredientProductDetail.getProduct().getProductId() == idName.getId()).
                                    forEach(ingredientProductDetail -> orderItemComposition.getAddons().add(ingredientProductDetail));
                        }
                    });
                }
            }
        }


        //Setting Ingredient Products
        item.getVariants().forEach(variant -> recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
            for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                if (ingredientProductDetail.getProduct().getName().equalsIgnoreCase(variant.getName().trim())) {
                    orderItemComposition.getProducts().add(ingredientProductDetail);
                }
            }
        }));
        //For Addon Mode for minimum step on menu
        if(!CollectionUtils.isEmpty(item.getAddons())){
            item.getAddons().forEach(addon -> recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                    if (ingredientProductDetail.getProduct().getName().equalsIgnoreCase(addon.getName().trim())) {
                        orderItemComposition.getProducts().add(ingredientProductDetail);
                    }
                }
            }));
        }
        recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
            boolean found = false;
            for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                for (Variant variant : item.getVariants()) {
                    if (ingredientProductDetail.getProduct().getName().equalsIgnoreCase(variant.getName().trim())) {
                        found = true;
                        break;
                    }
                }
            }
            if(!CollectionUtils.isEmpty(item.getAddons())){
                for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                    for (Addon addon : item.getAddons()) {
                        if (ingredientProductDetail.getProduct().getName().equalsIgnoreCase(addon.getName().trim())) {
                            found = true;
                            break;
                        }
                    }
                }
            }
            if (!found) {
                ingredientProduct.getDetails().forEach(ingredientProductDetail -> {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        orderItemComposition.getProducts().add(ingredientProductDetail);
                    }
                });
            }
        });

        //Setting Ingredient Variants
        item.getVariants().forEach(variant -> {
            recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                    if (ingredientVariantDetail.getAlias().equalsIgnoreCase(variant.getName().trim())) {
                        orderItemComposition.getVariants().add(ingredientVariantDetail);
                    }
                }
            });
        });

        //For Addon Mode for minimum step on menu
        if(!CollectionUtils.isEmpty(item.getAddons())){
            item.getAddons().forEach(addon -> {
                recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(addon.getName().trim())) {
                            orderItemComposition.getVariants().add(ingredientVariantDetail);
                        }
                    }
                });
            });
        }
        recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
            boolean found = false;
            for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                for (Variant variant : item.getVariants()) {
                    if (ingredientVariantDetail.getAlias().equalsIgnoreCase(variant.getName().trim())) {
                        found = true;
                        break;
                    }
                }
            }
            if(!CollectionUtils.isEmpty(item.getAddons())){
                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                    for (Addon addon : item.getAddons()) {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(addon.getName().trim())) {
                            found = true;
                            break;
                        }
                    }
                }
            }
            if (!found) {
                ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                    if (ingredientVariantDetail.isDefaultSetting()) {
                        orderItemComposition.getVariants().add(ingredientVariantDetail);
                    }
                });
            }
        });

        if(environmentProperties.getEditOrderCompositionBasisRemark() && product.getType()==5){
            editSugarRecipeIngredientVarientsBasisRemark(item,orderItemComposition,recipeDetail,instructions);
        }

        //COMBO
        if (recipeDetail.getIngredient().getCompositeProduct() != null) {
            Map<String,Integer> catalogueQuantityMap = new HashMap<>();
            Set<String> catalogues = new HashSet<>();
            for (Variant variant : item.getVariants()) {
                if (variant.getId().contains("_")) {
                    String[] arr = variant.getId().trim().split("_");
                    if (product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
                        catalogues.add(arr[2]);
                    } else {
                        if (!arr[0].equalsIgnoreCase(String.valueOf(product.getId()))) {
                            catalogues.add(arr[0]);
                            if(catalogueQuantityMap.containsKey(arr[0])){
                                catalogueQuantityMap.put(arr[0],catalogueQuantityMap.get(arr[0]) + 1);
                            }else{
                                catalogueQuantityMap.put(arr[0],1);
                            }
                        }
                    }
                }
            }
            if(!CollectionUtils.isEmpty(item.getAddons())){
                for (Addon addon : item.getAddons()) {
                    if (addon.getId().contains("_")) {
                        String[] arr = addon.getId().trim().split("_");
                        if (product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
                           // catalogues.add(arr[2]);
                        }else if(product.getSubType() == ChannelPartnerServiceConstants.FIXED_MEAL_COMBO_SUBCATEGORY_ID){
                            //skip
                        }else {
                            if (!arr[0].equalsIgnoreCase(String.valueOf(product.getId()))) {
                                catalogues.add(arr[0]);
                                if(addon.getId().contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)){
                                    if(catalogueQuantityMap.containsKey(arr[0])){
                                        catalogueQuantityMap.put(arr[0],catalogueQuantityMap.get(arr[0]) + 1);
                                    }else{
                                        catalogueQuantityMap.put(arr[0],1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (superCombo || fixedMealCombo) {
                for (Addon addOn : item.getAddons()) {
                    if (addOn.getId().contains("_SUPERCOMBO") || fixedMealCombo) {
                        String[] arr = addOn.getId().trim().split("_");
                        String itemId = fixedMealCombo ? arr[1] : arr[0];
                        catalogues.add(itemId);
                        if(catalogueQuantityMap.containsKey(itemId)){
                            catalogueQuantityMap.put(itemId,catalogueQuantityMap.get(itemId) + 1);
                        }else{
                            catalogueQuantityMap.put(itemId,1);
                        }
                    }
                }
            }
            BigDecimal comboTotalPrice = BigDecimal.ZERO;
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                .getDetails()) {
                boolean found = false;
                BigDecimal price = null;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    for (String itemId : catalogues) {
                        if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                            comboTotalPrice = comboTotalPrice.add(
                                AbstractConverters.getComboTotalPrice(products, ingredientProductDetail, orderItem.getQuantity()));
                            found = true;
                        }
                    }
                }
                if (!found) {
                    if (defaultMenuProduct == null) {
                        defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
                    }
                    comboTotalPrice = comboTotalPrice.add(AbstractConverters.getComboTotalPrice(products,
                        defaultMenuProduct, orderItem.getQuantity()));
                }
                /*heroComboDiscount = ChannelPartnerUtils.add(heroComboDiscount, ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(compositeIngredientData.getDiscount(),
                    BigDecimal.valueOf(100)), price));*/
            }

            BigDecimal comboDiscount = ChannelPartnerUtils.subtract(comboTotalPrice, ChannelPartnerUtils.multiply(orderItem.getPrice(), BigDecimal.valueOf(orderItem.getQuantity())));
            if (discountDetail.getDiscount().getValue().compareTo(BigDecimal.ZERO) > 0) {
                comboDiscount = comboDiscount.add(discountDetail.getDiscount().getValue());
            }
            /*if(product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID) {
                comboDiscount = heroComboDiscount;
            }*/

            BigDecimal comboDiscountPercentage = ChannelPartnerUtils.percentageWithScale10(comboDiscount, comboTotalPrice);
            //int maxQuantity = recipeDetail.getIngredient().getCompositeProduct().getMaxQuantity();
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                .getDetails()) {
                boolean found = false;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    for (String itemId : catalogues) {
                        if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                            // updating desi chai product Id
                            if (Integer.parseInt(itemId) == 10) {
                                for (Variant variant : item.getVariants()) {
                                    if (ChannelPartnerUtils.getDesiChaiMilkMap()
                                        .get(variant.getName().trim()) != null) {
                                        itemId = ChannelPartnerUtils.getDesiChaiMilkMap()
                                            .get(variant.getName().trim()).toString();
                                    }
                                }
                                if(!CollectionUtils.isEmpty(item.getAddons())){
                                    for (Addon addon : item.getAddons()) {
                                        if (ChannelPartnerUtils.getDesiChaiMilkMap()
                                                .get(addon.getName().trim()) != null) {
                                            itemId = ChannelPartnerUtils.getDesiChaiMilkMap()
                                                    .get(addon.getName().trim()).toString();
                                        }
                                    }
                                }
                            }
                            if (products.containsKey(Integer.parseInt(itemId))) {
                                if((superCombo || fixedMealCombo) && catalogueQuantityMap.containsKey(itemId)){
                                    if(catalogueQuantityMap.get(itemId) <=0 ){
                                        found = true;
                                        continue;
                                    }else{
                                        catalogueQuantityMap.put(itemId,catalogueQuantityMap.get(itemId) - 1);
                                    }
                                }
                                Product comboConstituent = products.get(Integer.parseInt(itemId));
                                String comboConstituentDimension = ingredientProductDetail.getDimension().getCode();
                                Item newItem = createItemOrder(ingredientProductDetail.getQuantity().intValue(), item,
                                    Integer.valueOf(ingredientProductDetail.getProduct().getProductId()).toString(),
                                        comboConstituent, masterDataCache, brandId,chaiProfiles,orderItemComposition,environmentProperties,
                                        comboConstituentDimension);
                                OrderItem comboConstituentOrderItem = convertOrderItemNew(comboConstituent,
                                    newItem, taxMap, products, discountPercent, comboConstituentDimension, comboDiscountPercentage,
                                    request, orderItem.getQuantity(), masterDataCache, brandId, null, unitId, pricingMap, true,
                                        parentTaxMap, paidAddonList, request.getInstructions(), environmentProperties, chaiProfiles, isRecommendedProduct,cafeProducts);
                                orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                                found = true;
                            }
                        }
                    }
                }
                if (!found) {
                    if (defaultMenuProduct == null) {
                        defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
                    }
                    if (products.containsKey(defaultMenuProduct.getProduct().getProductId())) {
                        Product comboConstituent = products.get(defaultMenuProduct.getProduct().getProductId());
                        String comboConstituentDimension = defaultMenuProduct.getDimension().getCode();
                        Item comboItem = new Item();
                        comboItem.setQuantity(defaultMenuProduct.getQuantity().intValue());
                        comboItem.setName(comboConstituent.getName());
                        EntityAliasKey key = new EntityAliasKey(comboConstituent.getId(), "PRODUCT", brandId);
                        EntityAliasMappingData data = masterDataCache.getEntityAliasMappingData().get(key);
                        if (data != null) {
                            comboItem.setName(data.getAlias());
                        }
                        comboItem.setPrice(BigDecimal.ZERO.floatValue()); // it will updated with recipe price automatically
                        comboItem.setGstLiability(item.getGstLiability());
                        comboItem.setVariants(new ArrayList<>());
                        comboItem.setAddons(new ArrayList<>());
                        if (defaultMenuProduct.getIngredient() != null) {
                            for (IngredientVariant ingredientVariant : defaultMenuProduct.getIngredient().getVariants()) {
                                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                                    if (ingredientVariantDetail.isDefaultSetting()) {
                                        Variant variant = new Variant();
                                        variant.setName(ingredientVariantDetail.getAlias());
                                        variant.setPrice(0);
                                        comboItem.getVariants().add(variant);
                                    }
                                }
                            }
                            for (IngredientProduct ingredientProduct : defaultMenuProduct.getIngredient().getProducts()) {
                                for (IngredientProductDetail ingredientProductDetail1 : ingredientProduct.getDetails()) {
                                    if (ingredientProductDetail1.isDefaultSetting()) {
                                        Variant variant = new Variant();
                                        variant.setName(ingredientProductDetail1.getProduct().getName());
                                        variant.setPrice(0);
                                        comboItem.getVariants().add(variant);
                                    }
                                }
                            }
                        }
                        if (defaultMenuProduct.getAddons() != null) {
                            for (IngredientProductDetail productDetail : defaultMenuProduct.getAddons()) {
                                if (productDetail.isDefaultSetting()) {
                                    Addon addon = new Addon();
                                    addon.setId(String.valueOf(productDetail.getProduct().getProductId()));
                                    addon.setName(productDetail.getProduct().getName());
                                    addon.setPrice(0);
                                    comboItem.getAddons().add(addon);
                                }
                            }
                        }
                        OrderItem comboConstituentOrderItem = convertOrderItemNew(comboConstituent, comboItem, taxMap, products, discountPercent,
                            comboConstituentDimension, comboDiscountPercentage, request, orderItem.getQuantity(), masterDataCache, brandId, null, unitId,
                            pricingMap, true, parentTaxMap, paidAddonList, request.getInstructions(), environmentProperties, chaiProfiles, isRecommendedProduct,
                                cafeProducts);
                        orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                    }
                }
            }
        }
        orderItem.setComposition(orderItemComposition);
        setOrderItemTaxesMappedFromSwiggy(product, orderItem, taxMap, discountDetail, parentQuantity, item,isRecommendedProduct,isRecommendedDish, isIncludesPaidAddon);
//        setOrderItemTaxesMappedfromSwiggy(orderItem, discountDetail,parentQuantity,item);
        return orderItem;
    }

    private static void setOrderItemTaxesMappedFromSwiggy(Product product, OrderItem orderItem, Map<String, TaxDataVO> taxMap,
                                                          DiscountDetail discountDetail, Integer parentQuantity, Item item,
                                                          boolean isRecommendedProduct, AtomicBoolean isRecommendedDish, AtomicBoolean isIncludesPaidAddon) {
        if (Objects.nonNull(item)) {
            BigDecimal totalItemTax= BigDecimal.ZERO;
            if( !product.getTaxCode().equals(AppConstants.COMBO_TAX_CODE) && (isRecommendedProduct || isRecommendedDish.get() || isIncludesPaidAddon.get())){
                AbstractConverters.setOrderItemTaxes(product, orderItem, taxMap, discountDetail,1);
            }else{
                totalItemTax= totalItemTax.add(setOrderItemTaxesMappedfromSwiggy(orderItem,discountDetail,parentQuantity,item));
                orderItem.setTax(totalItemTax);
            }
        }
    }

    private static BigDecimal getParentProductPriceWithPaidAddon(OrderItem orderItem, Item item, AtomicBoolean containsPaidAddon,
                                                                 BigDecimal recommendedDishCost , AtomicBoolean isRecommendedDish,
                                                                 Map<Integer, Product> products) {
        BigDecimal paidAddonTotalAmount =BigDecimal.ZERO ;
        BigDecimal paidVariantTotalAmount =BigDecimal.ZERO ;
        boolean variantPaidAddon = false;
        if (Objects.nonNull(item.getAddons()) && item.getAddons().size()>0){
            LOG.info("Added log for addon");
            for(Addon addon : item.getAddons()){
                List<String> arr = Arrays.stream(addon.getId().split("_")).map(String::toUpperCase).toList();
                if(arr.size()>0){
                    try{
                        if(arr.contains("PAIDADDON")){
                            LOG.info("Paid addon found ");
                            containsPaidAddon.set(true);
                            paidAddonTotalAmount=ChannelPartnerUtils.add(paidAddonTotalAmount,ChannelPartnerUtils.multiply(BigDecimal.valueOf(addon.getPrice())
                                   , BigDecimal.valueOf(item.getQuantity()))) ;
                        }
                    }catch(IndexOutOfBoundsException e){
                        //do nothing
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(item.getVariants())){
            for(Variant variant : item.getVariants()){
                if(Objects.nonNull(variant.getId())) {
                    List<String> variantList = Arrays.stream(variant.getId().split("_")).map(String::toUpperCase).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(variantList)) {
                        try {
                            if (variantList.contains("PAIDADDON")) {
                                LOG.info("Paid addon found in variant list");
                                containsPaidAddon.set(true);
                                float variantProductPrice = getVariantProductPrice(variant, products);
                                paidVariantTotalAmount = ChannelPartnerUtils.multiply(ChannelPartnerUtils.add(paidVariantTotalAmount, BigDecimal.valueOf(variantProductPrice))
                                        , BigDecimal.valueOf(item.getQuantity()));
                                variantPaidAddon = true;
                            }
                        } catch (IndexOutOfBoundsException e) {
                            //do nothing
                        }
                    }
                }
            }
        }
        if(variantPaidAddon && BigDecimal.ZERO.compareTo(paidVariantTotalAmount) < 0){
            orderItem.setPrice(ChannelPartnerUtils.subtract(orderItem.getPrice(),paidVariantTotalAmount));
        }
        return ChannelPartnerUtils.subtract(Boolean.TRUE.equals(isRecommendedDish.get()) && recommendedDishCost != null && BigDecimal.ZERO.compareTo(recommendedDishCost) < 0  ?
                recommendedDishCost :BigDecimal.valueOf(item.getSubtotal()), ChannelPartnerUtils.add(paidAddonTotalAmount,paidVariantTotalAmount));
    }

    private static float getVariantProductPrice(Variant variant,Map<Integer, Product> products){
        Integer id = findVariantId(variant.getId(), true);
        Product product = products.get(id);
        if(Objects.nonNull(variant.getPrice()) && variant.getPrice() > 0){
            return variant.getPrice();
        }
        else{
            if(!CollectionUtils.isEmpty(product.getPrices())){
                return product.getPrices().get(0).getPrice().floatValue();
            }
        }
        return Float.valueOf(0);
    }

    private static void createPaidAddonItem(Integer id, Map<Integer, Product> products, List<Item> paidAddonList,
                                            Item parentItem, Addon addon,Variant variant, boolean isFromAddon) {
        if(Objects.nonNull(products) && products.containsKey(id)){
            Product product = products.get(id);
            Item item = new Item();
            item.setName(product.getName());
            item.setId(Integer.toString(product.getId()));
            if(isFromAddon && addon.getPrice()>0){
                item.setPrice(addon.getPrice());
            } else if (!isFromAddon && Objects.nonNull(variant.getPrice()) && variant.getPrice() > 0) {
                item.setPrice(variant.getPrice());
            } else {
                if(Objects.nonNull(product.getPrices()) && !product.getPrices().isEmpty()){
                    item.setPrice(product.getPrices().get(0).getPrice().floatValue());
                }
            }
            item.setGstLiability(parentItem.getGstLiability());
            item.setItemRestaurantOfferDiscount(0);
            item.setCgstPercent(parentItem.getCgstPercent());
            item.setSgstPercent(parentItem.getSgstPercent());
            item.setIgstPercent(0);
            item.setPackingCharges(0);
            item.setQuantity(parentItem.getQuantity());
            item.setSubtotal(AppUtils.multiply(Float.valueOf(item.getQuantity()),item.getPrice()));
            item.setFinalSubTotal(AppUtils.subtract(item.getSubtotal(), item.getDiscount()));
            BigDecimal cgstPercentageBase = Objects.nonNull(item.getCgstPercent()) ? AppUtils.divideWithScale10(BigDecimal.valueOf(item.getCgstPercent()),
                    new BigDecimal(100)) : BigDecimal.ZERO;
            item.setCgst(AppUtils.multiply(cgstPercentageBase.floatValue(), item.getFinalSubTotal()));
            BigDecimal sgstPercentageBase = Objects.nonNull(item.getSgstPercent()) ? AppUtils.divideWithScale10(BigDecimal.valueOf(item.getSgstPercent()),
                    new BigDecimal(100)) : BigDecimal.ZERO;
            item.setSgst(AppUtils.multiply(sgstPercentageBase.floatValue(),item.getFinalSubTotal()));
            item.setVariants(new ArrayList<>());
            item.setAddons(new ArrayList<>());
            paidAddonList.add(item);
        }
    }

    private static void editSugarRecipeIngredientVarientsBasisRemark(Item item , OrderItemComposition orderItemComposition , RecipeDetail recipeDetail , String instructions ){
        Map<String, String> orderItemRemarkMap = ChannelPartnerUtils.getOrderItemRemarkMap();
        AtomicReference<String> key = new AtomicReference<>("");
        try{
            recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                boolean editApplicable = false;
//                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
//                    for (Variant variant : item.getVariants()) {
                        if (!orderItemRemarkMap.isEmpty() && instructions != null && instructions.length() > 0) {
                            for(Map.Entry<String,String> entry : orderItemRemarkMap.entrySet()) {
                                String instr = instructions.trim().toLowerCase();
                                String matcher = entry.getKey().trim().toLowerCase();
                                if(instr.contains(matcher)) {
                                    editApplicable = true;
                                    key.set(orderItemRemarkMap.get(matcher));
                                    break;
                                }
                            }
                        }
//                    }
//                }
                if (editApplicable) {
                    ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(key.get())) {
                            replaceExistingSugarVariant(ingredientVariantDetail,orderItemComposition,key);
                        }
                    });
                }
            });
        }catch(Exception e){
            LOG.error("Exception while editing order item composition for sugar", e);
        }

    }

    private static void replaceExistingSugarVariant(IngredientVariantDetail ingredientVariantDetail, OrderItemComposition orderItemComposition, AtomicReference<String> key) {
        if(Objects.nonNull(orderItemComposition) && Objects.nonNull(orderItemComposition.getVariants()) && !orderItemComposition.getVariants().isEmpty()){
            for (int i = 0 ; i<orderItemComposition.getVariants().size(); i++){
                if(orderItemComposition.getVariants().get(i).getAlias().equalsIgnoreCase(REGULAR_SUGAR)){
                    orderItemComposition.getVariants().set(i,ingredientVariantDetail);
                }
            }
        }
    }

    private static void setPaidAddonCharges(Addon addon, OrderItem orderItem) {
        Integer parentQty = orderItem.getQuantity();
        BigDecimal totalAmt = ChannelPartnerUtils.add(orderItem.getTotalAmount(), BigDecimal.valueOf(addon.getPrice()*parentQty));
        BigDecimal taxableAmt = ChannelPartnerUtils.add(orderItem.getAmount(), BigDecimal.valueOf(addon.getPrice()*parentQty));
        orderItem.setTotalAmount(totalAmt);
        orderItem.setAmount(taxableAmt);
    }

    private static Integer findAddonId(String addonId, boolean isPaidAddon) {
        int id =0;
        if (addonId.contains("_")) {
            String[] addonIds = addonId.split("_");
            try {
                id = !isPaidAddon?Integer.valueOf(addonIds[addonIds.length - 1]) : Integer.valueOf(addonIds[2]);
            } catch (Exception e) {
                if(isPaidAddon){
                    try{
                        if(addonId.contains("BOLT")){
                            id = Integer.valueOf(addonIds[4]);
                        }else{
                            id = Integer.valueOf(addonIds[3]);
                        }
                    }catch (Exception e1){}
                }
            }
        } else {
            try {
                id = Integer.parseInt(addonId);
            } catch (Exception e) {
            }
        }
        return id;
    }

    private static Integer findVariantId(String variantId, boolean isPaidAddon) {
        int id =0;
        if (variantId.contains("_")) {
            String[] variantIds = variantId.split("_");
            try {
                id = !isPaidAddon?Integer.valueOf(variantIds[variantIds.length - 1]) : Integer.valueOf(variantIds[2]);
            } catch (Exception e) {
            }
        } else {
            try {
                id = Integer.parseInt(variantId);
            } catch (Exception e) {
            }
        }
        return id;
    }

    private static BigDecimal setOrderItemTaxesMappedfromSwiggy(OrderItem orderItem, DiscountDetail discountDetail, Integer parentQuantity, Item item) {
        BigDecimal totalItemTax = BigDecimal.ZERO;
        Integer qty = orderItem.getQuantity() * parentQuantity;
        if (Objects.nonNull(item)) {
            if (false) {  //interstate is not supported
                if (item.getIgstPercent() > 0 && item.getIgst() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("IGST", "GST", BigDecimal.valueOf(item.getIgst()), BigDecimal.valueOf(item.getIgstPercent()).setScale(2),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
            } else {
                if (item.getCgstPercent() > 0 && item.getCgst() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST", BigDecimal.valueOf(item.getCgst()), BigDecimal.valueOf(item.getCgstPercent()).setScale(2),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
                if (item.getSgstPercent() > 0 && item.getSgst() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST", BigDecimal.valueOf(item.getSgst()), BigDecimal.valueOf(item.getSgstPercent()).setScale(2),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
            }
        }
        return totalItemTax;
    }

    private static BigDecimal setUnitPriceForCombo(Map<Integer, Product> products, RecipeDetail recipeDetail, Item item, Product product) {
        BigDecimal basePrice = BigDecimal.ZERO;
        Map<String,Integer> catalogueQuantityMap = new HashMap<>();
        Set<String> catalogues = new HashSet<>();
        if (product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
            for (Variant variant : item.getVariants()) {
                if (variant.getId().contains("_")) {
                    String[] arr = variant.getId().trim().split("_");
                    // rd is identifier for super combo which is now disabled in menu
                    if (variant.getId().startsWith("rd")) {
                        catalogues.add(arr[2]);
                    }
                }
            }
        }
        if (product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID) {
            for (Addon addOn : item.getAddons()) {
                if (addOn.getId().endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                    String[] arr = addOn.getId().trim().split("_");
                    catalogues.add(arr[0]);
                }
            }
        }
        if (product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
            for (Addon addOn : item.getAddons()) {
                if (addOn.getId().contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)) {
                    String[] arr = addOn.getId().trim().split("_");
                    catalogues.add(arr[0]);
                    if(catalogueQuantityMap.containsKey(arr[0])){
                        catalogueQuantityMap.put(arr[0],catalogueQuantityMap.get(arr[0]) + 1);
                    }else{
                        catalogueQuantityMap.put(arr[0],1);
                    }
                }
            }
        }
        for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
            .getDetails()) {
            boolean productMatched = false;
            IngredientProductDetail ingredient = null;
            for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                if (ingredientProductDetail.isDefaultSetting()) {
                    ingredient = ingredientProductDetail;
                }
                for (String itemId : catalogues) {
                    if(catalogueQuantityMap.get(itemId)<=0){
                        continue;
                    }
                    if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                        ingredient = ingredientProductDetail;
                        productMatched = true;
                        catalogueQuantityMap.put(itemId,catalogueQuantityMap.get(itemId)-1);
                        break;
                    }
                }
                if(productMatched){
                    break;
                }
            }
            if (!productMatched) {
                if (ingredient == null) {
                    ingredient = compositeIngredientData.getMenuProducts().get(0);
                }
            }
            BigDecimal totalPrice = getSuperComboTotalPrice(products, ingredient,item);

            //removing discounted value hero combo
            if (product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
                    Objects.nonNull(compositeIngredientData.getDiscount()) && compositeIngredientData.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                totalPrice = ChannelPartnerUtils.subtract(totalPrice, ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(compositeIngredientData.getDiscount(),
                    BigDecimal.valueOf(100)), totalPrice));
            }
            basePrice = basePrice.add(totalPrice);
        }
        return AppUtils.add(basePrice, BigDecimal.valueOf(item.getPrice()));
    }

    private static BigDecimal setUnitPriceForHeroCombo(Map<Integer, Product> products, RecipeDetail recipeDetail, Item item, Product product) {
        BigDecimal basePrice = BigDecimal.ZERO;
        Set<String> catalogues = new HashSet<>();
        Map<String,Integer> catalogueQuantityMap = new HashMap<>();
     /*   if (product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID) {
            for (Variant variant : item.getVariants()) {
                if (variant.getId().endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                    String[] arr = variant.getId().trim().split("_");
                    catalogues.add(arr[0]);
                    if(!catalogueQuantityMap.containsKey(arr[0])){
                        catalogueQuantityMap.put(arr[0],1);
                    }else{
                        catalogueQuantityMap.put(arr[0],catalogueQuantityMap.get(arr[0]) + 1);
                    }
                }
            }
        }*/
        if (product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID) {
            for (Addon addOn : item.getAddons()) {
                if (addOn.getId().endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                    String[] arr = addOn.getId().trim().split("_");
                    catalogues.add(arr[0]);
                    if(!catalogueQuantityMap.containsKey(arr[0])){
                        catalogueQuantityMap.put(arr[0],1);
                    }else{
                        catalogueQuantityMap.put(arr[0],catalogueQuantityMap.get(arr[0]) + 1);
                    }
                }
            }
        }
        for (String itemId : catalogues) {
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                    .getDetails()) {
                if(catalogueQuantityMap.get(itemId)<=0){
                    continue;
                }
                boolean productMatched = false;
                IngredientProductDetail ingredient = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        ingredient = ingredientProductDetail;
                    }
                    if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                            ingredient = ingredientProductDetail;
                            productMatched = true;
                            catalogueQuantityMap.put(itemId,catalogueQuantityMap.get(itemId)-1);
                    }

                }
                if (!productMatched) {
                    continue;
                }
                BigDecimal totalPrice = getSuperComboTotalPrice(products, ingredient,item);

                //removing discounted value hero combo
                if (product.getSubType() == ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
                        Objects.nonNull(compositeIngredientData.getDiscount()) && compositeIngredientData.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                    totalPrice = ChannelPartnerUtils.subtract(totalPrice, ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(compositeIngredientData.getDiscount(),
                            BigDecimal.valueOf(100)), totalPrice));
                }
                basePrice = basePrice.add(totalPrice);
            }
        }
        return AppUtils.add(basePrice, BigDecimal.valueOf(item.getPrice()));
    }

    /*private static BigDecimal getPackagingCost(BigDecimal totalPrice, MasterDataCache masterDataCache, int unitId) {
        BigDecimal packagingCharge = BigDecimal.ZERO;
        if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
            packagingCharge = masterDataCache.getUnit(unitId).getPackagingValue().divide(new BigDecimal(100)).multiply(totalPrice);
        } else if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("FIXED")) {
            packagingCharge = masterDataCache.getUnit(unitId).getPackagingValue();
        }
        return packagingCharge;
    }*/



    private static BigDecimal getRecommendedProductExtraction(Item item, List<Item> recommendedOrderItems, int unitId, MasterDataCache masterDataCache, Map<Integer, Product> products, Map<String, Float> parentTaxMap, AtomicBoolean isRecommendedDish, DiscountDetail recomDiscountDetail) {
        BigDecimal recommendedPrice = BigDecimal.ZERO;
        List<Addon> addOns = item.getAddons();
        for (Addon addOn : addOns) {
            String[] addonIds = addOn.getId().split("_");
            String recommendationConstant = ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER.substring(1);
            Boolean isRecommendedAddon = Arrays.asList(addonIds).contains(recommendationConstant);
            if(Boolean.TRUE.equals(isRecommendedAddon)) {
                isRecommendedDish.set(true);
                Item recommendedItem = createRecommendedItem(addOn, unitId, masterDataCache, products,item);
                calculateRecomDiscountPercent(item, recomDiscountDetail, recommendedItem);
                recommendedItem.setItemRestaurantOfferDiscount(recomDiscountDetail.getTotalDiscount().floatValue());
                recommendedOrderItems.add(recommendedItem);
                recommendedPrice = ChannelPartnerUtils.add(recommendedPrice, ChannelPartnerUtils.multiply(BigDecimal.valueOf(recommendedItem.getPrice()), BigDecimal.valueOf(item.getQuantity())));

            }
        }
        /*if(Objects.nonNull(recommendedOrderItems) && !recommendedOrderItems.isEmpty()){
            parentTaxMap.put(TaxType.CGST.getKey(),item.getCgstPercent());
            parentTaxMap.put(TaxType.SGST.getKey(), item.getSgstPercent());
            parentTaxMap.put(TaxType.IGST.getKey(), item.getIgstPercent());
        }*/
        if (recommendedPrice.compareTo(BigDecimal.ZERO) > 0) {
            recommendedPrice = ChannelPartnerUtils.subtract(BigDecimal.valueOf(item.getSubtotal()), recommendedPrice).setScale(2, RoundingMode.HALF_UP);
        } else {
            recommendedPrice = ChannelPartnerUtils.multiply(BigDecimal.valueOf(item.getPrice()), BigDecimal.valueOf(item.getQuantity())).setScale(2, RoundingMode.HALF_UP);
        }
        //Setting parentTax , cgst and igst to recommended item
//        bifurcateParentItemTaxToRecommendedItems(recommendedOrderItems,parentTaxMap);
        return recommendedPrice;
    }

    private static void calculateRecomDiscountPercent(Item parentItem, DiscountDetail discountDetail, Item recommendedItem) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(AppUtils.percentageWithScale10(BigDecimal.valueOf(parentItem.getItemRestaurantOfferDiscount()), BigDecimal.valueOf(parentItem.getSubtotal())));
        percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(percentageDetail.getPercentage(), BigDecimal.valueOf(recommendedItem.getSubtotal())));
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), discountDetail.getDiscount().getValue()));
        LOG.info("Recom Discount Percentage Detail :::::::{}", new Gson().toJson(discountDetail));
    }
    private static void bifurcateParentItemTaxToRecommendedItems(List<Item> recommendedOrderItems, Map<String,Float> parentTaxMap){
        if(Objects.nonNull(recommendedOrderItems) && !recommendedOrderItems.isEmpty()){
            for( Item item :recommendedOrderItems){
                item.setSgstPercent(parentTaxMap.get(TaxType.SGST.getKey()));
                item.setSgst(ChannelPartnerUtils.percentageOfWithScale10(BigDecimal.valueOf(item.getSgstPercent()),ChannelPartnerUtils.multiply(BigDecimal.valueOf(item.getQuantity()),BigDecimal.valueOf(item.getPrice()))).floatValue());
                item.setCgstPercent(parentTaxMap.get(TaxType.CGST.getKey()));
                item.setCgst(ChannelPartnerUtils.percentageOfWithScale10(BigDecimal.valueOf(item.getCgstPercent()),ChannelPartnerUtils.multiply(BigDecimal.valueOf(item.getQuantity()),BigDecimal.valueOf(item.getPrice()))).floatValue());
                item.setIgstPercent(parentTaxMap.get(TaxType.IGST.getKey()));
                item.setIgst(ChannelPartnerUtils.percentageOfWithScale10(BigDecimal.valueOf(item.getIgstPercent()),ChannelPartnerUtils.multiply(BigDecimal.valueOf(item.getQuantity()),BigDecimal.valueOf(item.getPrice()))).floatValue());
            }
        }
    }

    private static Item createRecommendedItem(Addon addOn, int unitId, MasterDataCache masterDataCache, Map<Integer, Product> products,Item item) {
        Item upsellingItem = new Item();
        String recommendedProductId = addOn.getId().split("_")[0];
//        BigDecimal packagingPrice = getPackagingCharge(addOn.getPrice(), products, recommendedProductId, masterDataCache, unitId);
        upsellingItem.setId(recommendedProductId);
        upsellingItem.setQuantity(item.getQuantity());
        upsellingItem.setName(addOn.getName());
        upsellingItem.setPrice(addOn.getPrice());
        upsellingItem.setVariants(new ArrayList<>());
        upsellingItem.setAddons(new ArrayList<>());
        upsellingItem.setGstLiability(addOn.getGstLiability());
        BigDecimal upsellTotalAmount =ChannelPartnerUtils.multiply(new BigDecimal(Float.toString(addOn.getPrice())), new BigDecimal(Float.toString(item.getQuantity())));
        upsellingItem.setSubtotal(upsellTotalAmount.floatValue());
        return upsellingItem;
    }

    private static BigDecimal getPackagingCharge(float recommendedPrice, Map<Integer, Product> products, String itemId,
                                                 MasterDataCache masterDataCache, int unitId) {
        BigDecimal recommendeProductPrice = BigDecimal.ZERO;
        BigDecimal packagingPrice = BigDecimal.ZERO;
        BigDecimal price = new BigDecimal(recommendedPrice);
        ProductPrice productPrice = null;
        if (products.containsKey(Integer.valueOf(itemId))) {
            Product product = products.get(Integer.valueOf(itemId));
            TreeMap<BigDecimal, ProductPrice> priceDifference = new TreeMap<>();
            for (ProductPrice pPrice : product.getPrices()) {
                priceDifference.put(ChannelPartnerUtils.subtract(price, pPrice.getPrice()).abs(), pPrice);
            }
            productPrice = priceDifference.get(priceDifference.firstKey());
            recommendeProductPrice = productPrice.getPrice().setScale(2, RoundingMode.HALF_UP);
        }
        if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
            packagingPrice = masterDataCache.getUnit(unitId).getPackagingValue().divide(new BigDecimal(100))
                .multiply(recommendeProductPrice);
        } else if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("FIXED")) {
            packagingPrice = masterDataCache.getUnit(unitId).getPackagingValue();
        }
        return packagingPrice;
    }

    private static Item createItemOrder(int quantity, Item item, String catalogueId, Product comboConstituent,
                                        MasterDataCache masterDataCache, Integer brandId,Map<String, DesiChaiCustomProfiles> chaiProfiles,
                                        OrderItemComposition orderItemComposition,EnvironmentProperties environmentProperties,
                                        String comboConstituentDimension) {
        Item comboItem = new Item();
        comboItem.setQuantity(quantity);
        comboItem.setName(comboConstituent.getName());
        EntityAliasKey key = new EntityAliasKey(comboConstituent.getId(), "PRODUCT", brandId);
        EntityAliasMappingData data = masterDataCache.getEntityAliasMappingData().get(key);
        if (data != null) {
            comboItem.setName(data.getAlias());
        }
        comboItem.setPrice(BigDecimal.ZERO.floatValue()); // it will updated with recipe price automatically
        comboItem.setVariants(new ArrayList<>());
        comboItem.setAddons(new ArrayList<>());
        List<Addon> addons = new ArrayList<>();
        for (Addon addOn : item.getAddons()) {
            if (catalogueId.equalsIgnoreCase("10")) {
                if (!addOn.getId().endsWith(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER)) {
                    addons.add(addOn);
                }
            }else if(catalogueId.equalsIgnoreCase(addOn.getId().split("_")[0])){
                if (!addOn.getId().endsWith(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER)) {
                    addons.add(addOn);
                }
            }
        }
        if(comboConstituent.getId() == 10) {
            getAddonItemForDesiChaiProfile(item, chaiProfiles, addons, environmentProperties, comboConstituent,
                    comboConstituentDimension);
        }

        comboItem.setAddons(addons);
        List<Variant> variants = new ArrayList<>();
        for (Variant variant : item.getVariants()) {
            if (variant.getId().startsWith(catalogueId)) {
                variants.add(variant);
            }
        }
        comboItem.setVariants(variants);
        comboItem.setGstLiability(item.getGstLiability());
        return comboItem;
    }

    private static BigDecimal getComboTotalPrice(Map<Integer, Product> products,
                                                 IngredientProductDetail ingredientProductDetail) {
        BigDecimal comboTotalPrice = BigDecimal.ZERO;
        boolean dimensionMatched = false;
        if (products.containsKey(ingredientProductDetail.getProduct().getProductId())) {
            Product comboConstituent = products.get(ingredientProductDetail.getProduct().getProductId());
            BigDecimal quantity = ingredientProductDetail.getQuantity();
            for (ProductPrice comboConstituentPrice : comboConstituent.getPrices()) {
                if (comboConstituentPrice.getDimension()
                    .equalsIgnoreCase(ingredientProductDetail.getDimension().getCode())) {
                    dimensionMatched = true;
                    comboTotalPrice = comboTotalPrice.add(comboConstituentPrice.getPrice().multiply(quantity));
                    break;
                }
            }
            if (!dimensionMatched) {
                comboTotalPrice = comboTotalPrice
                    .add(comboConstituent.getPrices().get(0).getPrice().multiply(quantity));
            }
        }
        return comboTotalPrice;
    }

    private static BigDecimal getSuperComboTotalPrice(Map<Integer, Product> products,
                                                 IngredientProductDetail ingredientProductDetail,Item item) {
        BigDecimal comboTotalPrice = BigDecimal.ZERO;
        boolean dimensionMatched = false;
        if (products.containsKey(ingredientProductDetail.getProduct().getProductId())) {
            Product comboConstituent = products.get(ingredientProductDetail.getProduct().getProductId());
            BigDecimal quantity = ingredientProductDetail.getQuantity();
            for(Addon addon : item.getAddons()){
                String[] addonId = addon.getId().split("_");
                if(ingredientProductDetail.getProduct().getProductId() == Integer.valueOf(addonId[0]) && ingredientProductDetail.getDimension().getCode().equalsIgnoreCase(addonId[1])){
                    dimensionMatched = true;
                    comboTotalPrice = comboTotalPrice.add(AppUtils.multiply(BigDecimal.valueOf(addon.getPrice()),quantity));
                    break;
                }
            }
            if (!dimensionMatched) {
                comboTotalPrice = comboTotalPrice
                        .add(comboConstituent.getPrices().get(0).getPrice().multiply(quantity));
            }
        }
        return comboTotalPrice;
    }

    private static BigDecimal getHeroComboDiscount(Map<Integer, Product> products,
                                                   IngredientProductDetail ingredientProductDetail) {
        BigDecimal comboTotalPrice = getComboTotalPrice(products, ingredientProductDetail);
        return comboTotalPrice;
    }

    /*private static void addComboMenuItem(Map<Integer, Product> products, IngredientProductDetail ingredientProductDetail,
                                         OrderItemComposition orderItemComposition, Map<String, TaxDataVO> taxMap,
                                         BigDecimal discountPercent, BigDecimal comboDiscountPercentage, SwiggyOrderRequest request, Integer parentQuantity,
                                         MasterDataCache masterDataCache, Integer brandId) {
        if (products.containsKey(ingredientProductDetail.getProduct().getProductId())) {
            Product product1 = products.get(ingredientProductDetail.getProduct().getProductId());
            String comboConstituentDimension = ingredientProductDetail.getDimension().getCode();
            Item comboItem = new Item();
            comboItem.setQuantity(ingredientProductDetail.getQuantity().intValue());
            comboItem.setName(product1.getName());
            EntityAliasKey key = new EntityAliasKey(product1.getId(), "PRODUCT", brandId);
            EntityAliasMappingData data = masterDataCache.getEntityAliasMappingData().get(key);
            if (data != null) {
                comboItem.setName(data.getAlias());
            }
            comboItem.setPrice(BigDecimal.ZERO.floatValue()); // it will updated with recipe price automatically
            comboItem.setVariants(new ArrayList<>());
            comboItem.setAddons(new ArrayList<>());
            if (ingredientProductDetail.getIngredient() != null) {
                for (IngredientVariant ingredientVariant : ingredientProductDetail.getIngredient().getVariants()) {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.isDefaultSetting()) {
                            Variant variant = new Variant();
                            variant.setName(ingredientVariantDetail.getAlias());
                            variant.setPrice(0);
                            comboItem.getVariants().add(variant);
                        }
                    }
                }
                for (IngredientProduct ingredientProduct : ingredientProductDetail.getIngredient().getProducts()) {
                    for (IngredientProductDetail ingredientProductDetail1 : ingredientProduct.getDetails()) {
                        if (ingredientProductDetail1.isDefaultSetting()) {
                            Variant variant = new Variant();
                            variant.setName(ingredientProductDetail1.getProduct().getName());
                            variant.setPrice(0);
                            comboItem.getVariants().add(variant);
                        }
                    }
                }
            }
            if (ingredientProductDetail.getAddons() != null) {
                for (IngredientProductDetail productDetail : ingredientProductDetail.getAddons()) {
                    if (productDetail.isDefaultSetting()) {
                        Addon addon = new Addon();
                        addon.setName(productDetail.getProduct().getName());
                        addon.setPrice(0);
                        comboItem.getAddons().add(addon);
                    }
                }
            }
            OrderItem comboConstituentOrderItem = convertOrderItemNew(product1, comboItem, taxMap, products, discountPercent,
                comboConstituentDimension, comboDiscountPercentage, request, parentQuantity, masterDataCache, brandId, null);
            orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
        }
    }*/

    private static PercentageDetail getDiscountValues(SwiggyOrderRequest request) {
        PercentageDetail percentageDetail = new PercentageDetail();
        if (request.getRestaurantDiscount() > 0) {
            BigDecimal subTotal = BigDecimal.ZERO;
            for (Item item : request.getItems()) {
                subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(item.getSubtotal())
                ).setScale(10, RoundingMode.HALF_UP);
            }
            if (request.getOrderPackingCharges() > 0) {
                subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(request.getOrderPackingCharges())
                ).setScale(10, RoundingMode.HALF_UP);
            }
            BigDecimal percentage = BigDecimal.valueOf(request.getRestaurantDiscount()).divide(subTotal, 10, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            percentageDetail.setPercentage(percentage);
            percentageDetail.setValue(BigDecimal.valueOf(request.getRestaurantDiscount()));
        } else {
            percentageDetail.setPercentage(BigDecimal.ZERO);
            percentageDetail.setValue(BigDecimal.ZERO);
        }
        return percentageDetail;
    }

    private static void checkFreebie(SwiggyOrderRequest request, DiscountDetail discountDetail) {
        boolean hasFreebie = false;
        BigDecimal freebieDiscount = BigDecimal.ZERO;
        for (Item item : request.getItems()) {
            if (!hasFreebie && item.getRewardType() != null && item.getRewardType().equalsIgnoreCase("Freebie")) {
                hasFreebie = true;
                freebieDiscount = freebieDiscount.add(BigDecimal.valueOf(item.getDiscount()));
            }
        }
        if (hasFreebie) {
            if (ChannelPartnerUtils.isEqual(freebieDiscount, discountDetail.getDiscount().getValue())) {
                discountDetail.setDiscountReason("FREEBIE");
            }
            if (freebieDiscount.compareTo(discountDetail.getDiscount().getValue()) == -1) {
                discountDetail.setDiscountReason("SWIGGY+FREEBIE");
            }
        }
    }

    private static void checkForPaidAddonInVariants(Item item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail,
                                                    List<Item> paidAddonList, Map<Integer, Product> products ,ProductPrice productPrice ,Integer productId,
                                                    AtomicBoolean isIncludesPaidAddon) {
        if (!CollectionUtils.isEmpty(item.getVariants())) {
            for (Variant variant : item.getVariants()) {
                Integer id = 0;
                String variantId = variant.getId();
                if (Objects.nonNull(variantId) && variantId.contains("PAIDADDON")) {
                    id = findVariantId(variantId, true);
                    //Converting Paid Addons To Item
                    if (Objects.nonNull(paidAddonList) && paidAddonList.size() >= 0) {
                        createPaidAddonItem(id, products, paidAddonList, item, null, variant, false);
                    }
                    List<ProductPrice> matchedPriceObj = products.get(productId).getPrices().stream().filter(price ->
                            price.getDimension().equals(productPrice.getDimension())).toList();
                    if(!matchedPriceObj.isEmpty()){
                        RecipeDetail originalProductRecipe = matchedPriceObj.get(0).getRecipe();
                        if (Objects.nonNull(originalProductRecipe.getOptions())) {
                            for (OptionData optionData : originalProductRecipe.getOptions()) {
                                if (id > 0 && (Objects.equals(optionData.getId(), id) ||
                                        optionData.getName().equalsIgnoreCase(variant.getName().trim()))) {
                                    orderItemComposition.getOptions().add(optionData.getName().trim());
                                } else {
                                    LOG.info("Paid Addon with id ::: {} , not found.", id);
                                }
                            }
                        }
                    }
                    isIncludesPaidAddon.set(true);
                }
            }
        }
    }

    private static void getAddonItemForDesiChaiProfile(Item item, Map<String, DesiChaiCustomProfiles> chaiProfiles,
                                                       List<Addon> addons, EnvironmentProperties environmentProperties,
                                                       Product product, String comboConstituentDimension) {
        DesiChaiCustomProfiles desiChaiProfileForSuperCombo = null;
        List<IngredientProductDetail> ingredientProductDetails = new ArrayList<>();
        if (!item.getAddons().isEmpty()) {
            List<Addon> addonList = item.getAddons();
            for (Addon addon : addonList) {
                if (Objects.nonNull(addon.getId())) {
                    if (addon.getId().contains(environmentProperties.getDesiChaiProfileNameForSuperCombo())) {
                        String[] addonIdArr = addon.getId().split("_");
                        if (chaiProfiles.containsKey(addonIdArr[addonIdArr.length - 1])) {
                            desiChaiProfileForSuperCombo = chaiProfiles.get(addonIdArr[addonIdArr.length - 1]);
                        }
                        LOG.info("desiChaiProfile::::::: {}", Objects.nonNull(desiChaiProfileForSuperCombo) ? desiChaiProfileForSuperCombo.getProfileName() : "no matched");
                        if (Objects.nonNull(desiChaiProfileForSuperCombo)) {
                            List<Integer> addonIds = addons.stream().map(addonData -> Integer.valueOf(addonData.getId().split("_")[0])).toList();
                            List<ProductPrice> priceData = product.getPrices().stream()
                                    .filter(productPrice -> productPrice.getDimension().replaceAll(" ", "").equals(comboConstituentDimension)).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(priceData)) {
                                desiChaiProfileForSuperCombo.getAddons().forEach(idName -> {
                                    if (!addonIds.contains(idName.getId())) {
                                        ingredientProductDetails.addAll(priceData.get(0).getRecipe().getAddons().stream().
                                                filter(ingredientProductDetail -> ingredientProductDetail.getProduct().getProductId() == idName.getId()).toList());
                                    }
                                });
                            }
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(ingredientProductDetails)) {
            for (IngredientProductDetail ingredientProductDetail : ingredientProductDetails) {
                Addon addon = new Addon();
                addon.setId(String.valueOf(ingredientProductDetail.getProduct().getProductId()));
                addon.setName(ingredientProductDetail.getProduct().getName());
                addon.setCgst(0);
                addon.setIgst(0);
                addon.setPrice(0);
                addon.setCgstPercent(0);
                addon.setGstLiability("SWIGGY");
                addon.setSgst(0);
                addon.setSgstPercent(0);
                addons.add(addon);
            }
            LOG.info("Addon List :::::: {}", new Gson().toJson(addons));
        }
    }
}
