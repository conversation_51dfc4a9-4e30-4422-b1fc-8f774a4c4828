package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name="partnerOrderComplaintResponse")
public class PartnerOrderComplaintResponse {

    @Id
    private Long orderId;

    private String referenceId;

    private String action;

    private String refundType;

    private float refundAmount;

    private Integer noRefundReasonId;

    private String noRefundReason;

    private PartnerOrderComplaintTempResponse partnerOrderComplaintTempResponse;


    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public float getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(float refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getNoRefundReasonId() {
        return noRefundReasonId;
    }

    public void setNoRefundReasonId(Integer noRefundReasonId) {
        this.noRefundReasonId = noRefundReasonId;
    }

    public String getNoRefundReason() {
        return noRefundReason;
    }

    public void setNoRefundReason(String noRefundReason) {
        this.noRefundReason = noRefundReason;
    }

    public PartnerOrderComplaintTempResponse getPartnerOrderComplaintTempResponse() {
        return partnerOrderComplaintTempResponse;
    }

    public void setPartnerOrderComplaintTempResponse(PartnerOrderComplaintTempResponse partnerOrderComplaintTempResponse) {
        this.partnerOrderComplaintTempResponse = partnerOrderComplaintTempResponse;
    }
}
