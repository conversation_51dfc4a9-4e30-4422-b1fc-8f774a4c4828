package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class DuplicateOrderCheckStep<R, T> extends OrderProcessingStep<R, T> {

    public DuplicateOrderCheckStep(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        Boolean isDuplicateOrder = checkForDuplicateOrder(request);
        if (Boolean.TRUE.equals(isDuplicateOrder)) {
            return super.partnerOrderManagementService.handlesDuplicateOrder(request, response, partnerPrimaryData);
        }
        log.info("\n----------- ,STEP 0, - ,Check Duplicate Order Step Executed in  ----------- , milliseconds {}",(System.currentTimeMillis() - startTime));
        return super.checkNext(request, isManual, response, partnerPrimaryData, partnerOrderDetail, super.nextOrderProcessingStep);
    }

    private <T> boolean checkForDuplicateOrder(T request) {
        PartnerPrimaryData partnerPrimaryData = super.partnerOrderManagementService.getPrimaryData(request);
        PartnerOrderCacheDetail partnerOrderCacheDetail = super.channelPartnerDataCache.getPartnerOrderCache().get(partnerPrimaryData.getOrderId());
        return partnerOrderCacheDetail != null && partnerOrderCacheDetail.getPartnerName().equalsIgnoreCase(partnerPrimaryData.getPartnerName());
    }
}
