package com.stpl.tech.kettle.channelpartner.core.util;

/**
 * Created by <PERSON><PERSON><PERSON> on 06-10-2016.
 */
public class MasterServiceClientEndpoints {

	public static final String MASTER_SERVICE_ENTRY_POINT = "master-service/rest/v1/";
	public static final String GET_ALL_UNITS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/unit/all";
	public static final String GET_ALL_LOCATIONS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/location/all";
	public static final String GET_ALL_UNIT_BASIC_DETAILS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd/all";
	public static final String GET_ALL_PRODUCT_BASIC_DETAILS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/productBasicDetails";
	public static final String GET_UNIT = MASTER_SERVICE_ENTRY_POINT + "redis-cache/unit";
	public static final String GET_UNIT_PRODUCTS = MASTER_SERVICE_ENTRY_POINT + "redis-cache/unit-products";
	public static final String GET_UNIT_BASIC_DETAIL = MASTER_SERVICE_ENTRY_POINT + "redis-cache/ubd";
	public static final String PRODUCT_RECIPES = MASTER_SERVICE_ENTRY_POINT + "redis-cache/product/recipes";
	public static final String WEB_CATEGORIES = MASTER_SERVICE_ENTRY_POINT + "redis-cache/categories/web";
	public static final String GET_MENUS = MASTER_SERVICE_ENTRY_POINT + "product-metadata/menus/get";
}
