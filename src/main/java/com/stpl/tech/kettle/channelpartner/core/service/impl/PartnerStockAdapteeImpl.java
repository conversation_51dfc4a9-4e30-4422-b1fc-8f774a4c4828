package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
public class PartnerStockAdapteeImpl implements PartnerAdaptee<StockReqMetadata, StockReqMetadata> {

    private WebServiceHelper webServiceHelper;

    private EnvironmentProperties environmentProperties;

    private MasterDataCache masterDataCache;

    private ChannelPartnerDataCache channelPartnerDataCache;

    private OrderValidationService orderValidationService;

    public PartnerStockAdapteeImpl(WebServiceHelper webServiceHelper, EnvironmentProperties environmentProperties, MasterDataCache masterDataCache, ChannelPartnerDataCache channelPartnerDataCache, OrderValidationService orderValidationService) {
        this.webServiceHelper = webServiceHelper;
        this.environmentProperties = environmentProperties;
        this.masterDataCache = masterDataCache;
        this.channelPartnerDataCache = channelPartnerDataCache;
        this.orderValidationService = orderValidationService;
    }

    @Override
    public StockReqMetadata setRequestCommonData(StockReqMetadata event) {
         filterBrandLevelProductsUsingPricingUnit(event);
        //        zomatoUnitProductStock.setCatalogueVendorEntityIds(event.getProductIds());
        updateDimensionLevelStock(event.getUnitProductsStockEvent(), event.getUnitPartnerBrandKey(), event.getUnitUpsellCombosMapping(), event);
        // ADDING NORMAL COMBO PRODUCTS - THIS EXCLUDE HERO/SUPER COMBOS
        addNormalComboItemsToStockEvent(event.getUnitProductsStockEvent(), event.getUnitPartnerBrandKey(), event);
        // ADDING UPSELLING AND HERO PRODUCTS
        addHeroAndUpsellingProductIds(event, event.getUnitProductsStockEvent(), event.getUnitUpsellCombosMapping());
        addAddOnProductIds(event, event.getUnitProductsStockEvent(), event.getUnitUpsellCombosMapping());
//        callUnitProductStockAPIV3(zomatoUnitProductStock, stockEvent, key);
        return event ;
    }

    private void filterBrandLevelProductsUsingPricingUnit(StockReqMetadata stockReqMetadata) {
        UnitProductsStockEvent event = stockReqMetadata.getUnitProductsStockEvent();
        UnitPartnerBrandKey key = stockReqMetadata.getUnitPartnerBrandKey();
        UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        List<String> matchingProductsIds = new ArrayList<>();
        List<IdName> matchingProductDimensions = new ArrayList<>();
        masterDataCache.getUnitProductDetails(brandMappingData.getPriceProfileUnitId()).forEach(product -> {
            if (product.isInventoryTracked()) {
                List<String> matchingIds = event.getProductIds().stream().filter(s -> Integer.parseInt(s) == product.getId()).
                        toList();
                if (!matchingIds.isEmpty()) {
                    matchingProductsIds.addAll(matchingIds);
                }
                if (event.getProductDimensions() != null) {
                    List<IdName> productDimensions = event.getProductDimensions().stream().filter(idName ->
                            idName.getId() == product.getId()).toList();
                    if (!productDimensions.isEmpty()) {
                        matchingProductDimensions.addAll(productDimensions);
                    }
                }
            }
        });
        event.setProductIds(matchingProductsIds);
        event.setProductDimensions(matchingProductDimensions);
        stockReqMetadata.setStockEventProductIds(event.getProductIds());
        stockReqMetadata.setCatalogueVendorEntityIds(event.getProductIds());
    }

    private void addHeroAndUpsellingProductIds(StockReqMetadata stockReqMetadata, UnitProductsStockEvent event,
                                               PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if (unitUpsellCombosMapping != null && !unitUpsellCombosMapping.getUpsellingProdIds().isEmpty()) {
            List<String> upsellingProductsIds = getHeroAndUpsellingProductsIds(event.getProductIds(),
                    unitUpsellCombosMapping.getUpsellingProdIds());
            if (!upsellingProductsIds.isEmpty()) {
                stockReqMetadata.getCatalogueVendorEntityIds().addAll(upsellingProductsIds);
                stockReqMetadata.getHeroAndUpsellingProductIds().addAll(upsellingProductsIds);
            }
        }
    }

    //GETTING LINKED UPSELLING AND HERO PRODUCTS
    private List<String> getHeroAndUpsellingProductsIds(List<String> productIds, List<String> upsellingProdIds) {
        List<String> heroAndUpsellingProductIds = new ArrayList<>();
        for (String product : productIds) {
            for (String upsellProds : upsellingProdIds) {
                //PRODUCT ID IS ALWAYS AT THE FRONT OF THE STRING FOR HERO AND UPSELLING
                if (product.equalsIgnoreCase(upsellProds.split("_")[0])) {
                    heroAndUpsellingProductIds.add(upsellProds);
                }
            }
        }
        return heroAndUpsellingProductIds;
    }

    private void addAddOnProductIds(StockReqMetadata stockReqMetadata, UnitProductsStockEvent stockEvent, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if (unitUpsellCombosMapping != null && unitUpsellCombosMapping.getAddOnProductIds() != null && !unitUpsellCombosMapping.getAddOnProductIds().isEmpty()) {
            List<String> addOnProductItemIds = getAddOnProductsIds(stockEvent.getProductIds(),
                    unitUpsellCombosMapping.getAddOnProductIds());
            if (!addOnProductItemIds.isEmpty()) {
                stockReqMetadata.getCatalogueVendorEntityIds().addAll(addOnProductItemIds);
                stockReqMetadata.getAddOnProductIds().addAll(addOnProductItemIds);
            }
        }
    }

    private List<String> getAddOnProductsIds(List<String> productIds, List<String> addOnProductIds) {
        List<String> addOnProductItemIds = new ArrayList<>();
        for (String product : productIds) {
            for (String addOn : addOnProductIds) {
                //PRODUCT ID IS ALWAYS AT THE FRONT OF THE STRING FOR HERO AND UPSELLING
                if (product.equalsIgnoreCase(addOn)) {
                    addOnProductItemIds.add(addOn);
                }
            }
        }
        return addOnProductItemIds;
    }


    // this function only adds normal combo ids and does not include super combo or hero combo ids
    private void addNormalComboItemsToStockEvent(UnitProductsStockEvent event, UnitPartnerBrandKey key, StockReqMetadata stockReqMetadata) {
        if (StockStatus.STOCK_OUT.equals(event.getStatus())) {
            Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                    getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
            Set<String> comboIds = new HashSet<>();
            for (String productId : event.getProductIds()) {
                // Finding combo mappings for normal products only
                if (map != null && map.containsKey(Integer.parseInt(productId)) && map.get(Integer.parseInt(productId)) != null) {
                    map.get(Integer.parseInt(productId)).stream().filter(comboId ->
                            !stockReqMetadata.getCatalogueVendorEntityIds().contains(comboId.toString())
                    ).forEach(comboId -> comboIds.add(comboId.toString()));

                    map.get(Integer.parseInt(productId)).stream().filter(comboId ->
                            !stockReqMetadata.getStockEventProductIds().contains(comboId.toString())
                    ).forEach(comboId -> comboIds.add(comboId.toString()));
                }
            }
            stockReqMetadata.getCatalogueVendorEntityIds().addAll(comboIds);
            stockReqMetadata.getStockEventProductIds().addAll(comboIds);
        } else {
            addNormalComboItemsToStockInEvent(event, key, stockReqMetadata);
        }
    }

    private void addNormalComboItemsToStockInEvent(UnitProductsStockEvent event, UnitPartnerBrandKey key, StockReqMetadata stockReqMetadata) {
        Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
                getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
        if (map != null) {
            Set<Integer> comboItems = new HashSet<>();
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                boolean setCombo = entry.getValue().stream().anyMatch(productId -> event.getProductIds().contains(productId.toString()));
                if (setCombo) {
                    comboItems.addAll(entry.getValue());
                }
            }
            Map productStock = orderValidationService.getUnitProductInventoryByProducts(key.getUnitId(), new ArrayList<>(comboItems));
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                Set<Integer> productIds = entry.getValue();
                boolean addCombo = productIds.stream().noneMatch(productId -> masterDataCache.getProduct(productId).isInventoryTracked() &&
                        (productStock.get(productId.toString()) == null || (Integer) productStock.get(productId.toString()) <= 0));

                if (addCombo && !stockReqMetadata.getStockEventProductIds().contains(entry.getKey().toString())) {
                    stockReqMetadata.getStockEventProductIds().add(entry.getKey().toString());
                }

                if (addCombo && !stockReqMetadata.getCatalogueVendorEntityIds().contains(entry.getKey().toString())) {
                    stockReqMetadata.getCatalogueVendorEntityIds().add(entry.getKey().toString());
                }
            }
        }
    }

    private void updateDimensionLevelStock(UnitProductsStockEvent event,
                                           UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping, StockReqMetadata stockReqMetadata) {
        if (key.getBrandId().equals(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID) && unitUpsellCombosMapping != null &&
                unitUpsellCombosMapping.getProductVariantsMap() != null && !unitUpsellCombosMapping.getProductVariantsMap().isEmpty()) {
            List<String> productIds = new ArrayList<>();
            List<String> variantIds = new ArrayList<>();
            Map<String, InventoryInfo> inventory = webServiceHelper.callInternalApi(
                    environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE_WEB,
                    environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Map.class, event.getUnitId(), null);
            if (inventory != null && !inventory.keySet().isEmpty()) {
                for (String productId : event.getProductIds()) {
                    InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                    log.info("Inventory Info For Product Id : {} :::: {} ", productId, new Gson().toJson(inventoryInfo));
                    if (inventoryInfo.getDim() == null || inventoryInfo.getDim().isEmpty()) {
                        productIds.add(productId);
                    } else {
                        addDimensionsWithMatchingStockState(inventoryInfo, event, productIds, variantIds, productId,
                                unitUpsellCombosMapping, stockReqMetadata);
                    }
                }
            }
            event.setProductIds(productIds);
            stockReqMetadata.setStockEventProductIds(productIds);
            stockReqMetadata.setCatalogueVendorEntityIds(productIds);
            stockReqMetadata.setVariantVendorEntityIds(variantIds);
//            zomatoUnitProductStock.setCatalogueVendorEntityIds(productIds);
//            zomatoUnitProductStock.setVariantVendorEntityIds(variantIds);
        }

    }

    private void addDimensionsWithMatchingStockState(InventoryInfo inventoryInfo, UnitProductsStockEvent event, List<String> productIds,
                                                     List<String> variantIds, String productId,
                                                     PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping, StockReqMetadata stockReqMetadata) {
        List<String> dimensions = new ArrayList<>();
        if (StockStatus.STOCK_IN.equals(event.getStatus())) {
            productIds.add(productId);
            if (everyDimensionInSameStockState(inventoryInfo)) {
                dimensions.addAll(inventoryInfo.getDim().keySet());
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() > 0)
                        .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        } else {
            if (everyDimensionInSameStockState(inventoryInfo)) {
                productIds.add(productId);
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() <= 0)
                        .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        }
        dimensions.forEach(dimension -> {
            if (unitUpsellCombosMapping.getProductVariantsMap().containsKey(productId)) {
                List<String> ids = unitUpsellCombosMapping.getProductVariantsMap().get(productId).
                        stream().filter(variantVendorEntityId ->
                                variantVendorEntityId.toLowerCase().contains(dimension.toLowerCase())
                        ).toList();
                variantIds.addAll(ids);
            }
        });
//        if (!stockReqMetadata.getProductDimensionLevelStockMap().isEmpty() && stockReqMetadata.getProductDimensionLevelStockMap().containsKey(productId)) {
//            stockReqMetadata.getProductDimensionLevelStockMap().get(productId).addAll(dimensions);
//        } else {
//            stockReqMetadata.getProductDimensionLevelStockMap().put(productId, dimensions);
//        }

        updateStockMap(stockReqMetadata.getProductDimensionLevelStockMap(), productId, dimensions);
        updateStockMap(stockReqMetadata.getProductDimensionAndVariantLevelStockMap(), productId, variantIds);
    }

    private void updateStockMap(Map<String, List<String>> stockMap, String productId, List<String> values) {
        if (!stockMap.isEmpty() && stockMap.containsKey(productId)) {
            stockMap.get(productId).addAll(values);
        } else {
            stockMap.put(productId, new ArrayList<>(values));
        }
    }

    private boolean everyDimensionInSameStockState(InventoryInfo inventoryInfo) {
        if (inventoryInfo.getDim().keySet().size() == 1) {
            return true;
        }
        Iterator<Map.Entry<String, Integer>> itr = inventoryInfo.getDim().entrySet().iterator();
        Boolean initialStatus = itr.next().getValue() > 0;
        while (itr.hasNext()) {
            boolean currentStatus = itr.next().getValue() > 0;
            if (currentStatus != Boolean.TRUE.equals(initialStatus)) {
                return false;
            }
        }
        return true;
    }


}
