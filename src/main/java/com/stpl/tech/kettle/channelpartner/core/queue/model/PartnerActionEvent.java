package com.stpl.tech.kettle.channelpartner.core.queue.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class PartnerActionEvent implements Serializable {

    private static final long serialVersionUID = -5911132165564572042L;
    private List<Integer> partnerIds;
    private Integer brandId;
    private PartnerActionEventType eventType;
    private Object eventData;
    private boolean isPartner = true;
    private Integer eventId;

    public List<Integer> getPartnerIds() {
        if(partnerIds == null){
            partnerIds = new ArrayList<>();
        }
        return partnerIds;
    }

    public void setPartnerIds(List<Integer> partnerIds) {
        this.partnerIds = partnerIds;
    }

    public PartnerActionEventType getEventType() {
        return eventType;
    }

    public void setEventType(PartnerActionEventType eventType) {
        this.eventType = eventType;
    }

    public Object getEventData() {
        return eventData;
    }

    public void setEventData(Object eventData) {
        this.eventData = eventData;
    }

    public boolean isPartner() {
        return isPartner;
    }

    public void setPartner(boolean partner) {
        isPartner = partner;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }


    @Override
    public String toString() {
        return "PartnerActionEvent{" +
                "partnerIds=" + partnerIds +
                ", eventType=" + eventType +
                ", eventData=" + eventData +
                ", isPartner=" + isPartner +
                ", eventId=" + eventId +
                ", brandId=" + brandId +
                '}';
    }
}
