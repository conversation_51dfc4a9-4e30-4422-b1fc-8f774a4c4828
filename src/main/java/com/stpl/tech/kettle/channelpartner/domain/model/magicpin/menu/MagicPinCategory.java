package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinCategory implements Serializable {
    private String id;
    private String title;
    private int rank;
    private List<Integer> timingIds;
    private List<MagicPinSubCategory> subCategories;
    private List<MagicPinItem> items;
}
