package com.stpl.tech.kettle.channelpartner.mysql.data.dao;

import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderComplaintTrack;
import lombok.Data;

import java.util.Date;
import java.util.List;


public interface PartnerOrderComplaintTrackDao extends AbstractMasterDao {
    public PartnerOrderComplaintTrack findByPartnerOrderId(String orderId);
    List<PartnerOrderComplaintTrack> findByStatusAndDateRange(String status, Date fromDate, Date toDate);
}
