package com.stpl.tech.kettle.channelpartner.core.service.menu.factory;

import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;

import java.util.Objects;

public class PartnerBuilderFactory {

    private static  PartnerMetadataBuilder partnerMetadataBuilder ;

    public static PartnerMetadataBuilder getInstance(PartnerMenuConverterDependency partnerMenuConverterDependency) {
        if(Objects.isNull(partnerMetadataBuilder)){
            partnerMetadataBuilder= new PartnerMetadataBuilder().defaultBuilder(partnerMenuConverterDependency);
        }
        return partnerMetadataBuilder;
    }

}
