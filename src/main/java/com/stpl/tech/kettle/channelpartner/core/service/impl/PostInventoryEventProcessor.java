package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PostEventProcessor;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class PostInventoryEventProcessor extends PostEventProcessor {
    private final PartnerAbstractFactory partnerAbstractFactory;
    private final PartnerActionEventDecorator partnerActionEventDecorator;
    protected final PartnerMetadataBuilder partnerMetadataBuilder;

    public PostInventoryEventProcessor(PartnerAbstractFactory partnerAbstractFactory, PartnerActionEventDecorator partnerActionEventDecorator ,PartnerMetadataBuilder partnerMetadataBuilder) {
        this.partnerAbstractFactory = partnerAbstractFactory;
        this.partnerActionEventDecorator = partnerActionEventDecorator;
        this.partnerMetadataBuilder = partnerMetadataBuilder;
    }

    @Override
    public <T, R> List processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        List<UnitProductsStockEvent> resultList = this.partnerActionEventDecorator.processRedisEvent(partnerName, partnerActionEvent, res, eventAbstractFactory);
        final EventAbstractFactory<?, ?> stockEventService = ServiceFactory.getPartnerEventFactoryService(EventType.Stock.name(), partnerMetadataBuilder);
        final PartnerActionEventDecorator postReqToPartnerService = ServiceFactory.getPostReqToPartnerService(EventType.Stock.name(), partnerAbstractFactory);
        final PartnerActionEventDecorator postEventProcessorService = ServiceFactory.getPostEventProcessorService(EventType.Stock.name(), partnerAbstractFactory, postReqToPartnerService, partnerMetadataBuilder);
        for (UnitProductsStockEvent event : resultList) {
            partnerActionEvent.setEventData(event);
            List responseList = postEventProcessorService.processRedisEvent(partnerName, partnerActionEvent, UnitProductsStockEvent.class, stockEventService);
            //return postEventProcessing(responseList, EventType.Stock);
        }
        return null;
    }
    @Override
    public <StockReqResponseData> List<PartnerEventResponse> postEventProcessing(List<StockReqResponseData> obj, EventType eventType) throws ChannelPartnerException {
        return null;
    }

}
