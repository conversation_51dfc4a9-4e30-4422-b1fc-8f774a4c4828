package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CAFE_RAISE_REQUEST_APPROVAL")
public class CafeRaiseRequestApproval implements Serializable {

    private static final long serialVersionUID = 2168424961633147967L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CAFE_RAISE_REQUEST_APPROVAL_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "CAFE_NAME")
    private String cafeName;

    @Column(name = "PARTNER_NAME")
    private String partnerName;

    @Column(name = "REASON")
    private String reason;

    @Column(name = "PRIORITY")
    private String priority;

    @Column(name = "START_TIME")
    private String startTime;

    @Column(name = "END_TIME")
    private String endTime;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUISNESS_DATE")
    private Date businessDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ACTION_TIMESTAMP")
    private Date actionTimestamp;

    @Column(name = "ACTlON_TAKEN")
    private String actionTaken;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "COMMENT_REASON")
    private String commentReason;

    @Column(name = "AUTO_SWITCH")
    private String autoSwitch;

    @Column(name = "REQUESTED_BY_EMP_ID")
    private Integer requestedByEmpId;

    @Column(name = "ACCEPTED_BY_EMP_ID")
    private Integer acceptedByEmpId;

    @Column(name = "CHANNEL_PARTNER_ID")
    private Integer channelPartnerId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REQUEST_TIMESTAMP")
    private Date requestTimestamp;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "APPROVAL_TIMESTAMP")
    private Date approvalTimestamp;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "COMPLETION_TIMESTAMP")
    private Date completionTimestamp;

    @Column(name = "REQUEST_TYPE")
    private String requestType;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "cafeRaiseRequestApproval" ,cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<CafeRaiseRequestApprovalProducts> cafeRaiseRequestApprovalProducts;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getCafeName() {
        return cafeName;
    }

    public void setCafeName(String cafeName) {
        this.cafeName = cafeName;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Date getActionTimestamp() {
        return actionTimestamp;
    }

    public void setActionTimestamp(Date actionTimestamp) {
        this.actionTimestamp = actionTimestamp;
    }

    public String getActionTaken() {
        return actionTaken;
    }

    public void setActionTaken(String actionTaken) {
        this.actionTaken = actionTaken;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getCommentReason() {
        return commentReason;
    }

    public void setCommentReason(String commentReason) {
        this.commentReason = commentReason;
    }

    public String getAutoSwitch() {
        return autoSwitch;
    }

    public void setAutoSwitch(String autoSwitch) {
        this.autoSwitch = autoSwitch;
    }

    public Integer getRequestedByEmpId() {
        return requestedByEmpId;
    }

    public void setRequestedByEmpId(Integer requestedByEmpId) {
        this.requestedByEmpId = requestedByEmpId;
    }

    public Integer getAcceptedByEmpId() {
        return acceptedByEmpId;
    }

    public void setAcceptedByEmpId(Integer acceptedByEmpId) {
        this.acceptedByEmpId = acceptedByEmpId;
    }

    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    public Date getRequestTimestamp() { return this.requestTimestamp; }

    public void setRequestTimestamp(Date requestTimestamp) { this.requestTimestamp = requestTimestamp; }

    public Date getApprovalTimestamp() { return this.approvalTimestamp; }

    public void setApprovalTimestamp(Date approvalTimestamp) { this.approvalTimestamp = approvalTimestamp; }

    public Date getCompletionTimestamp() { return this.completionTimestamp; }

    public void setCompletionTimestamp(Date completionTimestamp) { this.completionTimestamp = completionTimestamp; }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public List<CafeRaiseRequestApprovalProducts> getCafeRaiseRequestApprovalProducts() {
        return cafeRaiseRequestApprovalProducts;
    }

    public void setCafeRaiseRequestApprovalProducts(List<CafeRaiseRequestApprovalProducts> cafeRaiseRequestApprovalProducts) {
        this.cafeRaiseRequestApprovalProducts = cafeRaiseRequestApprovalProducts;
    }

}
