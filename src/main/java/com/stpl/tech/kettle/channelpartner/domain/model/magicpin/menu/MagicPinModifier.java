package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinModifier implements Serializable {
    private static final long serialVersionUID = -1663061203024278774L;

    private String id;
    private String title;
    private int minimum;
    private int maximum;
    private List<MagicPinOption> options;
    private boolean addon;
    private Integer rank;
}
