package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"name",
	"vendorEntityId",
	"order",
	"propertyValues",
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCatalogueProperties {
	
	@JsonProperty("vendorEntityId")
	private String vendorEntityId;
	@JsonProperty("name")
	private String name;
	@JsonProperty("order")
	private String order;
	@JsonProperty("propertyValues")
	private List<ZomatoCatPropPropertyVal> propertyValues = null;
	@JsonProperty("kind")
	private String kind;
	

	@JsonProperty("name")
	public String getName() {
		return name;
	}

	@JsonProperty("vendorEntityId")
	public String getVendorEntityId() {
		return vendorEntityId;
	}

	@JsonProperty("vendorEntityId")
	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}

	@JsonProperty("name")
	public void setName(String name) {
		this.name = name;
	}

	@JsonProperty("order")
	public String getOrder() {
		return order;
	}

	@JsonProperty("order")
	public void setOrder(String order) {
		this.order = order;
	}

	@JsonProperty("propertyValues")
	public List<ZomatoCatPropPropertyVal> getPropertyValues() {
		return propertyValues;
	}

	@JsonProperty("propertyValues")
	public void setPropertyValues(List<ZomatoCatPropPropertyVal> propertyValues) {
		this.propertyValues = propertyValues;
	}

	@JsonProperty("kind")
	public String getKind() {
		return kind;
	}

	@JsonProperty("kind")
	public void setKind(String kind) {
		this.kind = kind;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoCatalogueProperties that = (ZomatoCatalogueProperties) o;

		return new EqualsBuilder()
			.append(vendorEntityId, that.vendorEntityId)
			.append(name, that.name)
			.append(order, that.order)
			.append(propertyValues, that.propertyValues)
			.append(kind, that.kind)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(vendorEntityId)
			.append(name)
			.append(order)
			.append(propertyValues)
			.append(kind)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoCatalogueProperties{" +
			"vendorEntityId='" + vendorEntityId + '\'' +
			", name='" + name + '\'' +
			", order='" + order + '\'' +
			", propertyValues=" + propertyValues +
			", kind='" + kind + '\'' +
			'}';
	}
}
