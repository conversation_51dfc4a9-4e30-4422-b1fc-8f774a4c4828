
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuRequest;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "outlet_id",
    "charges",
    "menu",
        "restaurant_offers"
})
public class ZomatoMenuRequestOld extends MenuRequest {

    @JsonProperty("outlet_id")
    private String outletId;
	@JsonProperty("menu")
    private OldZomatoMenu menu;
    @JsonProperty("restaurant_offers")
    private List<ZomatoRestaurantOffer> restaurantOffers = null;

    @JsonProperty("outletId")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outletId")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("menu")
    public OldZomatoMenu getMenu() {
        return menu;
    }

    @JsonProperty("menu")
    public void setMenu(OldZomatoMenu menu) {
        this.menu = menu;
    }

    @JsonProperty("restaurant_offers")
    public List<ZomatoRestaurantOffer> getRestaurantOffers() {
        return restaurantOffers;
    }

    @JsonProperty("restaurant_offers")
    public void setRestaurantOffers(List<ZomatoRestaurantOffer> restaurantOffers) {
        this.restaurantOffers = restaurantOffers;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("outletId", outletId).append("menu", menu).append("restaurantOffers", restaurantOffers).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(menu).append(outletId).append(restaurantOffers).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoMenuRequestOld) == false) {
            return false;
        }
        ZomatoMenuRequestOld rhs = ((ZomatoMenuRequestOld) other);
        return new EqualsBuilder().append(menu, rhs.menu).append(outletId, rhs.outletId).append(restaurantOffers, rhs.restaurantOffers).isEquals();
    }

}
