package com.stpl.tech.kettle.channelpartner.controller;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

public abstract class ChannelPartnerAbstractResources {

    @Autowired
    private TokenService<JWTToken> jwtService;

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerAbstractResources.class);

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(ChannelPartnerException.class)
    @ResponseBody
    public ChannelPartnerError handleException(HttpServletRequest req, ChannelPartnerException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }

    public Integer getLoggedInUser(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
            if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
                JWTToken jwtToken = new JWTToken();
                jwtService.parseToken(jwtToken, authHeader);
                return Integer.valueOf(jwtToken.getUserId());
            }
        } catch (Exception e) {
        }
        return -1;
    }
}
