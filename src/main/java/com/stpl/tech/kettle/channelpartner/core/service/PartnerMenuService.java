package com.stpl.tech.kettle.channelpartner.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerUnitListVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuVersionData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoUnitProductStockV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuAuditHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;

public interface PartnerMenuService {
    List<PartnerMenuAuditHistory>   getMenuAuditHistory( Integer unitId,Integer partnerId,
                                                         Integer brandId, String menuType);
    PartnerMenuDetail getPartnerMenuDetail(UnitMenuAddVO event);

    public void addPartnerMenuDetail(UnitMenuAddVO event);

    PartnerUnitMenuDetail getActivePartnerUnitMenuDetail(Integer partnerId, Integer unitId, Integer brandId);

    PartnerUnitMenuDetail getActivePartnerUnitMenuDetail(Integer partnerId, Integer unitId, Boolean isNew, Integer brandId);

    void addPartnerUnitMenuDetail(UnitMenuAddVO event);

    List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnit(Integer partnerId, Integer unitId) throws ChannelPartnerException;

    List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnits(PartnerUnitListVO request) throws ChannelPartnerException;

    PartnerOfferDetail addPartnerOffer(PartnerUnitListVO request) throws ChannelPartnerException;

    public Boolean deactivatePartnerOffer(String partnerOfferId) throws ChannelPartnerException;

    public Boolean activatePartnerOffer(String partnerOfferId) throws ChannelPartnerException;

    List<PartnerOfferDetail> getActiveOffersForPartnerAndUnits(Integer unitId) throws ChannelPartnerException;

    PartnerOfferDetail addPartnerOfferForZomato(PartnerUnitListVO request) throws ChannelPartnerException;

    void addSingleServeMenuDetail(UnitMenuAddVO request);

    void addPartnerUnitMenuMappingForUpsellingProds(List<String> productIds, UnitMenuAddVO request, List<String> upsellingProductIds,
                                                    List<String> superComboproductIds, Map<String, List<String>> productVariantsMap, List<String> addOnItemIds,
                                                    Map<String, Set<String>> recipeVariantMap, Map<String, Set<String>> splitProductMap);

    List<String> getSingleServeProductItems(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitPartnerBrandKey key);

    void addPartnerUnitProductMappings(List<String> productsIds, UnitMenuAddVO request, List<String> upsellingProdIds, List<String> comboProdIds);

    String getCurrentUnitMenuVersion(UnitMenuAddVO request, Integer unitId);

    String addUnitMenuMapping(String currentVersion, UnitMenuAddVO request, Integer unitId) throws ChannelPartnerException;

    void markUnitMenuVersionData(Integer unitId, Integer kettlePartnerId, Integer brandId,
                                 String version, String status, String menuType);

    PartnerUnitMenuDetail getUnitVersionMenu(Integer unitId, Integer kettlePartnerId, Integer brandId, String version, String menuType);

    PartnerUnitMenuDetail getPartnerUnitVersionMenuDetail(Integer partnerId, Integer unitId, Integer brandId, String menuType, String version);

    List<UnitMenuVersionData> getUnitMenuVersionList(Integer unitId, Integer kettlePartnerId, Integer brandId,
                                                     String menuType);

    PartnerUnitMenuVersionMapping getPartnerUnitVersionData(Integer partnerId, Integer unitId, Integer brandId,
                                                            String menuType);

    List<PartnerMenuAuditHistory> getPartnerMenuHistoryData(Integer partnerId, Integer unitId, Integer brandId,
                                                            String menuType);

    List<PartnerMenuAuditHistory> getPartnerMenuHistoryByStatus(Integer partnerId, Integer unitId, Integer brandId,
                                                                String status);

    void fetchOrderReconciliationData();

    boolean checkVersionMenuAuditData(UnitMenuAddVO request, String partnerName);

    void updatePartnerMenuAuditData(Integer partnerId, Integer unitId, Integer brandId, int employeeId, String menuType,
                                    String version, String partnerName, Object response);

    PartnerUnitProductPricingDetail updatePartnerUnitProductPricing(Integer partnerId, Integer unitId, Integer brandId, int employeeId,
                                                                    Map<Integer, Map<String, BigDecimal>> pricing, String partnerName);

    Map<Integer, Map<Integer, CafeMenuAutoPush>> getMapValue (List<Integer> unitIdsForMenu);

    Map<String,List<String>> checkForStockInAndOutByRecipeMinAndMax(UnitProductsStockEvent event, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping,
                                                                    UnitPartnerBrandKey key,List<String> comboIds , Map<Integer,Boolean> knockAppStockOutMap);

    public void sendStockOutReport(List<Integer> unitIdsForMenu, MenuType menuType, String partnerName) throws IOException;
}
