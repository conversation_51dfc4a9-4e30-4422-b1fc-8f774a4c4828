package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"id", "name", "add_on", "third_party_id", "options"})
public class MagicpinOrderItemAddons {

    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("add_on")
    private boolean addon;
    @JsonProperty("third_party_id")
    private String thirdPartyId;
    @JsonProperty("options")
    private List<OrderItemAddonOptions> orderItemAddonsDetails;

    @JsonProperty("combo")
    private boolean combo;


}
