
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "categories",
    "catalogues",
    "modifierGroups"
    
})
public class ZomatoMenu {

    @JsonProperty("categories")
    private List<ZomatoCategory> categories = new ArrayList<>();
    @JsonProperty("catalogues")
    private List<ZomatoCatalogues> catalogues = new ArrayList<>();
	@JsonProperty("combos")
	private List<ZomatoCombo> combos = new ArrayList<>();
    @JsonProperty("modifierGroups")
    private List<ZomatoModifierGroups> modifierGroups = new ArrayList<>();
    

    @JsonProperty("categories")
	public List<ZomatoCategory> getCategories() {
		return categories;
	}

    @JsonProperty("categories")
	public void setCategories(List<ZomatoCategory> categories) {
		this.categories = categories;
	}

    @JsonProperty("catalogues")
	public List<ZomatoCatalogues> getCatalogues() {
		return catalogues;
	}

    @JsonProperty("catalogues")
	public void setCatalogues(List<ZomatoCatalogues> catalogues) {
		this.catalogues = catalogues;
	}

	@JsonProperty("combos")
	public List<ZomatoCombo> getCombos() {
		return combos;
	}

	@JsonProperty("combos")
	public void setCombos(List<ZomatoCombo> combos) {
		this.combos = combos;
	}

	@JsonProperty("modifierGroups")
	public List<ZomatoModifierGroups> getModifierGroups() {
		return modifierGroups;
	}

    @JsonProperty("modifierGroups")
	public void setModifierGroups(List<ZomatoModifierGroups> modifierGroups) {
		this.modifierGroups = modifierGroups;
	}

	@Override
	public String toString() {
		return "ZomatoMenu [categories=" + categories + ", catalogues=" + catalogues + ", modifierGroups="
				+ modifierGroups + "]";
	}

	@Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoMenu that = (ZomatoMenu) o;

        return new EqualsBuilder()
                .append(categories, that.categories)
                .append(catalogues, that.catalogues)
                .append(modifierGroups, that.modifierGroups)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(categories)
                .append(catalogues)
                .append(modifierGroups)
                .toHashCode();
    }

}
