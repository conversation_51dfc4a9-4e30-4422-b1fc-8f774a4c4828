package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class RecieveOrderCheckStep<R, T> extends OrderProcessingStep<R, T> {

    public RecieveOrderCheckStep(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        partnerOrderDetail = receiveOrder(request, isManual, response);
        if (Boolean.FALSE.equals(orderToProcess(partnerOrderDetail))) {
            return partnerOrderManagementService.handlesFailedOrder(request, response, partnerOrderDetail);
        }
        log.info("\n----------- ,STEP 1, - ,Check Recieve Order Step Executed in  ----------- , milliseconds {}",(System.currentTimeMillis() - startTime));
        return super.checkNext(request, isManual, response, partnerPrimaryData, partnerOrderDetail,super.nextOrderProcessingStep);
    }

    private <T, R> PartnerOrderDetail receiveOrder(T request, boolean isManual, R response) {
        PartnerPrimaryData partnerPrimaryData = partnerOrderManagementService.getPrimaryData(request);
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(partnerPrimaryData.getPartnerName());
        PartnerOrderDetail partnerOrderDetail = super.trackService.trackPartnerOrder(partnerPrimaryData.getOrderId(), partner.getPartnerId(),
                partner.getPartnerName(), partner.getPartnerCode(), request, null, isManual,
                "V3");
        if (partnerOrderDetail != null) {
            //Primary validations like unit and item
            partnerOrderDetail = partnerOrderManagementService.primaryChecks(request, partnerOrderDetail, response, partner.getKettlePartnerId(),
                    partnerPrimaryData);
        }
        partnerOrderDetail = enrichPartnerOrderDetail(partner, partnerOrderDetail, partnerPrimaryData.getOutletId());
        return partnerOrderDetail;
    }

    private PartnerOrderDetail enrichPartnerOrderDetail(PartnerDetail partner, PartnerOrderDetail partnerOrderDetail, String outletId) {
        if (partnerOrderDetail != null) {
            UnitPartnerBrandMappingData data = channelPartnerDataCache.getUnitPartnerBrandMappingData(outletId, partner.getKettlePartnerId());
            partnerOrderDetail.setRestaurantId(data.getRestaurantId());
            partnerOrderDetail.setUnitId(data.getUnitId());
            partnerOrderDetail.setBrandId(data.getBrandId());
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        }
        return partnerOrderDetail;
    }

    private boolean orderToProcess(PartnerOrderDetail partnerOrderDetail) {
        return partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeProcessed());
    }
}
