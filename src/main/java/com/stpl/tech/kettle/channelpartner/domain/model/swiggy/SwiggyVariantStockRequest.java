package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "restaurantId", "inStock", "externalVariantId", "externalItemId", "externalVariantGroupId" })
public class SwiggyVariantStockRequest {

	@JsonProperty("restaurantId")
	private String restaurantId;
	@JsonProperty("inStock")
	private boolean inStock;
	@JsonProperty("externalVariantId")
	private String externalVariantId;
	@JsonProperty("externalItemId")
	private String externalItemId;
	@JsonProperty("externalVariantGroupId")
	private String externalVariantGroupId;

	public String getRestaurantId() {
		return restaurantId;
	}

	public void setRestaurantId(String restaurantId) {
		this.restaurantId = restaurantId;
	}

	public boolean isInStock() {
		return inStock;
	}

	public void setInStock(boolean inStock) {
		this.inStock = inStock;
	}

	public String getExternalVariantId() {
		return externalVariantId;
	}

	public void setExternalVariantId(String externalVariantId) {
		this.externalVariantId = externalVariantId;
	}

	public String getExternalItemId() {
		return externalItemId;
	}

	public void setExternalItemId(String externalItemId) {
		this.externalItemId = externalItemId;
	}

	public String getExternalVariantGroupId() {
		return externalVariantGroupId;
	}

	public void setExternalVariantGroupId(String externalVariantGroupId) {
		this.externalVariantGroupId = externalVariantGroupId;
	}

	@Override
	public String toString() {
		return "SwiggyVariantStockRequest [restaurantId=" + restaurantId + ", inStock=" + inStock
				+ ", externalVariantId=" + externalVariantId + ", externalItemId=" + externalItemId
				+ ", externalVariantGroupId=" + externalVariantGroupId + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(restaurantId).append(inStock)
				.append(externalVariantId).append(externalItemId).append(externalVariantGroupId).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof SwiggyStockRequest) == false) {
			return false;
		}
		SwiggyVariantStockRequest rhs = ((SwiggyVariantStockRequest) other);
		return new EqualsBuilder().append(restaurantId, rhs.restaurantId)
				.append(inStock, rhs.inStock).append(externalVariantId, rhs.externalVariantId)
				.append(externalItemId, rhs.externalItemId).append(externalVariantGroupId, rhs.externalVariantGroupId)
				.isEquals();
	}

}
