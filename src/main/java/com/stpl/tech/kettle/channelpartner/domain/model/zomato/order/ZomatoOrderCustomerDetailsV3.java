package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "name",
        "phone_number",
        "address",
        "delivery_area",
        "delivery_area_latitude",
        "delivery_area_longitude",
        "city",
        "email",
        "address_instructions",
        "country",
        "customer_id"
})

public class ZomatoOrderCustomerDetailsV3 {

    @JsonProperty("name")
    private String name;
    @JsonProperty("phone_number")
    private String phoneNumber;
    @JsonProperty("address")
    private String address;
    @JsonProperty("delivery_area")
    private String deliveryArea;
    @JsonProperty("delivery_area_latitude")
    private Float deliveryAreaLatitude;
    @JsonProperty("delivery_area_longitude")
    private Float deliveryAreaLongitude;
    @JsonProperty("delivery_coordinates_type")
    private String deliveryCoordinatesType;
    @JsonProperty("city")
    private String city;
    @JsonProperty("email")
    private String email;
    @JsonProperty("address_instructions")
    private String addressInstructions;
    @JsonProperty("country")
    private String country;
    @JsonProperty("pincode")
    private String pincode;
    @JsonProperty("order_instructions")
    private String orderInstructions;
    @JsonProperty("address_type")
    private String addressType;
    @JsonProperty("customer_id")
    private String customerId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDeliveryArea() {
        return deliveryArea;
    }

    public void setDeliveryArea(String deliveryArea) {
        this.deliveryArea = deliveryArea;
    }

    public Float getDeliveryAreaLatitude() {
        return deliveryAreaLatitude;
    }

    public void setDeliveryAreaLatitude(Float deliveryAreaLatitude) {
        this.deliveryAreaLatitude = deliveryAreaLatitude;
    }

    public Float getDeliveryAreaLongitude() {
        return deliveryAreaLongitude;
    }

    public void setDeliveryAreaLongitude(Float deliveryAreaLongitude) {
        this.deliveryAreaLongitude = deliveryAreaLongitude;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddressInstructions() {
        return addressInstructions;
    }

    public void setAddressInstructions(String addressInstructions) {
        this.addressInstructions = addressInstructions;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("pincode")
    public String getPincode() {
        return pincode;
    }

    @JsonProperty("pincode")
    public void setPincode(String pincode) {
        this.pincode = pincode;
    }

    @JsonProperty("order_instructions")
    public String getOrderInstructions() {
        return orderInstructions;
    }

    @JsonProperty("order_instructions")
    public void setOrderInstructions(String orderInstructions) {
        this.orderInstructions = orderInstructions;
    }

    @JsonProperty("delivery_coordinates_type")
    public String getDeliveryCoordinatesType() {
        return deliveryCoordinatesType;
    }

    @JsonProperty("delivery_coordinates_type")
    public void setDeliveryCoordinatesType(String deliveryCoordinatesType) {
        this.deliveryCoordinatesType = deliveryCoordinatesType;
    }

    @JsonProperty("address_type")
    public String getAddressType() {
        return addressType;
    }

    @JsonProperty("address_type")
    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    @JsonProperty("customer_id")
    public String getCustomerId() {
        return customerId;
    }
    @JsonProperty("customer_id")
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @Override
    public String toString() {
        return "ZomatoOrderCustomerDetailsV3{" +
                "name='" + name + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", address='" + address + '\'' +
                ", deliveryArea='" + deliveryArea + '\'' +
                ", deliveryAreaLatitude=" + deliveryAreaLatitude +
                ", deliveryAreaLongitude=" + deliveryAreaLongitude +
                ", deliveryCoordinatesType='" + deliveryCoordinatesType + '\'' +
                ", city='" + city + '\'' +
                ", email='" + email + '\'' +
                ", addressInstructions='" + addressInstructions + '\'' +
                ", country='" + country + '\'' +
                ", customerId=" + customerId +
                '}';
    }


    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(name).append(phoneNumber).append(address).append(email).append(deliveryArea).append(deliveryAreaLatitude).append(deliveryAreaLongitude).append(city).append(addressInstructions).append(country).append(customerId).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderCustomerDetailsV3) == false) {
            return false;
        }
        ZomatoOrderCustomerDetailsV3 rhs = ((ZomatoOrderCustomerDetailsV3) other);
        return new EqualsBuilder().append(name, rhs.name).append(email, rhs.email).append(phoneNumber, rhs.phoneNumber).append(address, rhs.address).append(deliveryArea, rhs.deliveryArea).append(deliveryAreaLatitude, rhs.deliveryAreaLatitude).append(deliveryAreaLongitude, rhs.deliveryAreaLongitude).append(city, rhs.city).append(addressInstructions, rhs.addressInstructions).append(country, rhs.country).append(customerId,rhs.customerId).isEquals();
    }

}
