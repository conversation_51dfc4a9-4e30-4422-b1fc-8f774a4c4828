package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.scheduler.PartnerOrderCacheScheduler;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitProductPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeRaiseRequestApprovalDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApprovalProducts;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class CafeStatusChannelPartnerServiceImpl implements CafeStatusChannelPartnerService {

    @Autowired
    private CafeStatusChannelPartnerDao cafeStatusChannelPartnerDao;

    @Autowired
    private CafeRaiseRequestApprovalDao cafeRaiseRequestApprovalDao;

    @Autowired
    private PartnerMetadataManagementService partnerMetadataManagementService;

    private static final Logger LOG = LoggerFactory.getLogger(CafeStatusChannelPartnerServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate){
      return  cafeStatusChannelPartnerDao.getInActiveCafe(statusUpdate);
      //return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CafeRaiseRequestApproval> getUnitSwitchOffRequests(String actionTaken){
        return cafeRaiseRequestApprovalDao.getAllPendingCafeSwitchOffRequests(actionTaken);

    }

    @Override
    public Boolean checkIfActiveCafeClosureRequestExists(Integer unitId , Integer brandId , Integer partnerId){
        return cafeRaiseRequestApprovalDao.isActiveCafeClosureRequestExists(unitId,brandId,partnerId);
    }

    @Override
    public List<Integer> setKnockAppStockOutProducts(UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        List<Integer> knockAppStockOutProducts = getActiveProductClosureRequestProductIds(event.getUnitId()
                ,key.getBrandId(),key.getPartnerId());
        if(Boolean.TRUE.equals(event.getForceStockOut()) && StockStatus.STOCK_OUT.equals(event.getStatus()) &&
                CollectionUtils.isEmpty(knockAppStockOutProducts)){
            try {
                knockAppStockOutProducts.addAll(event.getProductIds().stream().map(Integer::parseInt).toList());
            }catch (Exception e){
                LOG.info("Error while fetching knock app stock out products");
            }
        }
        return knockAppStockOutProducts;
    }

    @Override
    public List<Integer> getActiveProductClosureRequestProductIds(Integer unitId, Integer brandId , Integer partnerId){
        CafeRaiseRequestApproval cafeRaiseRequestApproval = cafeRaiseRequestApprovalDao.getActiveProductClosureRequest(unitId,brandId,partnerId);
        if(Objects.nonNull(cafeRaiseRequestApproval)){
            return cafeRaiseRequestApproval.getCafeRaiseRequestApprovalProducts().stream()
                    .flatMap(p -> Arrays.stream(Arrays.stream(p.getProductIds().trim().split(",")).map(Integer::parseInt).toArray(Integer[]::new)))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatedRequestForUnit(CafeRaiseRequestApproval request){

        try {
            Boolean isUpdated = cafeRaiseRequestApprovalDao.updateActionTakenOnRequest(request.getId(), AppConstants.COMPLETED_REQUEST);
            if(Boolean.TRUE.equals(isUpdated)){
                CafeRaiseRequestApproval cafeRaiseRequestApproval = cafeRaiseRequestApprovalDao.getCafeRequestById(request.getId(),AppConstants.COMPLETED_REQUEST);
                if(Objects.nonNull(cafeRaiseRequestApproval) && "PRODUCT".equalsIgnoreCase(cafeRaiseRequestApproval.getRequestType())){
                    UnitProductPartnerStatusVO unitProductPartnerStatusVO = getUnitProductPartnerStatusVO(cafeRaiseRequestApproval,Boolean.TRUE);
                    partnerMetadataManagementService.setUnitProductStock(unitProductPartnerStatusVO);
                }
            }
            return isUpdated;
        } catch (Exception e){
            LOG.info("Error while updating  updatedRequestForUnit request",e);
        }
        return false;
    }

    private UnitProductPartnerStatusVO getUnitProductPartnerStatusVO(CafeRaiseRequestApproval cafeRaiseRequestApproval , Boolean status){
        UnitProductPartnerStatusVO unitProductPartnerStatusVO = new UnitProductPartnerStatusVO();
        unitProductPartnerStatusVO.setBrandId(cafeRaiseRequestApproval.getBrandId());
        unitProductPartnerStatusVO.setUnitId(cafeRaiseRequestApproval.getUnitId());
        unitProductPartnerStatusVO.setPartnerIds(new ArrayList<>(Collections.singletonList(cafeRaiseRequestApproval.getChannelPartnerId())));
        unitProductPartnerStatusVO.setProductIds(cafeRaiseRequestApproval.getCafeRaiseRequestApprovalProducts().stream()
                .flatMap(p -> Arrays.stream(Arrays.stream(p.getProductIds().split(",")).map(Integer::parseInt).toArray(Integer[]::new)))
                .collect(Collectors.toList()));
        unitProductPartnerStatusVO.setStatus(status);
        unitProductPartnerStatusVO.setForceStockOut(Boolean.TRUE);
        return unitProductPartnerStatusVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveCafeStatus(CafeStatusChannelPartner cafeStatusChannelPartner){
        cafeStatusChannelPartnerDao.add(cafeStatusChannelPartner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CafeStatusChannelPartner deleteActivatedCafe(Integer id){
        CafeStatusChannelPartner obj =cafeStatusChannelPartnerDao.find(CafeStatusChannelPartner.class,id);
        cafeStatusChannelPartnerDao.delete(obj);
        return  obj;
    }



}
