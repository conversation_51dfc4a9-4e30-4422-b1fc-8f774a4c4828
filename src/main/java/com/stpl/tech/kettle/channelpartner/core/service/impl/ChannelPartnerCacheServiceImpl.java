package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.stpl.tech.kettle.channelpartner.mongo.data.dao.DesiChaiCustomProfileDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.stpl.tech.kettle.channelpartner.core.service.ChannelPartnerCacheService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.CRMServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMetadataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitMenuDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitProductMappingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitProductPricingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadata;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadataKey;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductMappingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.domain.model.Customer;

@Service
public class ChannelPartnerCacheServiceImpl implements ChannelPartnerCacheService {

    @Autowired
    private PartnerDao partnerDao;

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private PartnerMetadataDao partnerMetadataDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private PartnerUnitProductMappingDao partnerUnitProductMappingDao;

    @Autowired
    private PartnerUnitProductPricingDao partnerUnitProductPricingDao;

    @Autowired
    private PartnerUnitMenuDao partnerUnitMenuDao;

    @Autowired
    private DesiChaiCustomProfileDao desiChaiCustomProfileDao;

    @Override
    public List<PartnerDetail> getPartners() {
        return partnerDao.findAll();
    }

    @Override
    public Customer getSwiggyCustomer() {
        StringBuilder endPoint = new StringBuilder(environmentProperties.getCRMServiceBasePath());
        endPoint.append(CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP);
        return webServiceHelper.postWithAuth(endPoint.toString(), environmentProperties.getChannelPartnerClientToken(),
            environmentProperties.getSwiggyCustomerContact(), Customer.class);
    }

    @Override
    public List<PartnerOrderDetail> getNotificationPendingOrders(Date date) {
        List<PartnerOrderDetail> partnerOrderDetails = new ArrayList<>();
        getPartners().forEach(partnerDetail -> partnerOrderDetails.addAll(partnerOrderDao.notificationPendingOrders(date,
            partnerDetail.getPartnerId())));
        return partnerOrderDetails;
    }

    @Override
    public List<PartnerOrderCacheDetail> getPastOrdersByTime(Date date) {
        List<PartnerOrderCacheDetail> partnerOrderCacheDetails = new ArrayList<>();
        for (PartnerOrderDetail partnerOrderDetail : partnerOrderDao.getPastOrdersByTime(date)) {
            PartnerOrderCacheDetail partnerOrderCacheDetail = new PartnerOrderCacheDetail();
            partnerOrderCacheDetail.setAddTime(partnerOrderDetail.getAddTime());
            partnerOrderCacheDetail.setPartnerName(partnerOrderDetail.getPartnerName());
            partnerOrderCacheDetail.setPartnerOrderId(partnerOrderDetail.getPartnerOrderId());
            partnerOrderCacheDetails.add(partnerOrderCacheDetail);
        }
        return partnerOrderCacheDetails;
    }

    /*@Override
    public List loadUnitChannelPartnerMappings() {
        StringBuilder endPoint = new StringBuilder(environmentProperties.getKettleServiceBasePath());
        endPoint.append(KettleServiceClientEndpoints.UNIT_CHANNEL_PARTNER_MAPPING);
        return webServiceHelper.callInternalApi(endPoint.toString(),environmentProperties.getChannelPartnerClientToken(),
                HttpMethod.GET, List.class, null, null);
    }*/

    /*@Override
    public List loadUnitPartnerMenuSequenceMappings() {
        StringBuilder endPoint = new StringBuilder(environmentProperties.getKettleServiceBasePath());
        endPoint.append(KettleServiceClientEndpoints.UNIT_PARTNER_MENU_MAPPING);
        return webServiceHelper.callInternalApi(endPoint.toString(),environmentProperties.getChannelPartnerClientToken(),
                HttpMethod.GET, List.class, null, null);
    }*/

    /*@Override
    public List loadMenuSequences() {
        StringBuilder endPoint = new StringBuilder(environmentProperties.getMasterServiceBasePath());
        endPoint.append(MasterServiceClientEndpoints.GET_MENUS);
        return webServiceHelper.callInternalApi(endPoint.toString(),environmentProperties.getChannelPartnerClientToken(),
                HttpMethod.GET, List.class, null, null);
    }*/

    @Override
    public List<String> loadPartnerUnitProductMap(Integer partnerId, String region) {
        List<PartnerUnitMenuDetail> menuList = findLast2ByPartnerIdAndRegion(partnerId, region);
        Set<String> productIds = new HashSet<>();
        menuList.forEach(partnerUnitMenuDetail -> {
            Object menuData = partnerUnitMenuDetail.getMenuData();
            if (menuData != null) {
                if (partnerUnitMenuDetail.getNew() == null || !partnerUnitMenuDetail.getNew()) {
                    List<String> products = ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), List.class);
                    if (products != null) {
                        productIds.addAll(products);
                    }
                }
            }
        });
        return new ArrayList<>(productIds);
    }

    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PartnerUnitMenuDetail> findLast2ByPartnerIdAndRegion(Integer partnerId, String region) {
        //PageRequest request = new PageRequest(0, 2);
        //List<PartnerUnitMenuDetail> content = partnerUnitMenuDao.findByPartnerIdAndRegionOrderByAddTimeDesc(partnerId, region, request).getContent();
		List<PartnerUnitMenuDetail> content = partnerUnitMenuDao
				.searchLastRegionData(partnerId, region, PageRequest.of(0, 2, Sort.by(Direction.DESC, "addTime")))
				.getContent();
        return content;
    }

    @Override
    public Map<Integer, ProductAlias> loadProductAliasMap(Integer partnerId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        Map<Integer, ProductAlias> aliasMap = new HashMap<>();
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productList = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_ALIAS_LIST);
            if (productList != null) {
                JsonArray array = new Gson().fromJson(productList, JsonArray.class);
                if (array != null) {
                    for (JsonElement item : array) {
                        ProductAlias alias = new Gson().fromJson(item, ProductAlias.class);
                        aliasMap.put(alias.getProductId(), alias);
                    }
                }
            }
        }
        return aliasMap;
    }

    @Override
    public List<PartnerUnitProductMappingDetail> getPartnerUnitProductMappings(Integer partnerId) {
        return partnerUnitProductMappingDao.findAllByActiveAndPartnerId(true, partnerId);
    }

    @Override
    public List<PartnerUnitProductMappingDetail> getPartnerUnitProductMappingsByPartnerUnitBrand(Integer partnerId, Integer unitId, Integer brandId) {
        return partnerUnitProductMappingDao.findAllByActiveAndPartnerIdAndUnitIdAndBrandId(true, partnerId, unitId, brandId);
    }

    @Override
    public List<PartnerUnitProductPricingDetail> getPartnerUnitProductPricing() {
        return partnerUnitProductPricingDao.findAllByActive(true);
    }

    @Override
    public List<DesiChaiCustomProfiles> getDesiChaiCustomProfiles() {
        return desiChaiCustomProfileDao.findAll();
    }
}
