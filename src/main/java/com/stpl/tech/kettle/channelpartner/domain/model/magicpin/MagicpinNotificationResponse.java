package com.stpl.tech.kettle.channelpartner.domain.model.magicpin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class MagicpinNotificationResponse {

    @JsonProperty("responseMessage")
    private String message;

    @JsonProperty("responseCode")
    private String code;


}
