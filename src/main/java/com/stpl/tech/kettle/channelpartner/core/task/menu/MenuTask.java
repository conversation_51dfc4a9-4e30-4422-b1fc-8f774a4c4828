package com.stpl.tech.kettle.channelpartner.core.task.menu;

import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqResponseData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;

import java.util.Objects;
import java.util.concurrent.Callable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Log4j2
public class MenuTask implements Callable<MenuReqResponseData> {
    private PartnerAbstractFactory partnerAbstractFactory;
    private volatile UnitMenuAddVO unitMenuAddVO;
    private volatile String requestId;

    @Override
    public MenuReqResponseData call() throws Exception {
        try {
            MDC.put("request.id", requestId);
            if (Objects.nonNull(unitMenuAddVO)) {
                return partnerAbstractFactory.sendMenuPushReqToPartner(unitMenuAddVO);
            }
        } catch (Exception ex) {
            log.error("Error processing menu task ", ex);
        } finally {
            MDC.clear();
        }
        return null;
    }
}
