package com.stpl.tech.kettle.channelpartner.core.service.order.factory;

import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.order.chain.*;
import org.apache.commons.lang3.NotImplementedException;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class OrderProcessingFactory<R, T> {
    private static Map<String, Supplier<OrderProcessingStep>> orderProcessingStepMap = new HashMap<>();

    public static OrderProcessingStep getInstance(String partnerName, PartnerOrderConverterDependency partnerOrderConverterDependency) {
        if (!orderProcessingStepMap.containsKey(partnerName)) {
            orderProcessingStepMap.put(partnerName, () -> getOrderProcessingStep(partnerName, partnerOrderConverterDependency));
        }
        return orderProcessingStepMap.get(partnerName).get();
    }

    private static <R, T> OrderProcessingStep<R, T> getOrderProcessingStep(String partnerName, PartnerOrderConverterDependency partnerOrderConverterDependency) {
        OrderProcessingChainBuilder<R, T> builder = new OrderProcessingChainBuilder<>();
        builder.defaultBuilderForProcessOrder(partnerOrderConverterDependency, partnerName);
        OrderProcessingStep<R, T> orderProcessingStep = builder.buildOrderProcessingStep();

        switch (partnerName) {
            case "MAGICPIN":
                orderProcessingStep = orderProcessingStep.linkNextStep(new DuplicateOrderCheckStep<>(builder),
                        new RecieveOrderCheckStep<>(builder),
                        new AddsOrderToCacheStep<>(builder),
                        new ConvertAndValidateOrderStep<>(builder),
                        new ProcessOrderInKettle<>(builder),
                        new SuccessOrRejectedOrderStep<>(builder));
                break;
            default:
                throw new NotImplementedException("Not Implemented yet For This Partner");
        }
        return orderProcessingStep;
    }
}

