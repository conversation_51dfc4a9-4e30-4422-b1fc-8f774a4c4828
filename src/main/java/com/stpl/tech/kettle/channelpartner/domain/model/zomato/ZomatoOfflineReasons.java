package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import java.util.HashMap;
import java.util.Map;

public enum ZomatoOfflineReasons {
    ANY_OTHER_REASON("UNKNOWN_REASON", false),

    DIRECTED_BY_YOU("Your outlet is currently offline as directed by you",false),
    YOUR_KITCHEN_IS_FULL("'Your outlet is currently offline as your kitchen is full",false),
    DEVICE_ISSUES("Your outlet is currently offline due to device issues",false),
    HIGH_NO_OF_REJECTIONS("Your outlet is currently offline due to high number of rejections",true),
    NOT_OPERATIONAL("Your outlet is currently offline due to your outlet not being operational",false),
    OUTLET_OFFLINE("Your outlet is currently offline",false),
    ONLINE_ORDERING_NOT_AVAILABLE("Your outlet is not available for Online Ordering any more",true),
    UPDATING_CONTENT_OR_MENU("Your outlet is offline since we are updating content / menu details",true),
    TEMPORARILY_CLOSED("Your outlet is temporarily closed",false),
    UNAVAILABILITY_OF_DELIVERY_PARTNERS("Your outlet is temporarily offline due to unavailability of delivery partners at this moment",true),
    MISSED_EXISTING_ORDERS_IN_TIME("Your outlet was turned off as you missed accepting order(s) in time",true);




    private final String reasonText;
    private final boolean autoSwitchOn;

    private static final Map<String, ZomatoOfflineReasons> reasonsMap;

    static {
        reasonsMap = new HashMap<>();
        for(ZomatoOfflineReasons  val : values()) {
            reasonsMap.put(val.getReasonText().toLowerCase(), val);
        }
    }

    public static ZomatoOfflineReasons getReason(String reasonString) {
        String key = reasonString.toLowerCase();
        if (reasonsMap.containsKey(key)) {
            return reasonsMap.get(key);
        }
        return ANY_OTHER_REASON;
    }

    private ZomatoOfflineReasons(String reasonText, boolean autoSwitchOn) {
        this.reasonText = reasonText;
        this.autoSwitchOn = autoSwitchOn;
    }
    public String getReasonText() {
        return reasonText;
    }
    public boolean isAutoSwitchOn() {
        return autoSwitchOn;
    }

}

