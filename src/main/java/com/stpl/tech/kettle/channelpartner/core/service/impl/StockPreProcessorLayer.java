package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import lombok.extern.log4j.Log4j2;

import java.util.List;
@Log4j2
public class StockPreProcessorLayer extends PartnerBaseDecorator {

    public StockPreProcessorLayer(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
    }

    @Override
    public <T, R> R preProcessData(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds, Class<R> returnType) throws ChannelPartnerException {
        try {
            Object obj = processStockEvent((UnitProductsStockEvent) reqObj, requestType, partnerIds);
            return returnType.cast(obj);
        } catch (ClassCastException e) {
            throw new ChannelPartnerException(new ChannelPartnerError("", "Class Cast Exception: Unable to cast obj to returnType: " + returnType + e));
        } catch (Exception e) {
            throw new ChannelPartnerException(new ChannelPartnerError("Unable to cast req Obj to UnitProductsStockEvent ", "This exception has occurred because unit product stock event req is received but req object is of type: " + reqObj.getClass().getName() + e));
        }
    }

    private PartnerActionEvent processStockEvent(UnitProductsStockEvent unitProductStockEvent, PartnerRequestType requestType, List<Integer> partnerIds) throws ChannelPartnerException {
        switch (requestType) {
            case UNIT_PRODUCT_STOCK:
                return createUnitProductStockUpdateEvent(unitProductStockEvent,partnerIds);
            default:
                throw new ChannelPartnerException("No Stock Update Type Event found for request :::" + requestType.name());
        }
    }

    private PartnerActionEvent createUnitProductStockUpdateEvent(UnitProductsStockEvent unitProductStockEvent, List<Integer> partnerIds) {
        PartnerActionEvent event = new PartnerActionEvent();
        event.setEventData(unitProductStockEvent);
        event.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
        event.setPartnerIds(partnerIds);
        event.setPartner(true);
        if (unitProductStockEvent.getPartnerId() != null) {
            event.getPartnerIds().add(unitProductStockEvent.getPartnerId());
        }
        return event;
    }

}
