package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"calorieCount"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoNutritionInfo {

    @JsonProperty("calorieCount")
    private float calorieCount;

    @JsonProperty("calorieCount")
    public float getCalorieCount() {
        return calorieCount;
    }

    @JsonProperty("calorieCount")
    public void setCalorieCount(float calorieCount) {
        this.calorieCount = calorieCount;
    }
}
