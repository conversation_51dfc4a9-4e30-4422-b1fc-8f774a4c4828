package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "start_duration",
        "end_duration",
        "penalty_amount",
        "message"
})
public class ZomatoOrderPenaltyMapV3 {
	
	@JsonProperty("start_duration")
    private Integer startDuration;
    @JsonProperty("end_duration")
    private Integer endDuration;
    @JsonProperty("penalty_amount")
    private Float penaltyAmount;
    @JsonProperty("message")
    private ZomatoOrderPenaltyMessageV3 message;
    
    @JsonProperty("start_duration")
	public Integer getStartDuration() {
		return startDuration;
	}
    
    @JsonProperty("start_duration")
	public void setStartDuration(Integer startDuration) {
		this.startDuration = startDuration;
	}
    
    @JsonProperty("end_duration")
	public Integer getEndDuration() {
		return endDuration;
	}
    
    @JsonProperty("end_duration")
	public void setEndDuration(Integer endDuration) {
		this.endDuration = endDuration;
	}
    
    @JsonProperty("penalty_amount")
	public Float getPenaltyAmount() {
		return penaltyAmount;
	}
    
    @JsonProperty("penalty_amount")
	public void setPenaltyAmount(Float penaltyAmount) {
		this.penaltyAmount = penaltyAmount;
	}
    
    @JsonProperty("message")
	public ZomatoOrderPenaltyMessageV3 getMessage() {
		return message;
	}
    
    @JsonProperty("message")
	public void setMessage(ZomatoOrderPenaltyMessageV3 message) {
		this.message = message;
	}

	@Override
	public String toString() {
		return "ZomatoOrderPenaltyMapV3 [startDuration=" + startDuration + ", endDuration=" + endDuration
				+ ", penaltyAmount=" + penaltyAmount + ", message=" + message + "]";
	}
    
	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(startDuration).append(endDuration).append(penaltyAmount).append(message).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderPenaltyMapV3) == false) {
            return false;
        }
        ZomatoOrderPenaltyMapV3 rhs = ((ZomatoOrderPenaltyMapV3) other);
        return new EqualsBuilder().append(startDuration, rhs.startDuration).append(endDuration, rhs.endDuration).append(penaltyAmount, rhs.penaltyAmount).append(message, rhs.message).isEquals();
    }
    

}
