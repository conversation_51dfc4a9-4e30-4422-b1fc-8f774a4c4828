package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinSlot implements Serializable {
    private String startTime;
    private String endTime;
}
