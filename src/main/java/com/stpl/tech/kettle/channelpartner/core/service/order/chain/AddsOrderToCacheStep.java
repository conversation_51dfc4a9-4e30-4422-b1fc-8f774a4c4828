package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class AddsOrderToCacheStep<R, T> extends OrderProcessingStep<R, T> {

    public AddsOrderToCacheStep(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        addPartnerOrderToCache(partnerOrderDetail);
        log.info("\n----------- ,STEP 2, - , Order Cached and primary checks executed in in  ----------- , milliseconds {}", (System.currentTimeMillis() - startTime));
        return super.process(request, isManual, response, partnerPrimaryData, partnerOrderDetail);
    }

    private void addPartnerOrderToCache(PartnerOrderDetail partnerOrderDetail) {
        PartnerOrderCacheDetail partnerOrderCacheDetail = new PartnerOrderCacheDetail();
        partnerOrderCacheDetail.setPartnerName(partnerOrderDetail.getPartnerName());
        partnerOrderCacheDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerOrderCacheDetail.setPartnerOrderId(partnerOrderDetail.getPartnerOrderId());
        partnerOrderCacheDetail.setUnitId(partnerOrderDetail.getUnitId());
        channelPartnerDataCache.getPartnerOrderCache().put(partnerOrderDetail.getPartnerOrderId(), partnerOrderCacheDetail);
    }
}

