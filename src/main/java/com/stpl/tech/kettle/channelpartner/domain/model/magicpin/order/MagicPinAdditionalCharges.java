package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MagicPinAdditionalCharges {
    @JsonProperty("title")
    private String title;
    @JsonProperty("amount")
    private float amount;
    @JsonProperty("tax_Liability")
    private String taxLiability;


    @JsonProperty("taxes")
    private List<MagicpinTaxDetails> magicpinTaxDetails;
}
