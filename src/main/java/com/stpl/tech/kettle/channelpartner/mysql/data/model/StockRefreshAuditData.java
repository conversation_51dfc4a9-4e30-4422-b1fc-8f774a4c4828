package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.*;

import javax.persistence.*;
import java.util.Date;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "STOCK_REFRESH_AUDIT_DATA")
public class StockRefreshAuditData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AUDIT_ID")
    Integer auditId;

    @Column(name = "UNIT_ID")
    Integer unitId;

    @Column(name = "CHANNEL_PARTNER_ID")
    Integer channelPartnerId;

    @Column(name = "BRAND_ID")
    Integer brandId;

    @Column(name = "LOG_TIME")
    Date timestamp;

    @Column(name = "TYPE")
    String logType;

    @Column(name = "STATUS")
    String status;
}
