
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "prep_time",
    "min_prep_time",
    "max_prep_time"
})
public class ZomatoOrderPrepTimeDetails {

    @JsonProperty("prep_time")
    private Integer prepTime;
    @JsonProperty("min_prep_time")
    private Integer minPrepTime;
    @JsonProperty("max_prep_time")
    private Integer maxPrepTime;

    @JsonProperty("prep_time")
    public Integer getPrepTime() {
        return prepTime;
    }

    @JsonProperty("prep_time")
    public void setPrepTime(Integer prepTime) {
        this.prepTime = prepTime;
    }

    @JsonProperty("min_prep_time")
    public Integer getMinPrepTime() {
        return minPrepTime;
    }

    @JsonProperty("min_prep_time")
    public void setMinPrepTime(Integer minPrepTime) {
        this.minPrepTime = minPrepTime;
    }

    @JsonProperty("max_prep_time")
    public Integer getMaxPrepTime() {
        return maxPrepTime;
    }

    @JsonProperty("max_prep_time")
    public void setMaxPrepTime(Integer maxPrepTime) {
        this.maxPrepTime = maxPrepTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoOrderPrepTimeDetails that = (ZomatoOrderPrepTimeDetails) o;

        return new EqualsBuilder()
            .append(prepTime, that.prepTime)
            .append(minPrepTime, that.minPrepTime)
            .append(maxPrepTime, that.maxPrepTime)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(prepTime)
            .append(minPrepTime)
            .append(maxPrepTime)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoOrderPrepTimeDetails{" +
            "prepTime=" + prepTime +
            ", minPrepTime=" + minPrepTime +
            ", maxPrepTime=" + maxPrepTime +
            '}';
    }
}
