package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;
import java.util.Objects;

@Repository
public class CafeStatusChannelPartnerDaoImpl extends AbstractMasterDaoImpl implements CafeStatusChannelPartnerDao {

    private static final Logger LOG = LoggerFactory.getLogger(CafeStatusChannelPartnerDaoImpl.class);

    @Override
    public List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate){
        Query query = manager.createQuery("FROM CafeStatusChannelPartner c WHERE c.statusUpdate= :statusUpdate ");
        query.setParameter("statusUpdate",statusUpdate);
        return query.getResultList();
    }

    @Override
    public Object[] getOrderDetailByGeneratedOrderId(String generatedOrderId) {
        Query query = manager.createNativeQuery("Select ORDER_ID , UNIT_ID , ORDER_SOURCE_ID FROM  ORDER_DETAIL E where E.GENERATED_ORDER_ID =:generatedOrderId ");
        query.setParameter("generatedOrderId", generatedOrderId);
        try {
            return (Object[]) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("Error while fetching Kettle Order Id  for Generated Order id :::: {} ",generatedOrderId);
        }
        return null;
    }


    @Override
    public List<Object[]> getProductCounts(List<Integer> productIds , Integer unitId , Integer numberOfDays , Integer partnerId) {
        String productIdsStr = productIds.toString().replaceAll("[\\[\\]]", "");
        String sql = "SELECT  " +
                "P.PRO, " +
                "COALESCE(CNT, 0) AS COUNT " +
                "FROM " +
                "(SELECT DISTINCT I.PRODUCT_ID AS PRO " +
                "FROM ORDER_ITEM I " +
                "WHERE I.PRODUCT_ID IN (" + productIdsStr + ")) P " +
                "LEFT JOIN ( " +
                "SELECT O.UNIT_ID AS UNIT_ID, " +
                "I.PRODUCT_ID AS PRODUCT_ID_O, " +
                "COUNT(*) AS CNT " +
                "FROM ORDER_DETAIL O " +
                "INNER JOIN ORDER_ITEM I ON O.ORDER_ID = I.ORDER_ID " +
                "WHERE O.CHANNEL_PARTNER_ID = :partnerId AND " +
                "O.UNIT_ID = :unitId AND " +
                "O.BILLING_SERVER_TIME >= :startDate AND " +
                "I.PRODUCT_ID IN (" + productIdsStr + ") " +
                "GROUP BY O.UNIT_ID, I.PRODUCT_ID) AS COUNT_TABLE " +
                "ON P.PRO = COUNT_TABLE.PRODUCT_ID_O " +
                "WHERE COUNT_TABLE.CNT IS NULL OR COUNT_TABLE.CNT = 0";

        Query query = manager.createNativeQuery(sql);
        query.setParameter("unitId", unitId);
        query.setParameter("startDate" , AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-numberOfDays));
        query.setParameter("partnerId", partnerId);
        return query.getResultList();
    }

    @Override
    public String getKettleCustomerContact(String partnerCustomerId){
        try {
            Query query = manager.createNativeQuery("SELECT pm.CUSTOMER_ID FROM PARTNER_ORDER_CUSTOMER_MAPPING pm " +
                    "WHERE pm.PARTNER_CUSTOMER_ID = :partnerCustomerId");
            query.setParameter("partnerCustomerId", partnerCustomerId);
            Integer customerId = (Integer) query.getSingleResult();
            if(Objects.nonNull(customerId)){
                Query query2 = manager.createNativeQuery("SELECT INFO.CONTACT_NUMBER FROM CUSTOMER_INFO INFO " +
                        "WHERE INFO.CUSTOMER_ID = :customerId");
                query2.setParameter("customerId", customerId);
                return (String) query2.getSingleResult();
            }
        }catch (NoResultException nre){
          //DO Nothing
        } catch (Exception e) {
            LOG.error("Error in fetching Kettle customer for partner customer id : {} ",
                    partnerCustomerId);
        }
        return null;
    }





}
