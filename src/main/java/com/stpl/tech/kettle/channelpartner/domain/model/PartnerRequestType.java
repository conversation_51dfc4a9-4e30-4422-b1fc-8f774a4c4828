package com.stpl.tech.kettle.channelpartner.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PartnerRequestType {
    UPDATE_UNIT_MENU("menu-add-per-unit"),
    UPDATE_MENU("menu-add"),
    UPDATE_ALL_UNIT_MENU("push-menu-to-units"),

    SCHEDULED_MENU_PUSH("scheduled-menu-push"),

    INVENTORY_UPDATE("inventory/publish"),
    UNIT_PRODUCT_STOCK("item-stock-update");
    private final String requestType ;

}
