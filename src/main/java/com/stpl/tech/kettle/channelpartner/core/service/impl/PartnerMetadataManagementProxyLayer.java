package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventPublisherLayerDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerBuilderFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Log4j2
public class PartnerMetadataManagementProxyLayer extends PartnerMetadataManagementServiceImpl {

    @Autowired
    private PartnerMenuConverterDependency partnerMenuConverterDependency ;

    private PartnerBaseDecorator partnerBaseDecorator;

    private void setPartnerBaseDecorator(EventType eventType, PartnerMetadataBuilder partnerMetadataBuilder){
        this.partnerBaseDecorator= ServiceFactory.getPartnerPreProcessorLayer(eventType.name() , partnerMetadataBuilder);
    };
    @Override
    public <T> boolean sendRequestBasisRequestType(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds)  {
        for (Integer partner : partnerIds) {
            //Added thia hardcoded check to exclude SWIGGY AND ZOMATO - OLD PARTNERS FUNCTIONALITY TO REMAIN THE SAME
            if (!ChannelPartnerUtils.getNewChannelPartners().contains(partner)) {
                try {
                    super.sendRequestBasisRequestType(reqObj, requestType, new ArrayList<>(Arrays.asList(partner)));
                } catch (ChannelPartnerException e) {
                    log.error("Exception caught while processing partner request event  ::{} for partner :::{}", requestType.name(), partner);
                }
            } else {
                for(EventType eventType : EventType.values()){
                    if(eventType.getEvents().contains(requestType.name())){
                        PartnerMetadataBuilder partnerMetadataBuilder = PartnerBuilderFactory.getInstance(partnerMenuConverterDependency);
                        setPartnerBaseDecorator(eventType,partnerMetadataBuilder);
                        PartnerBaseDecorator baseDecorator = new PartnerActionEventPublisherLayerDecorator(partnerMetadataBuilder, this.partnerBaseDecorator);
                        try {
                            baseDecorator.preProcessData(reqObj,requestType,partnerIds, PartnerActionEvent.class);
                        } catch (Exception e) {
                            log.error("Exception caught while sending request of :::::{} for event :::::{} ", eventType.name(), requestType.getRequestType());}
                    }
                }
            }
        }
        return true ;
    }
}
