package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "charge_id",
        "charge_name",
        "charge_type",
        "charge_value",
        "charge_is_active",
        "applicable_on",
        "charge_always_applicable",
        "charge_applicable_below_order_amount",
        "has_tier_wise_values",
        "tier_wise_values",
        "charge_taxes",
})
public class OldZomatoMenuCharge {

    @JsonProperty("charge_id")
    private String chargeId;
    @JsonProperty("charge_name")
    private String chargeName;
    @JsonProperty("charge_type")
    private String chargeType;
    @JsonProperty("charge_value")
    private Float chargeValue;
    @JsonProperty("charge_is_active")
    private Integer chargeIsActive;
    @JsonProperty("applicable_on")
    private String applicableOn;
    @JsonProperty("charge_always_applicable")
    private Integer chargeAlwaysApplicable;
    @JsonProperty("charge_applicable_below_order_amount")
    private Float chargeApplicableBelowOrderAmount;
    @JsonProperty("has_tier_wise_values")
    private Integer hasTierWiseValues;
    @JsonProperty("tier_wise_values")
    private List<ZomatoChargeTierValue> tierWiseValues;
    @JsonProperty("charge_taxes")
    private List<ZomatoChargeTaxes> chargeTaxes = null;

    @JsonProperty("charge_id")
    public String getChargeId() {
        return chargeId;
    }

    @JsonProperty("charge_id")
    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    @JsonProperty("charge_name")
    public String getChargeName() {
        return chargeName;
    }

    @JsonProperty("charge_name")
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    @JsonProperty("charge_type")
    public String getChargeType() {
        return chargeType;
    }

    @JsonProperty("charge_type")
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    @JsonProperty("charge_value")
    public Float getChargeValue() {
        return chargeValue;
    }

    @JsonProperty("charge_value")
    public void setChargeValue(Float chargeValue) {
        this.chargeValue = chargeValue;
    }

    public Integer getChargeIsActive() {
        return chargeIsActive;
    }

    public void setChargeIsActive(Integer chargeIsActive) {
        this.chargeIsActive = chargeIsActive;
    }

    public String getApplicableOn() {
        return applicableOn;
    }

    public void setApplicableOn(String applicableOn) {
        this.applicableOn = applicableOn;
    }

    public Integer getChargeAlwaysApplicable() {
        return chargeAlwaysApplicable;
    }

    public void setChargeAlwaysApplicable(Integer chargeAlwaysApplicable) {
        this.chargeAlwaysApplicable = chargeAlwaysApplicable;
    }

    public Float getChargeApplicableBelowOrderAmount() {
        return chargeApplicableBelowOrderAmount;
    }

    public void setChargeApplicableBelowOrderAmount(Float chargeApplicableBelowOrderAmount) {
        this.chargeApplicableBelowOrderAmount = chargeApplicableBelowOrderAmount;
    }

    public Integer getHasTierWiseValues() {
        return hasTierWiseValues;
    }

    public void setHasTierWiseValues(Integer hasTierWiseValues) {
        this.hasTierWiseValues = hasTierWiseValues;
    }

    public List<ZomatoChargeTierValue> getTierWiseValues() {
        return tierWiseValues;
    }

    public void setTierWiseValues(List<ZomatoChargeTierValue> tierWiseValues) {
        this.tierWiseValues = tierWiseValues;
    }

    @JsonProperty("charge_taxes")
    public List<ZomatoChargeTaxes> getChargeTaxes() {
        return chargeTaxes;
    }

    @JsonProperty("charge_taxes")
    public void setChargeTaxes(List<ZomatoChargeTaxes> chargeTaxes) {
        this.chargeTaxes = chargeTaxes;
    }

    @Override
    public String toString() {
        return "ZomatoMenuCharge{" +
                "chargeId=" + chargeId +
                ", chargeName='" + chargeName + '\'' +
                ", chargeType='" + chargeType + '\'' +
                ", chargeValue=" + chargeValue +
                ", chargeIsActive=" + chargeIsActive +
                ", applicableOn=" + applicableOn +
                ", chargeAlwaysApplicable=" + chargeAlwaysApplicable +
                ", chargeApplicableBelowOrderAmount=" + chargeApplicableBelowOrderAmount +
                ", hasTierWiseValues=" + hasTierWiseValues +
                ", tierWiseValues=" + tierWiseValues +
                ", chargeTaxes=" + chargeTaxes +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OldZomatoMenuCharge that = (OldZomatoMenuCharge) o;

        return new EqualsBuilder()
                .append(chargeId, that.chargeId)
                .append(chargeName, that.chargeName)
                .append(chargeType, that.chargeType)
                .append(chargeValue, that.chargeValue)
                .append(chargeIsActive, that.chargeIsActive)
                .append(applicableOn, that.applicableOn)
                .append(chargeAlwaysApplicable, that.chargeAlwaysApplicable)
                .append(chargeApplicableBelowOrderAmount, that.chargeApplicableBelowOrderAmount)
                .append(hasTierWiseValues, that.hasTierWiseValues)
                .append(tierWiseValues, that.tierWiseValues)
                .append(chargeTaxes, that.chargeTaxes)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(chargeId)
                .append(chargeName)
                .append(chargeType)
                .append(chargeValue)
                .append(chargeIsActive)
                .append(applicableOn)
                .append(chargeAlwaysApplicable)
                .append(chargeApplicableBelowOrderAmount)
                .append(hasTierWiseValues)
                .append(tierWiseValues)
                .append(chargeTaxes)
                .toHashCode();
    }
}