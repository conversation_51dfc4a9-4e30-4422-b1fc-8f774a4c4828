package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "timestamp",
        "cancellation_reason",
        "food_prepared_status",
        "swiggy_order_id",
        "external_order_id"
})
public class SwiggyCancelRequest {

    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("cancellation_reason")
    private String cancellationReason;
    @JsonProperty("food_prepared_status")
    private boolean foodPreparedStatus;
    @JsonProperty("swiggy_order_id")
    private long swiggyOrderId;
    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("cancellation_reason")
    public String getCancellationReason() {
        return cancellationReason;
    }

    @JsonProperty("cancellation_reason")
    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }

    @JsonProperty("food_prepared_status")
    public boolean isFoodPreparedStatus() {
        return foodPreparedStatus;
    }

    @JsonProperty("food_prepared_status")
    public void setFoodPreparedStatus(boolean foodPreparedStatus) {
        this.foodPreparedStatus = foodPreparedStatus;
    }

    @JsonProperty("swiggy_order_id")
    public long getSwiggyOrderId() {
        return swiggyOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public void setSwiggyOrderId(long swiggyOrderId) {
        this.swiggyOrderId = swiggyOrderId;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("timestamp", timestamp).append("cancellationReason", cancellationReason).append("foodPreparedStatus", foodPreparedStatus).append("swiggyOrderId", swiggyOrderId).append("externalOrderId", externalOrderId).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(timestamp).append(cancellationReason).append(externalOrderId).append(swiggyOrderId).append(foodPreparedStatus).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyCancelRequest) == false) {
            return false;
        }
        SwiggyCancelRequest rhs = ((SwiggyCancelRequest) other);
        return new EqualsBuilder().append(timestamp, rhs.timestamp).append(cancellationReason, rhs.cancellationReason).append(externalOrderId, rhs.externalOrderId).append(swiggyOrderId, rhs.swiggyOrderId).append(foodPreparedStatus, rhs.foodPreparedStatus).isEquals();
    }

}