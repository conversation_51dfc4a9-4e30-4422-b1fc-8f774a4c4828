package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "zomato_group_id",
        "group_id",
        "group_name",
        "items"
})
public class ZomatoGroup {

    @JsonProperty("zomato_group_id")
    private Long zomatoGroupId;
    @JsonProperty("group_id")
    private String groupId;
    @JsonProperty("group_name")
    private String groupName;
    @JsonProperty("items")
    private List<ZomatoOrderItem> items = null;

    @JsonProperty("zomato_group_id")
    public Long getZomatoGroupId() {
        return zomatoGroupId;
    }

    @JsonProperty("zomato_group_id")
    public void setZomatoGroupId(Long zomatoGroupId) {
        this.zomatoGroupId = zomatoGroupId;
    }

    @JsonProperty("group_id")
    public String getGroupId() {
        return groupId;
    }

    @JsonProperty("group_id")
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @JsonProperty("group_name")
    public String getGroupName() {
        return groupName;
    }

    @JsonProperty("group_name")
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @JsonProperty("items")
    public List<ZomatoOrderItem> getItems() {
        if(items == null){
            items = new ArrayList<>();
        }
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<ZomatoOrderItem> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("zomatoGroupId", zomatoGroupId).append("groupId", groupId).append("groupName", groupName).append("items", items).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(groupId).append(groupName).append(items).append(zomatoGroupId).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoGroup) == false) {
            return false;
        }
        ZomatoGroup rhs = ((ZomatoGroup) other);
        return new EqualsBuilder().append(groupId, rhs.groupId).append(groupName, rhs.groupName).append(items, rhs.items).append(zomatoGroupId, rhs.zomatoGroupId).isEquals();
    }

}

