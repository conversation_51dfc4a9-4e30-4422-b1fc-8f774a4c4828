package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerRequestTarget;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Objects;
@Log4j2
public class PartnerMenuRequestAdapter implements PartnerRequestTarget<MenuReqMetadata, UnitMenuAddVO> {

    private final PartnerAdaptee<MenuReqMetadata, MenuReqMetadata> partnerAdaptee;
    private final PartnerAbstractFactory partnerAbstractFactory;

    public PartnerMenuRequestAdapter(PartnerAdaptee<MenuReqMetadata, MenuReqMetadata> partnerAdaptee, PartnerAbstractFactory partnerAbstractFactory) {
        this.partnerAdaptee = partnerAdaptee;
        this.partnerAbstractFactory = partnerAbstractFactory;
    }

    @Override
    public UnitMenuAddVO convertAndSendResponse(MenuReqMetadata menuAddVO) throws ChannelPartnerException {
        if (Objects.nonNull(menuAddVO)) {
            MenuReqMetadata menuReqMetadata = this.partnerAdaptee.setRequestCommonData(menuAddVO);
            return this.partnerAbstractFactory.convertToPartnerMenu(menuReqMetadata, UnitMenuAddVO.class);
        }
        return null;
    }


}
