package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "statusMessage",
        "statusCode",
        "data"
})
public class SwiggyCancelResponse {

    @JsonProperty("statusMessage")
    private String statusMessage;
    @JsonProperty("statusCode")
    private int statusCode;
    @JsonProperty("data")
    private Object data;

    @JsonProperty("statusMessage")
    public String getStatusMessage() {
        return statusMessage;
    }

    @JsonProperty("statusMessage")
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @JsonProperty("statusCode")
    public int getStatusCode() {
        return statusCode;
    }

    @JsonProperty("statusCode")
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    @JsonProperty("data")
    public Object getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("statusMessage", statusMessage).append("statusCode", statusCode).append("data", data).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(statusCode).append(data).append(statusMessage).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyCancelResponse) == false) {
            return false;
        }
        SwiggyCancelResponse rhs = ((SwiggyCancelResponse) other);
        return new EqualsBuilder().append(statusCode, rhs.statusCode).append(data, rhs.data).append(statusMessage, rhs.statusMessage).isEquals();
    }

}