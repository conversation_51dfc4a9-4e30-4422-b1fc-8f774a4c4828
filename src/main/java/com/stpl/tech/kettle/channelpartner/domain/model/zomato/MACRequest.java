package com.stpl.tech.kettle.channelpartner.domain.model.zomato;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "order_id",
    "external_order_id",
    "customer_cancellation_reason",
    "initiated_at",
    "timeout_threshold"
})
public class MACRequest {

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("customer_cancellation_reason")
    private String customerCancellationReason;

    @JsonProperty("initiated_at")
    private Integer initiatedAt;

    @JsonProperty("timeout_threshold")
    private Integer timeout;

    @JsonProperty("order_id")
    public Long getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("customer_cancellation_reason")
    public String getCustomerCancellationReason() {
        return customerCancellationReason;
    }

    @JsonProperty("customer_cancellation_reason")
    public void setCustomerCancellationReason(String customerCancellationReason) {
        this.customerCancellationReason = customerCancellationReason;
    }

    @JsonProperty("initiated_at")
    public Integer getInitiatedAt() {
        return initiatedAt;
    }

    @JsonProperty("initiated_at")
    public void setInitiatedAt(Integer initiatedAt) {
        this.initiatedAt = initiatedAt;
    }

    @JsonProperty("timeout_threshold")
    public Integer getTimeout() {
        return timeout;
    }

    @JsonProperty("timeout_threshold")
    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }


}
