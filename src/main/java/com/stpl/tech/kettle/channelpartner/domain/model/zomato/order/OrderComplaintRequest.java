package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.stpl.tech.kettle.domain.model.OrderItem;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "external_order_id",
        "order_id",
        "reference_id",
        "image_urls",
        "complaint_message",
        "complaint_reason",
        "created_at",
        "expired_at",
        "ordered_items",
        "customer_complaints_count",
        "repeat_customer_count",
        "refund_options",
        "min_custom_refund"
})
public class OrderComplaintRequest {

    @JsonProperty("order_id")
    private Integer orderId;

    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("reference_id")
    private String referenceId;

    @JsonProperty("image_urls")
    private List<String> imageUrlsList = new ArrayList<>();

    @JsonProperty("complaint_message")
    private String complaintMessage;

    @JsonProperty("complaint_reason")
    private String complaintReason;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("expired_at")
    private Date expiredAt;

    @JsonProperty("ordered_items")
    private List<OrderItemComplaintRequest> orderItemComplaintRequestList = new ArrayList<>();

    @JsonProperty("customer_complaints_count")
    private Integer customerComplaintsCount;

    @JsonProperty("repeat_customer_count")
    private Integer repeatCustomerCount;

    @JsonProperty("refund_options")
    private List<RefundOptionsRequest> refundOptionsList = new ArrayList<>();

    @JsonProperty("min_custom_refund")
    private Float minCustomRefund;

    public OrderComplaintRequest() {
    }

    public OrderComplaintRequest(String externalOrderId, Integer orderId, String referenceId, List<String> imageUrlsList, String complaintMessage, String complaintReason, Date createdAt, Date expiredAt, List<OrderItemComplaintRequest> orderItemComplaintRequestList, Integer customerComplaintsCount, Integer repeatCustomerCount, List<RefundOptionsRequest> refundOptionsList, Float minCustomRefund) {
        this.externalOrderId = externalOrderId;
        this.orderId = orderId;
        this.referenceId = referenceId;
        this.imageUrlsList = imageUrlsList;
        this.complaintMessage = complaintMessage;
        this.complaintReason = complaintReason;
        this.createdAt = createdAt;
        this.expiredAt = expiredAt;
        this.orderItemComplaintRequestList=orderItemComplaintRequestList;
        this.customerComplaintsCount = customerComplaintsCount;
        this.repeatCustomerCount = repeatCustomerCount;
        this.refundOptionsList = refundOptionsList;
        this.minCustomRefund = minCustomRefund;
    }
    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("order_id")
    public Integer getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("reference_id")
    public String getReferenceId() {
        return referenceId;
    }

    @JsonProperty("reference_id")
    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    @JsonProperty("image_urls")
    public List<String> getImageUrlsList() {
        return imageUrlsList;
    }

    public void setImageUrlsList(List<String> imageUrlsList) {
        this.imageUrlsList = imageUrlsList;
    }

    @JsonProperty("complaint_message")
    public String getComplaintMessage() {
        return complaintMessage;
    }

    @JsonProperty("complaint_message")
    public void setComplaintMessage(String complaintMessage) {
        this.complaintMessage = complaintMessage;
    }

    @JsonProperty("complaint_reason")
    public String getComplaintReason() {
        return complaintReason;
    }

    @JsonProperty("complaint_reason")
    public void setComplaintReason(String complaintReason) {
        this.complaintReason = complaintReason;
    }

    @JsonProperty("created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("expired_at")
    public Date getExpiredAt() {
        return expiredAt;
    }

    @JsonProperty("expired_at")
    public void setExpiredAt(Date expiredAt) {
        this.expiredAt = expiredAt;
    }

    @JsonProperty("ordered_items")
    public List<OrderItemComplaintRequest> getOrderItemComplaintRequestList() {
        return orderItemComplaintRequestList;
    }

    public void setOrderItemComplaintRequestList(List<OrderItemComplaintRequest> orderItemComplaintRequestList) {
        this.orderItemComplaintRequestList = orderItemComplaintRequestList;
    }

    @JsonProperty("customer_complaints_count")
    public Integer getCustomerComplaintsCount() {
        return customerComplaintsCount;
    }

    @JsonProperty("customer_complaints_count")
    public void setCustomerComplaintsCount(Integer customerComplaintsCount) {
        this.customerComplaintsCount = customerComplaintsCount;
    }

    @JsonProperty("repeat_customer_count")
    public Integer getRepeatCustomerCount() {
        return repeatCustomerCount;
    }

    @JsonProperty("repeat_customer_count")
    public void setRepeatCustomerCount(Integer repeatCustomerCount) {
        this.repeatCustomerCount = repeatCustomerCount;
    }

    @JsonProperty("refund_options")
    public List<RefundOptionsRequest> getRefundOptionsList() {
        return refundOptionsList;
    }

    @JsonProperty("refund_options")
    public void setRefundOptionsList(List<RefundOptionsRequest> refundOptionsList) {
        this.refundOptionsList = refundOptionsList;
    }

    @JsonProperty("min_custom_refund")
    public Float getMinCustomRefund() {
        return minCustomRefund;
    }

    @JsonProperty("min_custom_refund")
    public void setMinCustomRefund(Float minCustomRefund) {
        this.minCustomRefund = minCustomRefund;
    }

    @Override
    public String toString() {
        return "OrderComplaintRequest{" +
                "imageUrlsList=" + imageUrlsList +
                '}';
    }
}
