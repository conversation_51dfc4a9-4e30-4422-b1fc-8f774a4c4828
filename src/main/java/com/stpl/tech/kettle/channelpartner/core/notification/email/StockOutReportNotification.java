package com.stpl.tech.kettle.channelpartner.core.notification.email;

import com.stpl.tech.kettle.channelpartner.core.notification.email.templates.OrderMismatchNotificationTemplate;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.StockOutReportDomain;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class StockOutReportNotification extends EmailNotification {


    private List<String> toEmails;

    private EnvType envType;
    private String subjectOfEmail;

    public StockOutReportNotification(List<String> toEmails , String subjectOfEmail ,
                               EnvType envType){
        this.toEmails= toEmails;
        this.subjectOfEmail = subjectOfEmail;
        this.envType = envType;
    }


    @Override
    public String[] getToEmails() {
        if (ChannelPartnerUtils.isDev(getEnvironmentType())) {
            return new String[]{"<EMAIL>"};
        } else {
            Set<String> mails = new HashSet<>();
            mails.addAll(toEmails);
            String[] simpleArray = new String[mails.size()];
            return mails.toArray(simpleArray);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        return "";
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
