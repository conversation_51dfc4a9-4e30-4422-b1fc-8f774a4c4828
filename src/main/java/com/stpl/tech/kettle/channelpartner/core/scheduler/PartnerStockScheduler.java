package com.stpl.tech.kettle.channelpartner.core.scheduler;


import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.task.stock.schedule.ItemStockScheduleTask;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerStockSchedulerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledFuture;


@Log4j2
@Component
public class PartnerStockScheduler {

    @Autowired
    private PartnerStockSchedulerDao partnerStockSchedulerDao;

    @Autowired
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    @Autowired
    private ZomatoService zomatoService;

    @Autowired
    private SwiggyService swiggyService;



    private static Map<Integer,List<ScheduledFuture<?>>>  scheduledFutureEventsMap = new HashMap();


    @PostConstruct
    void initializeEvents(){
        List<PartnerStockUpdateSchedule> partnerStockUpdateScheduleList = (List<PartnerStockUpdateSchedule>)
                partnerStockSchedulerDao.findAll();
        if(!CollectionUtils.isEmpty(partnerStockUpdateScheduleList)){
            for(PartnerStockUpdateSchedule event : partnerStockUpdateScheduleList){
                if(checkIfEventIsValidForSchedule(event)){
                    CronTrigger stockinTrigger = new CronTrigger(event.getStockInTime());
                    CronTrigger stockOutTrigger = new CronTrigger(event.getStockOutTime());
                    ItemStockScheduleTask itemStockInScheduleTask = ItemStockScheduleTask.builder()
                            .swiggyService(swiggyService).zomatoService(zomatoService).stockStatus(StockStatus.STOCK_IN)
                            .partnerStockUpdateSchedule(event).partnerStockSchedulerDao(partnerStockSchedulerDao).build();
                    ItemStockScheduleTask itemStockOutScheduleTask = ItemStockScheduleTask.builder()
                            .swiggyService(swiggyService).zomatoService(zomatoService).stockStatus(StockStatus.STOCK_OUT)
                            .partnerStockUpdateSchedule(event).partnerStockSchedulerDao(partnerStockSchedulerDao).build();
                    List<ScheduledFuture<?>> scheduledFutureList = new ArrayList<>();
                    scheduledFutureList.add(threadPoolTaskScheduler.schedule(itemStockInScheduleTask,stockinTrigger));
                    scheduledFutureList.add(threadPoolTaskScheduler.schedule(itemStockOutScheduleTask,stockOutTrigger));
                    log.info("Scheduled Stock Update Event For Event Id : {} ", event.getStockUpdateId());
                    log.info("Unit Ids For Scheduled Stock Update : {} ", event.getUnitIds());
                    log.info("Product Ids For Scheduled Stock Update : {} ", event.getProductIds());
                    scheduledFutureEventsMap.put(event.getStockUpdateId(),scheduledFutureList);

                }else{
                    if(scheduledFutureEventsMap.containsKey(event.getStockUpdateId())){
                        for(ScheduledFuture<?> scheduledFuture :  scheduledFutureEventsMap.get(event.getStockUpdateId())){
                            Boolean isCancelled = scheduledFuture.cancel(true);
                            log.info("Stock Update Schedule Event cancelled With Id : {} : {} ",event.getStockUpdateId(),
                                    isCancelled);
                        }
                    }
                }
            }
        }

    }

    public Integer   getCurrentEventListForStockUpdate(){
        ThreadPoolTaskScheduler threadPoolTaskScheduler1 = threadPoolTaskScheduler;
        return  threadPoolTaskScheduler.getActiveCount();
    }

    public void  refreshEvents(){
        List<PartnerStockUpdateSchedule> partnerStockUpdateScheduleList = (List<PartnerStockUpdateSchedule>)
                partnerStockSchedulerDao.findAll();
        for(Integer key : scheduledFutureEventsMap.keySet()){
            for(ScheduledFuture<?> scheduledFuture :  scheduledFutureEventsMap.get(key)){
                    Boolean isCancelled = scheduledFuture.cancel(true);
                    log.info("Stock Update Schedule Event cancelled With Id : {} : {} ",key,
                            isCancelled);
            }

        }
        scheduledFutureEventsMap.clear();
        for(PartnerStockUpdateSchedule event : partnerStockUpdateScheduleList){
            if(checkIfEventIsValidForSchedule(event)){
                CronTrigger stockinTrigger = new CronTrigger(event.getStockInTime());
                CronTrigger stockOutTrigger = new CronTrigger(event.getStockOutTime());
                ItemStockScheduleTask itemStockInScheduleTask = ItemStockScheduleTask.builder()
                        .swiggyService(swiggyService).zomatoService(zomatoService).stockStatus(StockStatus.STOCK_IN)
                        .partnerStockUpdateSchedule(event).partnerStockSchedulerDao(partnerStockSchedulerDao).build();
                ItemStockScheduleTask itemStockOutScheduleTask = ItemStockScheduleTask.builder()
                        .swiggyService(swiggyService).zomatoService(zomatoService).stockStatus(StockStatus.STOCK_OUT)
                        .partnerStockUpdateSchedule(event).partnerStockSchedulerDao(partnerStockSchedulerDao).build();
                List<ScheduledFuture<?>> scheduledFutureList = new ArrayList<>();
                scheduledFutureList.add(threadPoolTaskScheduler.schedule(itemStockInScheduleTask,stockinTrigger));
                scheduledFutureList.add(threadPoolTaskScheduler.schedule(itemStockOutScheduleTask,stockOutTrigger));
                scheduledFutureEventsMap.put(event.getStockUpdateId(),scheduledFutureList);
                log.info("Scheduled Stock Update Event For Event Id : {} ", event.getStockUpdateId());
                log.info("Unit Ids For Scheduled Stock Update : {} ", event.getUnitIds());
                log.info("Product Ids For Scheduled Stock Update : {} ", event.getProductIds());

            }
        }
    }


    private Boolean checkIfEventIsValidForSchedule(PartnerStockUpdateSchedule event){
        if(event.getStatus().equalsIgnoreCase(AppConstants.IN_ACTIVE) ||
                (Objects.nonNull(event.getStartTime()) &&
                        AppUtils.getDate(AppUtils.getCurrentDate()).compareTo
                                (AppUtils.getDate(event.getStartTime())) <0)){
            return false;
        }
        return true;
    }
}
