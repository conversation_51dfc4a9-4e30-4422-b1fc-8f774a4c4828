package com.stpl.tech.kettle.channelpartner.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.MagicpinService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderServiceFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicPinStatusUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinDeliveryUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.net.URISyntaxException;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.MAGICPIN;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.MAGICPIN_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;


@Log4j2
@RestController
@RequestMapping(value = API_VERSION +  SEPARATOR + MAGICPIN_ROOT_CONTEXT)
public class MagicpinResource {

    @Autowired
    private MagicpinService magicpinService;


    @Autowired
    private PartnerOrderConverterDependency partnerOrderConverterDependency;

    @PostMapping(value = "order/create", produces = MediaType.APPLICATION_JSON)
    public MagicpinOrderResponse addMagicpinOrder(@RequestBody Object request) throws URISyntaxException, ChannelPartnerException {
        log.info("Request to add Magicpin order : " + new Gson().toJson(request));
        String data = new Gson().toJson(request);
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MagicpinOrderRequest order = null;
        try {
            order = mapper.readValue(data, MagicpinOrderRequest.class);
            return PartnerOrderServiceFactory.getInstance(MAGICPIN,
                    partnerOrderConverterDependency).addOrder(order,false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @PostMapping(value = "delivery/update",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    public void updateMagicpinOrderDeliveryStatus(@RequestBody MagicpinDeliveryUpdate deliveryUpdate){
       String deliveryUpdateJson = new Gson().toJson(deliveryUpdate);
       log.info("Request to update delivery status of magicpin order : {}",deliveryUpdateJson);
       magicpinService.updateDeliveryStatus(deliveryUpdate);
    }


    @PostMapping(value = "order/status/update",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    public void updateOrderStatus(@RequestBody MagicPinStatusUpdate orderUpdate){
        String orderUpdateJson = new Gson().toJson(orderUpdate);
        log.info("Request to update order status of magicpin order ::  {} :: {}",orderUpdate.getOrderId(),orderUpdateJson);
        if(orderUpdate.getStatus().equalsIgnoreCase(PartnerOrderStatus.CANCELLED.name())){
            magicpinService.cancelOrder(orderUpdate);
        }
    }




}
