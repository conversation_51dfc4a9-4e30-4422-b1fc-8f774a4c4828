package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

public enum RateLimitApi {
    SWIGGY_ITEM_TOGGLE(1700) ,SWIGGY_VARIANT_TOGGLE(120) , SWIGGY_ADDON_TOGGLE(1000);


    private final Integer rateLimit;


    RateLimitApi(Integer rateLimit) {
        this.rateLimit = rateLimit;
    }

    public String value() {
        return name();
    }

    public static RateLimitApi fromValue(String v) {
        return valueOf(v);
    }


    public Integer getRateLimit() {
        return rateLimit;
    }
}
