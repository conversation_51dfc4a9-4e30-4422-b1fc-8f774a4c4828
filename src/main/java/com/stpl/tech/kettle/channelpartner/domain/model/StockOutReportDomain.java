package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.net.ntp.TimeStamp;

import java.math.BigDecimal;
import java.util.Date;

@ExcelSheet("Stock Out Partner Report")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class StockOutReportDomain {

    @ExcelField
    Integer unitId;

    @ExcelField
    String unitName;

    @ExcelField
    Integer productId;

    @ExcelField
    String productName;

    @ExcelField
    Boolean stockValue;

    @ExcelField
    String stockOutTime;


    @ExcelField
    BigDecimal currentInventory;

   /* @ExcelField
    Date lastOrderTime;*/




}
