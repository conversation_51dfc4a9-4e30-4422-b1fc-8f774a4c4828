
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "cgst",
    "cgst_percent",
    "id",
    "igst",
    "igst_percent",
    "name",
    "price",
    "sgst",
    "sgst_percent",
    "gst_liability"
})
public class Variant {

    @JsonProperty("cgst")
    private float cgst;
    @JsonProperty("cgst_percent")
    private float cgstPercent;
    @JsonProperty("id")
    private String id;
    @JsonProperty("igst")
    private float igst;
    @JsonProperty("igst_percent")
    private float igstPercent;
    @JsonProperty("name")
    private String name;
    @JsonProperty("price")
    private float price;
    @JsonProperty("sgst")
    private float sgst;
    @JsonProperty("sgst_percent")
    private float sgstPercent;
    @JsonProperty("gst_liability")
    private String gstLiability;

    @JsonProperty("cgst")
    public float getCgst() {
        return cgst;
    }

    @JsonProperty("cgst")
    public void setCgst(float cgst) {
        this.cgst = cgst;
    }

    @JsonProperty("cgst_percent")
    public float getCgstPercent() {
        return cgstPercent;
    }

    @JsonProperty("cgst_percent")
    public void setCgstPercent(float cgstPercent) {
        this.cgstPercent = cgstPercent;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("igst")
    public float getIgst() {
        return igst;
    }

    @JsonProperty("igst")
    public void setIgst(float igst) {
        this.igst = igst;
    }

    @JsonProperty("igst_percent")
    public float getIgstPercent() {
        return igstPercent;
    }

    @JsonProperty("igst_percent")
    public void setIgstPercent(float igstPercent) {
        this.igstPercent = igstPercent;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("price")
    public float getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(float price) {
        this.price = price;
    }

    @JsonProperty("sgst")
    public float getSgst() {
        return sgst;
    }

    @JsonProperty("sgst")
    public void setSgst(float sgst) {
        this.sgst = sgst;
    }

    @JsonProperty("sgst_percent")
    public float getSgstPercent() {
        return sgstPercent;
    }

    @JsonProperty("sgst_percent")
    public void setSgstPercent(float sgstPercent) {
        this.sgstPercent = sgstPercent;
    }

    @JsonProperty("gst_liability")
    public String getGstLiability() {
        return gstLiability;
    }

    @JsonProperty("gst_liability")
    public void setGstLiability(String gstLiability) {
        this.gstLiability = gstLiability;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Variant variant = (Variant) o;

        return new EqualsBuilder()
            .append(cgst, variant.cgst)
            .append(cgstPercent, variant.cgstPercent)
            .append(igst, variant.igst)
            .append(igstPercent, variant.igstPercent)
            .append(price, variant.price)
            .append(sgst, variant.sgst)
            .append(sgstPercent, variant.sgstPercent)
            .append(id, variant.id)
            .append(name, variant.name)
            .append(gstLiability, variant.gstLiability)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(cgst)
            .append(cgstPercent)
            .append(id)
            .append(igst)
            .append(igstPercent)
            .append(name)
            .append(price)
            .append(sgst)
            .append(sgstPercent)
            .append(gstLiability)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "Variant{" +
            "cgst=" + cgst +
            ", cgstPercent=" + cgstPercent +
            ", id='" + id + '\'' +
            ", igst=" + igst +
            ", igstPercent=" + igstPercent +
            ", name='" + name + '\'' +
            ", price=" + price +
            ", sgst=" + sgst +
            ", sgstPercent=" + sgstPercent +
            ", gstLiability='" + gstLiability + '\'' +
            '}';
    }
}
