package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductMappingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.domain.model.Customer;

import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ChannelPartnerCacheService {

    List<PartnerDetail> getPartners();

    Customer getSwiggyCustomer();

    List<PartnerOrderDetail> getNotificationPendingOrders(Date date);

    List<PartnerOrderCacheDetail> getPastOrdersByTime(Date date);

    /*List loadUnitChannelPartnerMappings() throws URISyntaxException;*/

    /*List loadUnitPartnerMenuSequenceMappings();*/

    /*List loadMenuSequences();*/

    List<String> loadPartnerUnitProductMap(Integer partnerId, String region);

    Map<Integer, ProductAlias> loadProductAliasMap(Integer partnerId);

	List<PartnerUnitProductMappingDetail> getPartnerUnitProductMappings(Integer partnerId);

    List<PartnerUnitProductMappingDetail> getPartnerUnitProductMappingsByPartnerUnitBrand(Integer partnerId, Integer unitId, Integer brandId);

    List<PartnerUnitProductPricingDetail> getPartnerUnitProductPricing();

    List<DesiChaiCustomProfiles> getDesiChaiCustomProfiles();
}
