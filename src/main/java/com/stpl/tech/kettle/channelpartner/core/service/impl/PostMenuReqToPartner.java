package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PostReqToPartnerProcessor;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqResponseData;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;


@Log4j2
public class PostMenuReqToPartner extends PostReqToPartnerProcessor {

    //    private EventAbstractFactory eventAbstractFactory;
    private PartnerAbstractFactory partnerAbstractFactory;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public PostMenuReqToPartner(PartnerAbstractFactory partnerAbstractFactory) {
//        this.eventAbstractFactory = eventAbstractFactory;
        this.partnerAbstractFactory = partnerAbstractFactory;
    }


    @Override
    public <T, R> List<R> processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        try {
            List<?> reqObj = super.processRedisEvent(partnerName,partnerActionEvent,res,eventAbstractFactory);
            if (Objects.nonNull(reqObj)) {
                return (List<R>) postReqToPartnerTemp(reqObj);
            }
        } catch (Exception e) {
            log.error("Error processing  redis event::::::::::::::: " + partnerActionEvent.toString(), e);
        }
        return null;
    }


    private MenuReqResponseData processMenuPushRequest(PartnerAbstractFactory partnerAbstractFactory,
                                                       UnitMenuAddVO unitMenuAddVO, String requestId) {
        ClassLoader ccl = Thread.currentThread().getContextClassLoader();
        ClassLoader classLoader = this.getClass().getClassLoader();
        Thread.currentThread().setContextClassLoader(classLoader);
        try {
            MDC.put("request.id", requestId);
            if (Objects.nonNull(unitMenuAddVO)) {
                return partnerAbstractFactory.sendMenuPushReqToPartner(unitMenuAddVO);
            }
        } catch (Exception ex) {
            log.error("Error processing menu task ", ex);
        } finally {
            MDC.clear();
        }
        return null;
    }

    private List<MenuReqResponseData> handleException(Throwable ex) {
        log.error("Exception occurred during asynchronous processing: ", ex);
        return null; // You can customize the response or rethrow the exception as needed
    }

    private  List<?> postReqToPartnerTemp(Object req) throws ChannelPartnerException {
        Gson gson = new Gson();
        TypeToken<List<UnitMenuAddVO>> typeToken = new TypeToken<List<UnitMenuAddVO>>() {
        };
        List<UnitMenuAddVO> unitMenuList = gson.fromJson(gson.toJson(req), typeToken.getType());

        int batchSize = 10;
        ExecutorService executor = Executors.newWorkStealingPool();

        try {
            List<CompletableFuture<List<MenuReqResponseData>>> batchFutures = new ArrayList<>();
            for (List<UnitMenuAddVO> batch : Lists.partition(unitMenuList, batchSize)) {
                CompletableFuture<List<MenuReqResponseData>> batchFuture = CompletableFuture.supplyAsync(() ->
                                batch.stream()
                                        .map(unitMenuAddVO ->
                                                processMenuPushRequest(partnerAbstractFactory, unitMenuAddVO,
                                                        MDC.get(ChannelPartnerUtils.generateID()))
                                        )
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()), executor)
                        .exceptionally(this::handleException);
                batchFutures.add(batchFuture);
            }

            CompletableFuture<List<MenuReqResponseData>> allBatchesFuture = CompletableFuture.allOf(
                    batchFutures.toArray(new CompletableFuture[0])
            ).thenApplyAsync(voidResult -> batchFutures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList()), executor);

            return allBatchesFuture.join();
        } finally {
            executor.shutdown();
        }
    }

    @Override
    public <UnitMenuAddVO> List<?> postReqToPartner(List<UnitMenuAddVO> req) throws ChannelPartnerException {
        Gson gson = new Gson();
        TypeToken<List<UnitMenuAddVO>> typeToken = new TypeToken<List<UnitMenuAddVO>>() {
        };
        List<UnitMenuAddVO> unitMenuList = gson.fromJson(gson.toJson(req), typeToken.getType());

        int batchSize = 10;
        ExecutorService executor = Executors.newWorkStealingPool();

        try {
            List<CompletableFuture<List<MenuReqResponseData>>> batchFutures = new ArrayList<>();
            for (List<UnitMenuAddVO> batch : Lists.partition(unitMenuList, batchSize)) {
                CompletableFuture<List<MenuReqResponseData>> batchFuture = CompletableFuture.supplyAsync(() ->
                                batch.stream()
                                        .map(unitMenuAddVO ->
                                                processMenuPushRequest(partnerAbstractFactory, (com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO) unitMenuAddVO,
                                                        MDC.get(ChannelPartnerUtils.generateID()))
                                        )
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()), executor)
                        .exceptionally(this::handleException);
                batchFutures.add(batchFuture);
            }

            CompletableFuture<List<MenuReqResponseData>> allBatchesFuture = CompletableFuture.allOf(
                    batchFutures.toArray(new CompletableFuture[0])
            ).thenApplyAsync(voidResult -> batchFutures.stream()
                    .map(CompletableFuture::join)
                    .flatMap(List::stream)
                    .collect(Collectors.toList()), executor);

            return allBatchesFuture.join();
        } finally {
            executor.shutdown();
        }
    }
}
