package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PostEventProcessor;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqResponseData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Objects;


@Log4j2
public class PostMenuEventProcessor extends PostEventProcessor {

    private final PartnerAbstractFactory partnerAbstractFactory;
    private final PartnerActionEventDecorator partnerActionEventDecorator;
    private final PartnerMetadataBuilder partnerMetadataBuilder;


    public PostMenuEventProcessor(PartnerAbstractFactory partnerAbstractFactory, PartnerActionEventDecorator partnerActionEventDecorator, PartnerMetadataBuilder partnerMetadataBuilder) {
        this.partnerAbstractFactory = partnerAbstractFactory;
        this.partnerActionEventDecorator = partnerActionEventDecorator;
        this.partnerMetadataBuilder = partnerMetadataBuilder;
    }

    @Override
    public <T, R> List<R> processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        List<R> resultList = this.partnerActionEventDecorator.processRedisEvent(partnerName, partnerActionEvent, res, eventAbstractFactory);
        if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
            Gson gson = new Gson();
            TypeToken<List<MenuReqResponseData>> typeToken = new TypeToken<List<MenuReqResponseData>>() {
            };
            List<MenuReqResponseData> menuReqResponseDataList = gson.fromJson(gson.toJson(resultList), typeToken.getType());
            postEventProcessing(menuReqResponseDataList, EventType.Menu);
            return resultList;
        }
        return null;
    }


    @Override
    public <T> List<PartnerEventResponse> postEventProcessing(List<T> obj, EventType eventType) throws ChannelPartnerException {
        return this.partnerAbstractFactory.postEventProcessing(obj, eventType);
    }
}
