package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class SuccessOrRejectedOrderStep<R, T> extends OrderProcessingStep<R, T> {
    public SuccessOrRejectedOrderStep(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        T successResponse;
        if (Boolean.TRUE.equals(isOrderToBeRejected(partnerOrderDetail))) {
            return partnerOrderManagementService.handlesRejectedOrder(request, partnerOrderDetail, PartnerOrderStates.REJECTED);
        } else {
           successResponse =   partnerOrderManagementService.handlesSuccessFullOrder(request, partnerOrderDetail);
        }
        log.info("\n----------- ,STEP 5, - ,Order Processed Successfully----------- milliseconds {}", (System.currentTimeMillis() - startTime));
        return super.checkNext(request, isManual, successResponse, partnerPrimaryData, partnerOrderDetail,super.nextOrderProcessingStep);
    }

    private boolean isOrderToBeRejected(PartnerOrderDetail partnerOrderDetail) {
        return partnerOrderDetail.getToBeRejected();
    }

}