package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax;

import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinTaxDetails;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TaxStrategy {

    <I> void addItemTax(I item , List<OrderItem> orderItem, Map<String, TaxDataVO> taxMap);

    public <I> BigDecimal getItemPackagingTax(OrderItem orderItem , I item);

    public <R> BigDecimal getOrderLevelPackagingTax(OrderItem orderItem, R request);
}
