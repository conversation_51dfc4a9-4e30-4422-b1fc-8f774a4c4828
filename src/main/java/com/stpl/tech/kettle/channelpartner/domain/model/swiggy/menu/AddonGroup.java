
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "name",
    "addon_free_limit",
    "addon_limit",
    "addon_min_limit",
    "order",
    "addons"
})
public class AddonGroup {

    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("addon_free_limit")
    private Integer addonFreeLimit;
    @JsonProperty("addon_limit")
    private Integer addonLimit;
    @JsonProperty("addon_min_limit")
    private Integer addonMinLimit;
    @JsonProperty("order")
    private Integer order;
    @JsonProperty("addons")
    private List<Addon> addons = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("addon_free_limit")
    public Integer getAddonFreeLimit() {
        return addonFreeLimit;
    }

    @JsonProperty("addon_free_limit")
    public void setAddonFreeLimit(Integer addonFreeLimit) {
        this.addonFreeLimit = addonFreeLimit;
    }

    @JsonProperty("addon_limit")
    public Integer getAddonLimit() {
        return addonLimit;
    }

    @JsonProperty("addon_limit")
    public void setAddonLimit(Integer addonLimit) {
        this.addonLimit = addonLimit;
    }

    @JsonProperty("addon_min_limit")
    public Integer getAddonMinLimit() {
        return addonMinLimit;
    }

    @JsonProperty("addon_min_limit")
    public void setAddonMinLimit(Integer addonMinLimit) {
        this.addonMinLimit = addonMinLimit;
    }

    @JsonProperty("order")
    public Integer getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(Integer order) {
        this.order = order;
    }

    @JsonProperty("addons")
    public List<Addon> getAddons() {
        return addons;
    }

    @JsonProperty("addons")
    public void setAddons(List<Addon> addons) {
        this.addons = addons;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("id", id).append("name", name).append("addonFreeLimit", addonFreeLimit).append("addonLimit", addonLimit).append("addonMinLimit", addonMinLimit).append("order", order).append("addons", addons).append("additionalProperties", additionalProperties).toString();
    }

}
