package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "CafeMenuAutoPush")
public class CafeMenuAutoPush {

    @Id
    private String id;
    private Integer unitId;
    private String unitName;
    private Integer brandId;
    private Boolean zomatoMenu;
    private Boolean swiggyMenu;
    private Date lastUpdatedTime;
    private String lastUpdatedTimeString;
    private Integer employeeId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getZomatoMenu() {
        return zomatoMenu;
    }

    public void setZomatoMenu(Boolean zomatoMenu) {
        this.zomatoMenu = zomatoMenu;
    }

    public Boolean getSwiggyMenu() {
        return swiggyMenu;
    }

    public void setSwiggyMenu(Boolean swiggyMenu) {
        this.swiggyMenu = swiggyMenu;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public String getLastUpdatedTimeString() {
        return lastUpdatedTimeString;
    }

    public void setLastUpdatedTimeString(String lastUpdatedTimeString) {
        this.lastUpdatedTimeString = lastUpdatedTimeString;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }
}
