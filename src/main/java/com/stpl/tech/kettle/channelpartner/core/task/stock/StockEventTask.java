package com.stpl.tech.kettle.channelpartner.core.task.stock;

import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqResponseData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;

import java.util.Objects;
import java.util.concurrent.Callable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Log4j2
public class StockEventTask implements Callable<StockReqResponseData> {
    private PartnerAbstractFactory partnerAbstractFactory;
    private volatile StockReqMetadata stockReqMetadata;
    private volatile String requestId;

    @Override
    public StockReqResponseData call() throws Exception {
        try {
            MDC.put("request.id", requestId);
            if (Objects.nonNull(stockReqMetadata)) {
                return partnerAbstractFactory.sendStockUpdateReqToPartner(stockReqMetadata);
            }
        } catch (Exception ex) {
            log.error("Error processing stock task ", ex);
        } finally {
            MDC.clear();
        }
        return null;
    }

}
