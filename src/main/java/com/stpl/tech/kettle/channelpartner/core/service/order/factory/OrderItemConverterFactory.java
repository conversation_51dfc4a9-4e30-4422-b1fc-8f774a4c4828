package com.stpl.tech.kettle.channelpartner.core.service.order.factory;


import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.OrderItemConverterService;
import org.apache.commons.lang3.NotImplementedException;

import java.util.HashMap;
import java.util.Map;


public class OrderItemConverterFactory {



    private static Map<String, OrderItemConverterService> orderItemConverterServiceMap = new HashMap<>();

    private OrderItemConverterFactory(){};

    public static OrderItemConverterService  getInstance(String partnerName ,
                                                         PartnerOrderConverterDependency partnerOrderConverterDependency){
        if(!orderItemConverterServiceMap.containsKey(partnerName)){
            orderItemConverterServiceMap.put(partnerName,getPartnerItemConverterInstance(partnerName,partnerOrderConverterDependency));
        }
        return orderItemConverterServiceMap.get(partnerName);
    }

    private static OrderItemConverterService getPartnerItemConverterInstance(String partnerName,
                                                                             PartnerOrderConverterDependency partnerOrderConverterDependency){
         switch (partnerName){
             case "MAGICPIN":
                 return  new OrderItemConverterService(
                         partnerOrderConverterDependency.getPartnerItemConverterService(partnerName),
                         partnerOrderConverterDependency.getdiscountStrategy(partnerName),
                         partnerOrderConverterDependency.getMasterDataCache(),
                         partnerOrderConverterDependency.getTaxStrategy(partnerName),
                         partnerOrderConverterDependency.getEnvironmentProperties(),
                         partnerOrderConverterDependency
                 );
             default:
                 throw new NotImplementedException("Not Implemented yet For This Partner");
         }
    }








}
