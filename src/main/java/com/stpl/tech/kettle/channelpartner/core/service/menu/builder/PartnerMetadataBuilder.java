package com.stpl.tech.kettle.channelpartner.core.service.menu.builder;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.CommonMetadataValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;


public class PartnerMetadataBuilder {

    public ChannelPartnerDataCache channelPartnerDataCache;
    public MasterDataCache masterDataCache;
    public EnvironmentProperties environmentProperties;
    public RedisPublisher redisPublisher;
    public PartnerMenuService partnerMenuService;
    public WebServiceHelper webServiceHelper;
    public PartnerOrderService partnerOrderService;
    public PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;
    public CommonPartnerEventNotificationService<String, SlackNotification, Boolean, String> commonPartnerEventNotificationService;
    public CommonMetadataValidationService commonMetadataValidationService;
    public OrderValidationService orderValidationService ;

    public PartnerMetadataBuilder defaultBuilder(PartnerMenuConverterDependency partnerMenuConverterDependency) {
        this.channelPartnerDataCache = partnerMenuConverterDependency.getChannelPartnerDataCache();
        this.masterDataCache = partnerMenuConverterDependency.getMasterDataCache();
        this.redisPublisher = partnerMenuConverterDependency.getRedisPublisher();
        this.environmentProperties = partnerMenuConverterDependency.getEnvironmentProperties();
        this.partnerMenuService = partnerMenuConverterDependency.getPartnerMenuService();
        this.webServiceHelper = partnerMenuConverterDependency.getWebServiceHelper();
        this.partnerOrderService = partnerMenuConverterDependency.getPartnerOrderService();
        this.partnerUnitStockSnapshotDao = partnerMenuConverterDependency.getPartnerUnitStockSnapshotDao();
        this.commonPartnerEventNotificationService = partnerMenuConverterDependency.getCommonPartnerEventNotificationService();
        this.commonMetadataValidationService = partnerMenuConverterDependency.getCommonMetadataValidationService();
        this.orderValidationService=partnerMenuConverterDependency.getOrderValidationService();
        return this;
    }
}
