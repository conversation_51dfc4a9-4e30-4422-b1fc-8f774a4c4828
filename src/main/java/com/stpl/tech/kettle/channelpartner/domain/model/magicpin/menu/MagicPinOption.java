package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinOption implements Serializable {
    private String id;
    private String title;
    private double price;
    private boolean inStock;
    private int foodType;
    private List<MagicPinCustomization> customizations;
    private List<String> modifierIds;
    private boolean addon;//TOdo -Ishman verify this field
    private Integer rank;

}
