package com.stpl.tech.kettle.channelpartner.controller;


import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.CafeLookUpService;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.CafeStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerOfflineStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.SwiggyCafeStatusDataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.ZomatoCafeStatusDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.CAFE_LOOKUP_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;


@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CAFE_LOOKUP_ROOT_CONTEXT) // 'v1/lookUp'
public class CafeLookUpResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(CafeLookUpResources.class);

    @Autowired
    private CafeLookUpService cafeLookUpService;
    @Autowired
    private MasterDataCache masterDataCache;


    @Autowired
    private SwiggyCafeStatusDataDao swiggyCafeStatusDataDao;


    @Autowired
    private ZomatoCafeStatusDao zomatoCafeStatusDao;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CafeStatusChannelPartnerService cafeStatusChannelPartnerService;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;


//    @Scheduled(fixedRate = 900000)
    @Scheduled(cron = "0 0/1 10-19 * * *", zone = "GMT+05:30")
    public void getSwiggyCafeStatus() throws URISyntaxException, ChannelPartnerException {
        if (ChannelPartnerUtils.isProd(props.getEnvType())) {
            LOG.info("Request to lookup  swiggy status restaurant ");
            cafeLookUpService.getSwiggyCafeStatus();
            cafeLookUpService.unitClosureNotify(AppConstants.CHANNEL_PARTNER_SWIGGY);
        }
        //don't commit
        cafeLookUpService.getSwiggyCafeStatus();
        cafeLookUpService.unitClosureNotify(AppConstants.CHANNEL_PARTNER_SWIGGY);
    }

    @Scheduled(fixedRate = 1200000)
    public void getZomatoCafeStatus() throws URISyntaxException, ChannelPartnerException {
        if (ChannelPartnerUtils.isProd(props.getEnvType()) && props.runCronForZomatoCafeStatus()) {
            LOG.info("Request to lookup Zomato status restaurant ");
            cafeLookUpService.getZomatoCafeStatus();
            cafeLookUpService.unitClosureNotify(AppConstants.CHANNEL_PARTNER_ZOMATO);
        }
    }

    @RequestMapping(method = RequestMethod.GET,value = "test-notification-swiggy-status")
    public void testNotificationForSwiggyStatus() throws URISyntaxException, ChannelPartnerException {
            LOG.info("Request to lookup  swiggy status restaurant ");
            cafeLookUpService.getSwiggyCafeStatus_();
    }

    @RequestMapping(method = RequestMethod.GET,value = "update-swiggy-status")
    public void updateSwiggyCafeStatus(@RequestParam (name = "unitId", required = false) int unitId,@RequestParam (name = "outletId", required = false) String outletId){
        LOG.info("Entered Update Swiggy Cafe Status");
        //cafeLookUpService.updateSwiggyCafeStatus(outletId,unitId);
    }

//    @Scheduled(fixedRate = 900000)
//    public void getZomatoCafeStatus() throws URISyntaxException, ChannelPartnerException {
//        if(ChannelPartnerUtils.isProd(props.getEnvType())) {
//            LOG.info("Request to lookup  zomato cafe ");
//            try {
//                cafeLookUpService.getZomatoCafeStatus();
//            } catch (Exception e) {
//                LOG.error("not able to  fetch cafe status from zomato");
//            }
//        }
//    }

    @RequestMapping(method = RequestMethod.GET, value = "all-zomato-status", produces = MediaType.APPLICATION_JSON)
    public List<ZomatoCafeStatusData> getZomatoCafeStatusData() {
        return cafeLookUpService.getZomatoCafeStatusData();
    }

    @RequestMapping(method = RequestMethod.GET, value = "cafe-status-channel-partner", produces = MediaType.APPLICATION_JSON)
    public Map<String,List<CafeStatusChannelPartner>> getCafeStatusChannelPartnerUpdate(){
        Map<String,List<CafeStatusChannelPartner>> cafeData = new HashMap<>();
        List<CafeStatusChannelPartner> data2 = cafeStatusChannelPartnerService.getInActiveCafe(AppConstants.IN_ACTIVE);
        cafeData.put("cafeStatusData",data2);
        return cafeData;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-off-requests", produces = MediaType.APPLICATION_JSON)
    public Map<String,List<CafeRaiseRequestApproval>> getUnitSwitchOffRequests(){
        Map<String,List<CafeRaiseRequestApproval>> uniOffData = new HashMap<>();
        List<CafeRaiseRequestApproval> data = cafeStatusChannelPartnerService.getUnitSwitchOffRequests(AppConstants.APPROVED_REQUEST);
        uniOffData.put("unitOffRequest",data);
        return uniOffData;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-request-unit-off-dashboard", produces = MediaType.APPLICATION_JSON)
    public boolean updatedRequestForUnit(@RequestBody CafeRaiseRequestApproval request){
       return cafeStatusChannelPartnerService.updatedRequestForUnit(request);

    }

    @RequestMapping(method = RequestMethod.GET, value = "cafe-status-for-unit", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, CafeStatus> getCafeStatusForUnit(@RequestParam Integer unitId) {
        Map<Integer, CafeStatus> cafeStatusList = new HashMap<>();
        if (channelPartnerDataCache.getUnitPartnerBrandMappingData(unitId.toString(), AppConstants.CHANNEL_PARTNER_ZOMATO) != null
            || channelPartnerDataCache.getUnitPartnerBrandMappingData(unitId.toString(), AppConstants.CHANNEL_PARTNER_SWIGGY) != null) {
            cafeStatusList.put(ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID, cafeLookUpService.getCafeStatusForUnit
                    (unitId, ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID));
        }
        if (channelPartnerDataCache.getUnitPartnerBrandMappingData(unitId.toString().concat("GNT"), AppConstants.CHANNEL_PARTNER_ZOMATO) != null
            || channelPartnerDataCache.getUnitPartnerBrandMappingData(unitId.toString().concat("GNT"), AppConstants.CHANNEL_PARTNER_SWIGGY) != null) {
            cafeStatusList.put(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID, cafeLookUpService.getCafeStatusForUnit
                    (unitId, ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID));
        }
        return cafeStatusList;
    }



    @RequestMapping(method = RequestMethod.GET, value = "cafe-status", produces = MediaType.APPLICATION_JSON)
    public List<CafeStatus> getCafeStatus(@RequestParam Integer brandId) {
        ExecutorService taskExecutor = Executors.newFixedThreadPool(5);
        List<UnitBasicDetail> units = masterDataCache.getAllUnits();
        List<CafeStatus> cafeStatusList = new ArrayList<>();
        for (UnitBasicDetail unit : units) {
            taskExecutor.execute(() -> {
                CafeStatus cafeStatus = new CafeStatus();
                SwiggyCafeStatusData swiggy = brandId.equals(ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID) ? swiggyCafeStatusDataDao.findByUnitId(unit.getId()) : null;
                ZomatoCafeStatusData zomato = zomatoCafeStatusDao.findByUnitIdAndBrandId(unit.getId(), brandId);
                cafeStatus.setName(unit.getName());
                cafeStatus.setCafeLive(unit.isLive());
                cafeStatus.setCity(unit.getCity());
                if (swiggy != null) {
                    cafeStatus.setSwiggyStatus(swiggy.getPartnerStatus());
                }
                if (zomato != null) {
                    cafeStatus.setZomatoStatus(zomato.getPartnerStatus());
                    cafeStatus.setZomatoStatusFromGetApi(zomato.isZomatoStatusFromGetApi());
                }
                cafeStatus.setUnitStatus(unit.getStatus());
                cafeStatus.setRegion(unit.getRegion());
                LOG.info(JSONSerializer.toJSON(cafeStatus));
                if (cafeStatus != null) {
                    cafeStatusList.add(cafeStatus);
                }
//                        }
            });
        }

        taskExecutor.shutdown();
        try {
            taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            LOG.error("Error in completion of  threads", e);

        }
        return cafeStatusList;
    }

    @RequestMapping(method = RequestMethod.GET, value = "cafe-offline-status", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerOfflineStatus> getCafesOfflineOnPartner() {
        return cafeLookUpService.getCafesOfflineOnPartner();
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/cafe-offline-status", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerOfflineStatus> getUnitCafesOfflineOnPartner(@RequestBody List<Integer> unitIds) {
        LOG.info("Getting Unit Cafe Offline Partner for {} units",unitIds.size());
        return cafeLookUpService.getUnitCafesOfflineOnPartner(unitIds);
    }

}
