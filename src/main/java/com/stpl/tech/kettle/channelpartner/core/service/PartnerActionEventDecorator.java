package com.stpl.tech.kettle.channelpartner.core.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

public abstract class PartnerActionEventDecorator {

    protected ThreadPoolExecutor threadPoolExecutor;



    public <T, R> List<R> processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            T eventD = mapper.readValue(new Gson().toJson(partnerActionEvent.getEventData()), res);
            return eventAbstractFactory.prepareMetadata(eventD, partnerActionEvent.getEventType(), partnerName);
        } catch (Exception e) {
            throw new ChannelPartnerException(new ChannelPartnerError( "Error while Deserialising partner action event data to type " + res, e.toString()));
        }
    }

}
