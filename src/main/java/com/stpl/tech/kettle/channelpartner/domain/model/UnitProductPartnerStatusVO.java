package com.stpl.tech.kettle.channelpartner.domain.model;

import java.util.List;

public class UnitProductPartnerStatusVO {

    private List<Integer> productIds;
    private List<Integer> partnerIds;
    private Integer brandId;
    private Integer unitId;
    private Boolean status;
    private Boolean forceStockOut;

    public List<Integer> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Integer> productIds) {
        this.productIds = productIds;
    }

    public List<Integer> getPartnerIds() {
        return partnerIds;
    }

    public void setPartnerIds(List<Integer> partnerIds) {
        this.partnerIds = partnerIds;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Boolean getForceStockOut() { return this.forceStockOut; }

    public void setForceStockOut(Boolean forceStockOut) { this.forceStockOut = forceStockOut; }

}
