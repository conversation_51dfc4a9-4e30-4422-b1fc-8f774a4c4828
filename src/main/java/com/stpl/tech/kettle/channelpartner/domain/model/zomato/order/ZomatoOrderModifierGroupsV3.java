package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "group_id",
        "group_name",
        "variants"
})
public class ZomatoOrderModifierGroupsV3 {
	
	@JsonProperty("group_id")
    private String groupId;
    @JsonProperty("group_name")
    private String groupName;
    @JsonProperty("variants")
    private List<ZomatoOrderVariantsV3> variants;
    
    @JsonProperty("group_id")
	public String getGroupId() {
		return groupId;
	}
    
    @JsonProperty("group_id")
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	
	@JsonProperty("group_name")
	public String getGroupName() {
		return groupName;
	}
	
	@JsonProperty("group_name")
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	@JsonProperty("variants")
	public List<ZomatoOrderVariantsV3> getVariants() {
		return variants;
	}
	
	@JsonProperty("variants")
	public void setVariants(List<ZomatoOrderVariantsV3> variants) {
		this.variants = variants;
	}

	@Override
	public String toString() {
		return "ZomatoOrderModifierGroupsV3 [groupId=" + groupId + ", groupName=" + groupName + ", variants=" + variants
				+ "]";
	}
    
	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(groupId).append(groupName).append(variants).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderModifierGroupsV3) == false) {
            return false;
        }
        ZomatoOrderModifierGroupsV3 rhs = ((ZomatoOrderModifierGroupsV3) other);
        return new EqualsBuilder().append(groupId, rhs.groupId).append(groupName, rhs.groupName).append(variants, rhs.variants).isEquals();
    }

}
