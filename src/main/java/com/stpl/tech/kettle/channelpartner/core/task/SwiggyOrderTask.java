package com.stpl.tech.kettle.channelpartner.core.task;

import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public class SwiggyOrderTask implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyOrderTask.class);

    private SwiggyService swiggyService;
    private volatile SwiggyOrderRequest swiggyOrderRequest;
    private volatile PartnerOrderDetail partnerOrderDetail;
    private volatile Order order;
    private volatile Boolean isManual;
    private volatile Boolean skipInventoryCheck;
    private volatile String requestId;

    public PartnerOrderDetail getPartnerOrderDetail() {
        return partnerOrderDetail;
    }

    public void setPartnerOrderDetail(PartnerOrderDetail partnerOrderDetail) {
        this.partnerOrderDetail = partnerOrderDetail;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Boolean getManual() {
        return isManual;
    }

    public void setManual(Boolean manual) {
        isManual = manual;
    }

    public Boolean getSkipInventoryCheck() {
        return skipInventoryCheck;
    }

    public void setSkipInventoryCheck(Boolean skipInventoryCheck) {
        this.skipInventoryCheck = skipInventoryCheck;
    }

    public SwiggyOrderRequest getSwiggyOrderRequest() {
        return swiggyOrderRequest;
    }

    public void setSwiggyOrderRequest(SwiggyOrderRequest swiggyOrderRequest) {
        this.swiggyOrderRequest = swiggyOrderRequest;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public SwiggyService getSwiggyService() {
        return swiggyService;
    }

    public void setSwiggyService(SwiggyService swiggyService) {
        this.swiggyService = swiggyService;
    }

    @Override
    public void run() {
        try {
            MDC.put("request.id", requestId);
            if (skipInventoryCheck == null) {
                skipInventoryCheck = false;
            }
            swiggyService.placeOrder(partnerOrderDetail, order, isManual, skipInventoryCheck);
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error processing swiggy order task ", ex);
        }
        try {
            MDC.put("request.id", requestId);
            if (!swiggyOrderRequest.isOrderEdit()) {
                swiggyService.notifyOrder(partnerOrderDetail, isManual);
            } else {
                LOG.info("Skipping swiggy confirmation as order edit flag is {}", swiggyOrderRequest.isOrderEdit());
            }
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error notifying swiggy order task ", ex);
        }
    }
}
