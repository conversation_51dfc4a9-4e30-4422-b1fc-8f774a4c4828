package com.stpl.tech.kettle.channelpartner.domain.model;


import com.stpl.tech.master.domain.model.UnitStatus;


public class CafeStatus {


    private String name;
    private Boolean swiggyStatus;
    private Boolean isCafeLive;
    private UnitStatus unitStatus;
    private Boolean zomatoStatus;
    private Boolean zomatoStatusFromGetApi;
    private String city;
    private String region;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getSwiggyStatus() {
        return swiggyStatus;
    }

    public void setSwiggyStatus(Boolean swiggyStatus) {
        this.swiggyStatus = swiggyStatus;
    }

    public Boolean getCafeLive() {
        return isCafeLive;
    }

    public void setCafeLive(Boolean cafeLive) {
        isCafeLive = cafeLive;
    }

    public UnitStatus getUnitStatus() {
        return unitStatus;
    }

    public void setUnitStatus(UnitStatus unitStatus) {
        this.unitStatus = unitStatus;
    }

    public Boolean getZomatoStatus() {
        return zomatoStatus;
    }

    public void setZomatoStatus(Boolean zomatoStatus) {
        this.zomatoStatus = zomatoStatus;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Boolean getZomatoStatusFromGetApi() {
        return zomatoStatusFromGetApi;
    }

    public void setZomatoStatusFromGetApi(Boolean zomatoStatusFromGetApi) {
        this.zomatoStatusFromGetApi = zomatoStatusFromGetApi;
    }
}
