package com.stpl.tech.kettle.channelpartner.domain.model.magicpin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PartnerEventResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 742210194742463777L;

    private String eventType;
    private String response;
    @Builder.Default
    private String error="";
    private Integer code;
}
