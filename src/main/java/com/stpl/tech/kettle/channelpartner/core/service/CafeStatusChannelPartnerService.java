package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;

import java.util.List;

public interface CafeStatusChannelPartnerService {

    List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate);

    void saveCafeStatus(CafeStatusChannelPartner cafeStatus);

    CafeStatusChannelPartner deleteActivatedCafe(Integer id);

    List<CafeRaiseRequestApproval> getUnitSwitchOffRequests(String actionTaken);

    boolean updatedRequestForUnit(CafeRaiseRequestApproval request);

    public Boolean checkIfActiveCafeClosureRequestExists(Integer unitId , Integer brandId , Integer partnerId);

    public List<Integer> getActiveProductClosureRequestProductIds(Integer unitId, Integer brandId , Integer partnerId);

    public List<Integer> setKnockAppStockOutProducts(UnitProductsStockEvent event, UnitPartnerBrandKey key);
}
