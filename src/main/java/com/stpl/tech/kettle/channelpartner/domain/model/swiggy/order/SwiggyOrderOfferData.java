package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SwiggyOrderOfferData {

    @JsonProperty("discount_cap")
    private SwiggyValue discountCap;

    @JsonProperty("share_percentage")
    private SwiggySharePercentage sharePercentage;

    @JsonProperty("selective_item_benefit")
    private SwiggySelectiveItemBenefit swiggySelectiveItemBenefit;

    @JsonProperty("min_cart_value")
    private SwiggyValue minCartValue;

    @JsonProperty("discount_cap")
    public SwiggyValue getDiscountCap() {
        return discountCap;
    }

    public void setDiscountCap(SwiggyValue discountCap) {
        this.discountCap = discountCap;
    }

    @JsonProperty("share_percentage")
    public SwiggySharePercentage getSharePercentage() {
        return sharePercentage;
    }

    public void setSharePercentage(SwiggySharePercentage sharePercentage) {
        this.sharePercentage = sharePercentage;
    }

    @JsonProperty("selective_item_benefit")
    public SwiggySelectiveItemBenefit getSwiggySelectiveItemBenefit() {
        return swiggySelectiveItemBenefit;
    }

    public void setSwiggySelectiveItemBenefit(SwiggySelectiveItemBenefit swiggySelectiveItemBenefit) {
        this.swiggySelectiveItemBenefit = swiggySelectiveItemBenefit;
    }

    @JsonProperty("min_cart_value")
    public SwiggyValue getMinCartValue() {
        return minCartValue;
    }

    public void setMinCartValue(SwiggyValue minCartValue) {
        this.minCartValue = minCartValue;
    }
}
