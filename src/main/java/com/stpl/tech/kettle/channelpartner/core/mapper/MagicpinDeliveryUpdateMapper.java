package com.stpl.tech.kettle.channelpartner.core.mapper;

import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinDeliveryUpdate;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MagicpinDeliveryUpdateMapper {
    MagicpinDeliveryUpdateMapper INSTANCE = Mappers.getMapper(MagicpinDeliveryUpdateMapper.class);

    @Mapping(target = "riderContact",source = "riderNumber")
    PartnerDeliveryStatus toPartnerDelivery(MagicpinDeliveryUpdate deliveryUpdate);
}
