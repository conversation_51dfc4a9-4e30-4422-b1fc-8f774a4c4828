package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import lombok.extern.log4j.Log4j2;

import java.util.HashMap;
import java.util.Map;
@Log4j2
public class ConvertAndValidateOrderStep<R, T> extends OrderProcessingStep<R, T> {
    private final OrderProcessingStep<R, T> orderExceptionStep;

    public ConvertAndValidateOrderStep(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
        orderExceptionStep = new OrderExceptionHandler<>(builder);
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        try {
            Order order = super.partnerOrderManagementService.convertOrder(partnerOrderDetail, isManual, partnerPrimaryData);
            Map<Integer, StateTaxVO> partnerProductTaxMap = getPartnerProductTaxMap();
            partnerOrderManagementService.checkOrder(partnerOrderDetail, order, partnerProductTaxMap, isManual,
                    partnerPrimaryData);
            log.info("\n----------- ,STEP 3, - ,Order Converted , Persisted in Mongo Db and Validated in ---------------------- , milliseconds {}",(System.currentTimeMillis() - startTime));
            return super.checkNext(request, isManual, (T) order, partnerPrimaryData, partnerOrderDetail,nextOrderProcessingStep);
        } catch (Exception e) {
            return orderExceptionStep.handleExceptionInProcessOrder(request, isManual, response, partnerPrimaryData,
                    partnerOrderDetail, e, "CONVERT_AND_VALIDATE_ORDER");
        }
    }

    private Map<Integer, StateTaxVO> getPartnerProductTaxMap() {
        return new HashMap<>();
    }

}
