package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Log4j2
public class MenuPreProcessorLayer extends PartnerBaseDecorator {

    public MenuPreProcessorLayer(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
    }


    @Override
    public <T, R> R preProcessData(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds, Class<R> returnType) throws ChannelPartnerException {
        UnitMenuAddVO unitMenuAddVO;
        try {
            unitMenuAddVO = (UnitMenuAddVO) reqObj;
            return returnType.cast(processMenuEvent(unitMenuAddVO, requestType, partnerIds));
        } catch (Exception e) {
            throw new ChannelPartnerException("Unable to cast to UnitMenuAddVO object .This exception has occurred because menu event req is recieved but req object is of type :::::{} :::::::" + reqObj.getClass().getName() + e);
        }
    }

    private PartnerActionEvent processMenuEvent(UnitMenuAddVO unitMenuAddVO, PartnerRequestType requestType, List<Integer> partnerIds) throws ChannelPartnerException {
        switch (requestType) {
            case UPDATE_ALL_UNIT_MENU:
                return pushMenuToUnits(unitMenuAddVO);
            case SCHEDULED_MENU_PUSH:
                return scheduledMenuPushProcessing();
            default:
                throw new ChannelPartnerException("No Menu Event found for " + requestType.name());
        }
    }

    private PartnerActionEvent scheduledMenuPushProcessing() {
        Date date = ChannelPartnerUtils.getCurrentTimestamp();
        MenuType type = MenuType.getCurrentMenuType(date, AppConstants.CHAAYOS_BRAND_ID);
        if (environmentProperties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);
            PartnerActionEvent event = new PartnerActionEvent();
            event.setEventType(PartnerActionEventType.SCHEDULED_MENU_PUSH);
            event.setEventData(unitMenu);
            return event;
            /*for (Integer partnerId : partnerIds) {
                PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
                if (partnerDetail != null) {
                    log.info("PUBLISHING REDIS EVENT " + event.getEventType().name() + " topic: "
                            + partnerDetail.getPartnerName());
                    String message = new Gson().toJson(event);
                    redisPublisher.publish(partnerDetail.getPartnerName(), message);
                } else {
                    log.error("Error publishing SCHEDULED_MENU_PUSH event: partner id " + partnerId + " is not valid.");
                }
            }*/

        }
        return null;
    }

    private List<Integer> getUnitIdsForMenuPush() {
        List<Integer> unit = new ArrayList<>();
        if (EnvType.PROD.equals(environmentProperties.getEnvType()) || EnvType.SPROD.equals(environmentProperties.getEnvType())) {
            List<UnitBasicDetail> units = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : units) {
                if (unitBasicDetail.isLive() && UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus())) {
                    unit.add(unitBasicDetail.getId());
                }
            }
        } else {
            unit.add(10000);
        }
        return unit;
    }

    private PartnerActionEvent pushMenuToUnits(UnitMenuAddVO request) throws ChannelPartnerException {
        PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
        if (partnerDetail != null) {
            List<Integer> partnerIds = new ArrayList<>(1);
            partnerIds.add(partnerDetail.getKettlePartnerId());
            return pushMenuEventToQueue(partnerIds, request, PartnerActionEventType.UPDATE_ALL_UNIT_MENU, request.getBrandId());
        } else {
            throw new ChannelPartnerException("Partner id is not valid!");
        }
    }

    private PartnerActionEvent pushMenuEventToQueue(List<Integer> partnerIds, Object data, PartnerActionEventType
            eventType, Integer brandId) {
        PartnerActionEvent event = new PartnerActionEvent();
        event.setPartnerIds(partnerIds);
        event.setEventType(eventType);
        event.setBrandId(brandId);
        event.setEventData(data);
        event.setPartner(true);
        return event;
    }

}
