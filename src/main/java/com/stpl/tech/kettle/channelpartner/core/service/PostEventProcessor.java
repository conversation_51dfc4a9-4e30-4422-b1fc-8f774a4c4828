package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;

import java.util.List;

public abstract class PostEventProcessor extends PartnerActionEventDecorator{
    public abstract <T> List<PartnerEventResponse> postEventProcessing(List<T> obj, EventType eventType) throws ChannelPartnerException;
}
