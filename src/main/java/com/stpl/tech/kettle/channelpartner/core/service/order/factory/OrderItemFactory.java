package com.stpl.tech.kettle.channelpartner.core.service.order.factory;


import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.converters.AbstractConverters;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItems;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppUtils;

import java.math.BigDecimal;
import java.util.Map;


public class OrderItemFactory {
    public static  <I> OrderItem createOrderItem(String partnerName , Product product , I item) {
        OrderItem orderItem = new OrderItem();
        switch (partnerName) {
            case "MAGICPIN":
                MagicpinOrderItems magicPinItem = (MagicpinOrderItems) item;
                orderItem.setProductId(product.getId());
                orderItem.setProductName(product.getName());
                orderItem.setItemName(magicPinItem.getDisplayText());
                boolean superCombo = product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID;
                orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
                orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
                orderItem.setQuantity(magicPinItem.getQuantity().intValue());

                //Assigning category and subCategory to OrderItem
               // orderItem.setSourceCategory();
               // orderItem.setSourceSubCategory();
                ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
                complimentaryDetail.setIsComplimentary(false);
                orderItem.setComplimentaryDetail(complimentaryDetail);
                orderItem.setBillType(product.getBillType());
                orderItem.setCode(product.getTaxCode());
                orderItem.setBookedWastage(false);
                orderItem.setTakeAway(false);
                orderItem.setDiscountDetail(getDiscountDetail());
                orderItem.setPartnerTaxType(magicPinItem.getTaxLiability());
                return orderItem;
            default:
                throw new IllegalArgumentException("Unknown Partner Name");
        }
    }

    public static OrderItem createPaidAddonProduct(String partnerName , Map<String, TaxDataVO> taxMap,
                                                   Product paidAddonProduct , PartnerItemData partnerItemData
    , PartnerItemAddonData partnerItemAddonData){
        switch (partnerName){
            case "MAGICPIN" :
                OrderItem orderItem = new OrderItem();
                orderItem.setProductId(paidAddonProduct.getId());
                orderItem.setProductName(paidAddonProduct.getName());
                orderItem.setProductCategory(new IdCodeName(paidAddonProduct.getType(), "", ""));
                orderItem.setProductSubCategory(new IdCodeName(paidAddonProduct.getSubType(), "", ""));
                orderItem.setQuantity(partnerItemData.getQuantity());
                BigDecimal unitCost = partnerItemAddonData.getUnitPrice();
                orderItem.setPrice(unitCost);
                orderItem.setTotalAmount(AppUtils.multiply(unitCost, BigDecimal.valueOf(partnerItemData.getQuantity())));
                ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
                complimentaryDetail.setIsComplimentary(false);
                orderItem.setComplimentaryDetail(complimentaryDetail);
                orderItem.setBillType(paidAddonProduct.getBillType());
                orderItem.setCode(paidAddonProduct.getTaxCode());
                orderItem.setBookedWastage(false);
                orderItem.setTakeAway(false);
                //orderItem.setTaxDeductedByPartner(TaxPayingEntity.PARTNER.getZomato().equalsIgnoreCase(item.getTaxes().get(0).getType()));
                // orderItem.setPartnerTaxType(item.getTaxes().get(0).getType());
                ProductPrice addonProductPrice = paidAddonProduct.getPrices().get(0);
                orderItem.setOriginalPrice(addonProductPrice.getPrice());
                orderItem.setDimension(addonProductPrice.getDimension());
                RecipeDetail recipe = addonProductPrice.getRecipe();
                orderItem.setRecipeId(recipe.getRecipeId());
                // orderItem.setTotalAmount(BigDecimal.valueOf(item.getTotalCost()));
                DiscountDetail discountDetail = getDiscountDetail(partnerItemAddonData);
                orderItem.setDiscountDetail(discountDetail);
                orderItem.setAmount(AppUtils.subtract(orderItem.getTotalAmount(), discountDetail.getTotalDiscount()));
                orderItem.setPartnerTaxType(partnerItemData.getTaxPayingEntity());
                return orderItem;
            default:
                throw new IllegalArgumentException("Unknown Partner Name");
        }

    }

    private static DiscountDetail getDiscountDetail(PartnerItemAddonData partnerItemAddonData){
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(partnerItemAddonData.getDiscount());
        PercentageDetail pDetail = new PercentageDetail();
        pDetail.setPercentage(partnerItemAddonData.getDiscountPercent());
        pDetail.setValue(partnerItemAddonData.getDiscount());
        discountDetail.setDiscount(pDetail);
        return discountDetail;
    }

    private static DiscountDetail getDiscountDetail(){
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        PercentageDetail pDetail = new PercentageDetail();
        pDetail.setPercentage(BigDecimal.ZERO);
        pDetail.setValue(BigDecimal.ZERO);
        discountDetail.setDiscount(pDetail);
        return discountDetail;
    }

    public static PartnerItemData getPartnerItemDataForRecommendedItem(String partnerName , PartnerItemAddonData partnerItemAddonData) {
        switch (partnerName){
            case "MAGICPIN":
               return PartnerItemData.builder().itemId(partnerItemAddonData.getPartnerItemId()).itemName(partnerItemAddonData.getName())
                        .quantity(partnerItemAddonData.getQuantity()).productId(partnerItemAddonData.getProductId()).build();
            default:
                throw new IllegalArgumentException("Unknown Partner Name");
        }

    }

    public static <I> I getPartnerItemForRecommendationProduct(String partnerName,I item , PartnerItemAddonData partnerItemAddonData
    ,PartnerItemData partnerItemData){
        switch (partnerName){
            case "MAGICPIN" :
                return (I) MagicpinOrderItems.builder().thirdPartyId(partnerItemAddonData.getPartnerItemId())
                        .amount(partnerItemAddonData.getUnitPrice().floatValue()).displayText(partnerItemAddonData.getName())
                        .discount(partnerItemAddonData.getDiscount().floatValue())
                        .quantity(partnerItemAddonData.getQuantity().floatValue()).taxLiability(partnerItemData.getTaxPayingEntity())
                        .build();
            default:
                throw new IllegalArgumentException("Unknown Partner Name");
        }

    }
}
