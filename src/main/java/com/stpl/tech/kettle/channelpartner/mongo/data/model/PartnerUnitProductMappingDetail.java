package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.bind.annotation.XmlRootElement;

import org.springframework.data.mongodb.core.mapping.Document;

@Document
@XmlRootElement(name = "PartnerUnitProductMappingDetail")
public class PartnerUnitProductMappingDetail {

    private String id;
    private String partnerName;
    private Integer partnerId;
    private Integer unitId;
    private Boolean active = false;
    private Date addTime;
    private String addTimeIST;
    private List<String> productIds;
    private List<String> upsellingProductIds;
    private List<String> superCombosProductIds;
    private List<String> addOnProductIds;
    private Map<String, List<String>> productVariantsMap;
    private Map<String, Set<String>> productRecipeVariantMap;
    private Map<String, Set<String>> splitProductMap;
    private Integer employeeId;
    private String employeeName;
    private Integer brandId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public List<String> getUpsellingProductIds() {
        return upsellingProductIds;
    }

    public void setUpsellingProductIds(List<String> upsellingProductIds) {
        this.upsellingProductIds = upsellingProductIds;
    }

    public List<String> getSuperCombosProductIds() {
        return superCombosProductIds;
    }

    public void setSuperCombosProductIds(List<String> superCombosProductIds) {
        this.superCombosProductIds = superCombosProductIds;
    }

    public List<String> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<String> productIds) {
        this.productIds = productIds;
    }

    public Map<String, List<String>> getProductVariantsMap() {
        return productVariantsMap;
    }

    public void setProductVariantsMap(Map<String, List<String>> productVariantsMap) {
        this.productVariantsMap = productVariantsMap;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }

    public List<String> getAddOnProductIds() {
        return addOnProductIds;
    }

    public void setAddOnProductIds(List<String> addOnProductIds) {
        this.addOnProductIds = addOnProductIds;
    }

    public Map<String, Set<String>> getProductRecipeVariantMap() {
        return productRecipeVariantMap;
    }

    public void setProductRecipeVariantMap(Map<String, Set<String>> productRecipeVariantMap) {
        this.productRecipeVariantMap = productRecipeVariantMap;
    }

    public Map<String, Set<String>> getSplitProductMap() {
        return splitProductMap;
    }

    public void setSplitProductMap(Map<String, Set<String>> splitProductMap) {
        this.splitProductMap = splitProductMap;
    }
}
