package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DesiChaiCustomProfileDao extends MongoRepository<DesiChaiCustomProfiles, String> {

    List<DesiChaiCustomProfiles> findAllByIdIn(List<String> profileIds);

    DesiChaiCustomProfiles findByProfileName(String profileName);
}
