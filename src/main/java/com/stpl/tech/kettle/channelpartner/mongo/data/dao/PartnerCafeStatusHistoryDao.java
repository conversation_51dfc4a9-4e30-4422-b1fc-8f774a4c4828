package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerCafeStatusHistoryDao extends MongoRepository<PartnerCafeStatusHistory, String> {

    List<PartnerCafeStatusHistory> findTop15ByBrandIdAndUnitIdAndPartnerNameOrderByLastUpdatedTimeDesc(Integer brandId,
                                                                                                       Integer unitId, String partnerName);

}
