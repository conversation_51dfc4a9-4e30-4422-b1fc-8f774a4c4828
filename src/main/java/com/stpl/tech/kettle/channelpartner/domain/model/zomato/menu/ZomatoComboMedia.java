package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "url",
        "usageType"
})
public class ZomatoComboMedia {

    @JsonProperty("url")
    private String url;
    @JsonProperty("usageType")
    private String usageType;

    @JsonProperty("url")
    public String getUrl() {
        return url;
    }

    @JsonProperty("url")
    public void setUrl(String url) {
        this.url = url;
    }

    @JsonProperty("usageType")
    public String getUsageType() {
        return usageType;
    }

    @JsonProperty("usageType")
    public void setUsageType(String usageType) {
        this.usageType = usageType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZomatoComboMedia that = (ZomatoComboMedia) o;
        return Objects.equals(url, that.url) &&
                Objects.equals(usageType, that.usageType);
    }

    @Override
    public int hashCode() {

        return Objects.hash(url, usageType);
    }

    @Override
    public String toString() {
        return "ZomatoComboMedia{" +
                "url='" + url + '\'' +
                ", usageType='" + usageType + '\'' +
                '}';
    }
}