package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"slug", "vendorEntityId", "multiItem", "chargeValue", "applicableOnItem", "taxGroups"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoMenuCharge {

    @JsonProperty("slug")
    private String slug;
    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("multiItem")
    private boolean multiItem;
    @JsonProperty("chargeValue")
    private Float chargeValue;
    /*@JsonProperty("taxes")
    private List<String> taxes = null;*/
    @JsonProperty("taxGroups")
    private List<ZomatoTaxGroup> taxGroups = null;
    @JsonProperty("applicableOnItem")
    private boolean applicableOnItem;

    @JsonProperty("slug")
    public String getSlug() {
        return slug;
    }

    @JsonProperty("slug")
    public void setSlug(String slug) {
        this.slug = slug;
    }

    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
        return vendorEntityId;
    }

    @JsonProperty("vendorEntityId")
    public void setVendorEntityId(String vendorEntityId) {
        this.vendorEntityId = vendorEntityId;
    }

    @JsonProperty("multiItem")
    public boolean isMultiItem() {
        return multiItem;
    }

    @JsonProperty("multiItem")
    public void setMultiItem(boolean multiItem) {
        this.multiItem = multiItem;
    }

    @JsonProperty("chargeValue")
    public Float getChargeValue() {
        return chargeValue;
    }

    @JsonProperty("chargeValue")
    public void setChargeValue(Float chargeValue) {
        this.chargeValue = chargeValue;
    }

	/*@JsonProperty("taxes")
	public List<String> getTaxes() {
		return taxes;
	}

	@JsonProperty("taxes")
	public void setTaxes(List<String> taxes) {
		this.taxes = taxes;
	}*/

    @JsonProperty("taxGroups")
    public List<ZomatoTaxGroup> getTaxGroups() {
        return taxGroups;
    }

    @JsonProperty("taxGroups")
    public void setTaxGroups(List<ZomatoTaxGroup> taxGroups) {
        this.taxGroups = taxGroups;
    }

    @JsonProperty("applicableOnItem")
    public boolean getApplicableOnItem() {
        return applicableOnItem;
    }

    @JsonProperty("applicableOnItem")
    public void setApplicableOnItem(boolean applicableOnItem) {
        this.applicableOnItem = applicableOnItem;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZomatoMenuCharge that = (ZomatoMenuCharge) o;
        return multiItem == that.multiItem &&
                applicableOnItem == that.applicableOnItem &&
                Objects.equals(slug, that.slug) &&
                Objects.equals(vendorEntityId, that.vendorEntityId) &&
                Objects.equals(chargeValue, that.chargeValue) &&
                Objects.equals(taxGroups, that.taxGroups);
    }

    @Override
    public int hashCode() {

        return Objects.hash(slug, vendorEntityId, multiItem, chargeValue, taxGroups, applicableOnItem);
    }

    @Override
    public String toString() {
        return "ZomatoMenuCharge{" +
                "slug='" + slug + '\'' +
                ", vendorEntityId='" + vendorEntityId + '\'' +
                ", multiItem=" + multiItem +
                ", chargeValue=" + chargeValue +
                ", taxGroups=" + taxGroups +
                ", applicableOnItem=" + applicableOnItem +
                '}';
    }
}