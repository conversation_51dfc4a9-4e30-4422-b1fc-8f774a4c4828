package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinMenu implements Serializable {
    private static final long serialVersionUID = 2082078535428276926L;
    private List<MagicPinCategory> categories ;
}
