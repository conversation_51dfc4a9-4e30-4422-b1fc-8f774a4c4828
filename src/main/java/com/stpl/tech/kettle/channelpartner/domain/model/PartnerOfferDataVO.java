package com.stpl.tech.kettle.channelpartner.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "offer_id",
    "start_date",
    "end_date",
    "offer_type",
    "discount_type",
    "min_order_amount",
    "discount_value",
    "is_active"
})
public class PartnerOfferDataVO {
	
	@JsonProperty("offer_id")
	private String offer_id;
	
	@JsonProperty("start_date")
	private String start_date;
	
	@JsonProperty("end_date")
	private String end_date;
	
	@JsonProperty("offer_type")
	private String offer_type;
	
	@JsonProperty("discount_type")
	private String discount_type;
	
	@JsonProperty("min_order_amount")
	private Float min_order_amount;
	
	@JsonProperty("discount_value")
	private Float discount_value;
	
	@JsonProperty("is_active")
	private String is_active;
	
	public String getOfferId() {
		return offer_id;
	}
	public void setOfferId(String offer_id) {
		this.offer_id = offer_id;
	}
	public String getStartDate() {
		return start_date;
	}
	public void setStartDate(String start_date) {
		this.start_date = start_date;
	}
	public String getEndDate() {
		return end_date;
	}
	public void setEndDate(String end_date) {
		this.end_date = end_date;
	}
	public String getOfferType() {
		return offer_type;
	}
	public void setOfferType(String offer_type) {
		this.offer_type = offer_type;
	}
	public String getDiscountType() {
		return discount_type;
	}
	public void setDiscountType(String discount_type) {
		this.discount_type = discount_type;
	}
	public Float getMinOrderAmount() {
		return min_order_amount;
	}
	public void setMinOrderAmount(Float min_order_amount) {
		this.min_order_amount = min_order_amount;
	}
	public Float getDiscountValue() {
		return discount_value;
	}
	public void setDiscountValue(Float discount_value) {
		this.discount_value = discount_value;
	}
	public String getIsActive() {
		return is_active;
	}
	public void setIsActive(String is_active) {
		this.is_active = is_active;
	}
	
	

}
