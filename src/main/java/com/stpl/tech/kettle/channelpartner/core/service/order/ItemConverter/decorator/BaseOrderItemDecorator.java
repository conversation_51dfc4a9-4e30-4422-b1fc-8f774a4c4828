package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.AbstractOrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderItemDecoratorBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public abstract class BaseOrderItemDecorator {
    protected OrderItemDecoratorBuilder orderItemDecoratorBuilder ;

    public BaseOrderItemDecorator(OrderItemDecoratorBuilder orderItemDecoratorBuilder) {
        this.orderItemDecoratorBuilder =orderItemDecoratorBuilder;
    }

    public abstract <R, I> Pair<PartnerItemData, List<OrderItem>> processItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts, int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) ;

    public <I, R> void addMenuItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail,
                                   Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                   int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap,
                                   Map<Integer, Product> cafeProducts, Product product, PartnerItemData partnerItemData,
                                   boolean isComboConstituentItem, String comboConstituentDimesnion,
                                   List<OrderItem> orderItemList) {
    };
}
