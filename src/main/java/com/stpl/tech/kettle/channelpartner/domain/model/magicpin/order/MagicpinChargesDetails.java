package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"name", "amount", "tax_Liability", "taxes"})
public class MagicpinChargesDetails {
    @JsonProperty("name")
    private String name;
    @JsonProperty("amount")
    private float amount;
    @JsonProperty("tax_Liability")
    private String taxLiability;


    @JsonProperty("taxes")
    private List<MagicpinTaxDetails> magicpinTaxDetails;

}
