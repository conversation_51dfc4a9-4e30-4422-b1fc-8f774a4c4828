package com.stpl.tech.kettle.channelpartner.core.service.order.factory;


import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.CODCustomerLoginData;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerAddressData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.math3.analysis.function.Add;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Component
public class OrderFactory {

    public static Order getOrderObject(Customer customer, Address address, MasterDataCache masterDataCache,
                                 UnitPartnerBrandMappingData data, EnvironmentProperties environmentProperties,
                                 PartnerPrimaryData partnerPrimaryData) {
        Order order = getCommonOrderObject(customer,address,masterDataCache,data,environmentProperties,
                partnerPrimaryData);
        switch (partnerPrimaryData.getPartnerName()){
            case "MAGICPIN":
                order.setChannelPartner(24);
                break;
        }
        return order;
    }

    private static Order getCommonOrderObject(Customer customer, Address address, MasterDataCache masterDataCache,
                                              UnitPartnerBrandMappingData data, EnvironmentProperties environmentProperties,
                                              PartnerPrimaryData partnerPrimaryData){
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Order order = new Order();
        order.setGenerateOrderId(ChannelPartnerUtils.generateRandomOrderId());
        order.setCustomerId(customer.getId());
        order.setEmployeeId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
        order.setPointsRedeemed(0);

        if (ZomatoOrderType.TAKEAWAY.name().equals(partnerPrimaryData.getOrderType())) {
            order.setSource("TAKE_AWAY");
        } else {
            order.setSource("COD");
        }
        order.setSourceId(partnerPrimaryData.getOrderId());
        order.setHasParcel(true);
        order.setStatus(OrderStatus.CREATED);
        order.setSettlementType(SettlementType.DEBIT);
        order.setUnitId(data.getUnitId());
        order.setUnitName(masterDataCache.getUnit(data.getUnitId()).getName());
        order.setTerminalId(1);
        order.setBillStartTime(currentTime);
        order.setBillCreationTime(currentTime);
        order.setBillCreationSeconds(0);
        order.setBillingServerTime(currentTime);
        if(environmentProperties.isDefaultPartnerCustomerFlow(partnerPrimaryData.getPartnerName())){
            order.setDeliveryPartner(5);
        }else {
            if (!ZomatoOrderType.TAKEAWAY.name().equals(partnerPrimaryData.getOrderType())
                    &&  partnerPrimaryData.isEnableDelivery()) {
                order.setDeliveryPartner(8); // PARTNER ID 8 FOR CHAAYOS DELIVERY
            } else {
                order.setDeliveryPartner(5); // for PICKUP
            }
        }

        order.setOrderRemark(partnerPrimaryData.getOrderInstructions());
        if (address != null) {
            order.setDeliveryAddress(address.getId());
        } else {
            order.setDeliveryAddress(0);
        }
        String name= partnerPrimaryData.getCustomerName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        order.setCustomerName(name);
        order.setContainsSignupOffer(false);
        order.setNewCustomer(false);
        order.setOrderType("order");
        order.setBrandId(data.getBrandId());
        //order.setSkipLoyaltyProducts(true);

        order.setPartnerCustomerId(partnerPrimaryData.getOrderId() +"_"+ partnerPrimaryData.getCustomerPhoneNumber());
        return order;
    }

    public  static CODCustomerLoginData getCustomerData(PartnerCustomerData customerDetail , PartnerOrderDetail
                                                        partnerOrderDetail , EnvironmentProperties environmentProperties){
        CODCustomerLoginData data = new CODCustomerLoginData();
        String contact = customerDetail.getContactNumber();
        if (contact.matches("^0[0-9]*")) {
            contact = contact.substring(1);
        }
        data.setContactNumber(contact);
        Customer customer = new Customer();
        boolean addEmail = true;
        if(Objects.nonNull(customerDetail.getEmail())){
            for (String domain : environmentProperties.getEmailFilterDomains().split(",")) {
                if (addEmail && customerDetail.getEmail().contains(domain)) {
                    addEmail = false;
                }
            }
        }
        if (addEmail) {
            customer.setEmailId(customerDetail.getEmail());
        }
        String name = customerDetail.getCustomerName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        customer.setFirstName(name);
        customer.setContactNumber(contact);
        data.setCustomer(customer);
        UserSessionDetail userSessionDetail = new UserSessionDetail();
        userSessionDetail.setUnitId(partnerOrderDetail.getUnitId());
        userSessionDetail.setUserId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID); //for user id system
        userSessionDetail.setBusinessDate(ChannelPartnerUtils.getBusinessDate());
        data.setSession(userSessionDetail);
        data.setAcquisitionBrandId(partnerOrderDetail.getBrandId());
        data.setChaayosCustomer(partnerOrderDetail.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID));
        return data;
    }

    public static Address getCustomerAddressData(CODCustomerLoginData codCustomerLoginData , PartnerCustomerData partnerCustomerData,
                           Customer customer,Integer unitId ,MasterDataCache masterDataCache){
        PartnerCustomerAddressData partnerCustomerAddressData = partnerCustomerData.getPartnerCustomerAddressData();
        codCustomerLoginData.setContactNumber(customer.getContactNumber());
        codCustomerLoginData.setCustomer(customer);
        Address address = new Address();
        address.setName(partnerCustomerData.getCustomerName());
        if (partnerCustomerData.getCustomerName() != null && partnerCustomerData.getCustomerName().length() > 50) {
            address.setName(partnerCustomerData.getCustomerName().substring(0, 50));
        }else{
            address.setName(partnerCustomerData.getCustomerName());
        }
        address.setAddressType("HOME");
        address.setCountry(partnerCustomerAddressData.getCountry());
        String line1 = partnerCustomerAddressData.getAddressLine1();
        if (partnerCustomerAddressData.getAddressLine1() != null && partnerCustomerAddressData.getAddressLine1().length() > 255) {
            line1 = partnerCustomerAddressData.getAddressLine1().substring(0, 255);
        }
        address.setLine1(line1);
        String locality = partnerCustomerAddressData.getLocality();
        if (partnerCustomerAddressData.getLocality()!= null && partnerCustomerAddressData.getLocality().length() > 80) {
            locality = partnerCustomerAddressData.getLocality().substring(0, 80);
        }
        address.setLocality(locality);
        Location location = null;
        for (Location loc : masterDataCache.getAllLocations().values()) {
            if (loc.getName().equalsIgnoreCase(partnerCustomerAddressData.getCity())) {
                location = loc;
            }
        }
        if (location == null) {
            location = masterDataCache.getAllLocations().get(masterDataCache.getUnitBasicDetail(unitId).getLocationCode());
        }
        address.setCity(location.getName());
        address.setState(location.getState().getName());
        address.setLatitude(partnerCustomerAddressData.getLatitude());
        address.setLongitude(partnerCustomerAddressData.getLongitude());
        if(Objects.nonNull(partnerCustomerAddressData.getLandmark())){
            address.setLandmark(partnerCustomerAddressData.getLandmark());
        }
        address.setZipCode(partnerCustomerAddressData.getZipcode());
        return address;
    }
}
