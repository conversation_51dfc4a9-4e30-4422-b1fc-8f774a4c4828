package com.stpl.tech.kettle.channelpartner.core.util;

public enum MagicpinServiceEndPoints {
    UPDATE_CATALOG("https://webapi.magicpin.in/oms_partner/catalog/update", "https://webapi.magicpin.in/oms_partner/catalog/update"),

    CONFIRM_ORDER("https://webapi.magicpin.in/oms_partner/catalog/update", "https://webapi.magicpin.in/oms_partner/catalog/update"),
    UPDATE_INVENORY("https://webapi.magicpin.in/oms_partner/inventory/update", "https://webapi.magicpin.in/oms_partner/inventory/update"),
    STORE_STATUS_UPDATE("https://webapi.magicpin.in/oms_partner/store/status/update", "https://webapi.magicpin.in/oms_partner/store/status/update"),
    ORDER_STATUS_UPDATE("https://webapi.magicpin.in/oms_partner/order/status/update", "https://webapi.magicpin.in/oms_partner/order/status/update"),
    DELIVERY_STATUS_UPDATE("https://webapi.magicpin.in/oms_partner/delivery/status/update", "https://webapi.magicpin.in/oms_partner/delivery/status/update"),
    ORDER_UPDATE("https://webapi.magicpin.in/oms_partner/order/update", "https://webapi.magicpin.in/oms_partner/order/update"),
    DELIVER_CHARGE_UPDATE("https://webapi.magicpin.in/oms_partner/delivery/charge/update", "https://webapi.magicpin.in/oms_partner/delivery/charge/update");

    String dev;
    String prod;

    MagicpinServiceEndPoints(String dev, String prod) {
        this.dev = dev;
        this.prod = prod;
    }

    public String getUrl(boolean isDev) {
        if (isDev) {
            return this.dev;
        } else {
            return this.prod;
        }
    }
}
