package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"addressLine1", "addressLine2", "city", "state", "country", "pincode", "lat", "lon", "contactNumber"})
public class MagicpinAddressDetail {
    @JsonProperty("addressLine1")
    private String addressLine1;
    @JsonProperty("addressLine2")
    private String addressLine2;

    @JsonProperty("addressLine3")
    private String addressLine3;

    @JsonProperty("localityName")
    private String localityName;


    @JsonProperty("city")
    private String city;
    @JsonProperty("state")
    private String state;

    @JsonProperty("country")
    private String country;
    @JsonProperty("pinCode")
    private String pinCode;
    @JsonProperty("lat")
    private Float lat;
    @JsonProperty("lon")
    private Float lon;
    @JsonProperty("name")
    private String name;
    @JsonProperty("contactNumbers")
    private List<String> contactNumbers;
}
