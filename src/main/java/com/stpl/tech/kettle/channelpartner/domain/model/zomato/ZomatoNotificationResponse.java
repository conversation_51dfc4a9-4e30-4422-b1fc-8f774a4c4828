package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

public class ZomatoNotificationResponse {
    private String status;
    private String message;
    private List<String> errors;
    private Integer code;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "ZomatoNotificationResponse{" +
                "status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", errors=" + errors +
                ", code=" + code +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoNotificationResponse that = (ZomatoNotificationResponse) o;

        return new EqualsBuilder()
                .append(status, that.status)
                .append(message, that.message)
                .append(errors, that.errors)
                .append(code, that.code)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(status)
                .append(message)
                .append(errors)
                .append(code)
                .toHashCode();
    }
}