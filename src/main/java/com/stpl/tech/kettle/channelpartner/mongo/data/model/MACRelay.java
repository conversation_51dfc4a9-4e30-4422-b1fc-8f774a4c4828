package com.stpl.tech.kettle.channelpartner.mongo.data.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.redis.core.index.Indexed;

import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "MACRelay")
public class MACRelay {

    @Id
    private String id;
    protected Long orderId;
    @Indexed
    private String externalOrderId;
    private String customerCancellationReason;
    private Integer initiatedAt;
    private Integer timeout;
    private String requestCancellationTime;
    private String status;
    private String updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public String getCustomerCancellationReason() {
        return customerCancellationReason;
    }

    public void setCustomerCancellationReason(String customerCancellationReason) {
        this.customerCancellationReason = customerCancellationReason;
    }

    public Integer getInitiatedAt() {
        return initiatedAt;
    }

    public void setInitiatedAt(Integer initiatedAt) {
        this.initiatedAt = initiatedAt;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getRequestCancellationTime() {
        return requestCancellationTime;
    }

    public void setRequestCancellationTime(String requestCancellationTime) {
        this.requestCancellationTime = requestCancellationTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
