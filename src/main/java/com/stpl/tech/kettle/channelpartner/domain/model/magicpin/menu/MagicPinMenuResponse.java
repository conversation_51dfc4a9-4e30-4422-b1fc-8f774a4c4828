package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "request_id", "status", "message", "responseCode", "responseMessage"
})
public class MagicPinMenuResponse implements Serializable {

    private static final long serialVersionUID = -6708903772588944710L;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("status")
    private boolean status;

    @JsonProperty("message")
    private String message;

    @JsonProperty("responseCode")
    private Integer responseCode;

    @JsonProperty("responseMessage")
    private String responseMessage;

}
