package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "rider_phone_number",
        "rbt",
        "is_high_temp"
})
public class ZomatoOrderRiderDataV3 {

    @JsonProperty("order_id")
    private String orderId;
    
    @JsonProperty("rider_phone")
    private String riderPhoneNumber;
    
    @JsonProperty("rbt")
    private float rbt;
    
    @JsonProperty("is_high_temp")
    private boolean isHighTemp;

	@JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

	@JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

	@JsonProperty("rider_phone")
	public String getRiderPhoneNumber() {
		return riderPhoneNumber;
	}

	@JsonProperty("rider_phone")
	public void setRiderPhoneNumber(String riderPhoneNumber) {
		this.riderPhoneNumber = riderPhoneNumber;
	}

	@JsonProperty("rbt")
	public float getRbt() {
		return rbt;
	}

	@JsonProperty("rbt")
	public void setRbt(float rbt) {
		this.rbt = rbt;
	}

	@JsonProperty("is_high_temp")
	public boolean isHighTemp() {
		return isHighTemp;
	}

	@JsonProperty("is_high_temp")
	public void setHighTemp(boolean isHighTemp) {
		this.isHighTemp = isHighTemp;
	}
}
