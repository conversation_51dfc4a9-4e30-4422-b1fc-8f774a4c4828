package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Objects;

public class SwiggyStockUpdateResponse {

    @JsonProperty("itemToggleStatus")
    private List<SwiggyStockUpdateItemResponse> itemToggleStatus;


    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("itemToggleStatus")
    public List<SwiggyStockUpdateItemResponse> getItemToggleStatus() {
        return itemToggleStatus;
    }

    public void setItemToggleStatus(List<SwiggyStockUpdateItemResponse> itemToggleStatus) {
        this.itemToggleStatus = itemToggleStatus;
    }

    @JsonProperty("requestId")
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SwiggyStockUpdateResponse that = (SwiggyStockUpdateResponse) o;
        return Objects.equals(itemToggleStatus, that.itemToggleStatus) && Objects.equals(requestId, that.requestId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(itemToggleStatus, requestId);
    }
}
