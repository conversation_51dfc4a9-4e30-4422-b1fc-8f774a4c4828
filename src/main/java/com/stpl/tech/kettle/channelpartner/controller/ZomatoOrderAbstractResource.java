package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Objects;

@Slf4j
public abstract class ZomatoOrderAbstractResource extends ChannelPartnerAbstractResources{
    @Autowired
    private ZomatoService zomatoService;

    public ZomatoOrderResponse addZomatoOrder(ZomatoOrderRequestV3 order) {
        String externalOrderId = null;
        if(Objects.nonNull(order)){
            try{
                externalOrderId = order.getOrderId().toString();
                ZomatoOrderResponse response =  zomatoService.addZomatoOrderV3(order, false);
                String responseJson = new Gson().toJson(response);
                log.info("Request to add zomato order {}: {}",order.getOrderId(), responseJson);
                return response;
            }catch (Exception  e) {
                log.error("Exception while adding zomato order for external order id :{}", order.getOrderId(), e);
            }
        }
        return null;
    }

}
