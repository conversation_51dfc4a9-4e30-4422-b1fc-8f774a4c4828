package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount;

import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;

import java.math.BigDecimal;
import java.util.List;

public interface DiscountStrategy {
    <R,T> void applyDiscount(R request , T item , List<OrderItem> items, PartnerItemData partnerItemData);

    <R,T> PercentageDetail getTotalOrderDiscount(R request);



    <R,T> DiscountDetail getTotalItemDiscount(R request , T item, PartnerItemData partnerItemData, OrderItem oItem ,
                                              BigDecimal catalogueDistributedDiscount, BigDecimal totalParentAmount);

   //  <I> void applyDiscount(I item , List<OrderItem> orderItems);




}
