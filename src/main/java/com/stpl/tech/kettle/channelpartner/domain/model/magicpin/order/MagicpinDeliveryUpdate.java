package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"orderId","thirdPartyOrderId","shipmentId","status","riderName","riderNumber","deliveryProvider","items"})
public class MagicpinDeliveryUpdate {

    @JsonProperty("orderId")
    private Integer orderId;

    @JsonProperty("thirdPartyOrderId")
    private String thirdPartyOrderId;

    @JsonProperty("shipmentId")
    private Integer shipmentId;

    @JsonProperty("status")
    private String status;
    @JsonProperty("riderName")
    private String riderName;

    @JsonProperty("riderNumber")
    private String riderNumber;

    @JsonProperty("deliveryProvider")
    private String deliveryProvider;

    @JsonProperty("items")
    private List<Items> items;
}
