package com.stpl.tech.kettle.channelpartner.core.util;

public enum ZomatoServiceEndpoints {
    MENU_ADD_V1("https://api.zomato.com/online-ordering/v1/menu/add", "https://api.zomato.com/online-ordering/v1/menu/add"),
    MENU_ADD_V2("https://api.zomato.com/online-ordering/v2/menu/add", "https://api.zomato.com/online-ordering/v2/menu/add"),
    MENU_ADD_V3("https://api.zomato.com/online-ordering/v3/menu/add", "https://api.zomato.com/online-ordering/v3/menu/add"),
    CONFIRM_ORDER("https://api.zomato.com/online-ordering/v1/order/confirm", "https://api.zomato.com/online-ordering/v1/order/confirm"),
    REJECT_ORDER("https://api.zomato.com/online-ordering/v1/order/reject", "https://api.zomato.com/online-ordering/v1/order/reject"),
    ORDER_PICKED_UP("https://api.zomato.com/online-ordering/v1/order/pickedup", "https://api.zomato.com/online-ordering/v1/order/pickedup"),
    RIDER_ASSIGNED("https://api.zomato.com/online-ordering/v1/order/assigned", "https://api.zomato.com/online-ordering/v1/order/assigned"),
    DELIVERED("https://api.zomato.com/online-ordering/v1/order/delivered", "https://api.zomato.com/online-ordering/v1/order/delivered"),
    GET_DELIVERY_STATUS("https://api.zomato.com/online-ordering/v1/restaurant_delivery_status/get",
        "https://api.zomato.com/online-ordering/v1/restaurant_delivery_status/get"),
    UPDATE_DELIVERY_STATUS("https://api.zomato.com/online-ordering/v1/restaurant_delivery_status/update",
        "https://api.zomato.com/online-ordering/v1/restaurant_delivery_status/update"),
    GET_OUTLET_DELIVERY_TIME("https://api.zomato.com/online-ordering/v1/restaurant/delivery-time/get",
        "https://api.zomato.com/online-ordering/v1/restaurant/delivery-time/get"),
    SET_SURGE_DELIVERY_TIME("https://api.zomato.com/online-ordering/v1/restaurant/delivery-time/add_surge",
        "https://api.zomato.com/online-ordering/v1/restaurant/delivery-time/add_surge"),
    GET_OUTLET_DELIVERY_AREA("https://api.zomato.com/online-ordering/v1/restaurant/delivery-area/get",
        "https://api.zomato.com/online-ordering/v1/restaurant/delivery-area/get"),
    GET_OUTLET_LOGISTICS_STATUS("https://api.zomato.com/online-ordering/v1/restaurant/logistics-status/get",
        "https://api.zomato.com/online-ordering/v1/restaurant/logistics-status/get"),
    UPDATE_SELF_DELIVERY_SERVICEABILITY("https://api.zomato.com/online-ordering/v1/restaurant/logistics-status/update_self_delivery_serviceability",
        "https://api.zomato.com/online-ordering/v1/restaurant/logistics-status/update_self_delivery_serviceability"),
    UPDATE_ITEM_STOCK("https://api.zomato.com/online-ordering/v1/menu/item/update", "https://api.zomato.com/online-ordering/v1/menu/item/update"),
    UPDATE_ITEM_STOCK_V3("https://api.zomato.com/online-ordering/v3/menu/item/stock", "https://api.zomato.com/online-ordering/v3/menu/item/stock"),
    GET_TAKEAWAY_STATUS("https://api.zomato.com/online-ordering/v1/restaurant_takeaway_status/get",
        "https://api.zomato.com/online-ordering/v1/restaurant_takeaway_status/get"),
    UPDATE_TAKEAWAY_STATUS("https://api.zomato.com/online-ordering/v1/restaurant_takeaway_status/update",
        "https://api.zomato.com/online-ordering/v1/restaurant_takeaway_status/update"),
    MARK_ORDER_READY("https://api.zomato.com/online-ordering/v1/order/ready", "https://api.zomato.com/online-ordering/v1/order/ready"),
    OFFER_ADD("https://api.zomato.com/online-ordering/v3/menu/offers/sync", "https://api.zomato.com/online-ordering/v3/menu/offers/sync"),
    RIDER_MASK("https://api.zomato.com/online-ordering/v1/rider/rider_mask", "https://api.zomato.com/online-ordering/v1/rider/rider_mask"),
    RIDER_DETAILS("https://api.zomato.com/online-ordering/v1/rider/rider_details", "https://api.zomato.com/online-ordering/v1/rider/rider_details"),
    //SINGLE_SERVE("https://api.zomato.com/online-ordering/v3/menu/single-serve/sync", "https://api.zomato.com/online-ordering/v3/menu/single-serve/sync"),
    MAC_STATUS("https://api.zomato.com/online-ordering/v1/mac/update", "https://api.zomato.com/online-ordering/v1/mac/update"),
    DELIVERY_CHARGE_UPDATE("https://api.zomato.com/online-ordering/v3/restaurant/delivery-charge/update",
        "https://api.zomato.com/online-ordering/v3/restaurant/delivery-charge/update"),
    UPDATE_CAFE_DELIVERY_TIME("https://api.zomato.com/online-ordering/v1/restaurant/zomato-delivery-timings/update",
            "https://api.zomato.com/online-ordering/v1/restaurant/zomato-delivery-timings/update"),
    GET_ORDER_RATING("https://api.zomato.com/online-ordering/v1/order-analytics/get_orders_rating",
                     "https://api.zomato.com/online-ordering/v1/order-analytics/get_orders_rating"),
    UPDATE_CAFE_TIMINGS("https://api.zomato.com/online-ordering/v1/restaurant/self-delivery-timings/update",
                        "https://api.zomato.com/online-ordering/v1/restaurant/self-delivery-timings/update");

    String dev;
    String prod;

    ZomatoServiceEndpoints(String dev, String prod) {
        this.dev = dev;
        this.prod = prod;
    }

    public String getUrl(boolean isDev) {
        if (isDev) {
            return this.dev;
        } else {
            return this.prod;
        }
    }
}
