package com.stpl.tech.kettle.channelpartner.core.service.menu.factory;

import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

@Component
public class ServiceFactory {

    private static final Map<String, String> partners = Map.of("SWIGG<PERSON>", "<PERSON>wiggy", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "MAGI<PERSON><PERSON>", "MagicPin");

    private static final String basePath = "com.stpl.tech.kettle.channelpartner.core.service.impl.";


    private static final Map<String, PartnerAbstractFactory> partnerAbstractServiceMap = new HashMap<>();
    private static final Map<String, PartnerBaseDecorator> eventPreProcessorMap = new HashMap<>();
    private static final Map<String, PartnerActionEventDecorator> postEventProcessorMap = new HashMap<>();

    private static final Map<String, EventAbstractFactory<?, ?>> eventAbstractFactoryMap = new HashMap<>();
    private static final Map<String, PartnerActionEventDecorator> postReqToPartnerProcessorMap = new HashMap<>();


    public static PartnerAbstractFactory getPartnerAbstractFactory(String partner, PartnerMetadataBuilder partnerMetadataBuilder) {
        if (!partnerAbstractServiceMap.isEmpty() && partnerAbstractServiceMap.containsKey(partner)) {
            return partnerAbstractServiceMap.get(partner);
        } else {
            try {
                String className = basePath + partners.get(partner) + ChannelPartnerUtils.PARTNER_FACTORY_SERVICE;
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor(PartnerMetadataBuilder.class);
                PartnerAbstractFactory service = (PartnerAbstractFactory) constructor.newInstance(partnerMetadataBuilder);
                partnerAbstractServiceMap.put(partner, service);
                return service;
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new IllegalArgumentException("Unsupported partner type: " + partner);
            }
        }
    }

    public static PartnerBaseDecorator getPartnerPreProcessorLayer(String eventType, PartnerMetadataBuilder partnerMetadataBuilder) {
        if (!eventPreProcessorMap.isEmpty() && eventPreProcessorMap.containsKey(eventType)) {
            return eventPreProcessorMap.get(eventType);
        } else {
            try {
                String className = basePath + eventType + ChannelPartnerUtils.PRE_PROCESSOR_SERVICE;
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor(PartnerMetadataBuilder.class);
                PartnerBaseDecorator partnerBaseDecorator = (PartnerBaseDecorator) constructor.newInstance(partnerMetadataBuilder);
                eventPreProcessorMap.put(eventType, partnerBaseDecorator);
                return partnerBaseDecorator;
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new IllegalArgumentException("Error creating instance for event type: " + eventType +
                        ". Exception: " + e.getMessage(), e);
            }
        }

    }

    public static PartnerActionEventDecorator getPostEventProcessorService(String eventType, PartnerAbstractFactory partnerAbstractFactory, PartnerActionEventDecorator partnerActionEventDecorator, PartnerMetadataBuilder partnerMetadataBuilder) {
        if(!postEventProcessorMap.isEmpty() && postEventProcessorMap.containsKey(eventType)){
            return postEventProcessorMap.get(eventType);
        }else{
            try {
                String className = basePath + "Post" + eventType + ChannelPartnerUtils.EVENT_PROCESSOR_SERVICE;
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor(PartnerAbstractFactory.class, PartnerActionEventDecorator.class, com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder.class);
                PartnerActionEventDecorator partnerActionEventDecorator1 = (PartnerActionEventDecorator) constructor.newInstance(partnerAbstractFactory, partnerActionEventDecorator, partnerMetadataBuilder);
                postEventProcessorMap.put(eventType, partnerActionEventDecorator1);
                return partnerActionEventDecorator1;
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new IllegalArgumentException("Unsupported post event type: " + eventType + e.getMessage());
            }
        }
    }


    public static EventAbstractFactory<?, ?> getPartnerEventFactoryService(String eventType, PartnerMetadataBuilder partnerMetadataBuilder) {
        if(!eventAbstractFactoryMap.isEmpty() && eventAbstractFactoryMap.containsKey(eventType)){
            return eventAbstractFactoryMap.get(eventType);
        }else{
            try {
                String className = basePath + eventType + ChannelPartnerUtils.EVENT_FACTORY_SERVICE;
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor(PartnerMetadataBuilder.class);
                EventAbstractFactory<?, ?> eventAbstractFactory = (EventAbstractFactory<?, ?>) constructor.newInstance(partnerMetadataBuilder);
                eventAbstractFactoryMap.put(eventType,eventAbstractFactory);
                return eventAbstractFactory;
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new IllegalArgumentException("Unsupported event type: " + eventType + e.getMessage());
            }
        }

    }

    public static PartnerActionEventDecorator getPostReqToPartnerService(String eventType, PartnerAbstractFactory partnerAbstractFactory) {
        if(!postReqToPartnerProcessorMap.isEmpty() && postReqToPartnerProcessorMap.containsKey(eventType)){
            return postReqToPartnerProcessorMap.get(eventType);
        }else{
            try {
                String className = basePath + "Post" + eventType + ChannelPartnerUtils.REQ_PROCESSOR_SERVICE;
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor(PartnerAbstractFactory.class);
                PartnerActionEventDecorator partnerActionEventDecorator =(PartnerActionEventDecorator) constructor.newInstance(partnerAbstractFactory);
                postReqToPartnerProcessorMap.put(eventType,partnerActionEventDecorator);
                return partnerActionEventDecorator;
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException |
                     InvocationTargetException e) {
                throw new IllegalArgumentException("Unsupported event type: " + eventType + e.getMessage());
            }
        }

    }

}
