package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitRefreshInventoryEvent;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Log4j2
public class InventoryEventFactoryService extends EventAbstractFactory<UnitRefreshInventoryEvent, UnitProductsStockEvent> {
    private PartnerMetadataBuilder partnerMetadataBuilder;

    public InventoryEventFactoryService(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
        this.partnerMetadataBuilder=partnerMetadataBuilder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitProductsStockEvent> prepareMetadata(UnitRefreshInventoryEvent event, PartnerActionEventType eventType, String partnerName) throws ChannelPartnerException {
        log.info("Processing INVENTORY_UPDATE EVENT for partner :::::::::::{}", partnerName);
        List<UnitProductsStockEvent> stockEvents = new ArrayList<>();
        if (Objects.nonNull((Integer) event.getBrandId())) {
            refreshUnitInventory(event.getUnitIds(), event.getBrandId(), partnerName, stockEvents);
        } else {
            refreshUnitInventory(event.getUnitIds(), null, partnerName, stockEvents);
        }
        return stockEvents;
    }

    private void refreshUnitInventory(List<Integer> unitIds, Integer brandId, String partnerName, List<UnitProductsStockEvent> stockEvents) {
        for (Integer unitId : unitIds) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerName).getKettlePartnerId();
            partnerMetadataBuilder.partnerOrderService.logStockRefreshEvent(unitId, partnerId, brandId,
                    "REFRESH_START", AppConstants.STATUS_SUCCESSFUL);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            Unit unit = masterDataCache.getUnit(unitId);
            if (masterDataCache.getUnits().containsKey(unitId) && mappingValid && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()) {
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
                stockOut.setStatus(StockStatus.STOCK_OUT);
                stockOut.setUnitId(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
                stockIn.setStatus(StockStatus.STOCK_IN);
                stockIn.setUnitId(unitId);
                partnerMetadataBuilder.orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
                if (Objects.nonNull(brandId)) {
                    stockOut.setBrandId(brandId);
                    stockIn.setBrandId(brandId);
                }
                if (stockOut.getProductIds() != null && !stockOut.getProductIds().isEmpty()) {
                    log.info("Stock out product ids are :::::::::::{}", new Gson().toJson(stockOut.getProductIds()));
                    stockEvents.add(stockOut);
                }
                if (stockIn.getProductIds() != null && !stockIn.getProductIds().isEmpty()) {
                    log.info("Stock in product ids are :::::::::::{}", new Gson().toJson(stockIn.getProductIds()));
                    stockEvents.add(stockIn);
                }
            }
        }
    }
}
