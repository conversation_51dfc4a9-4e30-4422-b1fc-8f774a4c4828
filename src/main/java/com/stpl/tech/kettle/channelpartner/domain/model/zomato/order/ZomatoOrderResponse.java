
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "status",
    "message",
    "external_order_id",
    "rejection_message_id",
    "code"
})
public class ZomatoOrderResponse {

    @JsonProperty("status")
    private ZomatoOrderStatus status;
    @JsonProperty("message")
    private String message;
    @JsonProperty("external_order_id")
    private String externalOrderId;
    @JsonProperty("rejection_message_id")
    private Integer rejectionMessageId;
    @JsonProperty("code")
    private Integer code;

    @JsonProperty("status")
    public ZomatoOrderStatus getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(ZomatoOrderStatus status) {
        this.status = status;
    }

    @JsonProperty("message")
    public String getMessage() {
        return message;
    }

    @JsonProperty("message")
    public void setMessage(String message) {
        this.message = message;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("rejection_message_id")
    public Integer getRejectionMessageId() {
        return rejectionMessageId;
    }

    @JsonProperty("rejection_message_id")
    public void setRejectionMessageId(Integer rejectionMessageId) {
        this.rejectionMessageId = rejectionMessageId;
    }

    @JsonProperty("code")
    public Integer getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "ZomatoOrderResponse{" +
                "status=" + status +
                ", message='" + message + '\'' +
                ", externalOrderId='" + externalOrderId + '\'' +
                ", rejectionMessageId=" + rejectionMessageId +
                ", code='" + code + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZomatoOrderResponse that = (ZomatoOrderResponse) o;
        return status == that.status &&
                Objects.equals(message, that.message) &&
                Objects.equals(externalOrderId, that.externalOrderId) &&
                Objects.equals(rejectionMessageId, that.rejectionMessageId) &&
                Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {

        return Objects.hash(status, message, externalOrderId, rejectionMessageId, code);
    }
}
