package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"storeId","partnerStoreId","status","deactivationReason"})
public class MagicpinOutletDeliveryStatusRequest {

    @JsonProperty("storeId")
    private String storeId;

    @JsonProperty("partnerStoreId")
    private String partnerStoreId;

    @JsonProperty("status")
    private boolean status;

    @JsonProperty("deactivationReason")
    private String deactivationReason;
}
