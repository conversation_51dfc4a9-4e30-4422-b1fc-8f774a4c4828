package com.stpl.tech.kettle.channelpartner.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitProductPartnerStatusBulkVO {
    private List<Integer> productIds;
    private List<Integer> partnerIds;
    private Integer brandId;
    private List<Integer> unitIds;
    private Boolean status;
    private Boolean forceStockOut;
}
