package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.converters.AbstractConverters;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderItemDecoratorBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ChargesItemDecorator extends BaseOrderItemDecorator {

    private BaseAddonItemDecorator baseAddonItemDecorator ;

    public ChargesItemDecorator(OrderItemDecoratorBuilder orderItemDecoratorBuilder, BaseAddonItemDecorator baseAddonItemDecorator) {
        super(orderItemDecoratorBuilder);
        this.baseAddonItemDecorator = baseAddonItemDecorator;
    }

    @Override
    public <R, I> Pair<PartnerItemData, List<OrderItem>> processItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts, int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) {
        final Pair<PartnerItemData, List<OrderItem>> partnerItemDataListPair = this.baseAddonItemDecorator.
                processItem(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap, cafeProducts);
        addChargeItemsToOrder(order, item, false, taxMap, priceProfileProducts, pricingUnitId, "",
                BigDecimal.ZERO, partnerItemDataListPair.getValue());
        return partnerItemDataListPair;
    }



    private OrderItem getChargeItemFromOrder(List<OrderItem> orderItemList , PartnerItemData partnerItemData){
        if(CollectionUtils.isEmpty(orderItemList)){
            return  null;
        }
        BigDecimal taxRate = partnerItemData.getTaxRate();
        for(OrderItem orderItem : orderItemList){
            if(orderItem.getProductId() == AppConstants.PACKAGING_PRODUCT_ID &&  !CollectionUtils.isEmpty(orderItem.getTaxes())){
                if(orderItem.getTaxes().get(0).getPercentage().compareTo(taxRate) == 0){
                    return orderItem;
                }
            }
        }
        return null;
    }

    public  <I> void addChargeItemsToOrder(Order order, I item, boolean isInterState,
                                           Map<String, TaxDataVO> taxMap, Map<Integer, Product> products,
                                           int pricingUnitId, String discountName, BigDecimal discountPercent,
                                           List<OrderItem> orderItemList) {
        Product product = products.get(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID);
        PartnerItemData packagingItemMetaData = orderItemDecoratorBuilder.partnerItemConverterService.getPackagingCharge(item);
        BigDecimal packagingCharge = packagingItemMetaData.getUnitPrice();
        if (packagingCharge.compareTo(BigDecimal.ZERO) > 0) {
            OrderItem packagingItem = getChargeItemFromOrder(order.getOrders(),packagingItemMetaData);
            Boolean packagingItemExist = false;
            if(Objects.nonNull(packagingItem)){
                packagingItem.setPrice(ChannelPartnerUtils.add(packagingItem.getPrice(),packagingCharge));
                packagingItem.setAmount(ChannelPartnerUtils.add(packagingItem.getAmount(),packagingCharge));
                packagingItem.setTotalAmount(ChannelPartnerUtils.add(packagingItem.getTotalAmount(),packagingCharge));
                packagingItemExist  =true;
            }else{
                packagingItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                        orderItemDecoratorBuilder.masterDataCache, isInterState, taxMap, false, discountPercent, discountName,
                        product.getPrices().get(0).getDimension(), pricingUnitId, BigDecimal.ZERO,
                        packagingItemMetaData.getTaxDeductedByPartner(), packagingItemMetaData.getTaxPayingEntity());
                packagingItem.setPrice(packagingCharge);
                packagingItem.setAmount(packagingCharge);
                packagingItem.setTotalAmount(packagingCharge);
            }
            packagingItem.getTaxes().clear();
            BigDecimal totalItemTax = BigDecimal.ZERO;
            totalItemTax = orderItemDecoratorBuilder.taxStrategy.getItemPackagingTax(packagingItem, item);
            packagingItem.setTax(totalItemTax);
            if(Boolean.FALSE.equals(packagingItemExist)){
                orderItemList.add(packagingItem);
            }
        }
    }



}
