package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.ProductStockSnapshot;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Document
@XmlRootElement(name = "PartnerUnitProductStockSnapshotV1")
public class PartnerUnitProductStockSnapshot {
    @Id
    private String id;
    private Integer partnerId;
    private String unitId;
    private Integer productId;
    private List<ProductStockSnapshot> productStockSnapshot;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<ProductStockSnapshot> getProductStockSnapshot() {
        return productStockSnapshot;
    }

    public void setProductStockSnapshot(List<ProductStockSnapshot> productStockSnapshot) {
        this.productStockSnapshot = productStockSnapshot;
    }
}
