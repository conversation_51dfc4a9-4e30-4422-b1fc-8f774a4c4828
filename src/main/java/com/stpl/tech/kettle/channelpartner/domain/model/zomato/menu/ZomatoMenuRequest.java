
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuRequest;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "outletId",
    "charges",
    "menu"
})
public class ZomatoMenuRequest extends MenuRequest {

    @JsonProperty("outletId")
    private String outletId;
    @JsonProperty("charges")
    private List<ZomatoMenuCharge> charges = null;
	@JsonProperty("menu")
    private ZomatoMenu menu;

    @JsonProperty("outletId")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outletId")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("menu")
    public ZomatoMenu getMenu() {
        return menu;
    }

    @JsonProperty("menu")
    public void setMenu(ZomatoMenu menu) {
        this.menu = menu;
    }
    
    @JsonProperty("charges")
    public List<ZomatoMenuCharge> getCharges() {
		return charges;
	}

    @JsonProperty("charges")
	public void setCharges(List<ZomatoMenuCharge> charges) {
		this.charges = charges;
	}

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("outletId", outletId).append("menu", menu).append("charges", charges).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(menu).append(outletId).append(charges).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoMenuRequest) == false) {
            return false;
        }
        ZomatoMenuRequest rhs = ((ZomatoMenuRequest) other);
        return new EqualsBuilder().append(menu, rhs.menu).append(outletId, rhs.outletId).append(charges, rhs.charges).isEquals();
    }

}
