package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.amazonaws.regions.Regions;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.mapper.MagicpinDeliveryUpdateMapper;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.MagicpinService;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.MagicpinServiceEndPoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.MagicpinOrderUpdateEnum;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicPinStatusUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinDeliveryUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOutletDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinUnitAndInventoryUpdateResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;

import javax.jms.JMSException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Log4j2
public class MagicpinServiceImpl implements MagicpinService {
    @Autowired
    TrackService trackService;

    @Autowired
    SQSNotificationService sqsNotificationService;

    @Autowired
    EnvironmentProperties environmentProperties;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private MasterDataCache masterDataCache;


    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private OrderValidationService orderValidationService;



    private static final String PARTNER_NAME = "MAGICPIN";

    @Override
    public MagicpinOrderResponse addMagicpinOrder(MagicpinOrder orderRequest) {
        return null;
    }

    @Override
    public void cancelOrder(MagicPinStatusUpdate magicPinStatusUpdate){
        List<PartnerOrderDetail> partnerOrderDetails = trackService
                .getPartnerOrderByPartnerOrderId(Long.toString(magicPinStatusUpdate.getOrderId()));
        if(!CollectionUtils.isEmpty(partnerOrderDetails)){
            log.info("Trying To Cancel Magic Pin Order With Order Id : {} ",magicPinStatusUpdate.getOrderId());
            if(Objects.nonNull(partnerOrderDetails.get(0).getKettleOrder())){
                Integer approvedBy = ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID;
                orderValidationService.cancelOrder(partnerOrderDetails.get(0),false,null, approvedBy);
            }
        }
    }

    @Override
    public void updateDeliveryStatus(MagicpinDeliveryUpdate deliveryUpdate) {
        List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(String.valueOf(deliveryUpdate.getOrderId()));
        if (Objects.isNull(deliveryUpdate.getRiderName()) && Objects.isNull(deliveryUpdate.getRiderNumber())) {
            log.info("Rider not assigned for magicpin order :::: {} ", new Gson().toJson(deliveryUpdate));
            return;
        }
        partnerOrderDetails.forEach(partnerOrderDetail -> {
            PartnerDeliveryStatus partnerDeliveryStatus = MagicpinDeliveryUpdateMapper.INSTANCE.toPartnerDelivery(deliveryUpdate);
            partnerDeliveryStatus.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
            partnerDeliveryStatus.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
            partnerOrderDetail.getDeliveryStatuses().add(partnerDeliveryStatus);
            trackService.updatePartnerOrder(partnerOrderDetail);
            try {
                if (Objects.nonNull(partnerOrderDetail.getPartnerOrderStatus()) && !PartnerOrderStatus.CHECKED.
                        equals(partnerOrderDetail.getPartnerOrderStatus())) {
                    publishToOrderDeliveryStatusQueue(partnerOrderDetail, deliveryUpdate);
                }

            } catch (Exception e) {
                log.info("Error While Publishing Partner Delivery Order Picked Up Event for MagicPin Order Id :::::: {} , {}",
                        deliveryUpdate.getOrderId(), e.getMessage());
            }
        });
    }

    private void publishToOrderDeliveryStatusQueue(PartnerOrderDetail partnerOrderDetail, MagicpinDeliveryUpdate deliveryUpdate) throws JMSException {
        OrderDeliveryStatusUpdate orderDeliveryStatusUpdate = getOrderDeliveryStatusUpdate(partnerOrderDetail, deliveryUpdate);
        sqsNotificationService.publishToSQSFifo(environmentProperties.getEnvType().name(), orderDeliveryStatusUpdate,
                "_PARTNER_ORDER_DELIVERY_STATUS.fifo", Regions.valueOf(environmentProperties.getAwsQueueRegion()));

    }

    private OrderDeliveryStatusUpdate getOrderDeliveryStatusUpdate(PartnerOrderDetail partnerOrderDetail, MagicpinDeliveryUpdate deliveryUpdate) {
        Date billingServerTime = AppUtils.parseDate(partnerOrderDetail.getAddTime());
        if (Objects.nonNull(partnerOrderDetail.getKettleOrder())) {
            Order order = (Order) partnerOrderDetail.getKettleOrder();
            billingServerTime = order.getBillingServerTime();
        }
        return OrderDeliveryStatusUpdate.builder().orderId(partnerOrderDetail.getKettleOrderId()).unitId(partnerOrderDetail.getUnitId()).
                addTime(ChannelPartnerUtils.getCurrentTimestamp()).status(deliveryUpdate.getStatus()).partnerName(partnerOrderDetail.getPartnerName())
                .partnerOrderId(partnerOrderDetail.getPartnerOrderId()).riderContact(Objects.nonNull(deliveryUpdate.getRiderNumber()) ? deliveryUpdate.getRiderNumber()
                        : null).riderName(Objects.nonNull(deliveryUpdate.getRiderName()) ? deliveryUpdate.getRiderName() : null)
                .billingServerTime(billingServerTime).build();
    }




    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public boolean setUnitDeliveryStatus(List<Integer> unitIds, Boolean status, Integer brandId)
            throws ChannelPartnerException {
        for (Integer unitId : unitIds) {
            if (masterDataCache.getUnit(unitId) != null) {
                Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
                boolean mappingValid = channelPartnerDataCache.isMappingValid(partnerId,unitId);
                if (mappingValid) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
                    String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                    MagicpinOutletDeliveryStatusRequest magicpinRequest = new MagicpinOutletDeliveryStatusRequest();
                    magicpinRequest.setPartnerStoreId(restaurantId);
                    magicpinRequest.setStatus(status);
                    MagicpinUnitAndInventoryUpdateResponse response;
                    try {
                        response = webServiceHelper.callMagicpinApi(environmentProperties,
                                MagicpinServiceEndPoints.STORE_STATUS_UPDATE, HttpMethod.POST, magicpinRequest,
                                MagicpinUnitAndInventoryUpdateResponse.class, null);
                        if (response == null || response.getResponseCode() != 0) {
                            String responseJson = new Gson().toJson(response);
                            log.error("Error in updating unit status on Magicpin:::: {}", responseJson);
                            String message = ChannelPartnerUtils.getMessage("Error in updating unit status on MAGICPIN", "::::REQUEST::::" + new Gson().toJson(magicpinRequest) + "\n" + "::::RESPONSE::::" + new Gson().toJson(response));

                            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                    message);
                            throw new ChannelPartnerException("Error updating unit status.");
                        }
                    } catch (HttpStatusCodeException e) {
                        catchLogAndThrowMagicpinException(e, "Error in updating unit status on MAGICPIN:::: ");
                    }
                }
            }
        }
        return true;
    }


    private void catchLogAndThrowMagicpinException(HttpStatusCodeException e, String title) {
        try {
            String errorBody = e.getResponseBodyAsString();
            log.error(title + ":" + errorBody);
            String message = ChannelPartnerUtils.getMessage(title, errorBody);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
        } catch (Exception ex) {
            log.error("Error in logging magicpin exception", ex);
        }
    }


    @Override
    public void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException{
        MagicpinOrderUpdate magicpinOrderUpdate = new MagicpinOrderUpdate();
        magicpinOrderUpdate.setOrderId(Long.valueOf(partnerOrderDetail.getPartnerOrderId()));
        switch(request.getState()){
            case PREPARED:
                magicpinOrderUpdate.setStatus(MagicpinOrderUpdateEnum.DISPTACH_READY.toString());
                break;
            case PICKEDUP:
                magicpinOrderUpdate.setStatus(MagicpinOrderUpdateEnum.DISPATCHED.toString());
                break;
            case DELIVERED:
                magicpinOrderUpdate.setStatus(MagicpinOrderUpdateEnum.DELIVERED.toString());
                break;
            default:
                magicpinOrderUpdate.setStatus(request.getState().name());
                break;
        }
        MagicpinUnitAndInventoryUpdateResponse response;
        try {
            response = webServiceHelper.callMagicpinApi(environmentProperties,
                    MagicpinServiceEndPoints.ORDER_STATUS_UPDATE, HttpMethod.POST, magicpinOrderUpdate,
                    MagicpinUnitAndInventoryUpdateResponse.class, null);
            if (response == null) {
                //log.error("Error in updating order status on Magicpin:::: {}", responseJson);
                String message = ChannelPartnerUtils.getMessage("Error in updating order status on MAGICPIN", "::::REQUEST::::" + new Gson().toJson(magicpinOrderUpdate) + "\n" + "::::RESPONSE::::" + new Gson().toJson(response));

                SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
                throw new ChannelPartnerException("Error updating order status.");
            }else{
                String responseJson = new Gson().toJson(response);
                log.info("Response from magicpin on order status update : {} ",responseJson);
            }
        } catch (HttpStatusCodeException e) {
            catchLogAndThrowMagicpinException(e, "Error in updating order status on MAGICPIN:::: ");
        }

    }

}
