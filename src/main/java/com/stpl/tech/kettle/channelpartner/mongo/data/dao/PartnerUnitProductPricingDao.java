package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface PartnerUnitProductPricingDao extends MongoRepository<PartnerUnitProductPricingDetail, String> {

	List<PartnerUnitProductPricingDetail> findAllByUnitIdAndPartnerIdAndBrandIdAndActive(Integer unitId,
                                                                                 Integer kettlePartnerId, Integer brandId, boolean active);

	List<PartnerUnitProductPricingDetail> findAllByActive(boolean active);

}
