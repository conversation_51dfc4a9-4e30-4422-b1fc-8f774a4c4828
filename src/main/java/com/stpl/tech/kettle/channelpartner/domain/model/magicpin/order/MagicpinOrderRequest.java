package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"orderId", "shipmentId", "createdAt", "userName", "phoneNo", "amount", "tax", "note", "orderType", "deliveryFee", "deliveryInitiator", "items", "contactLessDelivery", "paymentMode", "merchantFundedDiscount", "orderLevelCharges", "riderOtp", "shippingAddress", "billingAddress", "orderTaxes", "merchantData"})
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class MagicpinOrderRequest implements Serializable {

    @JsonProperty("orderId")
    private Long orderId;
    @JsonProperty("shipmentId")
    private Long shipmentId;

    @JsonProperty("createdAt")
    private Date createdAt;
    @JsonProperty("userName")
    private String userName;

    @JsonProperty("phoneNo")
    private String phoneNo;
    @JsonProperty("amount")
    private Float amount;
    @JsonProperty("tax")
    private Float tax;
    @JsonProperty("note")
    private String note;
    @JsonProperty("orderType")
    private String orderType;
    @JsonProperty("deliveryFee")
    private Float deliveryFee;
    @JsonProperty("deliveryInitiator")
    private String deliveryInitiator;

    @JsonProperty("items")
    private List<MagicpinOrderItems> magicpinOrderItems;
    @JsonProperty("contactLessDelivery")
    private boolean contactLessDelivery;
    @JsonProperty("paymentMode")
    private String paymentMode;
    @JsonProperty("merchantFundedDiscount")
    private Float merchantFundedDiscount;
    @JsonProperty("orderLevelCharges")
    private MagicpinChargesDetails orderLevelCharges;

    @JsonProperty("additionalCharges")
    private List<MagicPinAdditionalCharges> additionalCharges;
    @JsonProperty("riderOtp")
    private String riderOtp;
    @JsonProperty("shippingAddress")
    private MagicpinAddressDetail shippingAddress;
    @JsonProperty("billingAddress")
    private MagicpinAddressDetail billingAddress;
    @JsonProperty("orderTaxes")
    private List<MagicpinTaxDetails> orderTaxes;
    @JsonProperty("merchantData")
    private MagicpinMerchantData magicpinMerchantData;

}
