package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@Getter
@Setter
@Document
@XmlRootElement(name = "DealOfTheDay")
public class DealOfTheDay {

    @Id
    protected String dealOfTheDayId;

    protected Integer kettlePartnerId;

    protected String partnerName;

    protected List<DotdProduct> dotdProducts;

    protected List<Integer> productIds;
}
