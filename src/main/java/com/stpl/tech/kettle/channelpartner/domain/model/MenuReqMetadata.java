package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MenuReqMetadata implements Serializable {

    private Integer kettlePartnerId;
    private Integer brandId;
    private Integer employeeId;
    private Integer unitId;
    private MenuType menuType;

    private PartnerUnitMenuDetail partnerUnitMenuData;
    private PartnerUnitMenuVersionMapping partnerUnitMenuVersionMapping;
    private UnitMenuAddVO unitMenuAddVO;

    private String restaurantId ;
    private UnitPartnerBrandKey unitPartnerBrandKey ;

    public MenuReqMetadata(Integer partnerId, Integer brandId, Integer employeeId, Integer unitId, MenuType menuType) {
        this.kettlePartnerId = partnerId;
        this.brandId=brandId;
        this.employeeId=employeeId;
        this.unitId=unitId;
        this.menuType=menuType;
    }
}
