package com.stpl.tech.kettle.channelpartner.core;


import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Component
@RefreshScope
public class SyncLock {

    @Autowired
    private EnvironmentProperties properties;

    private final ConcurrentReferenceHashMap<String, ReentrantLock> map;
    private ReentrantLock currentLock;

    public SyncLock() {
        map = new ConcurrentReferenceHashMap<>();
    }

    public <T> T syncLock(String key, Callable<T> task) {
        return syncLock(key, null, task, properties.isAcquireLockUsed());
    }

    public <T> T syncLock(String key, Long timeInMilliseconds, Callable<T> task, Boolean isAcquiredLockUsed) {
        try {
            currentLock = this.map.compute(key, (K, V) -> V == null ? new ReentrantLock() : V);
            if (!isAcquiredLockUsed) {
                return task.call();
            } else {
                boolean isLockAcquired = currentLock.tryLock(timeInMilliseconds == null ? properties.lockTimeInMilliSecond() : timeInMilliseconds, TimeUnit.MILLISECONDS);
                if (isLockAcquired) {
                    return task.call();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            if (currentLock.isHeldByCurrentThread()) {
                currentLock.unlock();
            }
        }
        return null;
    }

    public void syncLock(String key, Runnable task) {
        syncLock(key, null, task);
    }

    public void syncLock(String key, Long timeInMilliseconds, Runnable task) {
        try {
            currentLock = this.map.compute(key, (K, V) -> V == null ? new ReentrantLock() : V);
            boolean isLockAcquired = currentLock.tryLock(timeInMilliseconds == null ? 1000 : timeInMilliseconds, TimeUnit.MILLISECONDS);
            if (isLockAcquired) {
                task.run();
            } else {
                throw new RuntimeException("Not able to acquire lock!");
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            if (currentLock.isHeldByCurrentThread()) {
                currentLock.unlock();
            }
        }
    }
}
