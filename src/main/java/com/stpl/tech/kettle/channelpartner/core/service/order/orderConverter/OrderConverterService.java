package com.stpl.tech.kettle.channelpartner.core.service.order.orderConverter;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.converters.AbstractConverters;
import com.stpl.tech.kettle.channelpartner.core.converters.ZomatoConvertersV3;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.OrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderFactory;
import com.stpl.tech.kettle.channelpartner.core.util.CRMServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.CODCustomerLoginData;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerAddressData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCustomerDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderable;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.analysis.function.Add;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Log4j2
public class OrderConverterService extends AbstractOrderConverterService {

    public OrderConverterService(OrderItemConverterService orderItemConverterService ,
                          EnvironmentProperties environmentProperties , ChannelPartnerDataCache channelPartnerDataCache,
                          WebServiceHelper webServiceHelper , MasterDataCache masterDataCache ,
                          OrderValidationService orderValidationService) {
        super(orderItemConverterService,environmentProperties,channelPartnerDataCache,webServiceHelper
        ,masterDataCache,orderValidationService);
    }

    @Override
    public <R> Order convertOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual,
                                  PartnerPrimaryData partnerPrimaryData) {
        R request = (R) partnerOrderDetail.getPartnerOrder();
        Customer customer = new Customer();
        Address selectedAddress = null;

        // this flow works only  if partner shares the customer contact number

        if(!environmentProperties.isDefaultPartnerCustomerFlow(partnerOrderDetail.getPartnerName())) {
            customer = getCustomer(partnerPrimaryData.getPartnerCustomerData(), partnerOrderDetail);
            if(Objects.nonNull(partnerPrimaryData.getPartnerCustomerData().getPartnerCustomerAddressData())) {
                addCustomerAddress(customer, partnerPrimaryData.getPartnerCustomerData(), partnerOrderDetail.getUnitId());
            }
        }else {
            customer = getPartnerCustomer(partnerOrderDetail.getPartnerName());
        }
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId();
        RestaurantPartnerKey key = new RestaurantPartnerKey(partnerPrimaryData.getOutletId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
        Order order = OrderFactory.getOrderObject(customer, selectedAddress, masterDataCache, data,
                environmentProperties , partnerPrimaryData);
        Map<String, TaxDataVO> taxMap = orderValidationService.getUnitProductTaxCodeMap(data).getTaxMap();
        Map<Integer, Product> products = new HashMap<>();
        masterDataCache.getUnitProductDetails(data.getPriceProfileUnitId()).forEach(product ->
                products.put(product.getId(), product)
        );
        Map<Integer, Product> cafeProducts = new HashMap<>();
        masterDataCache.getUnitProductDetails(data.getUnitId()).forEach(product ->
                cafeProducts.put(product.getId(), product)
        );
        Map<Integer, Map<String, BigDecimal>> pricingMap = channelPartnerDataCache.getPartnerUnitProductPricing(new UnitPartnerBrandKey(data.getUnitId(),
                data.getBrandId(), data.getPartnerId()));
        orderItemConverterService.addItemsToOrder(order, request, partnerOrderDetail, taxMap, products,
                data.getPriceProfileUnitId(), pricingMap, cafeProducts);
        orderItemConverterService.addOrderLevelChargeItemToOrder(order,request,partnerOrderDetail,false,taxMap,products
        ,data.getPriceProfileUnitId(),order.getOrders());
        setTransactionDetail(order, masterDataCache, partnerPrimaryData);
        return order;
    }




    private Customer getCustomer(PartnerCustomerData customerDetail, PartnerOrderDetail partnerOrderDetail) {
        CODCustomerLoginData data = OrderFactory.getCustomerData(customerDetail, partnerOrderDetail, this.environmentProperties);
        String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP_UPDATE;
        data = webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), data,
                CODCustomerLoginData.class);
        return data.getCustomer();
    }

    private void addCustomerAddress(Customer customer, PartnerCustomerData partnerCustomerData, Integer unitId) {
        CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
        Address newAddress = OrderFactory.getCustomerAddressData(codCustomerLoginData, partnerCustomerData, customer,
                unitId, this.masterDataCache);
        if(Boolean.FALSE.equals(isAddressAlreadyAdded(customer,newAddress))){
            codCustomerLoginData.setNewAddress(newAddress);
            String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_ADD_ADDRESS;
            webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
                    Integer.class);
        }
    }

    private Boolean isAddressAlreadyAdded(Customer customer,Address newAddress){
        Boolean isAddressAlreadyAdded = false;
        if(!CollectionUtils.isEmpty(customer.getAddresses())){
            for(Address address : customer.getAddresses()){
                if(address.toString().equalsIgnoreCase(newAddress.toString())){
                    isAddressAlreadyAdded = true;
                }
            }
        }
        return isAddressAlreadyAdded;
    }

    private Customer getPartnerCustomer(String partnerName) {
        if (channelPartnerDataCache.getPartnerKettleCustomer(partnerName) != null
                && channelPartnerDataCache.getPartnerKettleCustomer(partnerName).getId() != 0) {
            return channelPartnerDataCache.getPartnerKettleCustomer(partnerName);
        } else {
            CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
            codCustomerLoginData.setContactNumber(environmentProperties.getPartnerCustomerContact(partnerName));
            codCustomerLoginData.setCustomer(new Customer());
            UserSessionDetail userSessionDetail = new UserSessionDetail(AppConstants.SYSTEM_EMPLOYEE_ID, 26626, 1);
            userSessionDetail.setBusinessDate(AppUtils.getBusinessDate());
            codCustomerLoginData.setSession(userSessionDetail);
            codCustomerLoginData.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
            CODCustomerLoginData customer = webServiceHelper.postWithAuth(environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP,
                    environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
                    CODCustomerLoginData.class);
            channelPartnerDataCache.setPartnerCustomer(partnerName,customer.getCustomer());
            return customer.getCustomer();
        }
    }

    private void setTransactionDetail(Order order, MasterDataCache masterDataCache ,
                                        PartnerPrimaryData partnerPrimaryData) {
        TransactionDetail transactionDetail = new TransactionDetail();
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal taxable = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            total = ChannelPartnerUtils.add(total, item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            taxable = ChannelPartnerUtils.add(taxable, item.getAmount());
        }
        transactionDetail.setTotalAmount(total);
        transactionDetail.setTaxableAmount(taxable);
        BigDecimal discountValue = BigDecimal.ZERO; //totalDiscountValue
        BigDecimal discountPercent = BigDecimal.ZERO;
        BigDecimal discountPromotional = BigDecimal.ZERO;
        DiscountDetail discountDetail = new DiscountDetail();

        //        AGGREGATING DISOUNTS AT ITEM LEVEL AND SETTING THE FINAL TOTAL DISCOUNT VALUE TO TRANSACTIONALDETAIL
        log.info("Aggregating Discpunt Vales from orderItem Level to get finalDiscountValue for order with orderId :{}", order.getOrderId());
        for (OrderItem orderItem : order.getOrders()) {
            BigDecimal orderItemDiscountValue = BigDecimal.ZERO;
            BigDecimal percentage = BigDecimal.ZERO;
            BigDecimal promotionalOffer = BigDecimal.ZERO;
            if (Objects.nonNull(orderItem.getDiscountDetail())) {
                orderItemDiscountValue = orderItem.getDiscountDetail().getDiscount().getValue();
                percentage = orderItem.getDiscountDetail().getDiscount().getPercentage();
                promotionalOffer = orderItem.getDiscountDetail().getPromotionalOffer();
            }
//           BigDecimal percentage = orderItemDiscountValue.divide(orderItem.getTotalAmount(), 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            discountValue = discountValue.add(orderItemDiscountValue);
           // discountPercent = discountPercent.add(percentage);
            discountPromotional = discountPromotional.add(promotionalOffer);
        }
//        PercentageDetail percentageDetail = getDiscountValuesV3(request);
//        discountValue = percentageDetail.getValue();
//        discountPercent = percentageDetail.getPercentage();
        discountPercent = ChannelPartnerUtils.divide(discountPromotional,total);
        discountDetail.setDiscountReason(partnerPrimaryData.getDiscountName());
        discountDetail.setDiscountCode(2004);
        discountDetail.setDiscount(new PercentageDetail());
        discountDetail.getDiscount().setValue(discountValue);
        discountDetail.getDiscount().setPercentage(discountPercent);
        discountDetail.setPromotionalOffer(discountPromotional);
        discountDetail.setTotalDiscount(
                ChannelPartnerUtils.add(discountDetail.getDiscount().getValue(), discountDetail.getPromotionalOffer()));
        transactionDetail.setDiscountDetail(discountDetail);

        log.info("Aggregating Tax Vales from orderItem Level to get finalTaxValue for order with orderId :{}", order.getOrderId());
        Map<TaxDetailKey, TaxDetail> taxMap = new HashMap<>();
        BigDecimal totalTax = AbstractConverters.aggregateTaxesFromOrderItems(order.getOrders(), taxMap);
        transactionDetail.getTaxes().addAll(taxMap.values());
        transactionDetail.setTax(totalTax);

        Map<TaxDetailKey, TaxDetail> collectionTaxMap = new HashMap<>();
        BigDecimal collectionTotalTax = AbstractConverters.aggregateCollectionTaxesFromOrderItems(order.getOrders(), collectionTaxMap);
        transactionDetail.setCollectionTax(collectionTotalTax);
        BigDecimal collectionTotalAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(), transactionDetail.getCollectionTax());
        BigDecimal paidAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(),
                transactionDetail.getTax());
        transactionDetail.setPaidAmount(paidAmount.setScale(0, RoundingMode.HALF_UP));
        transactionDetail.setCollectionAmount(collectionTotalAmount.setScale(5, RoundingMode.HALF_UP));
        transactionDetail.setRoundOffValue(ChannelPartnerUtils.subtract(transactionDetail.getPaidAmount(), paidAmount));
        AbstractConverters.calculateSaving(transactionDetail, order);
        order.setTransactionDetail(transactionDetail);

        addSettlementToOrder(order , masterDataCache , partnerPrimaryData);
    }

    private void addSettlementToOrder(Order order,  MasterDataCache masterDataCache,
                                      PartnerPrimaryData partnerPrimaryData) {
        Settlement settlement = new Settlement();
        settlement.setAmount(order.getTransactionDetail().getPaidAmount());
        if (partnerPrimaryData.getCashToCollect().compareTo(BigDecimal.ZERO) > 0) {
            for (Integer key : masterDataCache.getPaymentModes().keySet()) {
                if (masterDataCache.getPaymentModes().get(key).getName()
                        .equalsIgnoreCase(partnerPrimaryData.getPaymentMode().trim())) {
                    settlement.setMode(key);
                }
            }
        } else {
            settlement.setMode(6); // FOR CREDIT
        }
        if (settlement.getMode() == 6) {
            ExternalSettlement externalSettlement = new ExternalSettlement();
            externalSettlement.setAmount(order.getTransactionDetail().getPaidAmount());
            externalSettlement.setExternalTransactionId(environmentProperties.
                    getPartnerCreditAccount(partnerPrimaryData.getPartnerName())); // partner credit account id
            settlement.getExternalSettlements().add(externalSettlement);
        }
        order.getSettlements().add(settlement);
    }



}
