package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "prep_time",
        "customer_arrival_timestamp"
})
public class ZomatoOrderTakeAwayDetailsV3 {

    @JsonProperty("prep_time")
    private Integer prepTime;
    @JsonProperty("customer_arrival_timestamp")
    private Integer customerArrivalTimestamp;

    @JsonProperty("prep_time")
    public Integer getPrepTime() {
        return prepTime;
    }

    @JsonProperty("prep_time")
    public void setPrepTime(Integer prepTime) {
        this.prepTime = prepTime;
    }

    @JsonProperty("customer_arrival_timestamp")
    public Integer getCustomerArrivalTimestamp() {
        return customerArrivalTimestamp;
    }

    @JsonProperty("customer_arrival_timestamp")
    public void setCustomerArrivalTimestamp(Integer customerArrivalTimestamp) {
        this.customerArrivalTimestamp = customerArrivalTimestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoOrderTakeAwayDetailsV3 that = (ZomatoOrderTakeAwayDetailsV3) o;

        return new EqualsBuilder()
                .append(prepTime, that.prepTime)
                .append(customerArrivalTimestamp, that.customerArrivalTimestamp)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(prepTime)
                .append(customerArrivalTimestamp)
                .toHashCode();
    }

	@Override
	public String toString() {
		return "ZomatoOrderTakeAwayDetailsV3 [prepTime=" + prepTime + ", customerArrivalTimestamp="
				+ customerArrivalTimestamp + "]";
	}
    
    

}