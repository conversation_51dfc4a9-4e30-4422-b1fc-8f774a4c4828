
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.math3.analysis.function.Add;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "addons",
    "paid_addons",
    "cgst",
    "cgst_percent",
    "id",
    "igst",
    "igst_percent",
    "name",
    "packing_charges",
    "price",
    "quantity",
    "reward_type",
    "sgst",
    "sgst_percent",
    "subtotal",
    "variants",
    "gst_inclusive"
})
public class Item {

    @JsonProperty("addons")
    private List<Addon> addons = null;
    @JsonProperty("paid_addons")
    private List<Addon> paidAddOns=null;
    @JsonProperty("cgst")
    private float cgst;
    @JsonProperty("cgst_percent")
    private float cgstPercent;
    @JsonProperty("id")
    private String id;
    @JsonProperty("igst")
    private float igst;
    @JsonProperty("igst_percent")
    private float igstPercent;
    @JsonProperty("name")
    private String name;
    @JsonProperty("packing_charges")
    private float packingCharges;
    @JsonProperty("price")
    private float price;
    @JsonProperty("quantity")
    private int quantity;
    @JsonProperty("reward_type")
    private String rewardType;
    @JsonProperty("sgst")
    private float sgst;
    @JsonProperty("sgst_percent")
    private float sgstPercent;
    @JsonProperty("subtotal")
    private float subtotal;
    @JsonProperty("variants")
    private List<Variant> variants = null;
    @JsonProperty("discount")
    private float discount;
    @JsonProperty("gst_inclusive")
    private boolean gstInclusive;
    @JsonProperty("final_sub_total")
    private float finalSubTotal;
    @JsonProperty("is_veg")
    private int isVeg;
    @JsonProperty("free_quantity")
    private int freeQuantity;
    @JsonProperty("item_restaurant_offers_discount")
    private float itemRestaurantOfferDiscount;
    @JsonProperty("gst_liability")
    private String gstLiability;

    @JsonProperty("addons")
    public List<Addon> getAddons() {
        return addons;
    }

    @JsonProperty("addons")
    public void setAddons(List<Addon> addons) {
        this.addons = addons;
    }

    @JsonProperty("cgst")
    public float getCgst() {
        return cgst;
    }

    @JsonProperty("cgst")
    public void setCgst(float cgst) {
        this.cgst = cgst;
    }

    @JsonProperty("cgst_percent")
    public float getCgstPercent() {
        return cgstPercent;
    }

    @JsonProperty("cgst_percent")
    public void setCgstPercent(float cgstPercent) {
        this.cgstPercent = cgstPercent;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("igst")
    public float getIgst() {
        return igst;
    }

    @JsonProperty("igst")
    public void setIgst(float igst) {
        this.igst = igst;
    }

    @JsonProperty("igst_percent")
    public float getIgstPercent() {
        return igstPercent;
    }

    @JsonProperty("igst_percent")
    public void setIgstPercent(float igstPercent) {
        this.igstPercent = igstPercent;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("packing_charges")
    public float getPackingCharges() {
        return packingCharges;
    }

    @JsonProperty("packing_charges")
    public void setPackingCharges(float packingCharges) {
        this.packingCharges = packingCharges;
    }

    @JsonProperty("price")
    public float getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(float price) {
        this.price = price;
    }

    @JsonProperty("quantity")
    public int getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("reward_type")
    public String getRewardType() {
        return rewardType;
    }

    @JsonProperty("reward_type")
    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    @JsonProperty("sgst")
    public float getSgst() {
        return sgst;
    }

    @JsonProperty("sgst")
    public void setSgst(float sgst) {
        this.sgst = sgst;
    }

    @JsonProperty("sgst_percent")
    public float getSgstPercent() {
        return sgstPercent;
    }

    @JsonProperty("sgst_percent")
    public void setSgstPercent(float sgstPercent) {
        this.sgstPercent = sgstPercent;
    }

    @JsonProperty("subtotal")
    public float getSubtotal() {
        return subtotal;
    }

    @JsonProperty("subtotal")
    public void setSubtotal(float subtotal) {
        this.subtotal = subtotal;
    }

    @JsonProperty("variants")
    public List<Variant> getVariants() {
        return variants;
    }

    @JsonProperty("variants")
    public void setVariants(List<Variant> variants) {
        this.variants = variants;
    }

    @JsonProperty("discount")
    public float getDiscount() {
        return discount;
    }

    @JsonProperty("discount")
    public void setDiscount(float discount) {
        this.discount = discount;
    }

    @JsonProperty("gst_inclusive")
    public boolean isGstInclusive() {
		return gstInclusive;
	}

    @JsonProperty("gst_inclusive")
	public void setGstInclusive(boolean gstInclusive) {
		this.gstInclusive = gstInclusive;
	}

    @JsonProperty("final_sub_total")
    public float getFinalSubTotal() {
        return finalSubTotal;
    }

    @JsonProperty("final_sub_total")
    public void setFinalSubTotal(float finalSubTotal) {
        this.finalSubTotal = finalSubTotal;
    }

    @JsonProperty("is_veg")
    public int getIsVeg() {
        return isVeg;
    }

    @JsonProperty("is_veg")
    public void setIsVeg(int isVeg) {
        this.isVeg = isVeg;
    }

    @JsonProperty("free_quantity")
    public int getFreeQuantity() {
        return freeQuantity;
    }

    @JsonProperty("free_quantity")
    public void setFreeQuantity(int freeQuantity) {
        this.freeQuantity = freeQuantity;
    }

    @JsonProperty("item_restaurant_offers_discount")
    public float getItemRestaurantOfferDiscount() {
        return itemRestaurantOfferDiscount;
    }

    @JsonProperty("item_restaurant_offers_discount")
    public void setItemRestaurantOfferDiscount(float itemRestaurantOfferDiscount) {
        this.itemRestaurantOfferDiscount = itemRestaurantOfferDiscount;
    }

    @JsonProperty("gst_liability")
    public String getGstLiability() {
        return gstLiability;
    }

    @JsonProperty("gst_liability")
    public void setGstLiability(String gstLiability) {
        this.gstLiability = gstLiability;
    }

    @JsonProperty("paid_addons")
    public List<Addon> getPaidAddOns() {
        return paidAddOns;
    }
    @JsonProperty("paid_addons")
    public void setPaidAddOns(List<Addon> paidAddOns) {
        this.paidAddOns = paidAddOns;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Item item = (Item) o;

        return new EqualsBuilder()
            .append(cgst, item.cgst)
            .append(cgstPercent, item.cgstPercent)
            .append(igst, item.igst)
            .append(igstPercent, item.igstPercent)
            .append(packingCharges, item.packingCharges)
            .append(price, item.price)
            .append(quantity, item.quantity)
            .append(sgst, item.sgst)
            .append(sgstPercent, item.sgstPercent)
            .append(subtotal, item.subtotal)
            .append(discount, item.discount)
            .append(gstInclusive, item.gstInclusive)
            .append(finalSubTotal, item.finalSubTotal)
            .append(isVeg, item.isVeg)
            .append(freeQuantity, item.freeQuantity)
            .append(itemRestaurantOfferDiscount, item.itemRestaurantOfferDiscount)
            .append(addons, item.addons)
            .append(id, item.id)
            .append(name, item.name)
            .append(rewardType, item.rewardType)
            .append(variants, item.variants)
            .append(gstLiability, item.gstLiability)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(addons)
            .append(cgst)
            .append(cgstPercent)
            .append(id)
            .append(igst)
            .append(igstPercent)
            .append(name)
            .append(packingCharges)
            .append(price)
            .append(quantity)
            .append(rewardType)
            .append(sgst)
            .append(sgstPercent)
            .append(subtotal)
            .append(variants)
            .append(discount)
            .append(gstInclusive)
            .append(finalSubTotal)
            .append(isVeg)
            .append(freeQuantity)
            .append(itemRestaurantOfferDiscount)
            .append(gstLiability)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "Item{" +
            "addons=" + addons +
            ", cgst=" + cgst +
            ", cgstPercent=" + cgstPercent +
            ", id='" + id + '\'' +
            ", igst=" + igst +
            ", igstPercent=" + igstPercent +
            ", name='" + name + '\'' +
            ", packingCharges=" + packingCharges +
            ", price=" + price +
            ", quantity=" + quantity +
            ", rewardType='" + rewardType + '\'' +
            ", sgst=" + sgst +
            ", sgstPercent=" + sgstPercent +
            ", subtotal=" + subtotal +
            ", variants=" + variants +
            ", discount=" + discount +
            ", gstInclusive=" + gstInclusive +
            ", finalSubTotal=" + finalSubTotal +
            ", isVeg=" + isVeg +
            ", freeQuantity=" + freeQuantity +
            ", itemRestaurantOfferDiscount=" + itemRestaurantOfferDiscount +
            ", gstLiability='" + gstLiability + '\'' +
            '}';
    }
}
