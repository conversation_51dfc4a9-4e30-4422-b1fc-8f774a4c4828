package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerRequestTarget;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.ApplicationName;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
public class MenuEventFactoryService extends EventAbstractFactory<UnitMenuAddVO, UnitMenuAddVO> {

    private PartnerMetadataBuilder partnerMetadataBuilder;
    public MenuEventFactoryService(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
        this.partnerMetadataBuilder=partnerMetadataBuilder;
    }

    private static PartnerAbstractFactory partnerAbstractFactory;

    @Override
    public List<UnitMenuAddVO> prepareMetadata(UnitMenuAddVO unitMenuAddVO, PartnerActionEventType eventType, String partnerName) throws ChannelPartnerException {
        if (Objects.nonNull(unitMenuAddVO)) {
            partnerAbstractFactory = ServiceFactory.getPartnerAbstractFactory(partnerName,partnerMetadataBuilder);
            switch (eventType) {
                case UPDATE_ALL_UNIT_MENU -> {
                    return pushMenuToUnits(unitMenuAddVO.getUnitIdsForMenu(), unitMenuAddVO.getBrandId(), unitMenuAddVO.getEmployeeId(), unitMenuAddVO.getKettlePartnerId(), unitMenuAddVO.getMenuType(),partnerName);
                }
                case SCHEDULED_MENU_PUSH -> {
                    return scheduledMenuPush(unitMenuAddVO.getUnitIdsForMenu(), unitMenuAddVO.getMenuType(), partnerName);
                }
            }
        } else {
            throw new ChannelPartnerException("Required null object of type UnitMenuAddVO before processing Menu Request");
        }
        return null;
    }


    private List<UnitMenuAddVO> pushMenuToUnits(List<Integer> unitIdsForMenu, Integer brandId, Integer employeeId,
                                                Integer kettlePartnerId, MenuType menuType, String partnerName) throws ChannelPartnerException {
        List<Integer> errorUnitIds = new ArrayList<>();
        List<UnitMenuAddVO> menuSuccessCreation = new ArrayList<>();
        for (Integer unitId : unitIdsForMenu) {
            if (validatePartnerAndMapping(kettlePartnerId, unitId) && brandId != null) {
                UnitMenuAddVO unitMenuAddVO = createMenu(kettlePartnerId, brandId, employeeId, unitId, menuType);
                if (Objects.isNull(unitMenuAddVO)) {
                    errorUnitIds.add(unitId);
                    continue;
                } else {
                    menuSuccessCreation.add(unitMenuAddVO);
                }
//                callZomatoMenuPushAPI(request, employeeId);
            }
        }
        if (!errorUnitIds.isEmpty()) {
            String title = partnerName+ " Bulk Menu Push Failed for This Units ";
            String message = ChannelPartnerUtils.getMessage(title, new ChannelPartnerException(errorUnitIds.toString()));
            commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(message,SlackNotification.PARTNER_INTEGRATION, false,partnerName, ApplicationName.KETTLE_SERVICE.name());

        }
        return menuSuccessCreation;
    }

    private boolean validatePartnerAndMapping(Integer kettlePartnerId, Integer unitId) {
        if (unitId != null && kettlePartnerId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                    .anyMatch(unitChannelPartnerMapping -> unitChannelPartnerMapping.getChannelPartner()
                            .getId() == kettlePartnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            return partnerDetail != null && mappingValid;
        }
        return false;
    }


    private List<UnitMenuAddVO> scheduledMenuPush(List<Integer> unitIdsForMenu, MenuType menuType, String partnerName) throws ChannelPartnerException {
        Integer employeeId = ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID;
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerName).getKettlePartnerId();
        List<UnitMenuAddVO> menuSuccessCreation = new ArrayList<>();
        Map<Integer, Map<Integer, CafeMenuAutoPush>> menuAutoPushMap = partnerMenuService.getMapValue(unitIdsForMenu);
        for (Integer unitId : unitIdsForMenu) {
            if (validatePartnerAndMapping(partnerId, unitId)) {
                masterDataCache.getUnitPartnerBrandMappingMetaData().keySet().stream().filter(key ->
                        key.getPartnerId().equals(partnerId) && key.getUnitId().equals(unitId)).forEach(key -> {
                    Map<Integer, CafeMenuAutoPush> flagDetail = menuAutoPushMap.get(key.getUnitId());
                    if (Objects.nonNull(flagDetail)) {
                        CafeMenuAutoPush cafeAutoPush = flagDetail.get(key.getBrandId());
                        if (cafeAutoPush != null && Boolean.TRUE.equals(cafeAutoPush.getZomatoMenu())) {
                            UnitMenuAddVO unitMenuAddVO = createMenu(partnerId, key.getBrandId(), employeeId, unitId, menuType);
                            if (Objects.nonNull(unitMenuAddVO)) {
                                menuSuccessCreation.add(unitMenuAddVO);
                            }

//                            if (partnerMenuService.checkVersionMenuAuditData(request, partnerName)) {
//                                callZomatoMenuPushAPI(request, employeeId);
//                            }
                        } else {
                            log.info(" Menu is not Push for " + partnerName + " unit is " + key.getUnitId() + "  and brand is " + key.getBrandId());
                        }
                    }
                });
            }
        }
        return menuSuccessCreation;
    }

    private UnitMenuAddVO createMenu(Integer partnerId, Integer brandId, Integer employeeId, Integer unitId, MenuType menuType) {
        MenuReqMetadata menuReqMetadata = new MenuReqMetadata(partnerId, brandId, employeeId, unitId, menuType);
        PartnerAdaptee<MenuReqMetadata, MenuReqMetadata> partnerAdaptee = new PartnerMenuAdapteeImpl(masterDataCache,partnerMenuService);
        PartnerRequestTarget<MenuReqMetadata, UnitMenuAddVO> partnerRequestTarget = new PartnerMenuRequestAdapter(partnerAdaptee, partnerAbstractFactory);
        try {
            return partnerRequestTarget.convertAndSendResponse(menuReqMetadata);
        } catch (ChannelPartnerException e) {
            log.error("Got exception while converting menu request to partner specific menu json for partner id ::::::::{}", partnerId, e);
        }
        return null;
    }


}
