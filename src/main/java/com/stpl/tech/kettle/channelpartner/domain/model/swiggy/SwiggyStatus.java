package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SwiggyStatus {

    @JsonProperty("isOpen")
    private Boolean open;
    @JsonProperty("dispositionName")
    private String dispositionName;
    @JsonProperty("dispositionDescription")
    private String dispositionDescription;
    @JsonProperty("isPenalised")
    private Boolean penalised;

    @JsonProperty("isOpen")
    public Boolean getOpen() {
        return open;
    }

    @JsonProperty("isOpen")
    public void setOpen(Boolean open) {
        this.open = open;
    }

    @JsonProperty("dispositionName")
    public String getDispositionName() {
        return dispositionName;
    }

    @JsonProperty("dispositionName")
    public void setDispositionName(String dispositionName) {
        this.dispositionName = dispositionName;
    }

    @JsonProperty("dispositionDescription")
    public String getDispositionDescription() {
        return dispositionDescription;
    }

    @<PERSON>sonProperty("dispositionDescription")
    public void setDispositionDescription(String dispositionDescription) {
        this.dispositionDescription = dispositionDescription;
    }

    @JsonProperty("isPenalised")
    public Boolean getPenalised() {
        return penalised;
    }

    @JsonProperty("isPenalised")
    public void setPenalised(Boolean penalised) {
        this.penalised = penalised;
    }

}
