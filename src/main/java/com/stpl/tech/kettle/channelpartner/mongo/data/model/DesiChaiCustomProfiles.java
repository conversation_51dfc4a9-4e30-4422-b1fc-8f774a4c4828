package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.master.domain.model.IdName;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Document
@XmlRootElement(name = "DesiChaiCustomProfiles")
public class DesiChaiCustomProfiles {

    @Id
    private String id;
    @Indexed(unique = true)
    private String profileName;
    private String milkType;
    private String dimensionType;
    private String sugarType;
    private String pattiType;
    private List<IdName> addons;
    private String productName;
    private String productDescription;
    private String profileType; //CUSTOMIZATION, PRODUCT
    private boolean superComboProduct;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public String getMilkType() {
        return milkType;
    }

    public void setMilkType(String milkType) {
        this.milkType = milkType;
    }

    public String getDimensionType() {
        return dimensionType;
    }

    public void setDimensionType(String dimensionType) {
        this.dimensionType = dimensionType;
    }

    public String getSugarType() {
        return sugarType;
    }

    public void setSugarType(String sugarType) {
        this.sugarType = sugarType;
    }

    public String getPattiType() {
        return pattiType;
    }

    public void setPattiType(String pattiType) {
        this.pattiType = pattiType;
    }

    public List<IdName> getAddons() {
        return addons;
    }

    public void setAddons(List<IdName> addons) {
        this.addons = addons;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public boolean isSuperComboProduct() {
        return superComboProduct;
    }

    public void setSuperComboProduct(boolean superComboProduct) {
        this.superComboProduct = superComboProduct;
    }
}
