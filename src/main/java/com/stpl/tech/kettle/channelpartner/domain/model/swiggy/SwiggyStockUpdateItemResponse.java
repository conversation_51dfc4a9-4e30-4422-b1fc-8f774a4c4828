package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;


public class SwiggyStockUpdateItemResponse {
    @JsonProperty("status")
    private String status;
    @JsonProperty("id")
    private String id;
    @JsonProperty("message")
    private String message;


    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("message")
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SwiggyStockUpdateItemResponse that = (SwiggyStockUpdateItemResponse) o;
        return Objects.equals(status, that.status) && Objects.equals(id, that.id) && Objects.equals(message, that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(status, id, message);
    }


}
