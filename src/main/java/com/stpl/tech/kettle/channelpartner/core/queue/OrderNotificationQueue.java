package com.stpl.tech.kettle.channelpartner.core.queue;

import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderNotification;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;

public class OrderNotificationQueue {

    private static BlockingQueue<OrderNotification> instance;

    private OrderNotificationQueue(){
    }

    public static BlockingQueue<OrderNotification> getInstance(){
        if(instance == null){
            instance = new DelayQueue<>();
        }
        return instance;
    }
}
