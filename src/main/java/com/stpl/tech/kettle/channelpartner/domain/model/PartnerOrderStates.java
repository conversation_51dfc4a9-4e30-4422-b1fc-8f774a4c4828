package com.stpl.tech.kettle.channelpartner.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PartnerOrderStates {
    REJECTED(400,"Rejected"),
    FAILED(300,"Failed"),
    DUPLICATE(100,"Duplicate Order"),
    EXCEPTION(500, "Exception in Order"),
    SUCCESS(200,"Order placed successfully !");
    private final Integer responseCode ;
    private final String responseMessage;

}
