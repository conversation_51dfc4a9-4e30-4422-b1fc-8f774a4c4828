package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "discount_name",
        "discount_type",
        "discount_code",
        "discount_category",
        "discount_value",
        "discount_is_taxed",
        "discount_with_items"
})
public class ZomatoItemDiscount {

    @JsonProperty("discount_name")
    private String discountName;
    @JsonProperty("discount_type")
    private String discountType;
    @JsonProperty("discount_code")
    private String discountCode;
    @JsonProperty("discount_category")
    private String discountCategory;
    @JsonProperty("discount_value")
    private Float discountValue;
    @JsonProperty("discount_is_taxed")
    private Boolean discountIsTaxed;
    @JsonProperty("discount_with_items")
    private List<DiscountWithItems> discountWithItems;

    @JsonProperty("discount_name")
    public String getDiscountName() {
        return discountName;
    }

    @JsonProperty("discount_name")
    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    @JsonProperty("discount_type")
    public String getDiscountType() {
        return discountType;
    }

    @JsonProperty("discount_type")
    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    @JsonProperty("discount_code")
    public String getDiscountCode() {
        return discountCode;
    }

    @JsonProperty("discount_code")
    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode;
    }

    @JsonProperty("discount_category")
    public String getDiscountCategory() {
        return discountCategory;
    }

    @JsonProperty("discount_category")
    public void setDiscountCategory(String discountCategory) {
        this.discountCategory = discountCategory;
    }

    @JsonProperty("discount_value")
    public Float getDiscountValue() {
        return discountValue;
    }

    @JsonProperty("discount_value")
    public void setDiscountValue(Float discountValue) {
        this.discountValue = discountValue;
    }

    @JsonProperty("discount_is_taxed")
    public Boolean getDiscountIsTaxed() {
        return discountIsTaxed;
    }

    @JsonProperty("discount_is_taxed")
    public void setDiscountIsTaxed(Boolean discountIsTaxed) {
        this.discountIsTaxed = discountIsTaxed;
    }

    @JsonProperty("discount_with_items")
    public List<DiscountWithItems> getDiscountWithItems() {
        return discountWithItems;
    }

    @JsonProperty("discount_with_items")
    public void setDiscountWithItems(List<DiscountWithItems> discountWithItems) {
        this.discountWithItems = discountWithItems;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("discountName", discountName).append("discountType", discountType).append("discountCode", discountCode).append("discountValue", discountValue).append("discountIsTaxed", discountIsTaxed).append("discountWithItems", discountWithItems).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(discountType).append(discountWithItems).append(discountName).append(discountIsTaxed).append(discountValue).append(discountCode).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoItemDiscount) == false) {
            return false;
        }
        ZomatoItemDiscount rhs = ((ZomatoItemDiscount) other);
        return new EqualsBuilder().append(discountType, rhs.discountType).append(discountWithItems, rhs.discountWithItems).append(discountName, rhs.discountName).append(discountIsTaxed, rhs.discountIsTaxed).append(discountValue, rhs.discountValue).append(discountCode, rhs.discountCode).isEquals();
    }

}