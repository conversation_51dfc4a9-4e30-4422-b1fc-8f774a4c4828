package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderComplaintTrackDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderComplaintTrack;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class PartnerOrderComplaintTrackDaoImpl extends AbstractMasterDaoImpl implements PartnerOrderComplaintTrackDao {
    @Override
    public PartnerOrderComplaintTrack findByPartnerOrderId(String orderId) {
        try {
            Query query = manager.createQuery("FROM PartnerOrderComplaintTrack p WHERE p.partnerOrderId= :partnerOrderId");
            query.setParameter("partnerOrderId", orderId);
            List<PartnerOrderComplaintTrack> data = query.getResultList();
            return data.isEmpty() ? null : data.get(0);
        } catch (NoResultException nre) {
            return null;

        }
    }

@Override
public List<PartnerOrderComplaintTrack> findByStatusAndDateRange(String status, Date fromDate, Date toDate) {
        try {
            String queryStr = "FROM PartnerOrderComplaintTrack p WHERE p.status = :status";
            if (fromDate != null) {
                queryStr += " AND p.addTime >= :fromDate";
            }
            if (toDate != null) {
                queryStr += " AND p.addTime <= :toDate";
            }
            queryStr += " ORDER BY p.addTime DESC";

            Query query = manager.createQuery(queryStr);
            query.setParameter("status", status);

            if (fromDate != null) {
                query.setParameter("fromDate", fromDate);
            }
            if (toDate != null) {
                query.setParameter("toDate", toDate);
            }

            return query.getResultList();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
}
