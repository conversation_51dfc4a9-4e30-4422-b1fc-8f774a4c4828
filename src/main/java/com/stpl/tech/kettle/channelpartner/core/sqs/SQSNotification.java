/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.channelpartner.core.sqs;

import com.amazon.sqs.javamessaging.ProviderConfiguration;
import com.amazon.sqs.javamessaging.SQSConnection;
import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazon.sqs.javamessaging.SQSMessageConsumer;
import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.MyAWSCredentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Queue;
import javax.jms.Session;
import java.util.HashMap;
import java.util.Map;

public class SQSNotification {

    private static final Logger LOG = LoggerFactory.getLogger(SQSNotification.class);

    private static SQSNotification INSTANCE;

    static {
        try {
            INSTANCE = new SQSNotification();
        } catch (JMSException e) {
            LOG.error("Failed to Initialize SQS ", e);
        }
    }

    private final SQSConnection sqsConnection;

    private final Map<Regions, SQSConnection> sqsConnections = new HashMap<>();

    private SQSNotification() throws JMSException {

        ClientConfiguration configuration = new ClientConfiguration();
        configuration.setMaxErrorRetry(3);
        configuration.setConnectionTimeout(0);
        configuration.setSocketTimeout(0);
        configuration.setProtocol(Protocol.HTTPS);
        /*sqsConnection = SQSConnectionFactory.builder().withClientConfiguration(configuration)
                .withAWSCredentialsProvider(new AWSCredentialsProvider() {

                    private AWSCredentials credentials = new MyAWSCredentials();

                    @Override
                    public void refresh() {

                    }

                    @Override
                    public AWSCredentials getCredentials() {
                        return credentials;
                    }
                }).withRegion(Region.getRegion(Regions.EU_WEST_1)).withNumberOfMessagesToPrefetch(10).build()
                .createConnection();*/
        SQSConnectionFactory connectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(10),
                AmazonSQSClientBuilder.standard().withClientConfiguration(configuration).withCredentials(new MyAWSCredentials()).withRegion(Regions.EU_WEST_1));
        sqsConnection = connectionFactory.createConnection(new MyAWSCredentials());
    }

    public static SQSNotification getInstance() {
        return INSTANCE;
    }

    public Queue getQueue(SQSSession session, EnvType env, String queueNameSuffix) throws JMSException {
        String queueName = env.name() + queueNameSuffix;
        LOG.info("Queue name::: " + queueName);
        return session.createQueue(queueName);
    }

    public SQSSession getSession() throws JMSException {
        return (SQSSession) sqsConnection.createSession(false, Session.AUTO_ACKNOWLEDGE);
    }

    public SQSSession getSession(Regions region) throws JMSException {
        return (SQSSession) sqsConnection(region).createSession(false, Session.AUTO_ACKNOWLEDGE);
    }

    private SQSConnection sqsConnection (Regions region) throws JMSException {
        if(!sqsConnections.containsKey(region)) {
            ClientConfiguration configuration = new ClientConfiguration();
            configuration.setMaxErrorRetry(3);
            configuration.setConnectionTimeout(0);
            configuration.setSocketTimeout(0);
            configuration.setProtocol(Protocol.HTTPS);
            /*SQSConnection sqsConnection = SQSConnectionFactory.builder().withClientConfiguration(configuration)
                    .withAWSCredentialsProvider(new AWSCredentialsProvider() {

                        private AWSCredentials credentials = new MyAWSCredentials();

                        @Override
                        public void refresh() {

                        }

                        @Override
                        public AWSCredentials getCredentials() {
                            return credentials;
                        }
                    }).withRegion(Region.getRegion(region)).withNumberOfMessagesToPrefetch(10).build()
                    .createConnection();*/

            SQSConnectionFactory connectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(10),
                    AmazonSQSClientBuilder.standard().withClientConfiguration(configuration).withCredentials(new MyAWSCredentials()).withRegion(region));
            SQSConnection sqsConnection = connectionFactory.createConnection(new MyAWSCredentials());
            sqsConnections.put(region, sqsConnection);
        }
        return sqsConnections.get(region);
    }

    public MessageConsumer getConsumer(SQSSession session, EnvType env, String queueNameSuffix) throws JMSException {
        return session.createConsumer(getQueue(session, env, queueNameSuffix));

    }

    public MessageProducer getProducer(SQSSession session, EnvType env, String queueNameSuffix) throws JMSException {
        return session.createProducer(getQueue(session, env, queueNameSuffix));
    }

    public SQSConnection getSqsConnection() {
        return sqsConnection;
    }

    public SQSConnection getSqsConnection(Regions region) {
        try {
            return sqsConnection(region);
        } catch (JMSException e) {
            return null;
        }
    }

}
