package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.scheduler.PartnerStockScheduler;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.impl.PartnerMetadataManagementProxyLayer;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.PARTNER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PARTNER_MANAGEMENT_ROOT_CONTEXT) // 'v1/partner-management'
public class PartnerManagementResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerManagementResources.class);

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private PartnerMetadataManagementProxyLayer partnerMetadataManagementProxyLayer;

    @Autowired
    private PartnerStockScheduler partnerStockScheduler;

    @RequestMapping(method = RequestMethod.POST, value = "add", produces = MediaType.APPLICATION_JSON)
    public PartnerDetail addNewPartner(@RequestBody PartnerDetail request) {
        LOG.info("Request to add partner : {}", new Gson().toJson(request));
        return partnerService.addPartner(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get", produces = MediaType.APPLICATION_JSON)
    public List<PartnerDetail> getAllPartners() {
        LOG.info("Request to get all partners : ");
        return partnerService.getAllPartners();
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-day-slot-menu", produces = MediaType.APPLICATION_JSON)
    public void pushMenuForDaySlot(@RequestParam(required = true) String daySlot , @RequestParam(required = true) String partnerName) throws ChannelPartnerException {
        LOG.info("Manual Push: pushMenuForDaySlot : STARTED For Day Slot  : {} , partner Name : {} ", daySlot, partnerName);
        MenuType type = MenuType.fromValue(daySlot);
        if (properties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get(partnerName).getKettlePartnerId());
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);
            partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );
        }
        LOG.info("Manual Push: pushMenuForDaySlot : FINISHED");
    }

    private List<Integer> getUnitIdsForMenuPush() {
        List<Integer> unit = new ArrayList<>();
        if (EnvType.PROD.equals(properties.getEnvType()) || EnvType.SPROD.equals(properties.getEnvType())) {
            List<UnitBasicDetail> units = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : units) {
                if (unitBasicDetail.isLive() && UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus())) {
                    unit.add(unitBasicDetail.getId());
                }
            }
        } else {
            unit.add(10000);
        }
        return unit;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-active", produces = MediaType.APPLICATION_JSON)
    public List<PartnerDetail> getActivePartners() {
        LOG.info("Request to get active partners : ");
        return partnerService.getActivePartners();
    }

    @RequestMapping(method = RequestMethod.POST, value = "activate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.TEXT_PLAIN)
    public PartnerDetail activatePartner(@RequestBody String partnerId) {
        LOG.info("Request to get active partners : ");
        return partnerService.activatePartner(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "deactivate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.TEXT_PLAIN)
    public PartnerDetail deactivatePartner(@RequestBody String partnerId) {
        LOG.info("Request to deactivate partner id : {}", partnerId);
        return partnerService.deactivatePartner(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-cafe-status-history", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerCafeStatusHistory> getPartnerCafeStatusHistory(@RequestBody IdCodeName detail) {
        LOG.info("Request to get cafe status history for : {}", detail.getId());
        return partnerService.getPartnerCafeStatusHistory(Integer.parseInt(detail.getCode()), detail.getId(), detail.getName());
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-edited-order-original-request", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,String> getEditedOrderOriginalRequest(@RequestParam String partnerOrderId) {
        LOG.info("Request to get Original Request For Edited Order Id :::: {}",partnerOrderId);
        return partnerService.getEditedOrderOriginalRequest(partnerOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-stock-events", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean refreshStockEvents() {
        partnerStockScheduler.refreshEvents();
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-current-live-events", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer getCurrentLiveStockEvents() {
      return partnerStockScheduler.getCurrentEventListForStockUpdate();

    }






}
