package com.stpl.tech.kettle.channelpartner.core.queue.model;

import com.google.common.primitives.Ints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class OrderNotification implements Delayed {

    private Object notificationData;
    private String partnerName;
    private long startTime;

    public OrderNotification(){

    }

    public OrderNotification(Object data, String partnerName, long delayInMilliseconds) {
        this.notificationData = data;
        this.partnerName = partnerName;
        this.startTime = ChannelPartnerUtils.getCurrentTimestamp().getTime() + delayInMilliseconds;
    }

    public Object getNotificationData() {
        return notificationData;
    }

    public void setNotificationData(Object notificationData) {
        this.notificationData = notificationData;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        long diff = startTime - ChannelPartnerUtils.getCurrentTimestamp().getTime();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        return Ints.saturatedCast(this.startTime - ((OrderNotification) o).startTime);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OrderNotification that = (OrderNotification) o;

        return new EqualsBuilder()
                .append(startTime, that.startTime)
                .append(notificationData, that.notificationData)
                .append(partnerName, that.partnerName)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(notificationData)
                .append(partnerName)
                .append(startTime)
                .toHashCode();
    }
}
