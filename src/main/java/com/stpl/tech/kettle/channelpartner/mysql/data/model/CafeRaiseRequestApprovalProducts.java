package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "CAFE_RAISE_REQUEST_APPROVAL_PRODUCTS")
public class CafeRaiseRequestApprovalProducts implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "CAFE_RAISE_REQUEST_APPROVAL_ID")
    @JsonBackReference
    private CafeRaiseRequestApproval cafeRaiseRequestApproval;

    @Column(name = "MACHINE_ID", nullable = false)
    private Integer machineId;

    @Column(name = "PRODUCT_IDS", nullable = false)
    private String productIds;

}
