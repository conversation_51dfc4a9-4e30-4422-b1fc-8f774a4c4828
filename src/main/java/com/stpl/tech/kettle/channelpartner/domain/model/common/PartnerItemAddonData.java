package com.stpl.tech.kettle.channelpartner.domain.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerItemAddonData {
    String partnerItemId;

    Integer productId;

    String name;

    Integer quantity;

    BigDecimal unitPrice;

    @Builder.Default
    BigDecimal discount  = BigDecimal.ZERO;

    @Builder.Default
    BigDecimal discountPercent = BigDecimal.ZERO ;
}
