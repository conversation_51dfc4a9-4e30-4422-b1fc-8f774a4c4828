
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "variant_group_id",
    "variant_id"
})
public class VariantCombination {

    @JsonProperty("variant_group_id")
    private String variantGroupId;
    @JsonProperty("variant_id")
    private String variantId;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("variant_group_id")
    public String getVariantGroupId() {
        return variantGroupId;
    }

    @JsonProperty("variant_group_id")
    public void setVariantGroupId(String variantGroupId) {
        this.variantGroupId = variantGroupId;
    }

    @JsonProperty("variant_id")
    public String getVariantId() {
        return variantId;
    }

    @JsonProperty("variant_id")
    public void setVariantId(String variantId) {
        this.variantId = variantId;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("variantGroupId", variantGroupId).append("variantId", variantId).append("additionalProperties", additionalProperties).toString();
    }

}
