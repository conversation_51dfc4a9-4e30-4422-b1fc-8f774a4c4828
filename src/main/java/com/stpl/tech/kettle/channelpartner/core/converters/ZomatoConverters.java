package com.stpl.tech.kettle.channelpartner.core.converters;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoChargeDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoGroup;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoItemDiscount;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompSelectionEntityV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompSelectionV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompositionPropV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompositionV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscount;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderItem;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderModifierGroupsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTaxDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderType;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderVariantsV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDiscountData;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ZomatoConverters {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoConverters.class);

    public static Order convertOrder(ZomatoOrderRequest request, Customer customer, Address address,
                                     MasterDataCache masterDataCache, UnitPartnerBrandMappingData data) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Order order = new Order();
        // order.setOrderId();
        order.setGenerateOrderId(ChannelPartnerUtils.generateRandomOrderId());
        // order.setExternalOrderId(externalOrderId);
        // order.setOptionResultEventId();
        // order.setUnitOrderId();
        // order.setCampaignId();
        order.setCustomerId(customer.getId());
        order.setEmployeeId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
        order.setPointsRedeemed(0);
        if (ZomatoOrderType.TAKEAWAY.equals(request.getOrderType())) {
            order.setSource("TAKE_AWAY");
        } else {
            order.setSource("COD");
        }
        order.setSourceId(request.getOrderId().toString());
        order.setHasParcel(true);
        order.setStatus(OrderStatus.CREATED);
        // order.setApplication();
        // order.setEnquiryItems();
        // setTransactionDetail(order, request);
        // order.setPrintCount(1);
        order.setSettlementType(SettlementType.DEBIT);
        order.setUnitId(data.getUnitId());
        order.setUnitName(masterDataCache.getUnit(data.getUnitId()).getName());
        order.setTerminalId(1);
        // order.setTableNumber();
        order.setBillStartTime(currentTime);
        order.setBillCreationTime(currentTime);
        order.setBillCreationSeconds(0);
        order.setBillingServerTime(currentTime);
        order.setChannelPartner(3);
        order.setBrandId(data.getBrandId());
        if (!ZomatoOrderType.TAKEAWAY.equals(request.getOrderType()) && request.getEnableDelivery() == 1) {
            order.setDeliveryPartner(8); // PARTNER ID 8 FOR CHAAYOS DELIVERY
        } else {
            order.setDeliveryPartner(5); // for PICKUP
        }
        // order.setSubscriptionDetail();
        // order.setOfferCode();
        // order.setReprints();
        // order.setCancellationDetails();
        if (request.getOrderInstructions() != null) {
            order.setOrderRemark(request.getOrderInstructions());
        }
        if (address != null) {
            order.setDeliveryAddress(address.getId());
        }
        String name = customer.getFirstName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        order.setCustomerName(name);
        order.setContainsSignupOffer(false);
        // order.setEmployeeMeal();
        // order.setEmployeeIdForMeal();
        // order.setTempCode();
        // order.setMetadataList();
        order.setNewCustomer(false);
        // order.setPendingCash();
        // order.setTokenNumber();
        // order.setLinkedOrderId();
        // order.setPaymentDetailId();
        // order.setAwardLoyalty();
        order.setOrderType("order");
        // order.setBillBookNo();
        return order;
    }

    public static void setTransactionDetail(Order order, ZomatoOrderRequest request, MasterDataCache masterDataCache) {
        TransactionDetail transactionDetail = new TransactionDetail();
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal taxable = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            total = ChannelPartnerUtils.add(total, item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            taxable = ChannelPartnerUtils.add(taxable, item.getAmount());
        }
        transactionDetail.setTotalAmount(total);
        transactionDetail.setTaxableAmount(taxable);
        BigDecimal discountValue = BigDecimal.ZERO;
        BigDecimal discountPercent = BigDecimal.ZERO;
        DiscountDetail discountDetail = new DiscountDetail();
        if (!request.getOrderDiscounts().isEmpty()) {
            PercentageDetail percentageDetail = getDiscountValues(request);
            discountValue = percentageDetail.getValue();
            discountPercent = percentageDetail.getPercentage();
            discountDetail.setDiscountReason("ZOMATO");
            discountDetail.setDiscountCode(2004);
        }
        discountDetail.setDiscount(new PercentageDetail());
        discountDetail.getDiscount().setValue(discountValue);
        discountDetail.getDiscount().setPercentage(discountPercent);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        discountDetail.setTotalDiscount(
                ChannelPartnerUtils.add(discountDetail.getDiscount().getValue(), discountDetail.getPromotionalOffer()));
        transactionDetail.setDiscountDetail(discountDetail);

        Map<TaxDetailKey, TaxDetail> taxMap = new HashMap<>();
        BigDecimal totalTax = AbstractConverters.aggregateTaxesFromOrderItems(order.getOrders(), taxMap);
        transactionDetail.getTaxes().addAll(taxMap.values());
        transactionDetail.setTax(totalTax);

        BigDecimal paidAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(),
                transactionDetail.getTax());
        transactionDetail.setPaidAmount(paidAmount.setScale(0, RoundingMode.HALF_UP));
        transactionDetail.setRoundOffValue(ChannelPartnerUtils.subtract(transactionDetail.getPaidAmount(), paidAmount));
        AbstractConverters.calculateSaving(transactionDetail, order);
        order.setTransactionDetail(transactionDetail);

        AbstractConverters.addSettlementToOrder(order, request, masterDataCache);
    }


    private static PercentageDetail getDiscountValues(ZomatoOrderRequest request) {
        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (ZomatoOrderDiscount zomatoOrderDiscount : request.getOrderDiscounts()) {
            if (zomatoOrderDiscount.getIsZomatoDiscount() == 0) {
                totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoOrderDiscount.getDiscountAmount()));
            }
        }
        for (ZomatoOrderItem item : request.getOrderItems()) {
            for (ZomatoItemDiscount zomatoItemDiscount : item.getItemDiscounts()) {
                totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoItemDiscount.getDiscountValue()));
            }
        }
        BigDecimal subTotal = BigDecimal.ZERO;
        for (ZomatoOrderItem item : request.getOrderItems()) {
            BigDecimal price = BigDecimal.valueOf(item.getItemUnitPrice());
            for (ZomatoGroup zomatoGroup : item.getGroups()) {
                for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                    price = ChannelPartnerUtils.add(price, BigDecimal.valueOf(zomatoOrderItem.getItemUnitPrice()));
                }
            }
            subTotal = ChannelPartnerUtils.add(subTotal,
                    price.multiply(BigDecimal.valueOf(item.getItemQuantity())).setScale(10, RoundingMode.HALF_UP));
        }
        if (request.getOrderAdditionalCharges() != null) {
            for (ZomatoChargeDetails zomatoChargeDetails : request.getOrderAdditionalCharges()) {
                subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(zomatoChargeDetails.getChargeAmount()))
                        .setScale(10, RoundingMode.HALF_UP);
            }
        }
        BigDecimal percentage = BigDecimal.ZERO;
        if (totalDiscount.compareTo(BigDecimal.ZERO) > 0) {
            percentage = totalDiscount.divide(subTotal, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(percentage);
        percentageDetail.setValue(totalDiscount);
        return percentageDetail;
    }

    public static void addItemsToOrder(Order order, ZomatoOrderRequest request, MasterDataCache masterDataCache,
                                       PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, int pricingUnitId) {
        boolean isInterState = false;
        updateDesiChaiProductIds(request);
        BigDecimal discountPercent = getDiscountValues(request).getPercentage();
        for (ZomatoOrderItem item : request.getOrderItems()) {
            boolean productMatched = false;
            if (products.containsKey(Integer.parseInt(item.getItemId()))) {
                Product product = products.get(Integer.parseInt(item.getItemId()));
                productMatched = true;
                OrderItem orderItem = convertOrderItem(product, item, isInterState, taxMap, products,
                        discountPercent, null, BigDecimal.ZERO, 1, false, masterDataCache, partnerOrderDetail.getBrandId());
                order.getOrders().add(orderItem);
            }
            if (!productMatched) {
                PartnerOrderError partnerOrderError = new PartnerOrderError();
                partnerOrderError.setErrorCode(PartnerOrderErrorCode.PRODUCT_MISMATCH);
                partnerOrderError.setErrorDescription(
                        "Product " + item.getItemName() + " with id " + item.getItemId() + " not found.");
                partnerOrderDetail.getOrderErrors().add(partnerOrderError);
            }
        }
        BigDecimal cartValue = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            cartValue = cartValue.add(item.getPrice().multiply(new BigDecimal(item.getQuantity())));
        }
        if (request.getOrderAdditionalCharges() != null) {
            for (ZomatoChargeDetails zomatoChargeDetails : request.getOrderAdditionalCharges()) {
                if (products.containsKey(zomatoChargeDetails.getChargeId())) {
                    Product product = products.get(zomatoChargeDetails.getChargeId());
                    // TODO check this for discounts
                    // BigDecimal discountPercentage = BigDecimal.ZERO;
                    OrderItem orderItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                            masterDataCache, isInterState, taxMap, false, discountPercent, "ZOMATO",
                            product.getPrices().get(0).getDimension(), pricingUnitId, cartValue, false, null);
                    order.getOrders().add(orderItem);
                }
            }
        }
    }

    private static void updateDesiChaiProductIds(ZomatoOrderRequest request) {
        request.getOrderItems().forEach(item -> {
            if (Integer.valueOf(item.getItemId()) == 10) {
                updateDesiChai(item);
            } else if (Integer.valueOf(item.getItemId()) == 1282) {
                updateBaarishWaliChai(item);
            } else {
                for (ZomatoGroup group : item.getGroups()) {
                    for (ZomatoOrderItem orderItem : group.getItems()) {
                        try {
                            if (Integer.valueOf(orderItem.getItemId()) == 10) {
                                updateDesiChai(orderItem);
                            }
                            if (Integer.valueOf(orderItem.getItemId()) == 1282) {
                                updateBaarishWaliChai(orderItem);
                            }
                        } catch (NumberFormatException e) {

                        }
                    }
                }
            }
        });
    }

    private static void updateDesiChai(ZomatoOrderItem item) {
        item.getGroups().forEach(zomatoGroup -> {
            zomatoGroup.getItems().forEach(zomatoOrderItem -> {
                if (ChannelPartnerUtils.getDesiChaiMilkMap().get(zomatoOrderItem.getItemName().trim()) != null) {
                    item.setItemId(ChannelPartnerUtils.getDesiChaiMilkMap().get(zomatoOrderItem.getItemName().trim())
                            .toString());
                }
            });
        });
    }

    private static void updateBaarishWaliChai(ZomatoOrderItem item) {
        item.getGroups().forEach(zomatoGroup -> {
            zomatoGroup.getItems().forEach(zomatoOrderItem -> {
                if (ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(zomatoOrderItem.getItemName().trim()) != null) {
                    item.setItemId(ChannelPartnerUtils.getBaarishWaliChaiMilkMap()
                            .get(zomatoOrderItem.getItemName().trim()).toString());
                }
            });
        });
    }

    private static OrderItem convertOrderItem(Product product, ZomatoOrderItem item, boolean isInterState,
                                              Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, BigDecimal discountPercent, String dimension,
                                              BigDecimal comboDiscountPercent, Integer parentQuantity, boolean isConstituent, MasterDataCache masterDataCache, Integer brandId) {
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setProductName(product.getName());
        EntityAliasKey key = new EntityAliasKey(product.getId(), "PRODUCT", brandId);
        EntityAliasMappingData data = masterDataCache.getEntityAliasMappingData().get(key);
        if (data != null) {
            orderItem.setProductName(data.getAlias());
        }
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
        orderItem.setQuantity(item.getItemQuantity());
        orderItem.setPrice(new BigDecimal(item.getItemUnitPrice()).setScale(2, RoundingMode.HALF_UP));
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        // orderItem.setComposition();
        // orderItem.setItemCode();
        orderItem.setCode(product.getTaxCode());
        // orderItem.setReasonId();
        orderItem.setBookedWastage(false);
        // orderItem.setCardType();
        orderItem.setTakeAway(false);

        ProductPrice productPrice = null;

        if (dimension == null) {
            BigDecimal price = new BigDecimal(item.getItemUnitPrice());
            for (ZomatoGroup group : item.getGroups()) {
                for (ZomatoOrderItem zomatoOrderItem : group.getItems()) {
                    if (zomatoOrderItem.getItemUnitPrice() > 0) {
                        price = ChannelPartnerUtils.add(price, BigDecimal.valueOf(zomatoOrderItem.getItemUnitPrice()));
                    }
                }
            }
            TreeMap<BigDecimal, ProductPrice> priceDifference = new TreeMap<>();
            for (ProductPrice pPrice : product.getPrices()) {
                priceDifference.put(ChannelPartnerUtils.subtract(price, pPrice.getPrice()).abs(), pPrice);
            }

            productPrice = priceDifference.get(priceDifference.firstKey());
            orderItem.setPrice(productPrice.getPrice().setScale(2, RoundingMode.HALF_UP));
            orderItem.setDimension(productPrice.getDimension());
        } else {
            for (ProductPrice pPrice : product.getPrices()) {
                if (pPrice.getDimension().equalsIgnoreCase(dimension)) {
                    productPrice = pPrice;
                }
            }
            if (productPrice == null) {
                productPrice = product.getPrices().get(0);
            }
            orderItem.setPrice(productPrice.getPrice());
            orderItem.setDimension(productPrice.getDimension());
        }

        RecipeDetail recipeDetail = productPrice.getRecipe();
        // LOG.error(new Gson().toJson(recipeDetail));
        orderItem.setRecipeId(recipeDetail.getRecipeId());

        orderItem.setTotalAmount(ChannelPartnerUtils.multiply(orderItem.getPrice(),
                new BigDecimal(orderItem.getQuantity() * parentQuantity)));
        // Setting discount data
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        PercentageDetail percentageDetail = new PercentageDetail();
        if (isConstituent) {
            percentageDetail.setPercentage(comboDiscountPercent);
        } else {
            percentageDetail.setPercentage(discountPercent);
        }
        percentageDetail.setValue(BigDecimal.ZERO);
        if (discountPercent.compareTo(BigDecimal.ZERO) > 0 && comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("ZOMATO+COMBO");
        } else if (discountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("ZOMATO");
        } else if (comboDiscountPercent != null && comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("COMBO");
        }
        if (percentageDetail.getPercentage().compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountCode(2004);
            percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(percentageDetail.getPercentage(),
                    orderItem.getTotalAmount()));
        }
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setTotalDiscount(
                ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), percentageDetail.getValue()));
        orderItem.setDiscountDetail(discountDetail);
        orderItem
                .setAmount(ChannelPartnerUtils.subtract(orderItem.getTotalAmount(), discountDetail.getTotalDiscount()));
        // Zomato does not provide tax bifurcations
        isInterState = false;

        // Setting addons
        OrderItemComposition orderItemComposition = new OrderItemComposition();
        for (ZomatoGroup zomatoGroup : item.getGroups()) {
            for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                // Setting addons
                recipeDetail.getAddons().forEach(ingredientProductDetail -> {
                    if (ingredientProductDetail.getProduct().getName()
                            .equalsIgnoreCase(zomatoOrderItem.getItemName().trim())) {
                        orderItemComposition.getAddons().add(ingredientProductDetail);
                    }
                });
                // Setting variants
                recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(zomatoOrderItem.getItemName().trim())) {
                            orderItemComposition.getVariants().add(ingredientVariantDetail);
                        }
                    }
                });
                // Setting Ingredient Products
                recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        if (ingredientProductDetail.getProduct().getName()
                                .equalsIgnoreCase(zomatoOrderItem.getItemName().trim())) {
                            orderItemComposition.getProducts().add(ingredientProductDetail);
                        }
                    }
                });
            }
        }
        // Setting default ingredient variants for missing ones
        recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
            boolean found = false;
            for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                for (ZomatoGroup zomatoGroup : item.getGroups()) {
                    for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(zomatoOrderItem.getItemName().trim())) {
                            found = true;
                            break;
                        }
                    }
                }
            }
            if (!found) {
                ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                    if (ingredientVariantDetail.isDefaultSetting()) {
                        orderItemComposition.getVariants().add(ingredientVariantDetail);
                    }
                });
            }
        });
        // Setting default ingredient products for missing ones
        recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
            boolean found = false;
            for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                for (ZomatoGroup zomatoGroup : item.getGroups()) {
                    for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                        if (ingredientProductDetail.getProduct().getName()
                                .equalsIgnoreCase(zomatoOrderItem.getItemName().trim())) {
                            found = true;
                            break;
                        }
                    }
                }
            }
            if (!found) {
                ingredientProduct.getDetails().forEach(ingredientProductDetail -> {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        orderItemComposition.getProducts().add(ingredientProductDetail);
                    }
                });
            }
        });

        if (recipeDetail.getIngredient().getCompositeProduct() != null) {
            BigDecimal comboTotalPrice = BigDecimal.ZERO;
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                    .getDetails()) {
                boolean found = false;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    for (ZomatoGroup zomatoGroup : item.getGroups()) {
                        for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                            if (Integer.parseInt(zomatoOrderItem.getItemId()) == ingredientProductDetail.getProduct()
                                    .getProductId()) {
                                comboTotalPrice = comboTotalPrice.add(
                                        AbstractConverters.getComboTotalPrice(products, ingredientProductDetail, orderItem.getQuantity()));
                                found = true;
                            }
                        }
                    }
                }
                if (!found) {
                    if (defaultMenuProduct != null) {
                        comboTotalPrice = comboTotalPrice
                                .add(AbstractConverters.getComboTotalPrice(products, defaultMenuProduct, orderItem.getQuantity()));
                    } else {
                        comboTotalPrice = comboTotalPrice.add(AbstractConverters.getComboTotalPrice(products,
                                compositeIngredientData.getMenuProducts().get(0), orderItem.getQuantity()));
                    }
                }
            }
            BigDecimal comboDiscount = comboTotalPrice
                    .subtract(orderItem.getPrice().multiply(BigDecimal.valueOf(orderItem.getQuantity())));
            if (discountDetail.getDiscount().getValue().compareTo(BigDecimal.ZERO) > 0) {
                comboDiscount = comboDiscount.add(discountDetail.getDiscount().getValue());
            }
            BigDecimal comboDiscountPercentage = ChannelPartnerUtils.percentageWithScale10(comboDiscount,
                    comboTotalPrice);

            // setting menu items
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                    .getDetails()) {
                boolean found = false;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    for (ZomatoGroup zomatoGroup : item.getGroups()) {
                        for (ZomatoOrderItem zomatoOrderItem : zomatoGroup.getItems()) {
                            if (matchProductId(Integer.parseInt(zomatoOrderItem.getItemId()), ingredientProductDetail.getProduct().getProductId())) {
                                if (products.containsKey(ingredientProductDetail.getProduct().getProductId())) {
                                    Product comboConstituent = products.get(ingredientProductDetail.getProduct().getProductId());
                                    Product consti = (Product) ChannelPartnerUtils.deepClone(comboConstituent);
                                    consti.setId(Integer.parseInt(zomatoOrderItem.getItemId()));
                                    if (ChannelPartnerUtils.getDesiChaiProductName().containsKey(consti.getId())) {
                                        consti.setName(ChannelPartnerUtils.getDesiChaiProductName().get(consti.getId()));
                                    }
                                    String comboConstituentDimension = ingredientProductDetail.getDimension()
                                            .getCode();
                                    zomatoOrderItem
                                            .setItemQuantity(ingredientProductDetail.getQuantity().intValue());
                                    OrderItem comboConstituentOrderItem = convertOrderItem(consti,
                                            zomatoOrderItem, isInterState, taxMap, products, discountPercent,
                                            comboConstituentDimension, comboDiscountPercentage,
                                            orderItem.getQuantity(), true, masterDataCache, brandId);
                                    orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                                    found = true;
                                }
                            }
                        }
                    }
                }
                if (!found) {
                    if (defaultMenuProduct == null) {
                        defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
                    }
                    if (products.containsKey(defaultMenuProduct.getProduct().getProductId())) {
                        Product comboConstituent = products.get(defaultMenuProduct.getProduct().getProductId());
                        String comboConstituentDimension = defaultMenuProduct.getDimension().getCode();
                        // creating default item without customizations
                        ZomatoOrderItem zomatoOrderItem = new ZomatoOrderItem();
                        zomatoOrderItem.setItemQuantity(defaultMenuProduct.getQuantity().intValue());
                        zomatoOrderItem.setGroups(new ArrayList<>());
                        zomatoOrderItem.setItemUnitPrice(BigDecimal.ZERO.floatValue());
                        zomatoOrderItem.setGroups(new ArrayList<>());
                        ZomatoGroup group = new ZomatoGroup();
                        group.setItems(new ArrayList<>());
                        if (defaultMenuProduct.getIngredient() != null) {
                            for (IngredientVariant ingredientVariant : defaultMenuProduct.getIngredient()
                                    .getVariants()) {
                                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant
                                        .getDetails()) {
                                    if (ingredientVariantDetail.isDefaultSetting()) {
                                        ZomatoOrderItem constituentItem = new ZomatoOrderItem();
                                        constituentItem.setItemName(ingredientVariantDetail.getAlias());
                                        constituentItem.setItemUnitPrice((float) 0);
                                        group.getItems().add(constituentItem);
                                    }
                                }
                            }
                            for (IngredientProduct ingredientProduct : defaultMenuProduct.getIngredient()
                                    .getProducts()) {
                                for (IngredientProductDetail ingredientProductDetail1 : ingredientProduct
                                        .getDetails()) {
                                    if (ingredientProductDetail1.isDefaultSetting()) {
                                        ZomatoOrderItem constituentItem = new ZomatoOrderItem();
                                        constituentItem
                                                .setItemName(ingredientProductDetail1.getProduct().getName());
                                        constituentItem.setItemUnitPrice((float) 0);
                                        group.getItems().add(constituentItem);
                                    }
                                }
                            }
                        }
                        if (defaultMenuProduct.getAddons() != null) {
                            for (IngredientProductDetail productDetail : defaultMenuProduct.getAddons()) {
                                if (productDetail.isDefaultSetting()) {
                                    ZomatoOrderItem constituentItem = new ZomatoOrderItem();
                                    constituentItem.setItemName(productDetail.getProduct().getName());
                                    constituentItem.setItemUnitPrice((float) 0);
                                    group.getItems().add(constituentItem);
                                }
                            }
                        }
                        OrderItem comboConstituentOrderItem = convertOrderItem(comboConstituent, zomatoOrderItem,
                                isInterState, taxMap, products, discountPercent, comboConstituentDimension,
                                comboDiscountPercentage, orderItem.getQuantity(), true, masterDataCache, brandId);
                        orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                        break;
                    }
                }
            }

        }

        orderItem.setComposition(orderItemComposition);

        // Setting taxes
        AbstractConverters.setOrderItemTaxes(product, orderItem, taxMap, discountDetail, parentQuantity);
        return orderItem;
    }

    private static boolean matchProductId(Integer srcId, Integer destId) {
        if (ChannelPartnerUtils.getDesiChaiVariantMap().containsKey(srcId)) {
            return ChannelPartnerUtils.getDesiChaiVariantMap().get(srcId).equals(destId);
        }
        return srcId.equals(destId);
    }


    public static void main(String[] args) {
        BigDecimal subTotal = ChannelPartnerUtils.add(BigDecimal.ZERO,
                BigDecimal.valueOf(142).multiply(BigDecimal.ONE).setScale(10, RoundingMode.HALF_UP));
        subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(14.199999809265137)).setScale(10,
                RoundingMode.HALF_UP);
        System.out.println("SubTotal" + subTotal);
        BigDecimal totalDiscount = BigDecimal.valueOf(35.22);
        BigDecimal percentage = totalDiscount.divide(subTotal, 10, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        System.out.println("Percentage" + percentage);
    }

}
