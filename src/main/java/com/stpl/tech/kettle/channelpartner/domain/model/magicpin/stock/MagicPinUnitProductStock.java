package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.stock;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinUnitProductStock implements Serializable {

    private static final long serialVersionUID = 742210194742463777L;
    private String partnerStoreId ;
    @Builder.Default
    private List<MagicPinSkuStockData> skus= new ArrayList<>();
    @Builder.Default
    private List<MagicPinOptionStockData> options= new ArrayList<>();
}
