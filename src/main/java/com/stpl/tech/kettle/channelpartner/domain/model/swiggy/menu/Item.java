
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "category_id",
    "sub_category_id",
    "name",
    "is_veg",
    "description",
    "price",
    "gst_details",
    "packing_charges",
    "enable",
    "in_stock",
    "addon_free_limit",
    "addon_limit",
    "image_url",
    "variant_groups",
    "addon_groups",
    "item_slots",
    "pricing_combinations"
})
public class Item {

    @JsonProperty("id")
    private String id;
    @JsonProperty("category_id")
    private String categoryId;
    @JsonProperty("sub_category_id")
    private String subCategoryId;
    @JsonProperty("name")
    private String name;
    @JsonProperty("is_veg")
    private Boolean isVeg;
    @JsonProperty("description")
    private String description;
    @JsonProperty("price")
    private Double price = null;
    @JsonProperty("gst_details")
    private GstDetails gstDetails;
    @JsonProperty("packing_charges")
    private Double packingCharges;
    @JsonProperty("enable")
    private Integer enable;
    @JsonProperty("in_stock")
    private Integer inStock;
    @JsonProperty("addon_free_limit")
    private Integer addonFreeLimit;
    @JsonProperty("addon_limit")
    private Integer addonLimit;
    @JsonProperty("image_url")
    private String imageUrl;
    @JsonProperty("variant_groups")
    private List<VariantGroup> variantGroups = null;
    @JsonProperty("addon_groups")
    private List<AddonGroup> addonGroups = null;
    @JsonProperty("item_slots")
    private List<ItemSlot> itemSlots = null;
    @JsonProperty("pricing_combinations")
    private List<PricingCombination> pricingCombinations = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("category_id")
    public String getCategoryId() {
        return categoryId;
    }

    @JsonProperty("category_id")
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    @JsonProperty("sub_category_id")
    public String getSubCategoryId() {
        return subCategoryId;
    }

    @JsonProperty("sub_category_id")
    public void setSubCategoryId(String subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("is_veg")
    public Boolean getIsVeg() {
        return isVeg;
    }

    @JsonProperty("is_veg")
    public void setIsVeg(Boolean isVeg) {
        this.isVeg = isVeg;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("gst_details")
    public GstDetails getGstDetails() {
        return gstDetails;
    }

    @JsonProperty("gst_details")
    public void setGstDetails(GstDetails gstDetails) {
        this.gstDetails = gstDetails;
    }

    @JsonProperty("packing_charges")
    public Double getPackingCharges() {
        return packingCharges;
    }

    @JsonProperty("packing_charges")
    public void setPackingCharges(Double packingCharges) {
        this.packingCharges = packingCharges;
    }

    @JsonProperty("enable")
    public Integer getEnable() {
        return enable;
    }

    @JsonProperty("enable")
    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    @JsonProperty("in_stock")
    public Integer getInStock() {
        return inStock;
    }

    @JsonProperty("in_stock")
    public void setInStock(Integer inStock) {
        this.inStock = inStock;
    }

    @JsonProperty("addon_free_limit")
    public Integer getAddonFreeLimit() {
        return addonFreeLimit;
    }

    @JsonProperty("addon_free_limit")
    public void setAddonFreeLimit(Integer addonFreeLimit) {
        this.addonFreeLimit = addonFreeLimit;
    }

    @JsonProperty("addon_limit")
    public Integer getAddonLimit() {
        return addonLimit;
    }

    @JsonProperty("addon_limit")
    public void setAddonLimit(Integer addonLimit) {
        this.addonLimit = addonLimit;
    }

    @JsonProperty("image_url")
    public String getImageUrl() {
        return imageUrl;
    }

    @JsonProperty("image_url")
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @JsonProperty("variant_groups")
    public List<VariantGroup> getVariantGroups() {
        return variantGroups;
    }

    @JsonProperty("variant_groups")
    public void setVariantGroups(List<VariantGroup> variantGroups) {
        this.variantGroups = variantGroups;
    }

    @JsonProperty("addon_groups")
    public List<AddonGroup> getAddonGroups() {
        return addonGroups;
    }

    @JsonProperty("addon_groups")
    public void setAddonGroups(List<AddonGroup> addonGroups) {
        this.addonGroups = addonGroups;
    }

    @JsonProperty("item_slots")
    public List<ItemSlot> getItemSlots() {
        return itemSlots;
    }

    @JsonProperty("item_slots")
    public void setItemSlots(List<ItemSlot> itemSlots) {
        this.itemSlots = itemSlots;
    }

    @JsonProperty("pricing_combinations")
    public List<PricingCombination> getPricingCombinations() {
        return pricingCombinations;
    }

    @JsonProperty("pricing_combinations")
    public void setPricingCombinations(List<PricingCombination> pricingCombinations) {
        this.pricingCombinations = pricingCombinations;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("id", id).append("categoryId", categoryId).append("subCategoryId", subCategoryId).append("name", name).append("isVeg", isVeg).append("description", description).append("price", price).append("gstDetails", gstDetails).append("packingCharges", packingCharges).append("enable", enable).append("inStock", inStock).append("addonFreeLimit", addonFreeLimit).append("addonLimit", addonLimit).append("imageUrl", imageUrl).append("variantGroups", variantGroups).append("addonGroups", addonGroups).append("itemSlots", itemSlots).append("pricingCombinations", pricingCombinations).append("additionalProperties", additionalProperties).toString();
    }

}
