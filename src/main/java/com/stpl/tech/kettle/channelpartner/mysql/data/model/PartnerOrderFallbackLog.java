package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PARTNER_ORDER_FALLBACK_LOGS")
public class PartnerOrderFallbackLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FALLBACK_STATUS_ID", nullable = false)
    private PartnerOrderFallbackStatus partnerOrderFallbackStatusId;

    @Column(name = "ORDER_ID", nullable = false, length = 50)
    private String orderId;


    @Column(name = "ACTION", columnDefinition = "enum('MARK_AS_RESOLVED','MANUAL_PROCESS','FORCE_PROCESS','VIEW_INVENTORY','GET_CUSTOMER_DETAIL','SHOW_ITEMS','CALL_SWIGGY_SUPPORT','CALL_SWIGGY_CUSTOMER','PICK_UP')")
    private String action;

    @Column(name = "PREVIOUS_STATE", length = 45)
    private String previousState;

    @Column(name = "CURRENT_STATUS", length = 45)
    private String currentStatus;

    @Column(name = "ACTION_TIME")
    private Date actionTime;

    @Column(name = "COMMENT", length = 255)
    private String comment;

    @Column(name = "ACTION_CATEGORY" , columnDefinition = "enum('BUTTON_CLICKED','AGENT_ASSIGNED','AGENT_CHANGED','ADD_KETTLE_ORDER','ZOMATO_ORDER_CANCELLED','SWIGGY_ORDER_CANCELLED','KETTLE_ORDER_CHECKED' )")
    private String actionCategory;

    @Column(name = "EMPLOYEE_ID", length = 45)
    private String employeeId;
}
