package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "status",
        "timestamp",
        "de_details"
})
public class SwiggyRiderStatusRequest {

    @JsonProperty("order_id")
    private Long orderId;
    @JsonProperty("status")
    private String status;
    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("de_details")
    private SwiggyRiderDetail riderDetail;

    @JsonProperty("order_id")
    public Long getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("de_details")
    public SwiggyRiderDetail getRiderDetail() {
        return riderDetail;
    }

    @JsonProperty("de_details")
    public void setRiderDetail(SwiggyRiderDetail riderDetail) {
        this.riderDetail = riderDetail;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        SwiggyRiderStatusRequest that = (SwiggyRiderStatusRequest) o;

        return new EqualsBuilder()
                .append(orderId, that.orderId)
                .append(status, that.status)
                .append(timestamp, that.timestamp)
                .append(riderDetail, that.riderDetail)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(orderId)
                .append(status)
                .append(timestamp)
                .append(riderDetail)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "SwiggyRiderStatusRequest{" +
                "orderId=" + orderId +
                ", status='" + status + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", riderDetail=" + riderDetail +
                '}';
    }
}