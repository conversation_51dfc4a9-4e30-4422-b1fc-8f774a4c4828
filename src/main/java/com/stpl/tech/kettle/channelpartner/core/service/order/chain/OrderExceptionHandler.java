package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class OrderExceptionHandler<R, T> extends OrderProcessingStep<R, T> {
    public OrderExceptionHandler(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        return null;
    }

    @Override
    public T handleExceptionInProcessOrder(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail, Exception e, String exceptionEvent) throws ChannelPartnerException {
        log.error("Exception caught while::::{} for partner ::::{} and partnerOrderId :::::::::::{}", exceptionEvent,partnerPrimaryData.getPartnerName(),partnerPrimaryData.getOrderId(),e);
        return partnerOrderManagementService.handlesExceptionInOrder(request, response, partnerOrderDetail, e, partnerPrimaryData);
    }
}
