package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.amazonaws.regions.Regions;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerStringConstants;
import com.stpl.tech.kettle.channelpartner.core.SyncLock;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.converters.ZomatoConverters;
import com.stpl.tech.kettle.channelpartner.core.converters.ZomatoConvertersV3;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ZomatoError;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.CommissionService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerLocalityService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.task.ZomatoOrderTask;
import com.stpl.tech.kettle.channelpartner.core.util.CRMServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.KettleServiceClientEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.core.util.ZomatoServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.domain.model.CODCustomerLoginData;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.ActionCategory;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOfferDataVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOfferDetailVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappingVU;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.CafeTimings;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACRespose;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.Slots;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.Timings;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryTimingUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoMenuRequestStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderComplaint;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderDataV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderMaskV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoRejectionCodes;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoSlots;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTimings;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoUnitProductStockV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCatalogueVariant;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCatalogues;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCategory;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCombo;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoEntities;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenu;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenuRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenuResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoModifierGroups;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoModifierVariants;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoOutletDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoRatingRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoSubcategory;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer.ZomatoOfferRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer.ZomatoOfferResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer.ZomatoRestaurantOffers;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.CustomerDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.FetchOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoChargeDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoFetchOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoItemDiscount;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCustomerDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscount;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderItem;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRejectRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTag;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTaxDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderType;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoRatingsData;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoRatingsDataResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.CafeMenuAutoPushDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.MACRelayDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMenuAuditDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderRiderDetailDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.ZomatoOrderRatingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.MACRelay;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerLocalityDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMaskTempResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderRiderDetails;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderStatusUpdate;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoOrderRating;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerStockSchedulerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackStatusDao;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.kettle.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.BusinessHourEvent;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdValueUnit;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.notification.ZomatoBusinessHourChangeEmailTemplate;
import com.stpl.tech.master.notification.ZomatoBusinessHourChangeOnUpdateEmailTemplate;
import com.stpl.tech.master.notification.ZomatoBussinessHourChange;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ZomatoServiceImpl implements ZomatoService {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoServiceImpl.class);
    @Autowired
    CommissionService commissionService;
    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private TrackService trackService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private OrderValidationService orderValidationService;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private PartnerLocalityService partnerLocalityService;

    @Autowired
    private PartnerMenuService partnerMenuService;

    @Autowired
    private PartnerMetadataManagementService partnerMetadataService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;

    @Autowired
    private
    PartnerOrderRiderDetailDao partnerOrderRiderDetailDao;

    @Autowired
    PartnerMenuAuditDao partnerMenuAuditDao;

    @Autowired
    CafeMenuAutoPushDao cafeMenuAutoPushDao;
    // MAC
    @Autowired
    MACRelayDao macRelayDao;

    @Autowired
    ZomatoOrderRatingDao zomatoOrderRatingDao;

    @Autowired
     private PartnerOrderService  partnerOrderService;

    @Autowired
    private SQSNotificationService sqsNotificationService;


    @Autowired
    private CafeStatusChannelPartnerDao partnerOrderDataDao;

    @Autowired
    private PartnerStockSchedulerDao partnerStockSchedulerDao;


    @Autowired
    private SyncLock lock;

    @Autowired
    CafeLookUpServiceImpl cafeLookUpService;

    @Autowired
    private CafeStatusChannelPartnerService cafeStatusChannelPartnerService;


    @Autowired
    private PartnerOrderFallbackStatusDao partnerOrderFallbackStatusDao;

    @Autowired
    private PartnerOrderServiceImpl partnerOrderServiceImpl;




    private static final String PARTNER_NAME = "ZOMATO";

    private static final List<String> WEEK_DAYS = Arrays.asList("Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday");

    /*@Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ZomatoOrderResponse addZomatoOrder(ZomatoOrderRequest request, boolean isManual) {
        ZomatoOrderResponse response = new ZomatoOrderResponse();
        PartnerOrderCacheDetail partnerOrderCacheDetail = channelPartnerDataCache.getPartnerOrderCache().get(request.getOrderId().toString());
        if (partnerOrderCacheDetail == null || !partnerOrderCacheDetail.getPartnerName().equalsIgnoreCase(PARTNER_NAME)) {
            PartnerOrderDetail partnerOrderDetail = receiveOrder(request, isManual, response);
            if (partnerOrderDetail != null && partnerOrderDetail.getToBeProcessed()) {
                trackService.addPartnerOrderToCache(partnerOrderDetail.getPartnerOrderId(),
                        partnerOrderDetail.getPartnerName(), partnerOrderDetail.getUnitId());

                try {
                    Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
                    Order order = convertOrder(partnerOrderDetail);
                    checkOrder(partnerOrderDetail, order, partnerProductTaxMap, isManual);
                    setOrderProcessingThread(partnerOrderDetail, order, isManual, null);
                } catch (Exception e) {
                    LOG.error("Zomato order processing error: ", e);
                    partnerOrderDetail.setBeingProcessed(false);
                    partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                }

                //Send response to Zomato
                response.setCode(200);
                response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                response.setMessage("Success");
                response.setStatus(ZomatoOrderStatus.SUCCESS);
            } else {
                //Send error response to Zomato
                if (response.getCode() == null) {
                    response.setCode(500);
                    response.setExternalOrderId(request.getOrderId().toString());
                    response.setMessage("Failed");
                    response.setStatus(ZomatoOrderStatus.FAILED);
                }
            }
            return response;
        }
        LOG.info("ZOMATO duplicate order received for order id:::: {}", request.getOrderId());
        response.setCode(500);
        response.setExternalOrderId(request.getOrderId().toString());
        response.setMessage("Duplicate order");
        response.setStatus(ZomatoOrderStatus.FAILED);
        if (environmentProperties.slackDuplicateOrders()) {
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    "Zomato duplicate order received:::::\n" + request.getOrderId());
        }
        return response;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ZomatoOrderResponse addZomatoOrderV3(ZomatoOrderRequestV3 request, boolean isManual) {
        long startTime = System.currentTimeMillis();
        ZomatoOrderResponse response = new ZomatoOrderResponse();
        ZomatoOrderResponse result;
        LOG.info("Before Creation of Zomato Order for partner order id {} , LOCK_START_TIME = {}",
                request.getOrderId().toString(), System.currentTimeMillis() - startTime);
        result = lock.syncLock(request.getOrderId().toString(), () -> {
            LOG.info("After Creation of Zomato Order for partner order id {} , LOCK_WAIT_TIME = {}",
                    request.getOrderId().toString(), System.currentTimeMillis() - startTime);
            PartnerOrderCacheDetail partnerOrderCacheDetail = channelPartnerDataCache.getPartnerOrderCache().get(request.getOrderId().toString());
            if (partnerOrderCacheDetail == null || !partnerOrderCacheDetail.getPartnerName().equalsIgnoreCase(PARTNER_NAME)) {
                PartnerOrderDetail partnerOrderDetail = receiveOrderV3(request, isManual, response);
                if (partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeProcessed())) {
                    trackService.addPartnerOrderToCache(partnerOrderDetail.getPartnerOrderId(),
                            partnerOrderDetail.getPartnerName(), partnerOrderDetail.getUnitId());
                    LOG.info("\n----------- ,STEP 0, - ,Order Cached and Primary Checks Executed ----------- , milliseconds {}",
                            (System.currentTimeMillis() - startTime));
                    try {
                        Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
                        Order order = convertOrderV3(partnerOrderDetail, isManual);
                        LOG.info("\n----------- ,STEP 1, - ,Order Converted and Persisted in Mongo ----------- , milliseconds {}",
                                (System.currentTimeMillis() - startTime));
                        checkOrderV3(partnerOrderDetail, order, partnerProductTaxMap, isManual);
                        LOG.info("\n----------- ,STEP 2, - ,Order  Validated ----------- , milliseconds {}",
                                (System.currentTimeMillis() - startTime));
                        if (partnerOrderDetail.getToBeRejected() == null || !partnerOrderDetail.getToBeRejected()) {
                            setOrderProcessingThread(partnerOrderDetail, order, isManual, false);
                            LOG.info("\n----------- ,STEP 3, - ,Order  Processed in Kettle----------- ,milliseconds {}",
                                    (System.currentTimeMillis() - startTime));

                            //Send response to Zomato
                            response.setCode(200);
                            response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                            response.setMessage("Success");
                            response.setStatus(ZomatoOrderStatus.SUCCESS);
                            LOG.info("\n----------- ,STEP 4, - ,Order Processed Successfully----------- milliseconds {}",
                                    (System.currentTimeMillis() - startTime));
                        } else {
                            Set<String> errorCodes = new HashSet<>();
                            if (partnerOrderDetail.getOrderErrors() != null
                                    && partnerOrderDetail.getOrderErrors().size() > 0) {
                                for (PartnerOrderError error : partnerOrderDetail.getOrderErrors()) {
                                    if (error.getErrorCode().isToBeRejected()) {
                                        errorCodes.add(error.getErrorCode().name());
                                    }
                                }
                            }
                            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(partnerOrderDetail.getUnitId());
                            LOG.error("Rejecting order for partner {} for cafe {} for partnerOrder Id {} with reasons {} ",
                                    partnerOrderDetail.getPartnerName(), ubd.getName(),
                                    partnerOrderDetail.getPartnerOrderId(), StringUtils.join(errorCodes, ","));
                            partnerOrderDetail.setBeingProcessed(false);
                            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                            partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                            partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
                            response.setCode(400);
                            response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                            response.setStatus(ZomatoOrderStatus.FAILED);
                            response.setRejectionMessageId(1);
                            return response;
                        }
                    } catch (Exception e) {
                        LOG.error("Zomato order processing error: ", e);
                        partnerOrderDetail.setBeingProcessed(false);
                        partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                        partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                        partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
                        response.setCode(500);
                        response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                        response.setStatus(ZomatoOrderStatus.FAILED);
                    }

                } else {
                    //Send error response to Zomato
                    if (response.getCode() == null) {
                        response.setCode(500);
                        response.setExternalOrderId(request.getOrderId().toString());
                        response.setMessage("Failed");
                        response.setStatus(ZomatoOrderStatus.FAILED);
                    }
                }
                if (partnerOrderDetail != null) {
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                }
                return response;
            }
            return getZomatoDuplicateOrderResponse(response, request.getOrderId().toString());
        });
        return Objects.isNull(result) ? getZomatoDuplicateOrderResponse(response, request.getOrderId().toString()) : result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException {
        if (partnerOrderDetail == null) {
            throw new ChannelPartnerException("Please provide proper order details for processing!");
        }
        partnerOrderDetail.setBeingProcessed(true);
        partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        if (partnerOrderDetail == null) {
            throw new ChannelPartnerException("Error in processing order!");
        }
        Map<Integer, StateTaxVO> partnerProductTaxMap = new HashMap<>();
        boolean isV3 = partnerOrderDetail.getPartnerOrderVersion() != null &&
            partnerOrderDetail.getPartnerOrderVersion().equalsIgnoreCase("V3");
        try {
            switch (partnerOrderDetail.getPartnerOrderStatus()) {
                case RECEIVED:
                    manualProcessReceivedStatus(isV3, partnerOrderDetail, skipInventoryCheck, partnerProductTaxMap);
                    break;
                case CHECKED:
                    manualProcessCheckedStatus(isV3, partnerOrderDetail, skipInventoryCheck);
                    break;
                case FAILED:
                    manualProcessFailedStatus(isV3, partnerOrderDetail, skipInventoryCheck);
                    break;
                case PLACED:
                    notifyOrder(partnerOrderDetail, true);
                    break;
                default:
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
            }
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
            LOG.error("Error in manual processing order ", e);
        }
    }


    private void manualProcessReceivedStatus(boolean isV3, PartnerOrderDetail partnerOrderDetail,
                                             boolean skipInventoryCheck, Map<Integer, StateTaxVO> partnerProductTaxMap) {
        Order order;
        if (isV3) {
            order = convertOrderV3(partnerOrderDetail, true);
            checkOrderV3(partnerOrderDetail, order, partnerProductTaxMap, true);
        } else {
            order = convertOrder(partnerOrderDetail);
            checkOrder(partnerOrderDetail, order, partnerProductTaxMap, true);
        }
        setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
    }

    private void manualProcessCheckedStatus(boolean isV3, PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) {
        if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null &&
            partnerOrderDetail.getToBeProcessed())) {
            partnerOrderDetail.getOrderErrors().clear();
            Order order;
            if (isV3) {
                order = convertOrderV3(partnerOrderDetail, true);
            } else {
                order = convertOrder(partnerOrderDetail);
            }
            setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
        }
    }

    private void manualProcessFailedStatus(boolean isV3, PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) {
        Order order;
        if (isV3) {
            order = convertOrderV3(partnerOrderDetail, true);
        } else {
            order = convertOrder(partnerOrderDetail);
        }
        setOrderProcessingThread(partnerOrderDetail, order, true, skipInventoryCheck);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void confirmZomatoOrder(ZomatoNotificationRequest request, boolean isManual) {
        LOG.info("Inside Zomato Confirmation Call for {}", request.getOrderId());
        String partnerOrderId = request.getOrderId();
        List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(partnerOrderId);
        if (partnerOrderDetails.isEmpty()) {
            LOG.error("No order found for confirmation call zomato: {}", request.getOrderId());
        }
        /*partnerOrderDetails = partnerOrderDetails.stream().filter(partnerOrderDetail ->
            PartnerOrderStatus.PLACED.equals(partnerOrderDetail.getPartnerOrderStatus())).collect(Collectors.toList());*/
        for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetails) {
            if(!partnerOrderDetail.isNotified()) {
                try {
                    ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.CONFIRM_ORDER, HttpMethod.POST,
                        request, ZomatoNotificationResponse.class, partnerOrderDetail.getBrandId());
                    String responseJson = new Gson().toJson(response);
                    LOG.info("Zomato order confirmation response:: {}", responseJson);
                    orderValidationService.updatePartnerNotificationStatus(partnerOrderDetail,
                        response != null && response.getCode() == 200, isManual);
                } catch (HttpStatusCodeException e) {
                    String errorBody = e.getResponseBodyAsString();
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    ZomatoError error = new Gson().fromJson(e.getResponseBodyAsString(), ZomatoError.class);
                    if (error != null && error.getMessage() != null) {
                        errorBody = error.getMessage();
                    }
                    slackAndLogNotification("Error confirming Zomato order:-\n" + errorBody);
                } catch (Exception e) {
                    partnerOrderDetail.setBeingProcessed(false);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    slackAndLogNotification("Error confirming Zomato order:\n" + e);
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoMenu(Integer unitId, boolean isNew, PartnerActionEventType eventType, Integer brandId, Integer employeeId) throws ChannelPartnerException {
        updateZomatoMenuNew(unitId, eventType, brandId, employeeId);
        /*if (!isNew) {
            updateZomatoMenuOld(unitId, eventType, brandId);
        } else {
            updateZomatoMenuNew(unitId, eventType, brandId, employeeId);
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoMenuNew(Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (validatePartnerAndMapping(partnerId, unitId)) {
            UnitMenuAddVO request = createZomatoMenuRequestObj(partnerId, unitId, eventType, brandId, employeeId);
            callZomatoMenuPushAPI(request, employeeId);
        }
    }

    private void updateUnitProductMappingCache(UnitMenuAddVO request) {
        ZomatoMenuRequest menuRequest = (ZomatoMenuRequest) request.getMenuRequest();
        List<String> upsellingProductIds = new ArrayList<>();
        List<String> superComboProductIds = new ArrayList<>();
        List<String> addOnProductIds = new ArrayList<>();
        Map<String, List<String>> productVariantsMap = new HashMap<>();
        Map<String, Set<String>> recipeVariantListMap = new HashMap<>();
        Map<String, Set<String>> recipeVariantMap = new HashMap<>();
        Map<String, Set<String>> splitProductMap = new HashMap<>();
        Set<Integer> splitDimensionProductIds = ChannelPartnerUtils.convertCommaSeparatedStringToList(environmentProperties.getProductIdsForSplitDimension());
        for (ZomatoCatalogues zomatoCatalogues : menuRequest.getMenu().getCatalogues()) {
            if (zomatoCatalogues.getVendorEntityId().endsWith(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER)
                || zomatoCatalogues.getVendorEntityId().contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                upsellingProductIds.add(zomatoCatalogues.getVendorEntityId());
            }
            if(zomatoCatalogues.getVendorEntityId().contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)){
                String[] entityId = zomatoCatalogues.getVendorEntityId().split("_");
                superComboProductIds.add(entityId[0]);
            }
            List<String> variantIds = new ArrayList<>();
            zomatoCatalogues.getVariants().forEach(zomatoCatalogueVariant ->
                variantIds.add(zomatoCatalogueVariant.getVendorEntityId())
            );
            if(!(zomatoCatalogues.getVendorEntityId().contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER))) {
                productVariantsMap.put(zomatoCatalogues.getVendorEntityId(), variantIds);
            }
            Set<String> catalogueVariants = new HashSet<>();
            if(!CollectionUtils.isEmpty(zomatoCatalogues.getVariants())) {
                zomatoCatalogues.getVariants().get(0).getModifierGroups().forEach(modGroup -> {
                    catalogueVariants.add(modGroup.getVendorEntityId());
                });
            }
            if(!CollectionUtils.isEmpty(catalogueVariants)) {
                recipeVariantListMap.put(zomatoCatalogues.getVendorEntityId(), catalogueVariants);
            }
        }
        Map<String,Set<String>> modGroupsRecipeVariant = new HashMap<>();
        for (ZomatoModifierGroups zomatoModifierGroups : menuRequest.getMenu().getModifierGroups()) {
            if (!zomatoModifierGroups.getVariants().isEmpty()) {
                zomatoModifierGroups.getVariants().forEach(variant -> {
                    try{
                        if (masterDataCache.getProduct(Integer.parseInt(variant.getVendorEntityId())).isInventoryTracked()) {
                            addOnProductIds.add(variant.getVendorEntityId());
                        }

                    } catch(NumberFormatException ignored) {

                    }
                });
            }
            if(Objects.nonNull(zomatoModifierGroups.getRecipeVariant()) && zomatoModifierGroups.getRecipeVariant()){
                Set<String> variantIds = zomatoModifierGroups.getVariants().stream()
                        .map(ZomatoModifierVariants::getVendorEntityId)
                        .collect(Collectors.toSet());
                if(!CollectionUtils.isEmpty(variantIds)) {
                    modGroupsRecipeVariant.put(zomatoModifierGroups.getVendorEntityId(), variantIds);
                }
            }
        }
        for (ZomatoCombo combos : menuRequest.getMenu().getCombos()) {
            superComboProductIds.add(combos.getVendorEntityId());
        }
        recipeVariantListMap.forEach((key, variants) -> {
            variants.stream()
                    .filter(modGroupsRecipeVariant::containsKey)
                    .forEach(variant -> {
                        if (recipeVariantMap.containsKey(key)) {
                            recipeVariantMap.get(key).addAll(modGroupsRecipeVariant.get(variant));
                        } else {
                            recipeVariantMap.put(key, modGroupsRecipeVariant.get(variant));
                        }
                    });
        });
        if (!CollectionUtils.isEmpty(splitDimensionProductIds)) {
            menuRequest.getMenu().getCategories().stream()
                    .flatMap(menuCategory -> menuCategory.getSubCategories().stream())
                    .flatMap(subcategory -> subcategory.getEntities().stream())
                    .forEach(zomatoEntity -> {
                        String[] entityId = zomatoEntity.getVendorEntityId().split("_");
                        String key = entityId[0];
                        if (splitDimensionProductIds.contains(Integer.parseInt(key))) {
                            splitProductMap.computeIfAbsent(key, k -> new HashSet<>()).add(zomatoEntity.getVendorEntityId());
                        }
                    });
        }

        LOG.info("Product variants map is not empty. Updating value to db.");
        if (!upsellingProductIds.isEmpty() || !superComboProductIds.isEmpty() || !productVariantsMap.isEmpty()) {
            partnerMenuService.addPartnerUnitMenuMappingForUpsellingProds(new ArrayList<>(), request, upsellingProductIds,
                superComboProductIds, productVariantsMap,addOnProductIds, recipeVariantMap,splitProductMap);
            channelPartnerDataCache.loadUnitUpsellingSuperComboMapping(request.getKettlePartnerId(),
                    request.getUnitId(), request.getBrandId());
        }
    }

    /*@Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoMenuOld(Integer unitId, PartnerActionEventType eventType, Integer brandId) throws ChannelPartnerException {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (validatePartnerAndMapping(partnerId, unitId)) {
            UnitMenuAddVO request = createZomatoMenuRequestObjOld(partnerId, unitId, eventType, brandId);
            ZomatoMenuRequestOld menuRequest = (ZomatoMenuRequestOld) request.getMenuRequest();
            menuRequest.setOutletId(getTestOutletForDev(brandId, menuRequest.getOutletId()));
            try {
                ZomatoMenuResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.MENU_ADD_V2, HttpMethod.POST, menuRequest, ZomatoMenuResponse.class, brandId);
                String responseString = new Gson().toJson(response);
                slackAndLogNotification("Zomato menu added response::::\n" + responseString);
            } catch (HttpStatusCodeException e) {
                catchAndLogZomatoException(e, "Error adding menu to Zomato:::::\n");
            } catch (Exception e) {
                catchAndLogException(e, "Error adding menu to Zomato:::::\n");
            }
        }
    }*/

    /*@Override
    public UnitMenuAddVO createZomatoMenuRequestObjOld(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId) throws ChannelPartnerException {
        UnitMenuAddVO request = new UnitMenuAddVO();
        request.setKettlePartnerId(partnerId);
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
        request.setUnitId(unitId);
        request.setBrandId(brandId);
        request.setNew(false);
        request.setRegion(masterDataCache.getUnit(unitId).getRegion());
        Object menuData = null;
        if (PartnerActionEventType.UPDATE_MENU.equals(eventType)) {
            menuData = partnerMenuService.getPartnerMenuDetail(request).getMenuData();
        } else if (PartnerActionEventType.UPDATE_UNIT_MENU.equals(eventType)) {
            menuData = partnerMenuService.getActivePartnerUnitMenuDetail(partnerId, unitId, false, brandId).getMenuData();
        }
        if (menuData != null) {
            ZomatoMenuRequestOld menuRequest = new ZomatoMenuRequestOld();
            menuRequest.setMenu(ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), OldZomatoMenu.class));
            removeFilterProductsOld(menuRequest.getMenu(), brandId);
            updateProductTagsOld(menuRequest.getMenu(), brandId);
            updateProductAliasesOld(menuRequest.getMenu(),
                channelPartnerDataCache.getProductAliasMap(channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME)
                    .getKettlePartnerId()));
            //Setting treats product
            IdCodeName treatsItem = partnerMetadataService.getZomatoTreatsItem();
            if (treatsItem != null) {
                menuRequest.getMenu().getCategories().forEach(zomatoCategory ->
                    zomatoCategory.getItems().forEach(zomatoMenuItem -> {
                        if (zomatoMenuItem.getItemId().equalsIgnoreCase(Integer.toString(treatsItem.getId()))) {
                            zomatoMenuItem.setItemIsTreatsActive(1);
                            zomatoMenuItem.getItemTags().add(20);
                        }
                    })
                );
            }
            setOfferInMenuOld(menuRequest, partnerId, unitId);
            menuRequest.setOutletId(restaurantId);
            request.setMenuRequest(menuRequest);
        }
        return request;
    }*/

    /*private void setOfferInMenuOld(ZomatoMenuRequestOld menuRequest, Integer partnerId, Integer unitId)
        throws ChannelPartnerException {
        List<PartnerOfferDetail> offers = partnerMenuService.getAllActiveOffersForPartnerAndUnit(partnerId, unitId);
        if (menuRequest.getRestaurantOffers() == null) {
            menuRequest.setRestaurantOffers(new ArrayList<>());
        }
        menuRequest.getRestaurantOffers().clear();
        for (PartnerOfferDetail partnerOfferDetail : offers) {
            ZomatoRestaurantOffer offer = ChannelPartnerUtils.deserializeObject(new Gson().toJson(partnerOfferDetail.getOfferData()), ZomatoRestaurantOffer.class);
            if (offer != null) {
                offer.setOfferId(partnerOfferDetail.getId());
                menuRequest.getRestaurantOffers().add(offer);
            }
        }

    }*/

    @Override
    public UnitMenuAddVO createZomatoMenuRequestObj(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) {
        UnitMenuAddVO request = new UnitMenuAddVO();
        request.setKettlePartnerId(partnerId);
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
        request.setUnitId(unitId);
        request.setBrandId(brandId);
        request.setEmployeeId(employeeId);
        request.setNew(true);
        request.setRegion(masterDataCache.getUnit(unitId).getRegion());
        Object menuData = null;
        List<Object> charges = null;
        if (PartnerActionEventType.UPDATE_MENU.equals(eventType)) {
            menuData = partnerMenuService.getPartnerMenuDetail(request).getMenuData();
        } else if (PartnerActionEventType.UPDATE_UNIT_MENU.equals(eventType)) {
            menuData = partnerMenuService.getActivePartnerUnitMenuDetail(partnerId, unitId, true, brandId).getMenuData();
            charges = partnerMenuService.getActivePartnerUnitMenuDetail(partnerId, unitId, true, brandId).getCharges();
        }
        if (menuData != null) {
            ZomatoMenuRequest menuRequest = new ZomatoMenuRequest();
            menuRequest.setMenu(ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), ZomatoMenu.class));
            menuRequest.setCharges(ChannelPartnerUtils.deserializeObject(new Gson().toJson(charges), List.class));
            removeFilterProducts(menuRequest.getMenu(), brandId);
            updateProductTags(menuRequest.getMenu(), brandId);
            updateMeatTypeTags(menuRequest.getMenu(), brandId);
            updateAllergenTypeTags(menuRequest.getMenu(),brandId);
            updateServingInfoTypeTags(menuRequest.getMenu(),brandId);
            updateServingSizeTypeTags(menuRequest.getMenu(),brandId);
            updateProductAliases(menuRequest.getMenu(), channelPartnerDataCache.getProductAliasMap(channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId()));
            menuRequest.setOutletId(restaurantId);
            request.setMenuRequest(menuRequest);
        }
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoStock(UnitProductsStockEvent event) {
        if (event != null && event.getUnitId() != null && masterDataCache.getUnits().containsKey(event.getUnitId())) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                .anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId &&
                        unitChannelPartnerMapping.getUnit().getId() == event.getUnitId());
            if (mappingValid) {
                updateZomatoStockV3(event);
                /*for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
                    if (key.getPartnerId().equals(partnerId) && key.getUnitId().equals(event.getUnitId())) {
                        updateZomatoStockV3(event);
                        break;
                    }
                }*/
            }
        } else {
            String eventJson = new Gson().toJson(event);
            String message = ChannelPartnerUtils.getMessage("Incorrect Event Data",eventJson);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                message);
            LOG.error("incorrect event data: {}", eventJson);
        }
    }

    /*@Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoStockV1(UnitProductsStockEvent event) {
        if (event != null && event.getUnitId() != null && masterDataCache.getUnits().containsKey(event.getUnitId())) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            boolean mappingValid = masterDataCache.getUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId &&
                    unitChannelPartnerMapping.getUnit().getId() == event.getUnitId());
            Unit unit = masterDataCache.getUnit(event.getUnitId());
            if (mappingValid && UnitStatus.ACTIVE.equals(unit.getStatus()) && unit.isLive()) {
                for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
                    if (key.getPartnerId().equals(partnerId) && key.getUnitId().equals(event.getUnitId())) {
                        String eventJson = new Gson().toJson(event);
                        LOG.info("Stock ON/OFF API call Zomato: {}", eventJson);
                        ZomatoUnitProductStock zomatoUnitProductStock = new ZomatoUnitProductStock();
                        event.getProductIds().forEach(s -> {
                            ZomatoProductStock zomatoProductStock = new ZomatoProductStock();
                            zomatoProductStock.setItemId(s);
                            zomatoProductStock.setItemInStock(event.getStatus().equals(StockStatus.STOCK_IN) ? 1 : 0);
                            zomatoUnitProductStock.getItems().add(zomatoProductStock);
                        });
                        zomatoUnitProductStock.setOutletId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
                        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                            zomatoUnitProductStock.setOutletId(ChannelPartnerServiceConstants.TEST_UNIT_NEW);
                        }
                        String zomatoUnitProductStockJson = new Gson().toJson(zomatoUnitProductStock);
                        try {
                            if (!zomatoUnitProductStock.getItems().isEmpty()) {
                                ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                                        ZomatoServiceEndpoints.UPDATE_ITEM_STOCK, HttpMethod.POST, zomatoUnitProductStock, ZomatoNotificationResponse.class, key.getBrandId());
                                String responseJson = new Gson().toJson(response);
                                LOG.info("Zomato stock API request::::: {}", zomatoUnitProductStockJson);
                                LOG.info("Zomato stock API response:::: {}", responseJson);
                                if (response == null || response.getCode() != 200) {
                                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                            "Zomato stock API returned incorrect response: " + eventJson +
                                                    "\nrequest: " + zomatoUnitProductStockJson);
                                }
                            }
                        } catch (HttpStatusCodeException e) {
                            String errorBody = e.getResponseBodyAsString();
                            ZomatoError zomatoError = new Gson().fromJson(e.getResponseBodyAsString(), ZomatoError.class);
                            if (zomatoError != null && zomatoError.getMessage() != null) {
                                errorBody = zomatoError.getMessage();
                            }
                            LOG.error("Error updating stock to Zomato::::: {}", errorBody);
                            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                                    "Error updating stock to Zomato:\n request : " + zomatoUnitProductStockJson
                                            + "\n" + masterDataCache.getUnit(event.getUnitId()).getName()
                                            + "\n" + errorBody);
                        } catch (Exception e) {
                            LOG.error("Error updating stock to Zomato:::::", e);
                            slackAndLogNotification("Error updating stock to Zomato::\n request : " + zomatoUnitProductStockJson
                                + "\n" + masterDataCache.getUnit(event.getUnitId()).getName() + "\n" + e);
                        }
                    }
                }
            }
        } else {
            String eventJson = new Gson().toJson(event);
            slackAndLogNotification(eventJson);
        }
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoStockV3(UnitProductsStockEvent event) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (event != null && event.getUnitId() != null && masterDataCache.getUnit(event.getUnitId()) != null &&
            validatePartnerAndMapping(partnerId, event.getUnitId()) && masterDataCache.getUnit(event.getUnitId()).isLive() &&
            UnitStatus.ACTIVE.equals(masterDataCache.getUnit(event.getUnitId()).getStatus())) {
            List<Integer> brandId = new ArrayList<>();
            if (Objects.nonNull(event.getBrandId())) {
                brandId = Arrays.asList(event.getBrandId());
            } else {
                brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID,AppConstants.GNT_BRAND_ID,AppConstants.DOHFUL_BRAND_ID);
            }
            for(Integer brand : brandId) {
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(event.getUnitId(), brand,partnerId);
                if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                     if(event.getStatus().equals(StockStatus.STOCK_IN) && Boolean.TRUE.equals(event.getForceStockOut())){
                        filterStockOutProductIdsForStockIn(event,key);
                     }
                     processUnitPartnerBrandMappingEvents(event, key);
                }
            }
//            for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
//                if (key.getPartnerId().equals(partnerId) && key.getUnitId().equals(event.getUnitId())) {
//                    processUnitPartnerBrandMappingEvents(event, key);
//                }
//            }
        } else {
            String eventJson = new Gson().toJson(event);
            slackAndLogNotification("UnitProductsStockEvent unit partner mapping not found:::::" + eventJson);
        }
    }

    private void filterStockOutProductIdsForStockIn(UnitProductsStockEvent event, UnitPartnerBrandKey unitPartnerBrandKey){
        try{
            List<Integer> productIds = event.getProductIds().stream().map(Integer::parseInt).toList();
            List<String> filteredProductIds = new ArrayList<>();
            Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
                    getUnitPartnerBrandMappingMetaData().get(unitPartnerBrandKey).getPriceProfileUnitId());
            for(Integer productId : productIds){
                if(AppUtils.isCombo(masterDataCache.getProduct(productId).getTaxCode())){
                    if(map.containsKey(productId)){
                        Set<Integer> comboItems = map.get(productId);
                        comboItems = comboItems.stream().filter(id-> masterDataCache.getProduct(id).isInventoryTracked()).
                                collect(Collectors.toSet());
                        Map productStock = orderValidationService.getUnitProductInventoryByProducts(unitPartnerBrandKey.getUnitId(),comboItems.stream().toList());
                        LOG.info("Inventory map for unit Id  :::: {} :::: Partner :::: {}  Time ::::: {}   ::::::  {} ", unitPartnerBrandKey.getUnitId() , PARTNER_NAME,
                                AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
                        Boolean comboStockOut = (comboItems.stream().anyMatch(comboItemId -> (!productStock.containsKey(comboItemId.toString())
                                || (Integer) productStock.get(comboItemId.toString()) <= 0)));
                        if(!comboStockOut){
                            filteredProductIds.add(String.valueOf(productId));
                        }else{
                            LOG.info("Combo Id : {} for unit Id : {} is stock out during scheduled stock in event , combo items : {} ",productId,
                                    event.getUnitId(),comboItems);
                        }
                    }
                }else{
                    Map productStock = orderValidationService.getUnitProductInventoryByProducts(unitPartnerBrandKey.getUnitId(),  Collections.singletonList(productId));
                    LOG.info("Inventory map for unit Id  :::: {} :::: Partner :::: {}  Time ::::: {}   ::::::  {} ", unitPartnerBrandKey.getUnitId() , PARTNER_NAME,
                            AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
                    Boolean productStockOut = productStock.containsKey(productId.toString()) && ((Integer) productStock.get(productId.toString()) <= 0);
                    if(!productStockOut){
                        filteredProductIds.add(String.valueOf(productId));
                    }else{
                        LOG.info("Product Id : {} for unit Id : {} is stock out during scheduled stock in event  ",productId,
                                event.getUnitId());
                    }
                }
            }
            event.setProductIds(filteredProductIds);
        }catch (Exception e){
            LOG.info("Error While Filtering stock out products : {} ",e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoStockV3ForScheduleStockUpdate(UnitProductsStockEvent event , PartnerStockUpdateSchedule partnerStockUpdateSchedule ) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (event != null && event.getUnitId() != null && masterDataCache.getUnit(event.getUnitId()) != null &&
                validatePartnerAndMapping(partnerId, event.getUnitId()) && masterDataCache.getUnit(event.getUnitId()).isLive() &&
                UnitStatus.ACTIVE.equals(masterDataCache.getUnit(event.getUnitId()).getStatus())) {
            List<Integer> brandId = new ArrayList<>();
            if (Objects.nonNull(event.getBrandId())) {
                brandId = Arrays.asList(event.getBrandId());
            } else {
                brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID,AppConstants.GNT_BRAND_ID,AppConstants.DOHFUL_BRAND_ID);
            }
            for(Integer brand : brandId) {
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(event.getUnitId(), brand,partnerId);
                if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                    if(event.getStatus().equals(StockStatus.STOCK_IN)){
                         filterStockOutProductIdsForStockIn(event,key);
                    }
                    processUnitPartnerBrandMappingEventsForScheduledStockUpdate(event, key,partnerStockUpdateSchedule);
                }
            }
//            for (UnitPartnerBrandKey key : masterDataCache.getUnitPartnerBrandMappingMetaData().keySet()) {
//                if (key.getPartnerId().equals(partnerId) && key.getUnitId().equals(event.getUnitId())) {
//                    processUnitPartnerBrandMappingEvents(event, key);
//                }
//            }
        } else {
            String eventJson = new Gson().toJson(event);
            slackAndLogNotification("UnitProductsStockEvent unit partner mapping not found:::::" + eventJson);
        }
    }


    private List<String> filterProductsScheduledForStockUpdate(UnitProductsStockEvent event , ZomatoUnitProductStockV3 stockEvent ,List<Integer> knockAppStockOutProducts){
        try{
            List<PartnerStockUpdateSchedule> partnerStockSchedulerList = (List<PartnerStockUpdateSchedule>)
                    partnerStockSchedulerDao.findAll();
            List<String> originalZomatoProductIds = stockEvent.getCatalogueVendorEntityIds();
            List<String> forceStockOutProductIds = new ArrayList<>(knockAppStockOutProducts.stream().map(String::valueOf).toList());
            if(!CollectionUtils.isEmpty(partnerStockSchedulerList)) {
                List<String> scheduledProductIds = new ArrayList<>();
                Date currentTime = AppUtils.getCurrentTimestamp();
                for (PartnerStockUpdateSchedule partnerStockUpdateSchedule : partnerStockSchedulerList) {
                    if (partnerStockUpdateSchedule.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)
                            && partnerStockUpdateSchedule.getPartnerId().equals(3)
                            && Objects.nonNull(partnerStockUpdateSchedule.getLastExecutionTime()) &&
                            (currentTime.after(partnerStockUpdateSchedule.getStockOutStartTime())
                                    && currentTime.before(partnerStockUpdateSchedule.getStockOutEndTime())
                            )) {
                        if (partnerStockUpdateSchedule.getUnitIds().contains(String.valueOf(event.getUnitId()))) {
                            try{
                                List<String> tempList = Arrays.stream(partnerStockUpdateSchedule.getProductIds().split(",")).toList();
                                scheduledProductIds.addAll(tempList);
                                tempList.stream().map(Integer::parseInt).forEach(knockAppStockOutProducts::add);
                            }catch (Exception e){
                                //parse error
                            }
                        }
                    }
                }
                forceStockOutProductIds.addAll(scheduledProductIds);
            }
                if(!org.springframework.util.CollectionUtils.isEmpty(forceStockOutProductIds)){
                    List<String> filteredProductIds = new ArrayList<>();
                    for(String productId : event.getProductIds()){
                        if(!forceStockOutProductIds.contains(productId)){
                            filteredProductIds.add(productId);
                        }
                    }
                    List<String> filteredZomatoProductIds = new ArrayList<>();
                    for(String zomatoProductId : stockEvent.getCatalogueVendorEntityIds()){
                        if(!forceStockOutProductIds.contains(zomatoProductId)){
                            filteredZomatoProductIds.add(zomatoProductId);
                        }
                    }
                    if(originalZomatoProductIds.size() != filteredProductIds.size()){
                        LOG.info("original Zomato Product Ids for Unit Id : {} ::::: {} ",event.getUnitId(),originalZomatoProductIds);
                        LOG.info("filtered Zomato Product Ids for Unit Id : {} ::::: {} ",event.getUnitId(),filteredProductIds);
                    }
                    event.setProductIds(filteredProductIds);
                    stockEvent.setCatalogueVendorEntityIds(filteredZomatoProductIds);
                }
                return forceStockOutProductIds;
        }catch (Exception e){
            LOG.info("Error while Filtering Scheduled Products For Stock in ::::: {} ", e);
        }
        return new ArrayList<>();


    }

    private boolean processUnitPartnerBrandMappingEvents(UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
        if (stockEvent == null) {
            LOG.info("Error cloning zomato stock event:::::");
            return true;
        }
        String stockEventJson = new Gson().toJson(stockEvent);
        List<Integer> knockAppStockOutProducts = cafeStatusChannelPartnerService.setKnockAppStockOutProducts(event, key);
        LOG.info("Stock ON/OFF API event Zomato: brandId:{}: {}", key.getBrandId(), stockEventJson);
        ZomatoUnitProductStockV3 zomatoUnitProductStock = new ZomatoUnitProductStockV3();
        Integer noOfDays = Boolean.TRUE.equals(event.getForceStockOut()) ? 1 : 2;
        zomatoUnitProductStock.setInStock(stockEvent.getStatus().equals(StockStatus.STOCK_IN));
        zomatoUnitProductStock.setOutletId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
        zomatoUnitProductStock.setCatalogueVendorEntityIds(stockEvent.getProductIds());
        zomatoUnitProductStock.setAutoTurnOnTime("custom");
        zomatoUnitProductStock.setCustomTurnOnTime(ChannelPartnerUtils
                .getFormattedTime(ChannelPartnerUtils.getDayBeforeOrAfterCurrentDay(noOfDays), "yyyy-MM-dd HH:mm"));
        PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping = channelPartnerDataCache
            .getPartnerUnitUpsellingCombosProductMappings().get(key);
        if(event.getStatus().equals(StockStatus.STOCK_IN)){
            filterProductsScheduledForStockUpdate(stockEvent,zomatoUnitProductStock, knockAppStockOutProducts);
        }
        Map<Integer,Boolean> forceStockOutMap = knockAppStockOutProducts.stream().collect(Collectors.toMap(productId -> productId,productId -> true,(a,b) -> a));
        //send only products which are applicable to this brand
        filterBrandLevelProductsUsingPricingUnit(zomatoUnitProductStock, stockEvent, key, unitUpsellCombosMapping,forceStockOutMap);
        zomatoUnitProductStock.setOutletId(getTestOutletForDev(key.getBrandId(), zomatoUnitProductStock.getOutletId()));
        // ADDING NORMAL COMBO PRODUCTS - THIS EXCLUDE HERO/SUPER COMBOS
        addNormalComboItemsToStockEvent(zomatoUnitProductStock, stockEvent, key , forceStockOutMap);
        if(Boolean.TRUE.equals(event.getForceStockOut())){
            updateHeroComboVariantsStock(zomatoUnitProductStock, event, key, unitUpsellCombosMapping,forceStockOutMap);
        }
        // ADDING SINGLE SERVE MENU PRODUCTS
        //addSingleServeItemsTOStockEvent(zomatoUnitProductStock, key);
        // ADDING UPSELLING AND HERO PRODUCTS
        //LOG.info("Force Stock Out Map : {} " , new Gson().toJson(forceStockOutMap));
        addHeroAndUpsellingProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
        // ADDING SUPER COMBO PRODUCTS
        addSuperComboProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
        try {
            if(Objects.nonNull(unitUpsellCombosMapping.getProductVariantsMap())) {
                List<String> comboIds = unitUpsellCombosMapping.getProductVariantsMap().keySet().stream().filter(variantMap ->
                        variantMap.contains(ChannelPartnerServiceConstants.HERO_COMBO_IDENTIFIER)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(comboIds)) {
                    Map<String, List<String>> stockMap = partnerMenuService.checkForStockInAndOutByRecipeMinAndMax(event, unitUpsellCombosMapping, key, comboIds
                    ,forceStockOutMap);
                    if (!CollectionUtils.isEmpty(stockMap.get(stockEvent.getStatus().name()))) {
                        zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(stockMap.get(stockEvent.getStatus().name()));
                    }
                }
            }
        }catch (Exception e){
            LOG.info("Error while updating Stock Event for combo ::::::::::::::: {}",e);
        }
        addRecipeVariantsToStockEvent(zomatoUnitProductStock,stockEvent, unitUpsellCombosMapping.getProductRecipeVariantsMap());
        addSplitProductToStockEvent(key,zomatoUnitProductStock,stockEvent, unitUpsellCombosMapping.getSplitProductMap());
        addAddOnProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
        callUnitProductStockAPIV3(zomatoUnitProductStock, stockEvent, key);
        return false;
    }


    private boolean processUnitPartnerBrandMappingEventsForScheduledStockUpdate(UnitProductsStockEvent event, UnitPartnerBrandKey key
    ,PartnerStockUpdateSchedule partnerStockUpdateSchedule) {
        UnitProductsStockEvent stockEvent = (UnitProductsStockEvent) ChannelPartnerUtils.deepClone(event);
        stockEvent.setForceStockOut(Boolean.TRUE);
        if (stockEvent == null) {
            LOG.info("Error cloning zomato stock event:::::");
            return true;
        }
        String stockEventJson = new Gson().toJson(stockEvent);
        Boolean isSuperComboEvent = event.getProductIds().stream().anyMatch(id -> (id.contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)
                || id.contains(ChannelPartnerServiceConstants.HERO_COMBO_IDENTIFIER)));
        Map<Integer,Boolean> forceStockOutMap = event.getProductIds().stream().filter(id -> !(id.contains(ChannelPartnerServiceConstants.SUPER_COMBO_ITEM_IDENTIFIER)
                || id.contains(ChannelPartnerServiceConstants.HERO_COMBO_IDENTIFIER))).map(Integer::parseInt).
                collect(Collectors.toMap(productId -> productId,productId -> true,(a,b) -> a));
        LOG.info("Stock ON/OFF API event Zomato: brandId:{}: {}", key.getBrandId(), stockEventJson);
        ZomatoUnitProductStockV3 zomatoUnitProductStock = new ZomatoUnitProductStockV3();
        zomatoUnitProductStock.setInStock(stockEvent.getStatus().equals(StockStatus.STOCK_IN));
        zomatoUnitProductStock.setOutletId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
        zomatoUnitProductStock.setCatalogueVendorEntityIds(stockEvent.getProductIds());
        zomatoUnitProductStock.setAutoTurnOnTime("custom");
        String dateForScheduleStockUpdate = ChannelPartnerUtils
                .getFormattedTime(AppUtils.addDays(partnerStockUpdateSchedule.getStockOutEndTime(),1)
                        , "yyyy-MM-dd HH:mm");
        LOG.info("Stock in Time For Scheduled Stock Update For Task Id ::::: {} is {} ",partnerStockUpdateSchedule.getStockUpdateId()
        , dateForScheduleStockUpdate);
        zomatoUnitProductStock.setCustomTurnOnTime(dateForScheduleStockUpdate);
        PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping = channelPartnerDataCache
                .getPartnerUnitUpsellingCombosProductMappings().get(key);
        //send only products which are applicable to this brand
        //filterBrandLevelProductsUsingPricingUnit(zomatoUnitProductStock, stockEvent, key, unitUpsellCombosMapping);
        zomatoUnitProductStock.setOutletId(getTestOutletForDev(key.getBrandId(), zomatoUnitProductStock.getOutletId()));
        if(Boolean.FALSE.equals(isSuperComboEvent)){
            // ADDING NORMAL COMBO PRODUCTS - THIS EXCLUDE HERO/SUPER COMBOS
            addNormalComboItemsToStockEvent(zomatoUnitProductStock, stockEvent, key,forceStockOutMap);
            // ADDING SINGLE SERVE MENU PRODUCTS
            //addSingleServeItemsTOStockEvent(zomatoUnitProductStock, key);
            // ADDING UPSELLING AND HERO PRODUCTS
            addHeroAndUpsellingProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
            // ADDING SUPER COMBO PRODUCTS
            addSuperComboProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
        }
        //addRecipeVariantsToStockEvent(zomatoUnitProductStock,stockEvent, unitUpsellCombosMapping.getProductRecipeVariantsMap());
        //addAddOnProductIds(zomatoUnitProductStock, stockEvent, unitUpsellCombosMapping);
        callUnitProductStockAPIV3(zomatoUnitProductStock, stockEvent, key);
        return false;
    }

    private void addAddOnProductIds(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent stockEvent, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if (unitUpsellCombosMapping != null && unitUpsellCombosMapping.getAddOnProductIds()!=null && !unitUpsellCombosMapping.getAddOnProductIds().isEmpty()) {
            List<String> addOnProductItemIds = getAddOnProductsIds(stockEvent.getProductIds(),
                    unitUpsellCombosMapping.getAddOnProductIds());
            if (!addOnProductItemIds.isEmpty()) {
                zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(addOnProductItemIds);
            }
        }
    }

    private List<String> getAddOnProductsIds(List<String> productIds, List<String> addOnProductIds) {
        List<String> addOnProductItemIds = new ArrayList<>();
        for (String product : productIds) {
            for (String addOn : addOnProductIds) {
                //PRODUCT ID IS ALWAYS AT THE FRONT OF THE STRING FOR HERO AND UPSELLING
                if (product.equalsIgnoreCase(addOn)) {
                    addOnProductItemIds.add(addOn);
                }
            }
        }
        return addOnProductItemIds;
    }

    private void addHeroAndUpsellingProductIds(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent event,
                                               PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if (unitUpsellCombosMapping != null && !unitUpsellCombosMapping.getUpsellingProdIds().isEmpty()) {
            List<String> upsellingProductsIds = getHeroAndUpsellingProductsIds(event.getProductIds(),
                unitUpsellCombosMapping.getUpsellingProdIds());
            if (!upsellingProductsIds.isEmpty()) {
                zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(upsellingProductsIds);
            }
        }
    }

    private void addSuperComboProductIds(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent event,
                                               PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if (!CollectionUtils.isEmpty(unitUpsellCombosMapping.getSuperCombosProdIds())) {
            List<String> superComboProductsIds = getSuperComboProductsIds(event,
                    unitUpsellCombosMapping);
            if (!CollectionUtils.isEmpty(superComboProductsIds)) {
                zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(superComboProductsIds);
            }
        }
    }

    //GETTING LINKED UPSELLING AND HERO PRODUCTS
    private List<String> getHeroAndUpsellingProductsIds(List<String> productIds, List<String> upsellingProdIds) {
        List<String> heroAndUpsellingProductIds = new ArrayList<>();
        for (String product : productIds) {
            for (String upsellProds : upsellingProdIds) {
                //PRODUCT ID IS ALWAYS AT THE FRONT OF THE STRING FOR HERO AND UPSELLING
                if (product.equalsIgnoreCase(upsellProds.split("_")[0])) {
                    heroAndUpsellingProductIds.add(upsellProds);
                }
            }
        }
        return heroAndUpsellingProductIds;
    }

    private List<String> getSuperComboProductsIds(UnitProductsStockEvent event, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        List<String> superComboIds = new ArrayList<>();
        for (String product : event.getProductIds()) {
            for(Map.Entry<String, List<String>> productId : unitUpsellCombosMapping.getProductVariantsMap().entrySet()){
                //PRODUCT ID IS ALWAYS AT THE SECOND INDEX OF THE STRING FOR SUPER COMBO PRODUCTS
                String[] key = productId.getValue().get(0).split("_");
                //LOG.info("Key for combo : {}", new Gson().toJson(key));
                if(unitUpsellCombosMapping.getSuperCombosProdIds().contains(key[0]) && key.length > 1) {
                    if (product.equalsIgnoreCase(key[1])) {
                        superComboIds.add(productId.getKey());
                    }
                }
            }
        }
        return superComboIds;
    }

    private void callUnitProductStockAPIV3(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent stockEvent,
                                           UnitPartnerBrandKey key) {
        String zomatoUnitProductStockJson = new Gson().toJson(zomatoUnitProductStock);
        try {
            if (!zomatoUnitProductStock.getCatalogueVendorEntityIds().isEmpty() || !zomatoUnitProductStock.getComboVendorEntityIds().isEmpty()
                || !zomatoUnitProductStock.getVariantVendorEntityIds().isEmpty()) {
                ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                    ZomatoServiceEndpoints.UPDATE_ITEM_STOCK_V3, HttpMethod.POST, zomatoUnitProductStock, ZomatoNotificationResponse.class,
                    key.getBrandId());
                String responseJson = new Gson().toJson(response);
//                LOG.info("Zomato stock API request: {}", zomatoUnitProductStockJson);
//                LOG.info("Zomato stock API response: {}", responseJson);
                LOG.info("STOCK API REQUEST PROCESSED :::: (ZOMATO) :::: REQUEST :::: {} :::: RESPONSE :::: {}",zomatoUnitProductStockJson,responseJson);
                if (response == null || response.getCode() != 200) {
                    String message = ChannelPartnerUtils.getMessage("Zomato stock API returned incorrect response",":::::REQUEST:::::"+zomatoUnitProductStockJson+"\n"+":::::RESPONSE:::::"+responseJson);
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
                } else {
                    logStockUpdateSnapshot(zomatoUnitProductStock, stockEvent);
                }
            }
        } catch (HttpStatusCodeException e) {
            String errorBody = e.getResponseBodyAsString();
            ZomatoError zomatoError = new Gson().fromJson(e.getResponseBodyAsString(), ZomatoError.class);
            if (zomatoError != null && zomatoError.getMessage() != null) {
                errorBody = zomatoError.getMessage();
            }
            partnerOrderService.logStockRefreshEvent(stockEvent.getUnitId(), key.getPartnerId(),
                    stockEvent.getBrandId(),stockEvent.getStatus().name(),
                    AppConstants.FAILED);
            slackAndLogNotification("Error updating stock to Zomato:::::\n request : " + zomatoUnitProductStockJson
                + "\n" + masterDataCache.getUnit(stockEvent.getUnitId()).getName() + "\n" + errorBody);
        } catch (Exception e) {
            partnerOrderService.logStockRefreshEvent(stockEvent.getUnitId(), key.getPartnerId(),
                    stockEvent.getBrandId(),stockEvent.getStatus().name(),
                    AppConstants.FAILED);
            slackAndLogNotification("Error updating stock to Zomato:::::\n request : " + zomatoUnitProductStockJson
                + "\n" + masterDataCache.getUnit(stockEvent.getUnitId()).getName() + "\n" + e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitInventory(List<Integer> unitIds) {
        for (Integer unitId : unitIds) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            partnerOrderService.logStockRefreshEvent(unitId,partnerId,null,
                    "REFRESH_START",AppConstants.STATUS_SUCCESSFUL);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            Unit unit = masterDataCache.getUnit(unitId);
            if (masterDataCache.getUnits().containsKey(unitId) && mappingValid && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()) {
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
                stockOut.setStatus(StockStatus.STOCK_OUT);
                stockOut.setUnitId(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
                stockIn.setStatus(StockStatus.STOCK_IN);
                stockIn.setUnitId(unitId);
                orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
                if (stockOut.getProductIds() != null && !stockOut.getProductIds().isEmpty()) {
                    updateZomatoStock(stockOut);
                }
                if (stockIn.getProductIds() != null && !stockIn.getProductIds().isEmpty()) {
                    updateZomatoStock(stockIn);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshUnitInventory(List<Integer> unitIds,Integer brandId) {
        for (Integer unitId : unitIds) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            partnerOrderService.logStockRefreshEvent(unitId,partnerId,brandId,
                    "REFRESH_START",AppConstants.STATUS_SUCCESSFUL);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                    unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            Unit unit = masterDataCache.getUnit(unitId);
            if (masterDataCache.getUnits().containsKey(unitId) && mappingValid && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.isLive()) {
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
                stockOut.setStatus(StockStatus.STOCK_OUT);
                stockOut.setUnitId(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
                stockIn.setStatus(StockStatus.STOCK_IN);
                stockIn.setUnitId(unitId);
                orderValidationService.refreshLiveUnitInventory(unitId, stockIn, stockOut);
                if (Objects.nonNull(brandId)) {
                    stockOut.setBrandId(brandId);
                    stockIn.setBrandId(brandId);
                }
                if (stockOut.getProductIds() != null && !stockOut.getProductIds().isEmpty()) {
                    updateZomatoStock(stockOut);
                }
                if (stockIn.getProductIds() != null && !stockIn.getProductIds().isEmpty()) {
                    updateZomatoStock(stockIn);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void setUnitDeliveryStatus(List<Integer> unitIds, Boolean status, Integer brandId) throws ChannelPartnerException {
        String unitIdsJson = new Gson().toJson(unitIds);
        LOG.info("Unit delivery status update API call Zomato: {}", unitIdsJson);
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        ZomatoOutletDeliveryStatusRequest request = new ZomatoOutletDeliveryStatusRequest();
        request.setOutletDeliveryStatus(Boolean.TRUE.equals(status) ? 1 : 0);
        if (!Boolean.TRUE.equals(status)) {
            request.setOutletDeliveryStatusUpdateReason("Cafe switched on By Chaayos");
        }
        List<UnitPartnerBrandKey> keys = masterDataCache.getUnitPartnerBrandMappingMetaData().keySet().stream().filter(key ->
                key.getBrandId().equals(brandId) && unitIds.contains(key.getUnitId()) && key.getPartnerId().equals(partnerId))
            .collect(Collectors.toList());
        for (UnitPartnerBrandKey key : keys) {
            String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
            request.setOutletId(restaurantId);
            try {
                ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                    ZomatoServiceEndpoints.UPDATE_DELIVERY_STATUS, HttpMethod.POST, request, ZomatoNotificationResponse.class, key.getBrandId());
                if (response == null || response.getCode() != 200) {
                    slackAndLogNotification("Zomato unit delivery status update API returned incorrect response:" + new Gson().toJson(response)
                        + "\n request: " + new Gson().toJson(request));
                } else {
                    cafeLookUpService.updatePartnerStatusAfterChangingCafeStatus(key.getUnitId(), status, brandId, AppConstants.CHANNEL_PARTNER_ZOMATO);
                    LOG.info("Zomato Status Updated After Changing Cafe Status");
                }
            } catch (HttpStatusCodeException e) {
                catchAndLogZomatoException(e, "Error updating unit delivery status to Zomato:");
            } catch (Exception e) {
                catchAndLogException(e, "Error updating unit delivery status to Zomato");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ZomatoLogisticsStatusResponse getOutletLogisticsStatus(Integer unitId, Integer brandId) throws ChannelPartnerException {
        if (unitId != null && masterDataCache.getUnits().containsKey(unitId)) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            if (mappingValid) {
                try {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
                    String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                    Map<String, String> uriVariables = new HashMap<>();
                    uriVariables.put("outlet_id", restaurantId);
                    return webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.GET_OUTLET_LOGISTICS_STATUS, HttpMethod.GET, uriVariables, ZomatoLogisticsStatusResponse.class, brandId);
                } catch (HttpStatusCodeException e) {
                    catchAndLogZomatoException(e, "Error in getting unit logistics status on ZOMATO::::");
                } catch (Exception e) {
                    catchAndLogException(e, "Error in getting unit logistics status on ZOMATO::::");
                }
            }
            return null;
        } else {
            throw new ChannelPartnerException(ChannelPartnerStringConstants.INVALID_UNIT_ID);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ZomatoDeliveryStatusResponse getOutletDeliveryStatus(Integer unitId, Integer brandId) throws ChannelPartnerException {
        if (unitId != null && masterDataCache.getUnits().containsKey(unitId)) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            if (mappingValid) {
                try {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
                    String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
                    Map<String, String> uriVariables = new HashMap<>();
                    uriVariables.put("outlet_id", restaurantId);
                    return webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.GET_DELIVERY_STATUS, HttpMethod.GET, uriVariables, ZomatoDeliveryStatusResponse.class, brandId);
                } catch (HttpStatusCodeException e) {
                    catchAndLogZomatoException(e, "Error in getting delivery logistics status on ZOMATO::::");
                } catch (Exception e) {
                    catchAndLogException(e, "Error in getting delivery logistics status on ZOMATO::::");
                }
            }
            return null;
        } else {
            throw new ChannelPartnerException(ChannelPartnerStringConstants.INVALID_UNIT_ID);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ZomatoNotificationResponse updateOutletLogisticsStatus(ZomatoLogisticsChangeRequest request) throws ChannelPartnerException {
        if (request == null || request.getOutletId() == null || request.getSelfDeliveryServiceabilityStatus() == null) {
            throw new ChannelPartnerException("Request is not valid.");
        }
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (validatePartnerAndMapping(partnerId, Integer.parseInt(request.getOutletId()))) {
            List<UnitPartnerBrandKey> keys = masterDataCache.getUnitPartnerBrandMappingMetaData().keySet().stream()
                .filter(key -> key.getPartnerId().equals(partnerId) && key.getUnitId().equals(Integer.parseInt(request.getOutletId())))
                .collect(Collectors.toList());
            for (UnitPartnerBrandKey key : keys) {
                request.setOutletId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
                try {
                    return webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.UPDATE_SELF_DELIVERY_SERVICEABILITY, HttpMethod.POST, request, ZomatoNotificationResponse.class, key.getBrandId());
                } catch (HttpStatusCodeException e) {
                    catchAndLogZomatoException(e, "Error in updating unit logistics status on ZOMATO::::");
                } catch (Exception e) {
                    catchAndLogException(e, "Error in updating unit logistics status on ZOMATO::::");
                }
            }
        }
        return null;
    }

    @Override
    public void updateZomatoOutletStatus(Integer brandId, ZomatoDeliveryChangeRequest request) {
        try{
            webServiceHelper.callZomatoApi(environmentProperties,
                    ZomatoServiceEndpoints.UPDATE_DELIVERY_STATUS, HttpMethod.POST, request, ZomatoNotificationResponse.class, brandId);
        }
        catch (HttpStatusCodeException e) {
            catchAndLogZomatoException(e, "Error updating unit delivery status to Zomato:::");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating unit delivery status to Zomato");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ZomatoNotificationResponse updateOutletDeliveryStatus(ZomatoDeliveryChangeRequest request) throws ChannelPartnerException {
        if (request == null || request.getOutletId() == null || request.getOutletDeliveryStatus() == null) {
            throw new ChannelPartnerException("Request is not valid.");
        }
        if (request.getOutletDeliveryStatusUpdateReason() == null) {
            request.setOutletDeliveryStatusUpdateReason("Business requirement");
        }
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (validatePartnerAndMapping(partnerId, Integer.parseInt(request.getOutletId()))) {
            List<UnitPartnerBrandKey> keys = masterDataCache.getUnitPartnerBrandMappingMetaData().keySet().stream().filter(key ->
                    key.getPartnerId().equals(partnerId) && key.getUnitId().equals(Integer.parseInt(request.getOutletId())))
                .collect(Collectors.toList());
            for (UnitPartnerBrandKey key : keys) {
                request.setOutletId(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId());
                try {
                    return webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.UPDATE_DELIVERY_STATUS, HttpMethod.POST, request, ZomatoNotificationResponse.class, key.getBrandId());
                } catch (HttpStatusCodeException e) {
                    catchAndLogZomatoException(e, "Error updating unit delivery status to Zomato:::");
                } catch (Exception e) {
                    catchAndLogException(e, "Error updating unit delivery status to Zomato");
                }
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ZomatoTakeawayStatusResponse getOutletTakeawayStatus(Integer unitId, Integer brandId) throws ChannelPartnerException {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        if (!validatePartnerAndMapping(partnerId, unitId)) {
            throw new ChannelPartnerException(ChannelPartnerStringConstants.INVALID_UNIT_ID);
        }
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        if (brandMappingData == null) {
            throw new ChannelPartnerException("Unit partner brand mapping not found");
        }
        try {
            Map<String, String> uriVariables = new HashMap<>();
            uriVariables.put("outlet_id", brandMappingData.getRestaurantId());
            return webServiceHelper.callZomatoApi(environmentProperties,
                ZomatoServiceEndpoints.GET_TAKEAWAY_STATUS, HttpMethod.GET, uriVariables, ZomatoTakeawayStatusResponse.class, brandId);
        } catch (HttpStatusCodeException e) {
            catchAndLogZomatoException(e, "Error in getting unit takeaway status on ZOMATO:::");
        } catch (Exception e) {
            catchAndLogException(e, "Error in getting unit takeaway status on ZOMATO");
        }
        return null;
    }

    private OrderDeliveryStatusUpdate getOrderDeliveryStatusUpdate(PartnerOrderDetail partnerOrderDetail, ZomatoDeliveryStatusRequest request) {
        Date billingServerTime = AppUtils.parseDate(partnerOrderDetail.getAddTime());
        if(Objects.nonNull(partnerOrderDetail.getKettleOrder())){
            Order order = (Order) partnerOrderDetail.getKettleOrder();
            billingServerTime = order.getBillingServerTime();
        }

        return OrderDeliveryStatusUpdate.builder().orderId(partnerOrderDetail.getKettleOrderId()).unitId(partnerOrderDetail.getUnitId()).
                addTime(ChannelPartnerUtils.getCurrentTimestamp()).status(request.getStatus()).partnerName(partnerOrderDetail.getPartnerName())
                .partnerOrderId(partnerOrderDetail.getPartnerOrderId()).riderContact(Objects.nonNull(request.getZomatoRiderData()) ? request.getZomatoRiderData()
                        .getRiderPhoneNumber() : null).riderName(Objects.nonNull(request.getZomatoRiderData()) ? request.getZomatoRiderData().getRiderName() : null)
                .billingServerTime(billingServerTime).build();
    }

    private void publishToOrderDeliveryStatusQueue(PartnerOrderDetail partnerOrderDetail ,ZomatoDeliveryStatusRequest request) throws JMSException {
        OrderDeliveryStatusUpdate orderDeliveryStatusUpdate = getOrderDeliveryStatusUpdate(partnerOrderDetail, request);
        sqsNotificationService.publishToSQSFifo(environmentProperties.getEnvType().name(),orderDeliveryStatusUpdate,
                    "_PARTNER_ORDER_DELIVERY_STATUS.fifo", Regions.valueOf(environmentProperties.getAwsQueueRegion()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateOutletTakeawayStatus(UnitPartnerStatusVO request) {
        int takeAwayStatus = Boolean.TRUE.equals(request.getStatus()) ? 1 : 0;
        for (Integer unitId : request.getUnitIds()) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, request.getBrandId(), partnerId);
            UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
            if (validatePartnerAndMapping(partnerId, unitId) && brandMappingData != null) {
                ZomatoTakeawayChangeRequest changeRequest = new ZomatoTakeawayChangeRequest();
                changeRequest.setOutletId(brandMappingData.getRestaurantId());
                changeRequest.setOutletTakeawayStatus(takeAwayStatus);
                changeRequest.setStatusUpdateReason("Business requirement");
                try {
                    ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                        ZomatoServiceEndpoints.UPDATE_TAKEAWAY_STATUS, HttpMethod.POST, changeRequest,
                        ZomatoNotificationResponse.class, brandMappingData.getBrandId());
                    slackAndLogNotification("Zomato takeaway status update response:::\n Unit: " +
                        masterDataCache.getUnit(unitId).getName() + "\n Status: " + request.getStatus() +
                        "\n response: " + new Gson().toJson(response));
                } catch (HttpStatusCodeException e) {
                    catchAndLogZomatoException(e, "Error in updating unit takeaway status on ZOMATO:::");
                } catch (Exception e) {
                    catchAndLogException(e, "Zomato takeaway status update error:::\n Unit: " +
                        masterDataCache.getUnit(unitId).getName() + "\n Status: " + request.getStatus());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoOrderDeliveryStatus(ZomatoDeliveryStatusRequest request) {
        List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(request.getOrderId());
        if(Objects.isNull(request.getZomatoRiderData())){
            LOG.info("Recieved Request For Additional Rider Assigned For Zomato Order :::: {} ",new Gson().toJson(request));
            return;
        }
        partnerOrderDetails.forEach(partnerOrderDetail -> {
            PartnerDeliveryStatus partnerDeliveryStatus = new PartnerDeliveryStatus();
            partnerDeliveryStatus.setRiderContact(request.getZomatoRiderData().getRiderPhoneNumber());
            partnerDeliveryStatus.setRiderName(request.getZomatoRiderData().getRiderName());
            partnerDeliveryStatus.setStatus(request.getStatus());
            partnerDeliveryStatus.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
            partnerDeliveryStatus.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
            partnerOrderDetail.getDeliveryStatuses().add(partnerDeliveryStatus);
            trackService.updatePartnerOrder(partnerOrderDetail);
            try {
                    if(Objects.nonNull(partnerOrderDetail.getPartnerOrderStatus()) && !PartnerOrderStatus.CHECKED.
                            equals(partnerOrderDetail.getPartnerOrderStatus())){
                    publishToOrderDeliveryStatusQueue(partnerOrderDetail,request);
                    }
            }catch (Exception e){
                LOG.info("Error While Publishing Partner Delivery Order Picked Up Event for Zomato Order Id :::::: {} , {}",
                        request.getOrderId(),e);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateZomatoOrderStatus(ZomatoOrderStatusRequest request, Integer approvedBy) {
        List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(request.getOrderId());
        partnerOrderDetails.forEach(partnerOrderDetail -> {
            PartnerOrderStatusUpdate status = new PartnerOrderStatusUpdate();
            status.setAction(request.getAction());
            status.setReason(request.getReason());
            status.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
            status.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
            partnerOrderDetail.getPartnerOrderStatusUpdates().add(status);

            partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCEL_REQUESTED,
                "Order cancellation requested by ZOMATO", null));
            partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                PartnerOrderStatus.CANCEL_REQUESTED, false, null));
            if (partnerOrderDetail.getKettleOrder() != null) {
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCEL_REQUESTED);
                if (Objects.isNull(approvedBy)) {
                    orderValidationService.cancelOrder(partnerOrderDetail, false,request.getReason(),
                            ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
                } else {
                    orderValidationService.cancelOrder(partnerOrderDetail, false,request.getReason(), approvedBy);
                }
            } else {
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCELLED);
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(PartnerOrderStatus.CANCEL_REQUESTED,
                    PartnerOrderStatus.CANCELLED, false, null));
                trackService.updatePartnerOrder(partnerOrderDetail);
            }
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
            if(Objects.nonNull(statusLogPrev)){
                partnerOrderServiceImpl.logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.CANCELLED),"Zomato Order Cancelled ",ActionCategory.ZOMATO_ORDER_CANCELLED.name(),statusLogPrev,approvedBy.toString());
            }
        });
    }

    @Override
    public void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        switch (request.getState()) {
            case PREPARED:
                markOrderReady(request, partnerOrderDetail);
                break;
            case ASSIGNED:
                sendAssignedStatus(request, partnerOrderDetail);
                break;
            case PICKEDUP:
                sendPickedUpStatus(request, partnerOrderDetail);
                break;
            case DELIVERED:
                sendDeliveredStatus(request, partnerOrderDetail);
                break;
            default:
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void savePartnerOrderDataMysql(PartnerOrderDetail partnerOrderDetail , Integer kettleOrderId){
        try {
            ZomatoOrderRequestV3 zomatoOrderRequest = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
            PartnerOrderData partnerOrderData = new PartnerOrderData();
            partnerOrderData.setPartnerOrderId(String.valueOf(zomatoOrderRequest.getOrderId()));
            partnerOrderData.setKettleOrderId(kettleOrderId);
            partnerOrderData.setPartnerId(AppConstants.CHANNEL_PARTNER_ZOMATO);
            if(Objects.nonNull(zomatoOrderRequest.getCustomerDetails())){
                partnerOrderData.setCustomerId(zomatoOrderRequest.getCustomerDetails().getCustomerId());
                partnerOrderData.setCustomerName(zomatoOrderRequest.getCustomerDetails().getName());
            }
            partnerOrderData.setNetAmount(BigDecimal.valueOf(zomatoOrderRequest.getNetAmount()));
            partnerOrderData.setGrossAmount(BigDecimal.valueOf(zomatoOrderRequest.getGrossAmount()));
            partnerOrderData.setAmountPaid(BigDecimal.valueOf(zomatoOrderRequest.getAmountPaid()));
            partnerOrderData.setOrderTax(ChannelPartnerUtils.subtract(partnerOrderData.getAmountPaid(),partnerOrderData.getGrossAmount()));
            partnerOrderData.setSaltDiscount(getSaltDiscount(zomatoOrderRequest));
            partnerOrderData.setOrderLevelDiscount(getOrderLevelDiscount(zomatoOrderRequest));
            partnerOrderData.setOrderPackagingCharges(getPackagingCharges(zomatoOrderRequest));
            partnerOrderData.setIsPriortizedOrder(ChannelPartnerUtils.isPriortizedOrder(getTagNames(zomatoOrderRequest.getZomatoOrderTag()),
                    environmentProperties.getZomatoExpressIndentifier()));
            partnerOrderDataDao.add(partnerOrderData);
        }catch (Exception e){
            LOG.error("Error While Saving partner Order Data in Mysql");
        }

    }

    private List<String> getTagNames(List<ZomatoOrderTag> zomatoOrderTagList){
        List<String> tagNames = new ArrayList<>();
        if(!CollectionUtils.isEmpty(zomatoOrderTagList)){
            tagNames =  zomatoOrderTagList.stream().map(ZomatoOrderTag::getTagType).collect(Collectors.toList());
        }
        return tagNames;
    }

    private BigDecimal getSaltDiscount(ZomatoOrderRequestV3 zomatoOrderRequest){
        BigDecimal totalSaltDiscount = BigDecimal.ZERO;
        for(ZomatoOrderDishesV3 dish : zomatoOrderRequest.getDishes()){
            if(!CollectionUtils.isEmpty(dish.getDishDiscounts())){
                for(ZomatoOrderDishDiscountV3 dishDiscount : dish.getDishDiscounts()){
                    totalSaltDiscount = ChannelPartnerUtils.add(totalSaltDiscount, BigDecimal.valueOf(dishDiscount.getDiscountValue()));
                }
            }
        }
        return totalSaltDiscount;
    }

    private BigDecimal getOrderLevelDiscount(ZomatoOrderRequestV3 zomatoOrderRequest){
        BigDecimal totalOrderLevelDiscount = BigDecimal.ZERO;
        for(ZomatoOrderDiscountV3 orderDiscount : zomatoOrderRequest.getOrderDiscounts()){
            if(orderDiscount.getIsZomatoDiscount().equals(0)){
                totalOrderLevelDiscount = ChannelPartnerUtils.add(totalOrderLevelDiscount, BigDecimal.valueOf(orderDiscount.getDiscountAmount()));
            }
        }
        return totalOrderLevelDiscount;
    }

    private BigDecimal getPackagingCharges(ZomatoOrderRequestV3 zomatoOrderRequestV3){
        BigDecimal totalPackagingCharges =  BigDecimal.ZERO;
        for(ZomatoOrderDishesV3 dish : zomatoOrderRequestV3.getDishes()){
            if(!CollectionUtils.isEmpty(dish.getItemCharges())){
                for (ZomatoOrderDishChargesV3 dishCharge : dish.getItemCharges()){
                    totalPackagingCharges = ChannelPartnerUtils.add(totalPackagingCharges, BigDecimal.valueOf(dishCharge.getChargeAmount()));
                }
            }
        }
        return totalPackagingCharges;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Stopwatch watch = Stopwatch.createUnstarted();
        orderValidationService.tagOrder(partnerOrderDetail, order);
        if (skipInventoryCheck || (partnerOrderDetail.getToBeProcessed() != null && partnerOrderDetail.getToBeProcessed())) {
            //Sending order to KETTLE
            Map<String, Object> uriVariables = new HashMap<>();
            uriVariables.put("addMetadata", true);
            boolean logFailure = false;
            try {
                order = webServiceHelper.callInternalApi(environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.CREATE_ORDER,
                    environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Order.class, order, uriVariables);
                if(order!=null){
                    watch.start();
                    commissionService.commissionCalculation(order,partnerOrderDetail);
                    LOG.info("{},ZOMATO STEP ,Commission Calculation",
                            watch.stop().elapsed(TimeUnit.MILLISECONDS));
                }
                if (order != null) {
                    partnerOrderDetail.setKettleOrder(order);
                    partnerOrderDetail.setKettleOrderId(order.getOrderId().toString());
                    partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                        PartnerOrderStatus.PLACED, isManual, currentTime));
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.PLACED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.PLACED, "Order placed!", null));
                    savePartnerOrderDataMysql(partnerOrderDetail,order.getOrderId());
                    partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                } else {
                    logFailure = true;
                }
            } catch (Exception e) {
                LOG.error("Zomato order exception {}", partnerOrderDetail.getPartnerOrderId(), e);
                logFailure = true;
            }
            if (logFailure) {
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderDetail.getPartnerOrderStatus(),
                    PartnerOrderStatus.FAILED, isManual, currentTime));
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.FAILED);
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.FAILED, "Order failed to punch!", null));
                partnerOrderDetail.setBeingProcessed(false);
                trackService.updatePartnerOrder(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
                partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
            }
        } else {
            // send slack notification here
            partnerOrderService.sendOrderNotPunchedNotification(partnerOrderDetail);
            partnerOrderService.sendOrderNotPunchedNotificationToKnock(partnerOrderDetail);
            partnerOrderDetail.setBeingProcessed(false);
            trackService.updatePartnerOrder(partnerOrderDetail);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual) {
        ZomatoNotificationRequest zomatoNotificationRequest = new ZomatoNotificationRequest();
        zomatoNotificationRequest.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
        zomatoNotificationRequest.setOrderId(partnerOrderDetail.getPartnerOrderId());
        zomatoNotificationRequest.setDeliveryTime(45);
        zomatoNotificationRequest.setPrepTime(15);
//       Dont  Run order confirmation checks only in Testing env
        if(AppUtils.isProd(environmentProperties.getEnvType())|| !environmentProperties.isTestingModeEnabled()){
            confirmZomatoOrder(zomatoNotificationRequest, isManual);
        }
    }

    /*private PartnerOrderDetail receiveOrder(ZomatoOrderRequest request, boolean isManual, ZomatoOrderResponse response) {
        //Log order data
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME);
        PartnerOrderDetail partnerOrderDetail = trackService.trackPartnerOrder(request.getOrderId().toString(), partner.getPartnerId(),
                partner.getPartnerName(), partner.getPartnerCode(), request, null, isManual, "V2");
        if (partnerOrderDetail != null) {
            //Primary validations like unit and item
            partnerOrderDetail = primaryChecks(request, partnerOrderDetail, response, partner.getKettlePartnerId());
        }
        partnerOrderDetail = enrichPartnerOrderDetail(partner, partnerOrderDetail, request.getOutletId());
        return partnerOrderDetail;
    }*/

    private PartnerOrderDetail receiveOrderV3(ZomatoOrderRequestV3 request, boolean isManual, ZomatoOrderResponse response) {
        //Log order data
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME);
        PartnerOrderDetail partnerOrderDetail = trackService.trackPartnerOrder(request.getOrderId().toString(), partner.getPartnerId(),
            partner.getPartnerName(), partner.getPartnerCode(), request, null, isManual,
            "V3");
        if (partnerOrderDetail != null) {
            //Primary validations like unit and item
            partnerOrderDetail = primaryChecksV3(request, partnerOrderDetail, response, partner.getKettlePartnerId());
        }
        partnerOrderDetail = enrichPartnerOrderDetail(partner, partnerOrderDetail, request.getOutletId());
        return partnerOrderDetail;
    }

    private PartnerOrderDetail enrichPartnerOrderDetail(PartnerDetail partner, PartnerOrderDetail partnerOrderDetail, String outletId) {
        if (partnerOrderDetail != null) {
            UnitPartnerBrandMappingData data = getUnitPartnerBrandMappingData(outletId, partner.getKettlePartnerId());
            partnerOrderDetail.setRestaurantId(data.getRestaurantId());
            partnerOrderDetail.setUnitId(data.getUnitId());
            partnerOrderDetail.setBrandId(data.getBrandId());
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        }
        return partnerOrderDetail;
    }

    /*private PartnerOrderDetail primaryChecks(ZomatoOrderRequest request, PartnerOrderDetail partnerOrderDetail, ZomatoOrderResponse response, Integer kettlePartnerId) {
        try {
            if (request.getOutletId() == null) {
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_MISSING, "Unit Id is missing in the order.");
                response.setMessage(ChannelPartnerStringConstants.MISSING_OUTLET_ID);
            } else {
                UnitPartnerBrandMappingData data = getUnitPartnerBrandMappingData(request.getOutletId(), kettlePartnerId);
                Integer unitId = data.getUnitId();
                if (masterDataCache.getUnit(unitId) == null) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not valid.");
                    response.setMessage("Outlet id not valid");
                }
                if (masterDataCache.getUnit(unitId).getStatus().equals(UnitStatus.IN_ACTIVE)) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not active.");
                    response.setMessage("Outlet is not active");
                }
                if (!masterDataCache.getUnit(unitId).isLive()) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not live.");
                    response.setMessage("Outlet is not live");
                }
                Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
                boolean mappingValid = masterDataCache.getUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                        unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
                if (!mappingValid) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not mapped.");
                    response.setMessage("Outlet is not mapped");
                }
                if (request.getOrderItems() == null || request.getOrderItems().isEmpty()) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.EMPTY_ORDER, "No items in the order.");
                    response.setMessage("No items in order");
                }
                if (partnerOrderDetail.getOrderErrors().isEmpty() && ZomatoOrderType.DELIVERY.equals(request.getOrderType())) {
                    updateDeliveryDetails(request, partnerOrderDetail);
                }
            }
            if (!partnerOrderDetail.getOrderErrors().isEmpty()) {
                partnerOrderDetail.setToBeProcessed(false);
                response.setCode(500);
                response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                response.setStatus(ZomatoOrderStatus.FAILED);
            } else {
                partnerOrderDetail.setToBeProcessed(true);
            }
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        }
        return partnerOrderDetail;
    }*/

    private PartnerOrderDetail primaryChecksV3(ZomatoOrderRequestV3 request, PartnerOrderDetail partnerOrderDetail,
                                               ZomatoOrderResponse response, Integer kettlePartnerId) {
        try {
            if (request.getOutletId() == null) {
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_MISSING, "Unit Id is missing in the order.");
                response.setMessage(ChannelPartnerStringConstants.MISSING_OUTLET_ID);
            } else {
                validateUnit(request, partnerOrderDetail, response, kettlePartnerId);
                if (request.getDishes() == null || request.getDishes().isEmpty()) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.EMPTY_ORDER, "No items in the order.");
                    response.setMessage("No items in order");
                }
                if (partnerOrderDetail.getOrderErrors().isEmpty() && ZomatoOrderType.DELIVERY.equals(request.getOrderType())) {
                    updateDeliveryDetailsV3(request, partnerOrderDetail);
                }
            }
            if (!partnerOrderDetail.getOrderErrors().isEmpty()) {
                partnerOrderDetail.setToBeProcessed(false);
                partnerOrderDetail.setToBeRejected(true);
                response.setCode(500);
                response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
                response.setStatus(ZomatoOrderStatus.FAILED);
            } else {
            	partnerOrderDetail.setToBeProcessed(true);
            	partnerOrderDetail.setToBeRejected(false);
            }
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
        }
        return trackService.updatePartnerOrder(partnerOrderDetail);
    }

    private void validateUnit(ZomatoOrderRequestV3 request, PartnerOrderDetail partnerOrderDetail,
                              ZomatoOrderResponse response, Integer kettlePartnerId) {
        UnitPartnerBrandMappingData data = getUnitPartnerBrandMappingData(request.getOutletId(), kettlePartnerId);
        Integer unitId = data.getUnitId();
        if (masterDataCache.getUnit(unitId) == null) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not valid.");
            response.setMessage("Outlet id not valid");
        }
        if (masterDataCache.getUnit(unitId).getStatus().equals(UnitStatus.IN_ACTIVE)) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not active.");
            response.setMessage("Outlet is not active");
        }
        if (!masterDataCache.getUnit(unitId).isLive()) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not live.");
            response.setMessage("Outlet is not live");
        }
        if (!validatePartnerAndMapping(kettlePartnerId, unitId)) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not mapped.");
            response.setMessage("Outlet is not mapped");
        }
    }


    private Order convertOrder(PartnerOrderDetail partnerOrderDetail) {
        ZomatoOrderRequest request = (ZomatoOrderRequest) partnerOrderDetail.getPartnerOrder();
        Customer customer = getCustomer(request.getCustomerDetails(), partnerOrderDetail);
        Address selectedAddress = new Address();
        if (ZomatoOrderType.DELIVERY.equals(request.getOrderType())) {
            if (request.getCustomerDetails().getState() != null) {
                List<Address> addresses = customer.getAddresses().stream().filter(address ->
                    address.getLine1().equalsIgnoreCase(request.getCustomerDetails().getAddress().trim()) &&
                        address.getCity().equalsIgnoreCase(request.getCustomerDetails().getCity().trim()) &&
                        address.getState().equalsIgnoreCase(request.getCustomerDetails().getState().trim()) &&
                        address.getCountry().equalsIgnoreCase(request.getCustomerDetails().getCountry().trim())).collect(Collectors.toList());
                if (!addresses.isEmpty()) {
                    selectedAddress = addresses.get(0);
                }
            } else {
                List<Address> addresses = customer.getAddresses().stream().filter(address ->
                    address.getLine1().equalsIgnoreCase(request.getCustomerDetails().getAddress().trim()) &&
                        address.getCity().equalsIgnoreCase(request.getCustomerDetails().getCity().trim()) &&
                        address.getCountry().equalsIgnoreCase(request.getCustomerDetails().getCountry().trim())).collect(Collectors.toList());
                if (!addresses.isEmpty()) {
                    selectedAddress = addresses.get(0);
                }
            }
            selectedAddress.setId(addCustomerAddress(customer, request, partnerOrderDetail.getUnitId()));
        }
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId();
        RestaurantPartnerKey key = new RestaurantPartnerKey(request.getOutletId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
        Order order = ZomatoConverters.convertOrder(request, customer, selectedAddress, masterDataCache, data);
        Map<String, TaxDataVO> taxMap = orderValidationService.getUnitProductTaxCodeMap(data).getTaxMap();
        Map<Integer, Product> products = new HashMap<>();
        masterDataCache.getUnitProductDetails(data.getPriceProfileUnitId()).forEach(product ->
            products.put(product.getId(), product)
        );
        ZomatoConverters.addItemsToOrder(order, request, masterDataCache, partnerOrderDetail, taxMap, products, data.getPriceProfileUnitId());
        ZomatoConverters.setTransactionDetail(order, request, masterDataCache);
        return order;
    }

    private Customer setKettleCustomerId(ZomatoOrderRequestV3 zomatoOrderRequest){
        try {
            String kettleCustomerContact = null;
            if(Objects.nonNull(zomatoOrderRequest.getCustomerDetails()) && Objects.nonNull(zomatoOrderRequest.getCustomerDetails().getCustomerId())){
                kettleCustomerContact =  partnerOrderDataDao.getKettleCustomerContact(zomatoOrderRequest.getCustomerDetails().getCustomerId());
            }
            if(Objects.nonNull(kettleCustomerContact)){
                CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
                codCustomerLoginData.setContactNumber(kettleCustomerContact);
                CODCustomerLoginData kettleCustomer = webServiceHelper.postWithAuth(environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP,
                        environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
                        CODCustomerLoginData.class);
                if(Objects.nonNull(kettleCustomer) && Objects.nonNull(kettleCustomer.getCustomer())){
                    return kettleCustomer.getCustomer();
                }
            }
        }catch (Exception e){
            LOG.error("Error While Setting Kettle Customer Id For Zomato Order ::::: {} ", zomatoOrderRequest.getOrderId());
        }
        return null;

    }

    private Order convertOrderV3(PartnerOrderDetail partnerOrderDetail, boolean isManual) {
        ZomatoOrderRequestV3 request = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
        if (isManual) {
            updateMissingData(partnerOrderDetail, request);
        }
        Customer customer = new Customer();
        Address selectedAddress = null;

        // this flow works only  if zomato shares the customer contact number

        if(!environmentProperties.isDefaultZomatoCustomerFlow()){
           customer= setCustomerAndAddressDetails(customer,request,partnerOrderDetail,selectedAddress);
        }else {
            customer =  setKettleCustomerId(request);
            if(Objects.isNull(customer)){
                customer = getZomatoCustomer();
            }
        }
        /*if (ZomatoOrderType.DELIVERY.equals(request.getOrderType()) && Objects.nonNull(request.getCustomerDetails().getAddress()) &&
                request.getCustomerDetails().getAddress().length() > 0) {
            for (Address address : customer.getAddresses()) {
                if (address.getLine1().equalsIgnoreCase(request.getCustomerDetails().getAddress().trim()) &&
                    address.getCity().equalsIgnoreCase(request.getCustomerDetails().getCity().trim()) &&
                    address.getCountry().equalsIgnoreCase(request.getCustomerDetails().getCountry().trim())) {
                    selectedAddress = address;
                }
            }
            if (selectedAddress == null) {
                selectedAddress = new Address();
                selectedAddress.setId(addCustomerAddressV3(customer, request, partnerOrderDetail.getUnitId()));
            }
        }*/
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId();
        RestaurantPartnerKey key = new RestaurantPartnerKey(request.getOutletId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
        Order order = ZomatoConvertersV3.convertOrderV3(request, customer, selectedAddress, masterDataCache, data, environmentProperties);
        Map<String, TaxDataVO> taxMap = orderValidationService.getUnitProductTaxCodeMap(data).getTaxMap();
        Map<Integer, Product> products = new HashMap<>();
        masterDataCache.getUnitProductDetails(data.getPriceProfileUnitId()).forEach(product ->
            products.put(product.getId(), product)
        );
        Map<Integer, Product> cafeProducts = new HashMap<>();
        masterDataCache.getUnitProductDetails(data.getUnitId()).forEach(product ->
                cafeProducts.put(product.getId(), product)
        );
        Map<Integer, Map<String, BigDecimal>> pricingMap = channelPartnerDataCache.getPartnerUnitProductPricing(new UnitPartnerBrandKey(data.getUnitId(),
            data.getBrandId(), data.getPartnerId()));
        ZomatoConvertersV3.addItemsToOrderV3(order, request, masterDataCache, partnerOrderDetail, taxMap, products, environmentProperties,
            data.getPriceProfileUnitId(), pricingMap, cafeProducts,channelPartnerDataCache);
        ZomatoConvertersV3.setTransactionDetailV3(order, request, masterDataCache);
        return order;
    }

    private Customer setCustomerAndAddressDetails(Customer customer, ZomatoOrderRequestV3 request, PartnerOrderDetail partnerOrderDetail, Address selectedAddress) {
        customer = getCustomerV3(request.getCustomerDetails(), partnerOrderDetail);
        if (ZomatoOrderType.DELIVERY.equals(request.getOrderType()) && Objects.nonNull(request.getCustomerDetails().getAddress()) &&
                request.getCustomerDetails().getAddress().length() > 0) {
            for (Address address : customer.getAddresses()) {
                if (address.getLine1().equalsIgnoreCase(request.getCustomerDetails().getAddress().trim()) &&
                    address.getCity().equalsIgnoreCase(request.getCustomerDetails().getCity().trim()) &&
                    address.getCountry().equalsIgnoreCase(request.getCustomerDetails().getCountry().trim())) {
                    selectedAddress = address;
                }
            }
            if (selectedAddress == null) {
                selectedAddress = new Address();
                selectedAddress.setId(addCustomerAddressV3(customer, request, partnerOrderDetail.getUnitId()));
            }
        }
        return customer;
    }

    private Customer getZomatoCustomer() {
        if (channelPartnerDataCache.getZomatoCustomer() != null
                && channelPartnerDataCache.getZomatoCustomer().getId() != 0) {
            return channelPartnerDataCache.getZomatoCustomer();
        } else {
            CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
            codCustomerLoginData.setContactNumber(environmentProperties.getZomatoCustomerContact());
            CODCustomerLoginData customer = webServiceHelper.postWithAuth(environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP,
                    environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
                    CODCustomerLoginData.class);
            channelPartnerDataCache.setZomatoCustomer(customer.getCustomer());
            return customer.getCustomer();
        }
    }

    private void updateMissingData(PartnerOrderDetail partnerOrderDetail, ZomatoOrderRequestV3 request) {
        PartnerDetail partner = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME);
        if (partnerOrderDetail != null) {
            //Primary validations like unit and item
            ZomatoOrderResponse response = new ZomatoOrderResponse();
            partnerOrderDetail.getOrderErrors().clear();
            partnerOrderDetail = primaryChecksV3(request, partnerOrderDetail, response, partner.getKettlePartnerId());
        }
        boolean tobeProcessed = partnerOrderDetail != null && Boolean.TRUE.equals(partnerOrderDetail.getToBeProcessed());
        if (tobeProcessed && (partnerOrderDetail.getUnitId() == null || partnerOrderDetail.getBrandId() == null)) {
            enrichPartnerOrderDetail(partner, partnerOrderDetail, request.getOutletId());
        }
    }

    private void checkOrder(PartnerOrderDetail partnerOrderDetail, Order order, Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(partnerOrderDetail.getUnitId(), partnerOrderDetail.getBrandId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        orderValidationService.runCommonValidations(order, partnerOrderDetail, partnerProductTaxMap, data, masterDataCache.getUnit(key.getUnitId()),null);
        //Validate transaction data
        checkTransactionalData(order, partnerOrderDetail);
        orderValidationService.updateCheckOrderStatus(partnerOrderDetail, isManual);
    }

	private void checkOrderV3(PartnerOrderDetail partnerOrderDetail, Order order,
			Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual) {
		Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
		UnitPartnerBrandKey key = new UnitPartnerBrandKey(partnerOrderDetail.getUnitId(),
				partnerOrderDetail.getBrandId(), partnerId);
		UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
		boolean inventoryCheckPasses = orderValidationService.runCommonValidations(order, partnerOrderDetail,
				partnerProductTaxMap, data, masterDataCache.getUnit(key.getUnitId()),null);
		LOG.info("Status of Inventory check {} for partner {} for partnerOrder Id {} ", inventoryCheckPasses,
				partnerOrderDetail.getPartnerName(), partnerOrderDetail.getPartnerOrderId());
		// Validate transaction data
		checkTransactionalDataV3(order, partnerOrderDetail);
		orderValidationService.updateCheckOrderStatus(partnerOrderDetail, isManual);
	}

    private void setOrderProcessingThread(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck) {
        ZomatoOrderTask zomatoOrderTask = new ZomatoOrderTask();
        zomatoOrderTask.setZomatoService(this);
        zomatoOrderTask.setManual(isManual);
        zomatoOrderTask.setOrder(order);
        zomatoOrderTask.setPartnerOrderDetail(partnerOrderDetail);
        zomatoOrderTask.setSkipInventoryCheck(skipInventoryCheck);
        zomatoOrderTask.setRequestId(MDC.get("request.id"));
        threadPoolTaskExecutor.execute(zomatoOrderTask);
    }

    private void checkTransactionalData(Order order, PartnerOrderDetail partnerOrderDetail) {
        TransactionDetail td1 = order.getTransactionDetail();
        ZomatoOrderRequest zomatoOrderRequest = (ZomatoOrderRequest) partnerOrderDetail.getPartnerOrder();
        partnerOrderDetail.setBillDifference(BigDecimal.ZERO);
        partnerOrderDetail.setBillPercentageDifference(BigDecimal.ZERO);
        BigDecimal paidAmount = BigDecimal.valueOf(zomatoOrderRequest.getTotalMerchant());
        if (!orderValidationService.isValidData(td1.getCollectionAmount(), paidAmount)) {
            orderValidationService.checkPaidAmount(order, partnerOrderDetail, td1, paidAmount);
        }
        BigDecimal taxableAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (ZomatoOrderItem item : zomatoOrderRequest.getOrderItems()) {
            BigDecimal total = BigDecimal.valueOf((item.getItemUnitPrice() * item.getItemQuantity()));
            taxableAmount = taxableAmount.add(total);
            for (ZomatoItemDiscount zomatoItemDiscount : item.getItemDiscounts()) {
                taxableAmount = taxableAmount.subtract(BigDecimal.valueOf(zomatoItemDiscount.getDiscountValue()));
            }
            totalAmount = totalAmount.add(total);
        }
        if (zomatoOrderRequest.getOrderAdditionalCharges() != null) {
            for (ZomatoChargeDetails zomatoChargeDetails : zomatoOrderRequest.getOrderAdditionalCharges()) {
                taxableAmount = taxableAmount.add(BigDecimal.valueOf(zomatoChargeDetails.getChargeValue()));
                totalAmount = totalAmount.add(BigDecimal.valueOf(zomatoChargeDetails.getChargeAmount() + zomatoChargeDetails.getChargeTaxesTotal()));
            }
        }
        if (!orderValidationService.isValidData(td1.getTaxableAmount(), taxableAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TAXABL", null, td1.getTaxableAmount().floatValue(),
                taxableAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                "Taxable Amount our/partner " + td1.getTaxableAmount() + "/" + taxableAmount);
        }
        if (!orderValidationService.isValidData(td1.getTotalAmount(), totalAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL", null, td1.getTotalAmount().floatValue(), totalAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                "Total Amount our/partner " + td1.getTotalAmount() + "/" + totalAmount);
        }
        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (ZomatoOrderDiscount zomatoOrderDiscount : zomatoOrderRequest.getOrderDiscounts()) {
            totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoOrderDiscount.getDiscountAmount()));
        }
        if (!orderValidationService.isValidData(td1.getDiscountDetail().getTotalDiscount(), totalDiscount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL_DISC", null, td1.getDiscountDetail().getTotalDiscount().floatValue(),
                totalDiscount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH, "Total Discount our/partner " +
                td1.getDiscountDetail().getTotalDiscount() + "/" + totalDiscount);
        }
    }

    private void checkTransactionalDataV3(Order order, PartnerOrderDetail partnerOrderDetail) {
        TransactionDetail td1 = order.getTransactionDetail();
        ZomatoOrderRequestV3 zomatoOrderRequest = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
        partnerOrderDetail.setBillDifference(BigDecimal.ZERO);
        partnerOrderDetail.setBillPercentageDifference(BigDecimal.ZERO);
        BigDecimal paidAmount = BigDecimal.valueOf(zomatoOrderRequest.getTotalMerchant());
        BigDecimal ourPaidAmount=BigDecimal.ZERO;
        if(td1.getRoundOffValue().negate().compareTo(BigDecimal.ZERO)>0){
             ourPaidAmount = ChannelPartnerUtils.add(td1.getPaidAmount(), td1.getRoundOffValue());
        }
        else{
             ourPaidAmount = ChannelPartnerUtils.subtract(td1.getPaidAmount(), td1.getRoundOffValue());
        }
        BigDecimal taxableAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal taxPaidByPartner = BigDecimal.ZERO;
        for (ZomatoOrderDishesV3 item : zomatoOrderRequest.getDishes()) {
            BigDecimal total = BigDecimal.valueOf((item.getTotalCost()));
            taxableAmount = taxableAmount.add(total);
            if(item.getTotalDiscount()>0){
                taxableAmount=taxableAmount.subtract(BigDecimal.valueOf(item.getTotalDiscount()));
            }
            for (ZomatoOrderDishDiscountV3 zomatoItemDiscount : item.getDishDiscounts()) { //dish discounts
                taxableAmount = taxableAmount.subtract(BigDecimal.valueOf(zomatoItemDiscount.getDiscountValue()));
            }
            for (ZomatoOrderDishChargesV3 zomatoOrderDishChargesV3 : item.getItemCharges()) {
                taxableAmount = taxableAmount.add(BigDecimal.valueOf(zomatoOrderDishChargesV3.getChargeAmount()));
                total = total.add(BigDecimal.valueOf(zomatoOrderDishChargesV3.getChargeAmount()));
                taxPaidByPartner = addTaxPaidByPartner(zomatoOrderDishChargesV3.getChargeTaxes(), taxPaidByPartner);
            }
            taxPaidByPartner = addTaxPaidByPartner(item.getTaxes(), taxPaidByPartner);
            totalAmount = totalAmount.add(total);
        }
        for (ZomatoOrderChargesV3 zomatoChargeDetails : zomatoOrderRequest.getOrderAdditionalCharges()) {
            taxableAmount = taxableAmount.add(BigDecimal.valueOf(zomatoChargeDetails.getChargeAmount()));
            totalAmount = totalAmount.add(BigDecimal.valueOf(zomatoChargeDetails.getChargeAmount()));
            setMissingTaxTypeInCharges(zomatoChargeDetails);
            if(!CollectionUtils.isEmpty(zomatoChargeDetails.getChargeTaxes())){
                taxPaidByPartner = addTaxPaidByPartner(zomatoChargeDetails.getChargeTaxes(), taxPaidByPartner);
            }
        }
        ourPaidAmount = ChannelPartnerUtils.subtract(ourPaidAmount, taxPaidByPartner);
        if (!orderValidationService.isValidData(td1.getCollectionAmount(), paidAmount)) {
             orderValidationService.checkPaidAmount(order, partnerOrderDetail, td1, paidAmount);
        }
        if (!orderValidationService.isValidData(td1.getTaxableAmount(), taxableAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TAXABL", null, td1.getTaxableAmount().floatValue(),
                taxableAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                "Taxable Amount our/partner " + td1.getTaxableAmount() + "/" + taxableAmount);
        }
        if (!orderValidationService.isValidData(td1.getTotalAmount(), totalAmount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL", null, td1.getTotalAmount().floatValue(), totalAmount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH,
                "Total Amount our/partner " + td1.getTotalAmount() + "/" + totalAmount);
        }
        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (ZomatoOrderDiscountV3 zomatoOrderDiscount : zomatoOrderRequest.getOrderDiscounts()) {
            if(zomatoOrderDiscount.getIsZomatoDiscount()==0){
                totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoOrderDiscount.getDiscountAmount()));
            }
        }
        for(ZomatoOrderDishesV3 dish : zomatoOrderRequest.getDishes()){
            if(Objects.nonNull(dish.getDishDiscounts()) && !dish.getDishDiscounts().isEmpty()){
                for(ZomatoOrderDishDiscountV3 dishDiscountV3 :dish.getDishDiscounts()){
                    totalDiscount = AppUtils.add(totalDiscount , BigDecimal.valueOf(dishDiscountV3.getDiscountValue()));
                }
            }
        }
        if (!orderValidationService.isValidData(td1.getDiscountDetail().getTotalDiscount(), totalDiscount)) {
            orderValidationService.logMetadataDifference(order, "CART", "TOTAL_DISC", null, td1.getDiscountDetail().getTotalDiscount().floatValue(),
                totalDiscount.floatValue());
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.TRANSACTION_MISMATCH, "Total Discount our/partner " +
                td1.getDiscountDetail().getTotalDiscount() + "/" + totalDiscount);
        }
    }

    private void setMissingTaxTypeInCharges(ZomatoOrderChargesV3 zomatoChargeDetails) {
        if(!CollectionUtils.isEmpty(zomatoChargeDetails.getChargeTaxes())){
            zomatoChargeDetails.getChargeTaxes().forEach(zomatoOrderTaxDetailsV3 -> {
                if (zomatoOrderTaxDetailsV3.getType() == null) {
                    zomatoOrderTaxDetailsV3.setType(zomatoChargeDetails.getChargeId().equalsIgnoreCase(String.valueOf(ChannelPartnerServiceConstants.DELIVERY_PRODUCT_ID)) ?
                            TaxPayingEntity.PARTNER.getZomato() : TaxPayingEntity.SELF.getZomato());
                }
            });
        }
    }

    private BigDecimal addTaxPaidByPartner(List<ZomatoOrderTaxDetailsV3> taxes, BigDecimal taxPaidByPartner) {
        for (ZomatoOrderTaxDetailsV3 zomatoOrderTaxDetailsV3 : taxes) {
            if (TaxPayingEntity.PARTNER.getZomato().equalsIgnoreCase(zomatoOrderTaxDetailsV3.getType())) {
                taxPaidByPartner = ChannelPartnerUtils.add(taxPaidByPartner, BigDecimal.valueOf(zomatoOrderTaxDetailsV3.getTaxAmount()));
            }
        }
        return taxPaidByPartner;
    }

    private Customer getCustomer(CustomerDetails customerDetails, PartnerOrderDetail partnerOrderDetail) {
        CODCustomerLoginData data = new CODCustomerLoginData();
        String contact = customerDetails.getPhoneNumber();
        if (contact.matches("^0[0-9]*")) {
            contact = contact.substring(1);
        }
        data.setContactNumber(contact);
        Customer customer = new Customer();
        boolean addEmail = true;
        for (String domain : environmentProperties.getEmailFilterDomains().split(",")) {
            if (addEmail && customerDetails.getEmail().contains(domain)) {
                addEmail = false;
            }
        }
        if (addEmail) {
            customer.setEmailId(customerDetails.getEmail());
        }
        String name = customerDetails.getName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        customer.setFirstName(name);
        customer.setContactNumber(contact);
        data.setCustomer(customer);
        UserSessionDetail userSessionDetail = new UserSessionDetail();
        userSessionDetail.setUnitId(partnerOrderDetail.getUnitId());
        userSessionDetail.setUserId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID); //for user id system
        userSessionDetail.setBusinessDate(ChannelPartnerUtils.getBusinessDate());
        data.setSession(userSessionDetail);
        data.setAcquisitionBrandId(partnerOrderDetail.getBrandId());
        data.setChaayosCustomer(partnerOrderDetail.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)); //brandId 1 is for Chaayos
        String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP_UPDATE;
        data = webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), data,
            CODCustomerLoginData.class);
        return data.getCustomer();
    }

    private Customer getCustomerV3(ZomatoOrderCustomerDetailsV3 customerDetails, PartnerOrderDetail partnerOrderDetail) {
        CODCustomerLoginData data = new CODCustomerLoginData();
        String contact = customerDetails.getPhoneNumber();
        if (contact.matches("^0[0-9]*")) {
            contact = contact.substring(1);
        }
        data.setContactNumber(contact);
        Customer customer = new Customer();
        boolean addEmail = true;
        for (String domain : environmentProperties.getEmailFilterDomains().split(",")) {
            if (addEmail && customerDetails.getEmail().contains(domain)) {
                addEmail = false;
            }
        }
        if (addEmail) {
            customer.setEmailId(customerDetails.getEmail());
        }
        String name = customerDetails.getName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        customer.setFirstName(name);
        customer.setContactNumber(contact);
        data.setCustomer(customer);
        UserSessionDetail userSessionDetail = new UserSessionDetail();
        userSessionDetail.setUnitId(partnerOrderDetail.getUnitId());
        userSessionDetail.setUserId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID); //for user id system
        userSessionDetail.setBusinessDate(ChannelPartnerUtils.getBusinessDate());
        data.setSession(userSessionDetail);
        data.setAcquisitionBrandId(partnerOrderDetail.getBrandId());
        data.setChaayosCustomer(partnerOrderDetail.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)); //brandId 1 is for Chaayos
        String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_CUSTOMER_LOOKUP_UPDATE;
        data = webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), data,
            CODCustomerLoginData.class);
        return data.getCustomer();
    }

    private Integer addCustomerAddress(Customer customer, ZomatoOrderRequest request, Integer unitId) {
        CustomerDetails customerDetails = request.getCustomerDetails();
        CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
        codCustomerLoginData.setContactNumber(customer.getContactNumber());
        codCustomerLoginData.setCustomer(customer);
        Address address = new Address();
        address.setName(request.getCustomerDetails().getName());
        if (request.getCustomerDetails().getName() != null && request.getCustomerDetails().getName().length() > 50) {
            address.setName(request.getCustomerDetails().getName().substring(0, 50));
        }
        address.setName(request.getCustomerDetails().getName());
        address.setAddressType("HOME");
        address.setCountry(customerDetails.getCountry());
        String line1 = customerDetails.getAddress();
        if (customerDetails.getAddress() != null && customerDetails.getAddress().length() > 255) {
            line1 = customerDetails.getAddress().substring(0, 255);
        }
        address.setLine1(line1);
        String locality = customerDetails.getDeliveryArea();
        if (customerDetails.getDeliveryArea() != null && customerDetails.getDeliveryArea().length() > 80) {
            locality = customerDetails.getDeliveryArea().substring(0, 80);
        }
        address.setLocality(locality);
        Location location = null;
        for (Location loc : masterDataCache.getAllLocations().values()) {
            if (loc.getName().equalsIgnoreCase(customerDetails.getCity())) {
                location = loc;
            }
        }
        if (location == null) {
            location = masterDataCache.getAllLocations().get(masterDataCache.getUnitBasicDetail(unitId).getLocationCode());
        }
        address.setCity(location.getName());
        address.setState(location.getState().getName());
        address.setLatitude(customerDetails.getDeliveryAreaLatitude().toString());
        address.setLongitude(customerDetails.getDeliveryAreaLongitude().toString());
        address.setLandmark(customerDetails.getAddressInstructions());
        address.setZipCode(customerDetails.getPincode());
        codCustomerLoginData.setNewAddress(address);
        String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_ADD_ADDRESS;
        return webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
            Integer.class);
    }

    private Integer addCustomerAddressV3(Customer customer, ZomatoOrderRequestV3 request, Integer unitId) {
        ZomatoOrderCustomerDetailsV3 customerDetails = request.getCustomerDetails();
        CODCustomerLoginData codCustomerLoginData = new CODCustomerLoginData();
        codCustomerLoginData.setContactNumber(customer.getContactNumber());
        codCustomerLoginData.setCustomer(customer);
        Address address = new Address();
        address.setName(request.getCustomerDetails().getName());
        if (request.getCustomerDetails().getName() != null && request.getCustomerDetails().getName().length() > 50) {
            address.setName(request.getCustomerDetails().getName().substring(0, 50));
        }
        address.setName(request.getCustomerDetails().getName());
        address.setAddressType("HOME");
        address.setCountry(customerDetails.getCountry());
        String line1 = customerDetails.getAddress();
        if (customerDetails.getAddress() != null && customerDetails.getAddress().length() > 255) {
            line1 = customerDetails.getAddress().substring(0, 255);
        }
        address.setLine1(line1);
        String locality = customerDetails.getDeliveryArea();
        if (customerDetails.getDeliveryArea() != null && customerDetails.getDeliveryArea().length() > 80) {
            locality = customerDetails.getDeliveryArea().substring(0, 80);
        }
        address.setLocality(locality);
        Location location = null;
        for (Location loc : masterDataCache.getAllLocations().values()) {
            if (loc.getName().equalsIgnoreCase(customerDetails.getCity())) {
                location = loc;
            }
        }
        if (location == null) {
            location = masterDataCache.getAllLocations().get(masterDataCache.getUnitBasicDetail(unitId).getLocationCode());
        }
        address.setCity(location.getName());
        address.setState(location.getState().getName());
        address.setLatitude(customerDetails.getDeliveryAreaLatitude().toString());
        address.setLongitude(customerDetails.getDeliveryAreaLongitude().toString());
        address.setLandmark(customerDetails.getAddressInstructions());
        address.setZipCode("00000");
        codCustomerLoginData.setNewAddress(address);
        String endpoint = environmentProperties.getCRMServiceBasePath() + CRMServiceClientEndpoints.COD_ADD_ADDRESS;
        return webServiceHelper.postWithAuth(endpoint, environmentProperties.getChannelPartnerClientToken(), codCustomerLoginData,
            Integer.class);
    }

    /*private void updateDeliveryDetails(ZomatoOrderRequest request, PartnerOrderDetail partnerOrderDetail) {
        CustomerDetails customerDetails = request.getCustomerDetails();
        UnitPartnerLocalityMapping mapping = null;
        PartnerLocalityDetail partnerLocalityDetail = partnerLocalityService.getLocality(
                channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId(), customerDetails.getDeliveryArea(),
                customerDetails.getCity(), customerDetails.getState(), customerDetails.getCountry(), customerDetails.getPincode());
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        RestaurantPartnerKey key = new RestaurantPartnerKey(request.getOutletId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
        if (partnerLocalityDetail == null) {
            partnerLocalityDetail = new PartnerLocalityDetail();
            partnerLocalityDetail.setCity(customerDetails.getCity());
            partnerLocalityDetail.setCountry(customerDetails.getCountry());
            partnerLocalityDetail.setKettlePartnerId(channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId());
            partnerLocalityDetail.setLocality(customerDetails.getDeliveryArea());
            partnerLocalityDetail.setPartnerName(partnerOrderDetail.getPartnerName());
            partnerLocalityDetail.setPinCode(customerDetails.getPincode());
            partnerLocalityDetail.setState(customerDetails.getState());
            partnerLocalityService.addLocality(partnerLocalityDetail);
        } else {
            mapping = partnerLocalityService.getPartnerLocalityMapping(partnerLocalityDetail);
            if (mapping == null) {
                mapping = new UnitPartnerLocalityMapping();
                mapping.setPartnerLocalityDetail(partnerLocalityDetail);
                partnerLocalityService.addMapping(mapping);
                if (!orderValidationService.unitCanDeliver(data.getUnitId())) {
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                            "Zomato order outside delivery hours:::\n Order Id: " + request.getOrderId() + "\n Unit: " +
                                    masterDataCache.getUnit(data.getUnitId()).getName() + "\n Time: " + request.getOrderDateTime());
                }
            }
        }
        customerDetails.setCity(masterDataCache.getUnit(data.getUnitId()).getAddress().getCity());
        customerDetails.setState(masterDataCache.getUnit(data.getUnitId()).getAddress().getState());
        customerDetails.setCountry(masterDataCache.getUnit(data.getUnitId()).getAddress().getCountry());
    }*/

    private void updateDeliveryDetailsV3(ZomatoOrderRequestV3 request, PartnerOrderDetail partnerOrderDetail) {
        ZomatoOrderCustomerDetailsV3 customerDetails = request.getCustomerDetails();
        boolean mappingFound = false;
        UnitPartnerLocalityMapping mapping = null;
        PartnerLocalityDetail partnerLocalityDetail = partnerLocalityService.getLocality(
            channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId(), customerDetails.getDeliveryArea(),
            customerDetails.getCity(), null, customerDetails.getCountry(), customerDetails.getPincode());
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        RestaurantPartnerKey key = new RestaurantPartnerKey(request.getOutletId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
        if (partnerLocalityDetail == null) {
            partnerLocalityDetail = new PartnerLocalityDetail();
            partnerLocalityDetail.setCity(customerDetails.getCity());
            partnerLocalityDetail.setCountry(customerDetails.getCountry());
            partnerLocalityDetail.setKettlePartnerId(channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerName()).getKettlePartnerId());
            partnerLocalityDetail.setLocality(customerDetails.getDeliveryArea());
            partnerLocalityDetail.setPartnerName(partnerOrderDetail.getPartnerName());
            partnerLocalityDetail.setPinCode(customerDetails.getPincode());
            partnerLocalityService.addLocality(partnerLocalityDetail);
        } else {
            mapping = partnerLocalityService.getPartnerLocalityMapping(partnerLocalityDetail);
            if (mapping != null) {
                if (!(mapping.getPending() != null && mapping.getPending())) {
                    mappingFound = true;
                }
            } else {
                mapping = new UnitPartnerLocalityMapping();
                mapping.setPartnerLocalityDetail(partnerLocalityDetail);
                partnerLocalityService.addMapping(mapping);
                if (!orderValidationService.unitCanDeliver(data.getUnitId())) {
                    String message = ChannelPartnerUtils.getMessage("Zomato order outside delivery hours","::::::::::::::::OrderDetails:::::::::::::::: "+"\n"+"OrderId:"+request.getOrderId()+"Unit"+masterDataCache.getUnit(Integer.parseInt(request.getOutletId())).getName()+"Time"+request.getOrderDateTime());
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                        message);
                }
            }
        }
        if (!mappingFound) {
            customerDetails.setCity(masterDataCache.getUnit(data.getUnitId()).getAddress().getCity());
            customerDetails.setCountry(masterDataCache.getUnit(data.getUnitId()).getAddress().getCountry());
        } else {
            customerDetails.setCity(mapping.getKettleCity());
            UnitPartnerLocalityMapping finalMapping = mapping;
            List<Location> locations = masterDataCache.getAllLocations().values().stream().filter(locality ->
                locality.getName().equalsIgnoreCase(finalMapping.getKettleCity())
            ).collect(Collectors.toList());
            customerDetails.setCountry(locations.get(0).getCountry().getName());
        }
    }

    private void markOrderReady(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) {
        if (partnerOrderDetail != null) {
            ZomatoOrderRiderData data = new ZomatoOrderRiderData();
            data.setOrderId(partnerOrderDetail.getPartnerOrderId());
            sendOrderStateUpdateAPICall(ZomatoServiceEndpoints.MARK_ORDER_READY, data, request.getState(), partnerOrderDetail.getBrandId());
        }
    }

    private void sendAssignedStatus(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) {
        if (partnerOrderDetail != null) {
            ZomatoOrderRiderData data = new ZomatoOrderRiderData();
            data.setOrderId(partnerOrderDetail.getPartnerOrderId());
            IdCodeName riderData = ChannelPartnerUtils.deserializeObject(request.getData(), IdCodeName.class);
            if (riderData != null) {
                data.setRiderName(riderData.getName());
                data.setRiderPhoneNumber(riderData.getCode());
                sendOrderStateUpdateAPICall(ZomatoServiceEndpoints.RIDER_ASSIGNED, data, request.getState(), partnerOrderDetail.getBrandId());
            }
        }
    }

    private void sendPickedUpStatus(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) {
        if (partnerOrderDetail != null) {
            ZomatoOrderRequestV3 order = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
            if(order.getEnableDelivery() != 0) {
                ZomatoOrderRiderData data = new ZomatoOrderRiderData();
                data.setOrderId(partnerOrderDetail.getPartnerOrderId());
                if (request.getData() != null) {
                    IdCodeName riderData = ChannelPartnerUtils.deserializeObject(request.getData(), IdCodeName.class);
                    if (riderData != null) {
                        data.setRiderName(riderData.getName());
                        data.setRiderPhoneNumber(riderData.getCode());
                    }
                }
                sendOrderStateUpdateAPICall(ZomatoServiceEndpoints.ORDER_PICKED_UP, data, request.getState(), partnerOrderDetail.getBrandId());
            }
        }
    }

    private void sendDeliveredStatus(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) {
        if (partnerOrderDetail != null) {
            ZomatoOrderRiderData data = new ZomatoOrderRiderData();
            data.setOrderId(partnerOrderDetail.getPartnerOrderId());
            sendOrderStateUpdateAPICall(ZomatoServiceEndpoints.DELIVERED, data, request.getState(), partnerOrderDetail.getBrandId());
        }
    }

    private void sendOrderStateUpdateAPICall(ZomatoServiceEndpoints endPoint, ZomatoOrderRiderData data, PartnerOrderStates state, Integer brandId) {
        try {
            ZomatoNotificationResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                endPoint, HttpMethod.POST, data, ZomatoNotificationResponse.class, brandId);
            if (response == null || response.getCode() != 200) {
                String responseJson = new Gson().toJson(response);
                slackAndLogNotification("Error updating " + state + " status for id " + data.getOrderId() + " to Zomato: " + responseJson);
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogZomatoException(e, "Error updating " + state + " status for id " + data.getOrderId() + " to Zomato:-");
        } catch (Exception e) {
            catchAndLogException(e, "Error updating " + state + " status for id " + data.getOrderId() + " to Zomato :");
        }
    }

    /*private void removeFilterProductsOld(OldZomatoMenu menu, Integer brandId) {
        List<Integer> productList = partnerMetadataService.getPartnerProductFilter(
                channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        List<OldZomatoCategory> categoryList = new ArrayList<>();
        for (OldZomatoCategory zomatoCategory : menu.getCategories()) {
            List<ZomatoMenuItem> itemList = new ArrayList<>();
            for (ZomatoMenuItem zomatoMenuItem : zomatoCategory.getItems()) {
                if (!productList.contains(Integer.parseInt(zomatoMenuItem.getItemId()))) {
                    itemList.add(zomatoMenuItem);
                }
            }
            List<OldZomatoSubcategory> subcategoryList = new ArrayList<>();
            for (OldZomatoSubcategory zomatoSubcategory : zomatoCategory.getSubcategories()) {
                List<ZomatoMenuItem> subItemList = new ArrayList<>();
                for (ZomatoMenuItem zomatoMenuItem : zomatoSubcategory.getItems()) {
                    if (!productList.contains(Integer.parseInt(zomatoMenuItem.getItemId()))) {
                        subItemList.add(zomatoMenuItem);
                    }
                }
                if (!subItemList.isEmpty()) {
                    zomatoSubcategory.setItems(subItemList);
                    subcategoryList.add(zomatoSubcategory);
                }
            }
            if (!subcategoryList.isEmpty()) {
                zomatoCategory.setSubcategories(subcategoryList);
            }
            if (!itemList.isEmpty() || !subcategoryList.isEmpty()) {
                zomatoCategory.setItems(itemList);
                categoryList.add(zomatoCategory);
            }
        }
        menu.setCategories(categoryList);
    }*/

    private void removeFilterProducts(ZomatoMenu menu, Integer brandId) {
        List<Integer> productList = partnerMetadataService.getPartnerProductFilter(
            channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        List<ZomatoCatalogues> cataloguesList = new ArrayList<>();
        for (ZomatoCatalogues catalogue : menu.getCatalogues()) {
            String data  = catalogue.getVendorEntityId().split("_")[0];
            if (!productList.contains(Integer.parseInt(data))) {
                cataloguesList.add(catalogue);
            }
        }
        if (!cataloguesList.isEmpty()) {
            menu.setCatalogues(cataloguesList);
        }
        List<ZomatoCombo> comboList = menu.getCombos().stream().filter(zomatoCombo ->
            !productList.contains(Integer.parseInt(zomatoCombo.getVendorEntityId()))).collect(Collectors.toList());
        if (!comboList.isEmpty()) {
            menu.setCombos(comboList);
        }
        List<ZomatoCategory> categories = new ArrayList<>();
        menu.getCategories().forEach(zomatoCategory -> {
            List<ZomatoSubcategory> zomatoSubcategories = new ArrayList<>();
            zomatoCategory.getSubCategories().forEach(zomatoSubcategory -> {
                List<ZomatoEntities> entities = zomatoSubcategory.getEntities().stream().filter(zomatoEntities ->
                    !productList.contains(Integer.valueOf(zomatoEntities.getVendorEntityId()
                        .replaceAll(ChannelPartnerStringConstants.ZOMATO_PRODUCT_ID_REGEX, "")))).collect(Collectors.toList());
                if (!entities.isEmpty()) {
                    zomatoSubcategory.setEntities(entities);
                    zomatoSubcategories.add(zomatoSubcategory);
                }
            });
            if (!zomatoSubcategories.isEmpty()) {
                zomatoCategory.setSubCategories(zomatoSubcategories);
                categories.add(zomatoCategory);
            }
        });
        menu.setCategories(categories);
    }

    /*private void updateProductTagsOld(OldZomatoMenu menu, Integer brandId) {
        List<IdName> qvmTags = partnerMetadataService.getPartnerProductQVMTags(
            channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId());
        Map<Integer, IdName> qvmMap = new HashMap<>();
        for (IdName qvmTag : qvmTags) {
            qvmMap.put(qvmTag.getId(), qvmTag);
        }
        List<ProductTagsMappings> mappingsList = partnerMetadataService.getPartnerProductTagsMappings(
                channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        if (mappingsList != null && !mappingsList.isEmpty()) {
            for (OldZomatoCategory zomatoCategory : menu.getCategories()) {
                setTagsToMenuItemsOld(mappingsList, zomatoCategory.getItems(), zomatoCategory.getCategoryName()
                    .equals("QVM"), qvmMap);
                for (OldZomatoSubcategory zomatoSubcategory : zomatoCategory.getSubcategories()) {
                    setTagsToMenuItemsOld(mappingsList, zomatoSubcategory.getItems(),
                        zomatoCategory.getCategoryName().equals("QVM"), qvmMap);
                }
            }
        }
    }*/

    private void updateProductTags(ZomatoMenu menu, Integer brandId) {
        // Setting product tags to the catalogs
        List<ProductTagsMappings> mappings = partnerMetadataService.getPartnerProductTagsMappings(
            channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        Map<Integer, List<IdName>> tagsMap = new HashMap<>();
        mappings.forEach(productTagsMappings ->
            tagsMap.put(productTagsMappings.getProductId(), productTagsMappings.getTags())
        );
        menu.getCatalogues().forEach(zomatoCatalogues -> {
            String productId = zomatoCatalogues.getVendorEntityId();
            if (productId.contains("_")) {
                productId = productId.split("_")[0];
            }
            Integer prodId = null;
            try {
                prodId = Integer.parseInt(productId);
            } catch (Exception e) {
                LOG.error("Error addding tags to ", e);
            }
            if (prodId != null && tagsMap.containsKey(prodId)) {
                if (zomatoCatalogues.getTags() == null) {
                    zomatoCatalogues.setTags(new ArrayList<>());
                }
                tagsMap.get(prodId).forEach(idName ->
                    zomatoCatalogues.getTags().add(idName.getName())
                );
            }
        });
    }

    private void updateMeatTypeTags(ZomatoMenu menu, Integer brandId) {
        List<ProductTagsMappings> mappings = partnerMetadataService.getPartnerProductMeatTagsMappings(
            channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        Map<Integer, List<IdName>> tagsMap = new HashMap<>();
        mappings.forEach(productTagsMappings ->
            tagsMap.put(productTagsMappings.getProductId(), productTagsMappings.getTags())
        );
        menu.getCatalogues().forEach(zomatoCatalogues -> {
            String productId = zomatoCatalogues.getVendorEntityId();
            if (productId.contains("_")) {
                productId = productId.split("_")[0];
            }
            Integer prodId = null;
            try {
                prodId = Integer.parseInt(productId);
            } catch (Exception e) {
                LOG.error("Error adding tags to ", e);
            }
            if (prodId != null && tagsMap.containsKey(prodId)) {
                if (zomatoCatalogues.getMeatTypes() == null) {
                    zomatoCatalogues.setMeatTypes(new ArrayList<>());
                }
                tagsMap.get(prodId).forEach(idName ->
                    zomatoCatalogues.getMeatTypes().add(idName.getName())
                );
            }
        });
    }

    private void updateAllergenTypeTags(ZomatoMenu menu, Integer brandId) {
        List<ProductTagsMappings> mappings = partnerMetadataService.getPartnerProductAllergenTagsMappings(
                channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        Map<Integer, List<IdName>> tagsMap = new HashMap<>();
        mappings.forEach(productTagsMappings ->
                tagsMap.put(productTagsMappings.getProductId(), productTagsMappings.getTags())
        );
        menu.getCatalogues().forEach(zomatoCatalogues -> {
            String productId = zomatoCatalogues.getVendorEntityId();
            if (productId.contains("_")) {
                productId = productId.split("_")[0];
            }
            Integer prodId = null;
            try {
                prodId = Integer.parseInt(productId);
            } catch (Exception e) {
                LOG.error("Error adding tags to ", e);
            }
            if (prodId != null && tagsMap.containsKey(prodId)) {
                if (zomatoCatalogues.getAllergenTypes() == null) {
                    zomatoCatalogues.setAllergenTypes(new ArrayList<>());
                }
                tagsMap.get(prodId).forEach(idName ->
                        zomatoCatalogues.getAllergenTypes().add(idName.getName())
                );
            }
        });
    }

    private void updateServingInfoTypeTags(ZomatoMenu menu, Integer brandId) {
        List<ProductTagsMappings> mappings = partnerMetadataService.getPartnerProductServingInfoTagsMappings(
                channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        Map<Integer, List<IdName>> tagsMap = new HashMap<>();
        mappings.forEach(productTagsMappings ->
                tagsMap.put(productTagsMappings.getProductId(), productTagsMappings.getTags())
        );
        menu.getCatalogues().forEach(zomatoCatalogues -> {
            String productId = zomatoCatalogues.getVendorEntityId();
            if (productId.contains("_")) {
                productId = productId.split("_")[0];
            }
            Integer prodId = null;
            try {
                prodId = Integer.parseInt(productId);
            } catch (Exception e) {
                LOG.error("Error adding tags to ", e);
            }
            if (prodId != null && tagsMap.containsKey(prodId)) {
                tagsMap.get(prodId).forEach(idName ->
                        zomatoCatalogues.setServingInfo(idName.getName())
                );
            }
        });
    }

    private void updateServingSizeTypeTags(ZomatoMenu menu, Integer brandId) {
        List<ProductTagsMappingVU> mappings = partnerMetadataService.getPartnerProductServingSizeMappings(
                channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId(), brandId);
        Map<Integer, IdValueUnit> tagsMap = new HashMap<>();
        mappings.forEach(productTagsMappingVU ->
                tagsMap.put(productTagsMappingVU.getProductId(), productTagsMappingVU.getTags())
        );
        menu.getCatalogues().forEach(zomatoCatalogues -> {
            String productId = zomatoCatalogues.getVendorEntityId();
            if (productId.contains("_")) {
                productId = productId.split("_")[0];
            }
            Integer prodId = null;
            try {
                prodId = Integer.parseInt(productId);
            } catch (Exception e) {
                LOG.error("Error adding tags to ", e);
            }
            if (prodId != null && tagsMap.containsKey(prodId)) {
                if (zomatoCatalogues.getServingSize() == null) {
                    zomatoCatalogues.setServingSize(new IdValueUnit());
                }
                zomatoCatalogues.getServingSize().setValue(tagsMap.get(prodId).getValue());

            }
        });
    }

    /*private void setTagsToMenuItemsOld(List<ProductTagsMappings> mappingsList, List<ZomatoMenuItem> zomatoMenuItems,
                                       boolean isQVM, Map<Integer, IdName> qvmMap) {
        Map<Integer, List<IdName>> mappingMap = new HashMap<>();
        mappingsList.forEach(mappings -> mappingMap.put(mappings.getProductId(), mappings.getTags()));
        for (ZomatoMenuItem zomatoMenuItem : zomatoMenuItems) {
            List<IdName> tags = mappingMap.get(Integer.valueOf(zomatoMenuItem.getItemId()));
            if (tags != null && !tags.isEmpty()) {
                if (isQVM) {
                    tags.forEach(idName -> zomatoMenuItem.getItemTags().add(idName.getId()));
                } else {
                    tags.stream().filter(idName -> qvmMap.containsKey(idName.getId()))
                        .forEach(idName -> zomatoMenuItem.getItemTags().add(idName.getId()));
                }
            }
        }
    }*/

    @Override
    public void updateMenuStatus(ZomatoMenuRequestStatus request) {
        PartnerMenuStatus partnerMenuStatus = trackService.getPartnerMenuResponseByRequestId(request.getRequestId());
        partnerMenuStatus.setRequestId(request.getRequestId());
        partnerMenuStatus.setSuccess(request.isSuccess());
        partnerMenuStatus.setError(request.getError());
        try {
            trackService.updatePartnerMenuStatusDetail(partnerMenuStatus);
        } catch (Exception e) {
            LOG.error("Error updating menu status", e);
        }

    }

    /*private void updateProductAliasesOld(OldZomatoMenu menu, Map<Integer, ProductAlias> aliasMap) {
        for (OldZomatoCategory zomatoCategory : menu.getCategories()) {
            for (ZomatoMenuItem zomatoMenuItem : zomatoCategory.getItems()) {
                Integer productId = Integer.parseInt(zomatoMenuItem.getItemId());
                if (aliasMap.containsKey(productId)) {
                    zomatoMenuItem.setItemName(aliasMap.get(productId).getProductAlias());
                }
            }
            for (OldZomatoSubcategory zomatoSubcategory : zomatoCategory.getSubcategories()) {
                for (ZomatoMenuItem zomatoMenuItem : zomatoSubcategory.getItems()) {
                    Integer productId = Integer.parseInt(zomatoMenuItem.getItemId());
                    if (aliasMap.containsKey(productId)) {
                        zomatoMenuItem.setItemName(aliasMap.get(productId).getProductAlias());
                    }
                }
            }
        }
    }*/

    private void updateProductAliases(ZomatoMenu menu, Map<Integer, ProductAlias> aliasMap) {
        for (ZomatoCatalogues zomatoCatalogues : menu.getCatalogues()) {
            if (!zomatoCatalogues.getVendorEntityId().contains(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER) &&
                !zomatoCatalogues.getVendorEntityId().contains(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                Integer productId = Integer.parseInt(zomatoCatalogues.getVendorEntityId().split("_")[0]);
                if (aliasMap.containsKey(productId)) {
                    zomatoCatalogues.setName(aliasMap.get(productId).getProductAlias());
                }
            }
        }
        if (menu.getCombos() != null) {
            for (ZomatoCombo zomatoCombo : menu.getCombos()) {
                Integer productId = Integer.parseInt(zomatoCombo.getVendorEntityId());
                if (aliasMap.containsKey(productId)) {
                    zomatoCombo.setName(aliasMap.get(productId).getProductAlias());
                }
            }
        }
    }

    @Override
    public boolean sendMenuOffers(PartnerOfferDetail partnerOfferDetail,
                                  List<PartnerOfferDetail> activePartnerOfferDetails) throws ChannelPartnerException {
        ZomatoOfferRequest offerRequest = new ZomatoOfferRequest();
        PartnerOfferDetailVO vo = new PartnerOfferDetailVO();
        vo.setOfferData(ChannelPartnerUtils.deserializeObject(new Gson().toJson(partnerOfferDetail.getOfferData()),
            PartnerOfferDataVO.class));
        if (partnerOfferDetail.getBrandId() == null) {
            partnerOfferDetail.setBrandId(ChannelPartnerServiceConstants.CHAAYOS_BRAND_ID);
        }
        if (vo.getOfferData().getOfferType().equalsIgnoreCase("DISCOUNT")) {
            offerRequest = createDiscountObject(partnerOfferDetail, vo);
        } else if (vo.getOfferData().getOfferType().equalsIgnoreCase("BOGO")) {
            offerRequest = createBogoObject(partnerOfferDetail, vo);
        }
        if (masterDataCache.getUnits().containsKey(partnerOfferDetail.getUnitId()) &&
            validatePartnerAndMapping(partnerOfferDetail.getPartnerId(), partnerOfferDetail.getUnitId())) {
            String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData()
                .get(new UnitPartnerBrandKey(partnerOfferDetail.getUnitId(), partnerOfferDetail.getBrandId(),
                    partnerOfferDetail.getPartnerId())).getRestaurantId();
            offerRequest.setOutletId(restaurantId);
            if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
                offerRequest.setOutletId(ChannelPartnerUtils.getZomatoTestOutletId(partnerOfferDetail.getBrandId())); //test outlet id
            }
            boolean offerExists = activePartnerOfferDetails.stream().anyMatch(partnerOfferDetail1 ->
                (!partnerOfferDetail1.getId().equalsIgnoreCase(partnerOfferDetail.getId()) &&
                    partnerOfferDetail1.getUnitId().equals(partnerOfferDetail.getUnitId()) &&
                    Boolean.TRUE.equals(partnerOfferDetail1.getActive())));
            if (offerExists) {
                throw new ChannelPartnerException("There are other offers already running on "
                    + masterDataCache.getUnit(partnerOfferDetail.getUnitId()).getName() + " from "
                    + partnerOfferDetail.getStartDate() + " to " + partnerOfferDetail.getEndDate());
            }
            try {
                ZomatoOfferResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                    ZomatoServiceEndpoints.OFFER_ADD, HttpMethod.POST, offerRequest, ZomatoOfferResponse.class,
                    partnerOfferDetail.getBrandId());
                String responseJson = new Gson().toJson(response);
                LOG.info("response: {}", responseJson);
                return (response != null && response.isValid());
            } catch (HttpStatusCodeException e) {
                catchAndLogZomatoException(e, "Error adding offers to Zomato:");
            } catch (Exception e) {
                catchAndLogException(e, "Error adding offers to Zomato:-");
            }
        }
        return false;
    }

    private ZomatoOfferRequest createBogoObject(PartnerOfferDetail partnerOfferDetail, PartnerOfferDetailVO partnerDetailVo) {
        List<String> productList = partnerMetadataService.getPartnerBogoProductsforOffer(
            channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId());
        ZomatoOfferRequest offerRequest = new ZomatoOfferRequest();
        offerRequest.setOutletId(partnerOfferDetail.getUnitId().toString());
        List<ZomatoRestaurantOffers> restOffers = new ArrayList<>();
        ZomatoRestaurantOffers restOffer = new ZomatoRestaurantOffers();
        restOffer.setOfferId(partnerOfferDetail.getUnitId().toString() + "_" + partnerDetailVo.getOfferData().getOfferType());
        restOffer.setOfferType(partnerDetailVo.getOfferData().getOfferType());
        restOffer.setStartDate(partnerDetailVo.getOfferData().getStartDate());
        restOffer.setEndDate(partnerDetailVo.getOfferData().getEndDate());
        restOffer.setFirstOrderOnly(0);
        restOffer.setActive(partnerOfferDetail.getActive() != null && partnerOfferDetail.getActive());
        restOffer.setVendorEntityIds(productList);
        restOffers.add(restOffer);
        offerRequest.setRestaurantOffers(restOffers);
        return offerRequest;
    }

    private ZomatoOfferRequest createDiscountObject(PartnerOfferDetail partnerOfferDetail,
                                                    PartnerOfferDetailVO partnerDetailVo) {
        ZomatoOfferRequest offerRequest = new ZomatoOfferRequest();
        offerRequest.setOutletId(partnerOfferDetail.getUnitId().toString());
        List<ZomatoRestaurantOffers> restOffers = new ArrayList<>();
        ZomatoRestaurantOffers restOffer = new ZomatoRestaurantOffers();
        restOffer.setOfferId(partnerOfferDetail.getCouponCode());
        restOffer.setOfferType(partnerDetailVo.getOfferData().getOfferType());
        restOffer.setStartDate(partnerDetailVo.getOfferData().getStartDate());
        restOffer.setEndDate(partnerDetailVo.getOfferData().getEndDate());
        restOffer.setDiscountType(partnerDetailVo.getOfferData().getDiscountType());
        restOffer.setDiscountValue(partnerDetailVo.getOfferData().getDiscountValue());
        restOffer.setMinOrderAmount(partnerDetailVo.getOfferData().getMinOrderAmount());
        restOffer.setFirstOrderOnly(0);
        restOffer.setActive(partnerOfferDetail.getActive() != null && partnerOfferDetail.getActive());
        restOffers.add(restOffer);
        offerRequest.setRestaurantOffers(restOffers);
        return offerRequest;
    }

    private UnitPartnerBrandMappingData getUnitPartnerBrandMappingData(String outletId, Integer partnerId) {
        RestaurantPartnerKey key = new RestaurantPartnerKey(outletId, partnerId);
        return masterDataCache.getUnitPartnerBrandMappingMetaData2().get(key);
    }

    private void catchAndLogZomatoException(HttpStatusCodeException e, String title) {
        try {
            String errorBody = e.getResponseBodyAsString();
            LOG.error(title + ":" + errorBody);
            String message = ChannelPartnerUtils.getMessage(title, errorBody);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                message);
        } catch (Exception ex) {
            LOG.error("Error in logging zomato exception", ex);
        }
    }

    private void catchAndLogException(Exception e, String title) {
        LOG.error(title, e);
        String message = ChannelPartnerUtils.getMessage(title, e.getMessage());

        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
            message);
    }

    // this function only adds normal combo ids and does not include super combo or hero combo ids
    private void addNormalComboItemsToStockEvent(ZomatoUnitProductStockV3 zomatoUnitProductStock,
                                                 UnitProductsStockEvent event, UnitPartnerBrandKey key
            , Map<Integer,Boolean> forceStockOutMap) {
        if (StockStatus.STOCK_OUT.equals(event.getStatus())) {
            Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
            Set<String> comboIds = new HashSet<>();
            for (String productId : event.getProductIds()) {
                // Finding combo mappings for normal products only
                if (map != null && map.containsKey(Integer.parseInt(productId)) && map.get(Integer.parseInt(productId)) != null) {
                    map.get(Integer.parseInt(productId)).stream().filter(comboId ->
                        !zomatoUnitProductStock.getCatalogueVendorEntityIds().contains(comboId.toString())
                    ).forEach(comboId -> comboIds.add(comboId.toString()));
                }
            }
            zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(comboIds);
        } else {
            addNormalComboItemsToStockInEvent(zomatoUnitProductStock, event, key,forceStockOutMap);
        }
    }

    private void addNormalComboItemsToStockInEvent(ZomatoUnitProductStockV3 zomatoUnitProductStock,
                                                   UnitProductsStockEvent event, UnitPartnerBrandKey key , Map<Integer,Boolean> forceStockOutMap) {
        Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitComboProductMappings(masterDataCache.
            getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
        if (map != null) {
            Set<Integer> comboItems = new HashSet<>();
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                boolean setCombo = entry.getValue().stream().anyMatch(productId -> event.getProductIds().contains(productId.toString()));
                if (setCombo) {
                    comboItems.addAll(entry.getValue());
                }
            }
            if(!CollectionUtils.isEmpty(comboItems)){
                LOG.info("Product Ids in Combo Partner :::: {}  To Check For Stock In :::: {} ",PARTNER_NAME,comboItems);
            }
            Map productStock = orderValidationService.getUnitProductInventoryByProducts(key.getUnitId(), new ArrayList<>(comboItems));
            LOG.info("Inventory map for unit Id  :::: {} :::: Partner :::: {}  Time ::::: {}   ::::::  {} ", key.getUnitId() , PARTNER_NAME,
                    AppUtils.getCurrentTimestamp(), new Gson().toJson(productStock));
            Boolean printEvent = false;
            for(String productId : event.getProductIds()){
                if((productStock.get(productId) != null &&
                        (Integer)productStock.get(productId) <= 0)){
                    LOG.info("stock event : {} ", new Gson().toJson(event));
                    LOG.info("Got Event For Stock In For Partner :::: {} , Brand : {} But Got Inventory 0 on refetch !!  for product Id :: {} ",PARTNER_NAME ,key.getBrandId(),productId);
                    productStock.put(productId,1);
                    printEvent = true;
                }
            }
            if(Boolean.TRUE.equals(printEvent)){
                LOG.info("stock event : {} ", new Gson().toJson(event));
            }
            for (Map.Entry<Integer, Set<Integer>> entry : map.entrySet()) {
                Set<Integer> productIds = entry.getValue();
                boolean addCombo = productIds.stream().noneMatch((productId -> (masterDataCache.getProduct(productId).isInventoryTracked()
                        && (productStock.get(productId.toString()) == null || (Integer) productStock.get(productId.toString()) <= 0))
                        || forceStockOutMap.containsKey(productId)));
                if (addCombo && !zomatoUnitProductStock.getCatalogueVendorEntityIds().contains(entry.getKey().toString())) {
                    zomatoUnitProductStock.getCatalogueVendorEntityIds().add(entry.getKey().toString());
                }
            }
        }
    }

    private void addSingleServeItemsTOStockEvent(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitPartnerBrandKey key) {
        List<String> ssProductIds = new ArrayList<>();
        if (key.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)) {
            ssProductIds = partnerMenuService.getSingleServeProductItems(zomatoUnitProductStock, key);
        }
        if (ssProductIds != null && !ssProductIds.isEmpty()) {
            zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(ssProductIds);
        }
    }

    private void logStockUpdateSnapshot(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent event)
        throws ChannelPartnerException {
        LOG.info("Updating Zomato unit product stock snapshot");
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        Set<String> productIds = new HashSet<>(zomatoUnitProductStock.getCatalogueVendorEntityIds());
        for (String productId : productIds) {
            // r_ for upselling and rd_ for Hero product and ss for single serve menu
            if (!productId.endsWith(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER) &&
                !productId.endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER) && !productId.startsWith("ss_")) {
                try {
                    Integer product = Integer.valueOf(productId);
                    PartnerUnitProductStockSnapshot snapshot = partnerUnitStockSnapshotDao
                            .findByUnitIdAndPartnerIdAndProductId(zomatoUnitProductStock.getOutletId(), partnerId, product);
                    if (snapshot == null) {
                        snapshot = new PartnerUnitProductStockSnapshot();
                        snapshot.setPartnerId(partnerId);
                        snapshot.setUnitId(zomatoUnitProductStock.getOutletId());
                        snapshot.setProductId(product);
                        snapshot.setProductStockSnapshot(new ArrayList<>());
                    }
                    ProductStockSnapshot stockSnapshot = new ProductStockSnapshot();
                    stockSnapshot.setStockStatus(event.getStatus());
                    stockSnapshot.setUpdateTime(ChannelPartnerUtils.getFormattedTime(currentTime, "yyyy-MM-dd HH:mm:ss"));
                    snapshot.getProductStockSnapshot().add(stockSnapshot);
                    snapshot = partnerUnitStockSnapshotDao.save(snapshot);
                    if (snapshot == null) {
                        throw new ChannelPartnerException("Error updating stock snapshot!");
                    }
                }catch (NumberFormatException numberFormatException){
                    LOG.info("Number Format Exception for Product Id : {} ",productId);
                }
            }
        }
    }

    @Override
    public boolean updateRiderDetails(ZomatoOrderRiderDataV3 riderData) {
        ZomatoNotificationResponse response = new ZomatoNotificationResponse();
        PartnerMaskTempResponse maskTempResponse = new PartnerMaskTempResponse();
        PartnerOrderDetail partnerOrderDetail = new PartnerOrderDetail();
        PartnerDeliveryStatus pd = new PartnerDeliveryStatus();
        try {
            LOG.info("Fetching Partner Order Details for order Id: {}", riderData.getOrderId());
            partnerOrderDetail = trackService.getPartnerData(riderData.getOrderId());
            LOG.info("Partner Order Details Recieved for order Id: {}", riderData.getOrderId());
            if (partnerOrderDetail.getDeliveryStatuses() != null && !partnerOrderDetail.getDeliveryStatuses().isEmpty()) {
                pd = partnerOrderDetail.getDeliveryStatuses()
                    .get(partnerOrderDetail.getDeliveryStatuses().size() - 1);
            }
            if (pd.getRiderContact() != null) {
                riderData.setRiderPhoneNumber(pd.getRiderContact());
                if (riderData.getRbt() > ChannelPartnerServiceConstants.ZOMATO_RIDER_MAX_TEMP) {
                    riderData.setRbt(ChannelPartnerServiceConstants.ZOMATO_RIDER_MAX_TEMP);
                }
                LOG.info("Sending request to {}", ZomatoServiceEndpoints.RIDER_DETAILS);
                response = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.RIDER_DETAILS,
                    HttpMethod.POST, riderData, ZomatoNotificationResponse.class, partnerOrderDetail.getBrandId());
                LOG.info("Updated Zomato Rider Details Successfully");
                if (response == null) {
                    LOG.error("Error updating rider temperature for id {} to Zomato:", riderData.getOrderId());
                    maskTempResponse.setMessage("Response is null");
                } else {
                    maskTempResponse.setMessage(response.getMessage());
                }
            }
        } catch (HttpStatusCodeException e) {
            catchAndLogZomatoException(e,
                "Error updating rider status for id: " + riderData.getOrderId() + " to Zomato:-");
            maskTempResponse.setMessage(e.getResponseBodyAsString());
        } catch (Exception e) {
            catchAndLogException(e, "Error updating rider status for id: " + riderData.getOrderId() + " to Zomato::");
            maskTempResponse.setMessage(e.getMessage());
        } finally {
            maskTempResponse.setType("Temperature");
            if (partnerOrderDetail != null && pd != null && pd.getRiderContact() != null) {
                PartnerOrderRiderDetails partnerOrderRiderDetails = new PartnerOrderRiderDetails();
                maskTempResponse.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
                maskTempResponse.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
                if (response != null) {
                    maskTempResponse.setStatus(response.getStatus());
                }
                partnerOrderRiderDetails.setPartnerName("Zomato");
                partnerOrderRiderDetails.setPartnerOrderId(riderData.getOrderId());
                partnerOrderRiderDetails.setRiderName(pd.getRiderName());
                partnerOrderRiderDetails.setRiderContact(pd.getRiderContact());
                partnerOrderRiderDetails.setRbt(String.valueOf(riderData.getRbt()));
                partnerOrderRiderDetails.setIsHighTemp(String.valueOf(riderData.isHighTemp()));
                partnerOrderRiderDetails.setPartnerMaskTempResponse(maskTempResponse);
                partnerOrderRiderDetails.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
                partnerOrderRiderDetails.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
                LOG.info("Inserting Response in Partner Order Rider Detail for Order Id: {}", riderData.getOrderId());
                partnerOrderRiderDetailDao.save(partnerOrderRiderDetails);
                LOG.info("Added Response successfully in Partner Order Rider Detail");
            }
        }
        return true;

    }

    @Override
    public boolean updateRiderMask(ZomatoOrderRiderMaskV3 riderMask) {
        ZomatoNotificationResponse response = new ZomatoNotificationResponse();
        PartnerMaskTempResponse maskTempResponse = new PartnerMaskTempResponse();
        PartnerOrderDetail partnerOrderDetail = trackService.getPartnerData(riderMask.getOrderId());
        if (partnerOrderDetail != null) {
            LOG.info("Partner Order Details Received for order Id: {}", riderMask.getOrderId());
            try {
                LOG.info("Sending request to: {}", ZomatoServiceEndpoints.RIDER_MASK);
                response = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.RIDER_MASK,
                    HttpMethod.POST, riderMask, ZomatoNotificationResponse.class, partnerOrderDetail.getBrandId());
                LOG.info("Updated Zomato Rider Details Successfully");
                if (response == null) {
                    LOG.error("Error updating rider status for id {} to Zomato:", riderMask.getOrderId());
                    maskTempResponse.setMessage("Response is null");
                } else {
                    maskTempResponse.setMessage(response.getMessage());
                }
            } catch (HttpStatusCodeException e) {
                catchAndLogZomatoException(e,
                    "Error updating rider status for id " + riderMask.getOrderId() + " to Zomato:");
                maskTempResponse.setMessage(e.getResponseBodyAsString());
            } catch (Exception e) {
                catchAndLogException(e, "Error updating rider status for id " + riderMask.getOrderId() + " to Zomato:");
                maskTempResponse.setMessage(e.getMessage());
            } finally {
                PartnerDeliveryStatus pd = partnerOrderDetail.getDeliveryStatuses().
                    get(partnerOrderDetail.getDeliveryStatuses().size() - 1);
                maskTempResponse.setType("Mask");
                PartnerOrderRiderDetails partnerOrderRiderDetails = new PartnerOrderRiderDetails();
                maskTempResponse.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
                maskTempResponse.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
                if (response != null) {
                    maskTempResponse.setStatus(response.getStatus());
                }
                partnerOrderRiderDetails.setPartnerName("Zomato");
                partnerOrderRiderDetails.setPartnerOrderId(riderMask.getOrderId());
                partnerOrderRiderDetails.setPartnerName(pd.getRiderName());
                partnerOrderRiderDetails.setRiderContact(pd.getRiderContact());
                partnerOrderRiderDetails.setIsWearingMask(riderMask.getIsRiderWearingMask());
                partnerOrderRiderDetails.setPartnerMaskTempResponse(maskTempResponse);
                partnerOrderRiderDetails.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
                partnerOrderRiderDetails.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
                LOG.info("Updating Response in Partner Order  Rider Detail for Order Id: {}", riderMask.getOrderId());
                partnerOrderRiderDetailDao.save(partnerOrderRiderDetails);
                LOG.info("Response updated successfully in Partner Order  Rider Detail");
            }
        }
        return true;
    }

    /*@Override
    public boolean addSingleServeMenu(UnitMenuAddVO request) {
        ZomatoMenuRequest menuRequest = new ZomatoMenuRequest();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(request.getUnitId(), request.getBrandId(), request.getKettlePartnerId());
        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
        menuRequest.setMenu(ChannelPartnerUtils.deserializeObject(new Gson().toJson(request.getMenuRequest()), ZomatoMenu.class));
        menuRequest.setOutletId(restaurantId);
        if (!environmentProperties.getEnvType().equals(EnvType.SPROD) && !environmentProperties.getEnvType().equals(EnvType.PROD)) {
            String outletId = ChannelPartnerUtils.getZomatoTestOutletId(request.getBrandId());
            menuRequest.setOutletId(outletId);
        }
        try {
            ZomatoMenuResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                ZomatoServiceEndpoints.SINGLE_SERVE, HttpMethod.POST, menuRequest, ZomatoMenuResponse.class, request.getBrandId());
            String responseJson = new Gson().toJson(response);
            LOG.info("response:: {}", responseJson);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                "Zomato menu added response:-\n" + new Gson().toJson(response));
        } catch (HttpStatusCodeException e) {
            catchAndLogZomatoException(e, ChannelPartnerStringConstants.ERROR_ADD_ZOMATO_MENU);
            return false;
        } catch (Exception e) {
            catchAndLogException(e, ChannelPartnerStringConstants.ERROR_ADD_ZOMATO_MENU);
            return false;
        }
        return true;
    }*/



    @Override
    public void pushMenuToUnits(List<Integer> unitIdsForMenu, Integer brandId, Integer employeeId,
                                Integer kettlePartnerId, MenuType menuType) {
        List<Integer> errorUnitIds =  new ArrayList<>();
        loadProductComboMappingsBeforeMenuPush(unitIdsForMenu,brandId,kettlePartnerId);
        for (Integer unitId : unitIdsForMenu) {
            if (validatePartnerAndMapping(kettlePartnerId, unitId) && brandId != null) {
                UnitMenuAddVO request = createZomatoMenu(kettlePartnerId, unitId, brandId, employeeId, menuType.toString());
                if(Objects.isNull(request)){
                    errorUnitIds.add(unitId);
                    continue;
                }
                callZomatoMenuPushAPI(request, employeeId);
            }
        }
        if(!errorUnitIds.isEmpty()){
            String title = "Zomato Bulk Menu Push Failed for This Units ";
            catchAndLogException(new ChannelPartnerException(errorUnitIds.toString()),title);
        }
    }

    private boolean validatePartnerAndMapping(Integer kettlePartnerId, Integer unitId) {
        if (unitId != null && kettlePartnerId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                .anyMatch(unitChannelPartnerMapping -> unitChannelPartnerMapping.getChannelPartner()
                    .getId() == kettlePartnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            return partnerDetail != null && mappingValid;
        }
        return false;
    }

    private  void updateModifierGroupMax(ZomatoMenuRequest menuRequest){
        for(ZomatoModifierGroups zomatoModifierGroup : menuRequest.getMenu().getModifierGroups()){
            if(Objects.nonNull(zomatoModifierGroup.getMaxSelectionsPerItem())){
                if(environmentProperties.multipleQuantityZomatoModifierEnabled()){
                    zomatoModifierGroup.setMax(zomatoModifierGroup.getMaxSelectionsPerItem());
                }else{
                    zomatoModifierGroup.setMax(zomatoModifierGroup.getVariants().size());
                    zomatoModifierGroup.setMaxSelectionsPerItem(1);
                }
            }
        }
    }

    private UnitMenuAddVO createZomatoMenu(Integer partnerId, Integer unitId, Integer brandId, Integer employeeId,
                                           String menuType) {
        UnitMenuAddVO request = new UnitMenuAddVO();
        request.setKettlePartnerId(partnerId);
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        if(Objects.isNull(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key))){
             return null;
        }
        String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
        request.setUnitId(unitId);
        request.setBrandId(brandId);
        request.setEmployeeId(employeeId);
        request.setNew(true);
        request.setMenuType(MenuType.fromValue(menuType));
        request.setRegion(masterDataCache.getUnit(unitId).getRegion());
        Object menuData = null;
        PartnerUnitMenuDetail partnerUnitMenuData;
        PartnerUnitMenuVersionMapping partnerUnitMenuVersionMapping = partnerMenuService
            .getPartnerUnitVersionData(partnerId, unitId, brandId, menuType);
        if (partnerUnitMenuVersionMapping == null) {
            partnerUnitMenuVersionMapping = partnerMenuService.getPartnerUnitVersionData(partnerId, unitId, brandId,
                MenuType.DEFAULT.name());
            request.setMenuType(MenuType.DEFAULT);
        }
        if (partnerUnitMenuVersionMapping != null) {
            partnerUnitMenuData = partnerMenuService.getPartnerUnitVersionMenuDetail(partnerId, unitId, brandId,
                partnerUnitMenuVersionMapping.getMenuType().name(), partnerUnitMenuVersionMapping.getVersion());
            List<Object> charges = null;
            if (partnerUnitMenuData != null) {
                menuData = partnerUnitMenuData.getMenuData();
                charges = partnerUnitMenuData.getCharges();
                if (menuData != null) {
                    ZomatoMenuRequest menuRequest = new ZomatoMenuRequest();
                    menuRequest
                        .setMenu(ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), ZomatoMenu.class));
                    menuRequest.setCharges(ChannelPartnerUtils.deserializeObject(new Gson().toJson(charges), List.class));
                    removeFilterProducts(menuRequest.getMenu(), brandId);
                    updateModifierGroupMax(menuRequest);
                    updateProductTags(menuRequest.getMenu(), brandId);
                    updateMeatTypeTags(menuRequest.getMenu(), brandId);
                    updateAllergenTypeTags(menuRequest.getMenu(),brandId);
                    updateServingInfoTypeTags(menuRequest.getMenu(),brandId);
                    updateServingSizeTypeTags(menuRequest.getMenu(),brandId);
                    updateProductAliases(menuRequest.getMenu(), channelPartnerDataCache.getProductAliasMap(
                        channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId()));
                    menuRequest.setOutletId(restaurantId);
                    request.setMenuRequest(menuRequest);
                    request.setVersion(partnerUnitMenuVersionMapping.getVersion());
                }
            } else {
                LOG.error("No Menu Mapping found for Unit: {} with brandId: {} for {}", unitId, brandId, PARTNER_NAME);
            }
        }
        return request;
    }

    private String getTestOutletForDev(Integer brandId, String outletId) {
        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            outletId = ChannelPartnerUtils.getZomatoTestOutletId(brandId);
        }
        return outletId;
    }

    private ZomatoMenuRequest updateStockValueForMenuPush(ZomatoMenuRequest menuRequestObj ,UnitPartnerBrandKey key){
        try {
            if (Objects.isNull(key.getBrandId())) {
                return menuRequestObj;
            }
            List<String> forceStockOutProducts = filterProductsScheduledForStockUpdate(new UnitProductsStockEvent(key.getUnitId(),StockStatus.STOCK_OUT),
                    new ZomatoUnitProductStockV3(),new ArrayList<>());
            LOG.info("Trying To update In Stock Value IN Menu For Unit Id :::: {} , Brand Id :::: {} , Partner Id :::: {} ", key.getUnitId(), key.getBrandId(), key.getPartnerId());
            Boolean isUnitEnabledForStockInFilter = environmentProperties.isStockInProductsDuringMenuUnitIds(key.getUnitId());
            LOG.info("Stock In Filter Check Status For Unit Id : {}  is :::: {} ",key.getUnitId(),isUnitEnabledForStockInFilter);
            UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
            stockOut.setStatus(StockStatus.STOCK_OUT);
            stockOut.setUnitId(key.getUnitId());
            UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
            stockIn.setStatus(StockStatus.STOCK_IN);
            stockIn.setUnitId(key.getUnitId());
            orderValidationService.refreshLiveUnitInventory(key.getUnitId(), stockIn, stockOut);
            stockOut.getProductIds().addAll(forceStockOutProducts);
            Set<String> stockOutProductIds = new HashSet<>();
            Set<String> stockInProductIds = new HashSet<>();

            for (String productId : stockOut.getProductIds()) {
                Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                        getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
                if (map.containsKey(Integer.valueOf(productId))) {
                    Set<String> comboIds = map.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                    if (!CollectionUtils.isEmpty(comboIds)) {
                        stockOutProductIds.addAll(comboIds);
                    }
                }
            }
            if(!CollectionUtils.isEmpty(stockOut.getProductDimensions())){
                for(IdName idName : stockOut.getProductDimensions()){
                    StringBuilder dimensionKey = new StringBuilder();
                    stockOutProductIds.add(dimensionKey.append(idName.getId()).append("_").append(idName.getName()).toString());
                }
            }
            stockOutProductIds.addAll(stockOut.getProductIds());
            if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                for (String productId : stockIn.getProductIds()) {
                    Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                            getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
                    if (map.containsKey(Integer.valueOf(productId))) {
                        Set<String> comboIds = map.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                        if (!CollectionUtils.isEmpty(comboIds)) {
                            stockInProductIds.addAll(comboIds);
                        }
                    }
                }
                stockInProductIds.addAll(stockOut.getProductIds());
            }

            LOG.info("Stock Out Products For Unit Id : {} , partner Id :: {}  During Menu Push :::: {}", key.getUnitId(), key.getPartnerId(), new Gson().toJson(stockOutProductIds));

            Map<String, Boolean> stockOuts = stockOutProductIds.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                return true;
            }));
            Map<String, Boolean> stockIns = new HashMap<>();
            if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                stockIns = stockInProductIds.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                    return true;
                }));
            }

            for(ZomatoCatalogues zomatoCatalogue : menuRequestObj.getMenu().getCatalogues()){
                String productKey = zomatoCatalogue.getVendorEntityId().split("_")[0];
                if (zomatoCatalogue.getVendorEntityId().contains(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER)
                && stockOuts.containsKey(productKey)) {
                    zomatoCatalogue.setInStock(false);
                }else if (stockOuts.containsKey(zomatoCatalogue.getVendorEntityId())){
                    zomatoCatalogue.setInStock(false);
                }
                if(Boolean.TRUE.equals(environmentProperties.isStockInProductsDuringMenuPushEnabled()) && Boolean.TRUE.equals(isUnitEnabledForStockInFilter)){
                    if (zomatoCatalogue.getVendorEntityId().contains(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER)) {
                        if(!stockIns.containsKey(productKey) && masterDataCache.getProduct(Integer.parseInt(productKey)).isInventoryTracked()){
                            zomatoCatalogue.setInStock(false);
                        }
                    }else{
                        try{
                           Integer productId = Integer.parseInt(zomatoCatalogue.getVendorEntityId());
                            if(!stockIns.containsKey(productId) && masterDataCache.getProduct(productId).isInventoryTracked()){
                                zomatoCatalogue.setInStock(false);
                            }
                        }catch (Exception e){
                            //DO nothing
                        }
                    }
                }

            }

        }catch (Exception e){
            LOG.info("Error While Updating Stock Values During Menu Push  :::: {} ",e);
        }

        return  menuRequestObj;

    }




    private void callZomatoMenuPushAPI(UnitMenuAddVO request, Integer employeeId) {
        ZomatoMenuRequest menuRequest = (ZomatoMenuRequest) request.getMenuRequest();
        updateStockValueForMenuPush(menuRequest,new UnitPartnerBrandKey(request.getUnitId(),request.getBrandId(),AppConstants.CHANNEL_PARTNER_ZOMATO));
        if (menuRequest != null) {
            menuRequest.setOutletId(getTestOutletForDev(request.getBrandId(), menuRequest.getOutletId()));
            try {
                ZomatoMenuResponse response = webServiceHelper.callZomatoApi(environmentProperties,
                    ZomatoServiceEndpoints.MENU_ADD_V3, HttpMethod.POST, menuRequest,
                    ZomatoMenuResponse.class, request.getBrandId());
                menuPushPostProcessing(response, request, employeeId);
                String responseJson = new Gson().toJson(response);
                slackAndLogNotification("Zomato menu added response:\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                    + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n" + responseJson,
                    SlackNotification.PARTNER_MENU);
                trackService.addPartnerMenuStatusEntry(response.getRequestId(), menuRequest.getOutletId());
            } catch (HttpStatusCodeException e) {
                catchAndLogZomatoException(e, "Error adding menu to Zomato:\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                    + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n");
            } catch (Exception e) {
                catchAndLogException(e, "Error adding menu to Zomato:\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                    + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n");
            }
        }
    }

    private Integer getUnitIdFromStoreId(String storeId){
       return Integer.valueOf(storeId.replaceAll("\\D.*$", ""));
    }

    private Integer getBrandIdFromStoreId(String storeId){
        String brandCode =  storeId.replaceAll("^\\d+", "");
        switch (brandCode){
            case "GNT" :
                return AppConstants.GNT_BRAND_ID;
            case "DOH" :
                return AppConstants.DOHFUL_BRAND_ID;
            default:
                return AppConstants.CHAAYOS_BRAND_ID;
        }
    }

    @Override
    public void postMenuProcessNotification(ZomatoMenuRequestStatus request , PartnerMenuStatus partnerMenuStatus){
        try {
            String requestJson = new Gson().toJson(request);
            slackAndLogNotification("Zomato menu Failed response:\n" + "unit:" + masterDataCache.getUnit(getUnitIdFromStoreId(partnerMenuStatus.getStoreId()))
                            .getName()
                            + "\n brand: " + masterDataCache.getBrandMetaData().get(getBrandIdFromStoreId(partnerMenuStatus.getStoreId())).getBrandName() +
                            "\n" + requestJson,
                    SlackNotification.PARTNER_MENU);
        }catch (Exception e){
            LOG.error("Error while sending notification for failed zomato menu processing : {} ", e);
        }
    }


    private void menuPushPostProcessing(ZomatoMenuResponse response, UnitMenuAddVO request, Integer employeeId) {
        if (response.getMenuResponse().getProcessed() != null && response.getMenuResponse().getProcessed()) {
            ZomatoMenuRequest menuRequest = (ZomatoMenuRequest) request.getMenuRequest();
            partnerMenuService.updatePartnerMenuAuditData(request.getKettlePartnerId(), request.getUnitId(),
                request.getBrandId(), employeeId, request.getMenuType().toString(), request.getVersion(), PARTNER_NAME, response);
            updateUnitProductMappingCache(request);
            updatePartnerUnitProductPricing(request.getKettlePartnerId(), request.getUnitId(), request.getBrandId(),
                employeeId, menuRequest);
            channelPartnerDataCache.loadUnitProductComboMappings(masterDataCache.getUnitBasicDetail(request.getUnitId()));
        } else {
            slackAndLogNotification("ERROR::: Zomato menu push response:\n" + new Gson().toJson(response));
        }
    }

    private void loadProductComboMappingsBeforeMenuPush(List<Integer> unitIdsForMenu , Integer menuPushBrandId , Integer kettlePartnerId){
        Set<Integer> priceProfileUnits = new HashSet<>();
        Integer brandId  = Objects.isNull(menuPushBrandId) ? AppConstants.CHAAYOS_BRAND_ID : menuPushBrandId;
        try{
            unitIdsForMenu.forEach(unitId ->{
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, kettlePartnerId);
                priceProfileUnits.add(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
            });
            priceProfileUnits.stream().forEach(priceProfileUnitId -> channelPartnerDataCache.
                    loadUnitProductComboMappings(masterDataCache.getUnitBasicDetail(priceProfileUnitId)));
        }catch (Exception e){
            LOG.error("Error While Loading Product Combo Mappings :: {}" , e);
        }
    }

    @Override
    public void scheduledMenuPush(List<Integer> unitIdsForMenu, MenuType menuType , Integer brandId) throws IOException {
        Integer employeeId = ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID;
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        brandId = Objects.isNull(brandId) ? AppConstants.CHAAYOS_BRAND_ID : brandId;
        loadProductComboMappingsBeforeMenuPush(unitIdsForMenu,brandId,partnerId);
        Integer brandIdForMenuPush = brandId;
        Map<Integer, Map<Integer, CafeMenuAutoPush>> menuAutoPushMap = partnerMenuService.getMapValue(unitIdsForMenu);
        List<Integer> menuPushUnitIds = new ArrayList<>();
        for (Integer unitId : unitIdsForMenu) {
            if (validatePartnerAndMapping(partnerId, unitId)) {
                masterDataCache.getUnitPartnerBrandMappingMetaData().keySet().stream().filter(key ->
                    key.getPartnerId().equals(partnerId) && key.getUnitId().equals(unitId) &&
                            brandIdForMenuPush.equals(key.getBrandId())).forEach(key -> {
                    Map<Integer, CafeMenuAutoPush> flagDetail = menuAutoPushMap.get(key.getUnitId());
                    if(Objects.nonNull(flagDetail)) {
                        CafeMenuAutoPush cafeAutoPush = flagDetail.get(key.getBrandId());
                        if (cafeAutoPush != null && Boolean.TRUE.equals(cafeAutoPush.getZomatoMenu())) {
                            UnitMenuAddVO request = createZomatoMenu(partnerId, unitId, key.getBrandId(), employeeId,
                                    menuType.toString());
                            if (partnerMenuService.checkVersionMenuAuditData(request, PARTNER_NAME)) {
                                callZomatoMenuPushAPI(request, employeeId);
                                menuPushUnitIds.add(request.getUnitId());
                            }
                        } else {
                            LOG.info(" Menu is not Push for " + PARTNER_NAME + " unit is " + key.getUnitId() + "  and brand is " + key.getBrandId());
                        }
                    }
                });
            }
        }
        if(Boolean.TRUE.equals(environmentProperties.toSendStockOutReportAfterMenuPush())){
            partnerMenuService.sendStockOutReport(menuPushUnitIds,menuType,PARTNER_NAME);
        }
    }



    @Override
    public MACRespose macRelay(MACRequest request) {
        storeMacResponse(request);
        try {
            Thread t = new Thread(() -> {
                try {
                    Date requestTime = ChannelPartnerUtils.getCurrentTimestamp();
                    List<PartnerOrderDetail> partnerOrderDetails = trackService.getPartnerOrderByPartnerOrderId(String.valueOf(request.getOrderId()));
                    MACStatusRequest requestStatus = new MACStatusRequest();
                    String cancellationUpdate = "";
                    if (partnerOrderDetails != null && partnerOrderDetails.size() > 0) {
                        for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetails) {
                            if (partnerOrderDetail != null) {
                                if(partnerOrderDetail.getKettleOrder() != null && partnerOrderDetail.getKettleOrderId() != null) {
                                    cancellationUpdate = AppConstants.REJECTED;
                                    requestStatus.setAction(cancellationUpdate);
                                    requestStatus.setOrderId(request.getOrderId());
                                } else {
                                    cancellationUpdate = AppConstants.ACCEPTED;
                                    requestStatus.setAction(cancellationUpdate);
                                    requestStatus.setOrderId(request.getOrderId());
                                }
                                try {
                                    MACStatusResponse responseStatus = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.MAC_STATUS,
                                        HttpMethod.POST, requestStatus, MACStatusResponse.class, partnerOrderDetail.getBrandId());
                                    if (responseStatus.getCode() == 200) {
                                        MACRelay macRelay = macRelayDao.findByOrderId(request.getOrderId());
                                        if (macRelay != null) {
                                            macRelay.setRequestCancellationTime(requestTime.toString());
                                            macRelay.setStatus(cancellationUpdate);
                                            macRelay.setUpdateTime(ChannelPartnerUtils.getCurrentTimeISTString());
                                            macRelayDao.save(macRelay);
                                            LOG.info("successfully updated  MAC Request details");
                                        }
                                    }
                                } catch (Exception e) {
                                    LOG.error("error while updating merchant order cancellation status:", e);
                                }
                            } else {
                                LOG.info("No order found" + request.getOrderId());
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.error("error while running  thread {}", e.getMessage());  //check
                }
            });
            t.start();
        } catch (Exception e) {
            LOG.error("error while processing mac relay::", e);
        }
        MACRespose response = new MACRespose();
        response.setCode(200);
        response.setStatus("success");
        return response;
    }

    private void storeMacResponse(MACRequest request) {
        MACRelay macRelay = macRelayDao.findByOrderId(request.getOrderId());
        if(macRelay == null) {
            MACRelay result = new MACRelay();
            result.setOrderId(request.getOrderId());
            result.setExternalOrderId(request.getExternalOrderId());
            result.setCustomerCancellationReason(request.getCustomerCancellationReason());
            result.setInitiatedAt(request.getInitiatedAt());
            result.setTimeout(request.getTimeout());
            LOG.info("updating the MAC Request details ");
            macRelayDao.save(result);
        }
    }


    private void filterBrandLevelProductsUsingPricingUnit(ZomatoUnitProductStockV3 zomatoUnitProductStock,
                                                          UnitProductsStockEvent event, UnitPartnerBrandKey key,
                                                          PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping ,
                                                          Map<Integer,Boolean> forceStockOutMap){
        UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        List<String> matchingProductsIds = new ArrayList<>();
        List<IdName> matchingProductDimensions = new ArrayList<>();
        masterDataCache.getUnitProductDetails(brandMappingData.getPriceProfileUnitId()).forEach(product -> {
            if (product.isInventoryTracked() || key.getBrandId() == AppConstants.DOHFUL_BRAND_ID ||
                    Boolean.TRUE.equals(event.getForceStockOut())) {
                List<String> matchingIds = event.getProductIds().stream().filter(s -> Integer.parseInt(s) == product.getId()).
                        collect(Collectors.toList());
                if (!matchingIds.isEmpty()) {
                    matchingProductsIds.addAll(matchingIds);
                }
                if (event.getProductDimensions() != null) {
                    List<IdName> productDimensions = event.getProductDimensions().stream().filter(idName ->
                            idName.getId() == product.getId()).collect(Collectors.toList());
                    if (!productDimensions.isEmpty()) {
                        matchingProductDimensions.addAll(productDimensions);
                    }
                }
            }
        });
        event.setProductIds(matchingProductsIds);
        event.setProductDimensions(matchingProductDimensions);
        zomatoUnitProductStock.setCatalogueVendorEntityIds(event.getProductIds());
        updateDimensionLevelStock(zomatoUnitProductStock, event, key, unitUpsellCombosMapping);
        //LOG.info("force stock out : {} " , event.getForceStockOut());
    }



    private void updateHeroComboVariantsStock(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent event,
                                              UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping ,
                                              Map<Integer,Boolean> forceStockUpdateMap){
        Map<String,List<String>> productVariantMap = unitUpsellCombosMapping.getProductVariantsMap();
        Map<String,Set<String>> splitProductMap = unitUpsellCombosMapping.getSplitProductMap();
        List<String> heroComboIds = unitUpsellCombosMapping.getProductVariantsMap().keySet().stream().filter(variantMap ->
                variantMap.contains(ChannelPartnerServiceConstants.HERO_COMBO_IDENTIFIER)).collect(Collectors.toList());
        List<String> stockOutVariantIds = CollectionUtils.isEmpty(zomatoUnitProductStock.getVariantVendorEntityIds()) ?
                new ArrayList<>() : zomatoUnitProductStock.getVariantVendorEntityIds();
        List<String> stockUpdateProductIds = CollectionUtils.isEmpty(zomatoUnitProductStock.getCatalogueVendorEntityIds()) ?
                new ArrayList<>() : zomatoUnitProductStock.getCatalogueVendorEntityIds();
        if(StockStatus.STOCK_IN.equals(event.getStatus())){
            event.getProductIds().forEach(pId -> forceStockUpdateMap.put(Integer.parseInt(pId),Boolean.TRUE));
        }
        LOG.info("hero combo Ids : {} " , heroComboIds);
        for(String heroComboId : heroComboIds){
            try{
                List<String>  variantIds = productVariantMap.get(heroComboId).stream()
                        .filter(variantId -> forceStockUpdateMap.containsKey(Integer.parseInt(variantId.split("_")[0]))).collect(Collectors.toList());
                if(variantIds.size() == productVariantMap.get(heroComboId).size()){
                    stockUpdateProductIds.add(heroComboId);
                }else if(!CollectionUtils.isEmpty(variantIds)){
                    stockOutVariantIds.addAll(variantIds);
                }
            }catch (Exception e){
                LOG.info("Exception while parsing hero combo sub item for force stock update");
            }
        }
        if(Objects.nonNull(splitProductMap) && !splitProductMap.isEmpty()){
            try{
                splitProductMap.keySet().stream().filter(splitProduct -> forceStockUpdateMap.containsKey(Integer.parseInt(splitProduct)))
                        .forEach(splitProduct -> {
                            stockUpdateProductIds.addAll(splitProductMap.get(splitProduct).stream().toList());
                        });
            }catch (Exception e){
                LOG.info("Exception while parsing split item for force stock update");
            }
        }
        if(StockStatus.STOCK_IN.equals(event.getStatus())){
            event.getProductIds().forEach(pId -> forceStockUpdateMap.remove(Integer.parseInt(pId)));
        }
        LOG.info("STOCK out variants: {]" , stockOutVariantIds);
        zomatoUnitProductStock.setCatalogueVendorEntityIds(stockUpdateProductIds);
        zomatoUnitProductStock.setVariantVendorEntityIds(stockOutVariantIds);
    }

    //Dimension level stock fundamentals for Zomato system:
    //While Stocking out mark stockouts at variant level if not all variants are stocked out
    //While stock In send product ids and relevant variant ids both
    //Case where stockin complete product and then stock out one dimension is not covered here because it is a rare case
    //Not covered case exists because you cannot stock in one dimension when complete product is stocked out,
    // you will have to stockin whole product and then stock out missing dimensions
    private void updateDimensionLevelStock(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent event,
                                           UnitPartnerBrandKey key, PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        if ((key.getBrandId().equals(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID) || Objects.equals(AppConstants.DOHFUL_BRAND_ID, key.getBrandId())) && unitUpsellCombosMapping != null &&
            unitUpsellCombosMapping.getProductVariantsMap() != null && !unitUpsellCombosMapping.getProductVariantsMap().isEmpty()) {
            List<String> productIds = new ArrayList<>();
            List<String> variantIds = new ArrayList<>();
            Map<String, InventoryInfo> inventory = webServiceHelper.callInternalApi(
                environmentProperties.getKettleServiceBasePath() + KettleServiceClientEndpoints.UNIT_INVENTORY_LIVE_WEB,
                environmentProperties.getChannelPartnerClientToken(), HttpMethod.POST, Map.class, event.getUnitId(), null);
            if (inventory != null && !inventory.keySet().isEmpty()) {
                for (String productId : event.getProductIds()) {
                    Product product = masterDataCache.getProduct(Integer.valueOf(productId));
                    if((Objects.nonNull(product) && ChannelPartnerServiceConstants.COMBO.equalsIgnoreCase(product.getTaxCode()))
                            || Boolean.TRUE.equals(event.getForceStockOut())){
                        productIds.add(productId);
                        continue;
                    }
                    InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                    LOG.info("Inventory Info For Product Id : {} :::: {} ",productId,new Gson().toJson(inventoryInfo));
                    if (inventoryInfo.getDim() == null || inventoryInfo.getDim().isEmpty()) {
                        productIds.add(productId);
                    } else {
                        addDimensionsWithMatchingStockState(inventoryInfo, event, productIds, variantIds, productId,
                            unitUpsellCombosMapping);
                    }
                }
            }
            event.setProductIds(productIds);
            zomatoUnitProductStock.setCatalogueVendorEntityIds(productIds);
            zomatoUnitProductStock.setVariantVendorEntityIds(variantIds);
        }
    }

    private void addDimensionsWithMatchingStockState(InventoryInfo inventoryInfo, UnitProductsStockEvent event, List<String> productIds,
                                                     List<String> variantIds, String productId,
                                                     PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping) {
        List<String> dimensions = new ArrayList<>();
        if (StockStatus.STOCK_IN.equals(event.getStatus())) {
            productIds.add(productId);
            if (everyDimensionInSameStockState(inventoryInfo)) {
                dimensions.addAll(inventoryInfo.getDim().keySet());
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() > 0)
                    .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        } else {
            // Updating Inventory to cancel out the diff between inventory and event (Only for stock out event)
            updatingInventoryAsPerEvent(inventoryInfo,event);
            if (everyDimensionInSameStockState(inventoryInfo)) {
                productIds.add(productId);
            } else {
                inventoryInfo.getDim().entrySet().stream().filter(dimEntry -> dimEntry.getValue() <= 0)
                    .forEach(dimEntry -> dimensions.add(dimEntry.getKey()));
            }
        }
        dimensions.forEach(dimension -> {
            if (unitUpsellCombosMapping.getProductVariantsMap().containsKey(productId)) {
                List<String> ids = unitUpsellCombosMapping.getProductVariantsMap().get(productId).
                    stream().filter(variantVendorEntityId ->
                        variantVendorEntityId.toLowerCase().contains(dimension.toLowerCase())
                    ).collect(Collectors.toList());
                variantIds.addAll(ids);
            }
        });
    }

    private void updatingInventoryAsPerEvent(InventoryInfo inventoryInfo, UnitProductsStockEvent event) {
        //Creating a Set for quick lookup of product names that match the id
        try {
            if(!org.springframework.util.CollectionUtils.isEmpty(inventoryInfo.getDim()) && !org.springframework.util.CollectionUtils.isEmpty(event.getProductDimensions())) {
                Set<String> eventProductNames = event.getProductDimensions().stream()
                        .filter(eventProduct -> eventProduct.getId() == inventoryInfo.getId())
                        .map(eventProduct -> eventProduct.getName().toLowerCase())
                        .collect(Collectors.toSet());
                inventoryInfo.getDim().replaceAll((key, value) ->
                        eventProductNames.contains(key.toLowerCase()) ? 0 : value);
            }
        }catch (Exception e){
            LOG.info("Error while updating dimension level stock in inventory::::: {}",e);
        }
    }

    private boolean everyDimensionInSameStockState(InventoryInfo inventoryInfo) {
        if (inventoryInfo.getDim().keySet().size() == 1) {
            return true;
        }
        Iterator<Map.Entry<String, Integer>> itr = inventoryInfo.getDim().entrySet().iterator();
        Boolean initialStatus = itr.next().getValue() > 0;
        while (itr.hasNext()) {
            boolean currentStatus = itr.next().getValue() > 0;
            if (currentStatus != Boolean.TRUE.equals(initialStatus)) {
                return false;
            }
        }
        return true;
    }

    private void updatePartnerUnitProductPricing(Integer kettlePartnerId, Integer unitId, Integer brandId,
                                                 Integer employeeId, ZomatoMenuRequest menuRequest) {
        Map<Integer, Map<String, BigDecimal>> pricing = new HashMap<>();
        Integer pricingUnit = masterDataCache.getUnitPartnerBrandMappingMetaData().get(new UnitPartnerBrandKey(unitId, brandId,
            kettlePartnerId)).getPriceProfileUnitId();
        Collection<Product> products = masterDataCache.getUnitProductDetails(pricingUnit);
        Map<Integer, Product> productMap = new HashMap<>();
        products.forEach(product -> productMap.put(product.getId(), product));
        menuRequest.getMenu().getCatalogues().forEach(zomatoCatalogues -> {
            Integer productId = zomatoCatalogues.getVendorEntityId().contains("_") ?
                Integer.parseInt(zomatoCatalogues.getVendorEntityId().split("_")[0]) :
                Integer.parseInt(zomatoCatalogues.getVendorEntityId());
            if (masterDataCache.getProduct(productId) != null &&
                ProductClassification.MENU.equals(masterDataCache.getProduct(productId).getClassification())) {
                if (!pricing.containsKey(productId)) {
                    pricing.put(productId, new HashMap<>());
                }
                findDimensionAndAddPrice(zomatoCatalogues, productMap, pricing, productId);
            }
        });
        PartnerUnitProductPricingDetail pricingDetail = partnerMenuService.updatePartnerUnitProductPricing(kettlePartnerId,
            unitId, brandId, employeeId, pricing, PARTNER_NAME);
        channelPartnerDataCache.loadPartnerUnitProductPricingMap(pricingDetail);
    }

    private void findDimensionAndAddPrice(ZomatoCatalogues zomatoCatalogues, Map<Integer, Product> productMap,
                                          Map<Integer, Map<String, BigDecimal>> pricing, Integer productId) {
        boolean dimensionFound = false;
        if (zomatoCatalogues.getVariants() != null && !zomatoCatalogues.getVariants().isEmpty()) {
            List<String> dimensionIdentifiers = new ArrayList<>();
            dimensionFound = zomatoCatalogues.getProperties().stream().anyMatch(zomatoCatalogueProperties ->
                zomatoCatalogueProperties.getName().trim().equalsIgnoreCase("size"));
            zomatoCatalogues.getProperties().stream().filter(zomatoCatalogueProperties ->
                    zomatoCatalogueProperties.getName().trim().equalsIgnoreCase("size"))
                .forEach(zomatoCatalogueProperties ->
                    zomatoCatalogueProperties.getPropertyValues().forEach(zomatoCatPropertyVal ->
                        dimensionIdentifiers.add(zomatoCatPropertyVal.getVendorEntityId())
                    )
                );
            dimensionIdentifiers.forEach(s -> {
                for (ZomatoCatalogueVariant zomatoCatalogueVariant : zomatoCatalogues.getVariants()) {
                    if (zomatoCatalogueVariant.getVendorEntityId().contains(s)) {
                        String dimension = s.split("_")[1];
                        pricing.get(productId).put(dimension, BigDecimal.valueOf(zomatoCatalogueVariant.
                            getPrices().get(0).getPrice()));
                        break;
                    }
                }
            });
        }
        if (!dimensionFound) {
            BigDecimal price = BigDecimal.valueOf(zomatoCatalogues.getVariants().get(0).getPrices().get(0).getPrice());
            if (productMap.containsKey(productId)) {
                if(productMap.get(productId).getPrices().size() == 1) {
                    pricing.get(productId).put(productMap.get(productId).getPrices().get(0).getDimension(), price);
                } else {
                    productMap.get(productId).getPrices().forEach(productPrice -> {
                        if (productPrice.getPrice().compareTo(price) == 0) {
                            pricing.get(productId).put(productPrice.getDimension(), price);
                        }
                    });
                }
            }
        }
    }

    private void slackAndLogNotification(String message) {
        slackAndLogNotification(message, SlackNotification.PARTNER_INTEGRATION);
    }


    public void slackAndLogNotification(String message, SlackNotification channel) {
        SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
            ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
            message);
        LOG.error(message);
    }

    @Override
    public boolean updateCafeDeliveryTimeZomato(CafeTimingChangeRequest cafeTimingChangeRequest){
        String [] daysOfWeek = cafeTimingChangeRequest.getDayOfWeekText().split(",");
        ZomatoDeliveryTimingUpdate deliverTimeUpdate = new ZomatoDeliveryTimingUpdate();
//        deliverTimeUpdate.setData(new ArrayList<>());
        ArrayList<ZomatoTimings>  timingList= new ArrayList<>();
        for(String days : daysOfWeek){
            ZomatoTimings obj = new ZomatoTimings();
            obj.setDay(days);
            obj.setSlots(new ArrayList<>());
            ZomatoSlots slot = new ZomatoSlots();
            slot.setFrom(cafeTimingChangeRequest.getDeliveryOpeningTime());
            slot.setTo(cafeTimingChangeRequest.getDeliveryClosingTime());
            obj.getSlots().add(slot);
            obj.setActive(true);
            timingList.add(obj);
        }
       deliverTimeUpdate.setTimings(timingList);
        Integer zomatoId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        UnitPartnerBrandKey unitPartnerBrandKey = new UnitPartnerBrandKey();
        unitPartnerBrandKey.setUnitId(cafeTimingChangeRequest.getUnitId());
        unitPartnerBrandKey.setBrandId(cafeTimingChangeRequest.getBrandId()!=null?cafeTimingChangeRequest.getBrandId():1);
        unitPartnerBrandKey.setPartnerId(zomatoId);
        String outletid = masterDataCache.getUnitPartnerBrandMappingMetaData().get(unitPartnerBrandKey).getRestaurantId();
        deliverTimeUpdate.setOutlet_id(outletid);
        ZomatoNotificationResponse response;
        String request = new Gson().toJson(deliverTimeUpdate);
        LOG.info("Value of Zomato Request Body::   ",request);
        try{
            response = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.UPDATE_CAFE_DELIVERY_TIME, HttpMethod.POST, deliverTimeUpdate, ZomatoNotificationResponse.class,unitPartnerBrandKey.getBrandId() );
            LOG.info("Called updateCafeDeliveryTimeZomato for Unit:::::{}",cafeTimingChangeRequest.getUnitId());

            if (response == null || response.getCode() != 200) {
                slackAndLogNotification("Zomato Update Delivery Timing API returned incorrect response:" + new Gson().toJson(response)
                        + "\n request: " + new Gson().toJson(request));
            }
            if(response.getCode()==200) {
                LOG.info("Successfully  Updated Delivery timing for unit:{}",cafeTimingChangeRequest.getUnitId());
                LOG.info("Zomato Response Message ::{}",response.getMessage());
                return true;
            }
        } catch (HttpStatusCodeException e) {
            String errorMessage = "Error Updating Delivery Timing for Zomato for unit:"+ cafeTimingChangeRequest.getUnitId();
            LOG.info(errorMessage);
            catchAndLogZomatoException(e,errorMessage );
        } catch (Exception e) {
            catchAndLogException(e, "Error Updating Delivery Timing for Zomato");
        }
        return false;
    }

    @Override
    public void uploadZomatoOrderRating() {
        if(AppUtils.isProd(environmentProperties.getEnvType())) {
            List<ZomatoOrderRating> zomatoOrderRatingList = new ArrayList<>();
            List<UnitBasicDetail> unitBasicDetailList = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : unitBasicDetailList) {
                Collection<UnitPartnerBrandMappingData> unitPartnerBrandMappingDataList = masterDataCache.getUnitwisePartnerBrandMappingMetaData(unitBasicDetail.getId());
                unitPartnerBrandMappingDataList.forEach(unitPartnerBrandMappingData -> {
                    if (unitPartnerBrandMappingData.getPartnerId() == AppConstants.CHANNEL_PARTNER_ZOMATO) {
                        ZomatoRatingRequest zomatoRatingRequest = new ZomatoRatingRequest();
                        zomatoRatingRequest.setOutletId(unitPartnerBrandMappingData.getRestaurantId());
                        zomatoRatingRequest.setDate(AppUtils.getPreviousDateFormatted());
                        getZomatoOrderRatingList(zomatoRatingRequest, zomatoOrderRatingList);
                    }
                });
            }
            if (zomatoOrderRatingList.size() > 0 && !zomatoOrderRatingList.isEmpty()) {
                zomatoOrderRatingDao.saveAll(zomatoOrderRatingList);
            }
        }
    }

    @Override
    public boolean getZomatoOutletOrderRating(ZomatoRatingRequest zomatoRatingRequest) {
        if(AppUtils.isProd(environmentProperties.getEnvType())) {
            List<ZomatoOrderRating> zomatoOrderRatingList = new ArrayList<>();
            getZomatoOrderRatingList(zomatoRatingRequest, zomatoOrderRatingList);
            if (zomatoOrderRatingList.size() > 0 && !zomatoOrderRatingList.isEmpty()) {
                zomatoOrderRatingDao.saveAll(zomatoOrderRatingList);
            }
        }
        return true;
    }

    public void getZomatoOrderRatingList(ZomatoRatingRequest zomatoRatingRequest, List<ZomatoOrderRating> zomatoOrderRatingList) {
        String request = new Gson().toJson(zomatoRatingRequest);
        ZomatoRatingsDataResponse response;
        LOG.info("Value of Zomato Request Body:: {}", request);
        try {
            response = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.GET_ORDER_RATING, HttpMethod.POST, zomatoRatingRequest, ZomatoRatingsDataResponse.class, 1);
            LOG.info("Called updateCafeDeliveryTimeZomato for Unit:::::{}", zomatoRatingRequest.getOutletId());
            if (Objects.isNull(response) || response.getCode() != 200) {
                LOG.info("Zomato Order rating API returned incorrect response: {} \n request: {}" , new Gson().toJson(response),new Gson().toJson(request));
            }
            if (response.getCode().equals(200)) {
                LOG.info("Successfully Fetched Order Ratings for unit:{}", zomatoRatingRequest.getOutletId());
                LOG.info("Zomato Response Message ::{}", response.getMessage());
                LOG.info("Zomato Order rating API returned {} \n request: {}", new Gson().toJson(request),new Gson().toJson(response));
                if (response.getData().size() > 0 && !response.getData().isEmpty()) {
                    for (ZomatoRatingsData zomatoRatingsData : response.getData()) {
                        ZomatoOrderRating zomatoOrderRating = new ZomatoOrderRating();
                        zomatoOrderRating.setOrderId(zomatoRatingsData.getOrderId());
                        zomatoOrderRating.setRating(zomatoRatingsData.getUserRating());
                        zomatoOrderRating.setOutletId(zomatoRatingRequest.getOutletId());
                        zomatoOrderRating.setLastUpdatedTime(AppUtils.getCurrentTimestamp().toString());
                        zomatoOrderRatingList.add(zomatoOrderRating);
                    }
                }
            }
        } catch (HttpStatusCodeException e) {
            String errorMessage = "Error Fetching Order Ratings for unit:" + zomatoRatingRequest.getOutletId();
            LOG.info(errorMessage);
            catchAndLogZomatoException(e, errorMessage);
        } catch (Exception e) {
            catchAndLogException(e, "Fetched Order Ratings for Zomato");
        }
    }

    @Override
    public void updateAllZomatoCafeTimings(){
        Set<Integer> unitIdSet = masterDataCache.getUnits().keySet();
        for(Integer outletId : unitIdSet) {
            updateZomatoCafeTimings(outletId,BusinessHourEvent.ENABLE_BUSINESS_HOURS.value(),null);
        }
    }

    @Override
    public ZomatoFetchOrderResponse fetchOrderStatus(String zomatoOrderId){
       List<PartnerOrderDetail> partnerOrderDetailList = trackService.
               getPartnerOrderByPartnerOrderId(zomatoOrderId);
       List<PartnerOrderStatus> validStatuses = new ArrayList<>(Arrays.asList(PartnerOrderStatus.PLACED,
               PartnerOrderStatus.RESOLVED));
       ZomatoFetchOrderResponse zomatoFetchOrderResponse;
       if(!CollectionUtils.isEmpty(partnerOrderDetailList)){
           Integer size = partnerOrderDetailList.size();
           if(validStatuses.contains(partnerOrderDetailList.get(size-1).getPartnerOrderStatus())){
               return ZomatoFetchOrderResponse.builder().status(ZomatoOrderStatus.SUCCESS.getStatus())
                       .orderStatus(FetchOrderStatus.CONFIRMED.getStatus())
                       .code(200).build();
           }
       }
        return ZomatoFetchOrderResponse.builder().status(ZomatoOrderStatus.SUCCESS.getStatus())
                .orderStatus(FetchOrderStatus.UNACKNOWLEDGED.getStatus())
                .code(200).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateZomatoCafeTimings(Integer outletId,String event,List<UnitHours> oldBusinessHours) {
        try {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
            if (Objects.nonNull(outletId)) {
                List<Integer> brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID, AppConstants.GNT_BRAND_ID);
                for (Integer brand : brandId) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(outletId, brand, partnerId);
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
                    if (Objects.nonNull(unitPartnerBrandMappingData) &&
                            AppConstants.ACTIVE.equals(unitPartnerBrandMappingData.getStatus())) {
                        Integer unitId = unitPartnerBrandMappingData.getUnitId();
                        Unit unit = masterDataCache.getUnit(unitId);
                        if (UnitCategory.CAFE.equals(unit.getFamily()) && unitPartnerBrandMappingData.getPartnerId().equals(partnerId) &&
                                unit.isLive() && UnitStatus.ACTIVE.equals(unit.getStatus())) {
                            List<UnitHours> unitHours = unit.getOperationalHours();
                            CafeTimings cafeTimings = CafeTimings.builder().build();
                            cafeTimings.setOutlet_id(unitPartnerBrandMappingData.getRestaurantId());
                            cafeTimings.setActive(true);
                            Map<Integer,Timings> timingsMap = new HashMap<>();
                            for(UnitHours hours : unitHours){
                                List<Slots> slotsList = new ArrayList<>();
                                int openingHour= Integer.parseInt(AppUtils.convertToHourMinute(hours.getDeliveryOpeningTime()).substring(0,2));
                                int closingHour = Integer.parseInt(AppUtils.convertToHourMinute(hours.getDeliveryClosingTime()).substring(0,2));
                                Slots slotForDay = null;
                                Slots slotForNextday = null;
                                if(openingHour>=closingHour){
                                    slotForDay = new Slots();
                                    slotForNextday = new Slots();
                                    slotForDay.setFrom(roundToNearest15Minutes(hours.getDeliveryOpeningTime().toLocalTime()).toString());
                                    slotForDay.setTo("00:00");
                                    slotForNextday.setFrom("00:00");
                                    slotForNextday.setTo(roundToNearest15Minutes(hours.getDeliveryClosingTime().toLocalTime()).toString());
                                    slotsList.add(slotForDay);
                                }
                                else{
                                    slotForDay = new Slots();
                                    slotForDay.setFrom(roundToNearest15Minutes(hours.getDeliveryOpeningTime().toLocalTime()).toString());
                                    slotForDay.setTo(roundToNearest15Minutes(hours.getDeliveryClosingTime().toLocalTime()).toString());
                                    slotsList.add(slotForDay);
                                }
                                if(!timingsMap.containsKey(hours.getDayOfTheWeekNumber())){
                                    Timings time = new Timings();
                                    time.setDay(hours.getDayOfTheWeek());
                                    time.setSlots(slotsList);
                                    timingsMap.put(hours.getDayOfTheWeekNumber(),time);
                                }
                                else{
                                    timingsMap.get(hours.getDayOfTheWeekNumber()).getSlots().add(slotForDay);
                                }
                                if(Objects.nonNull(slotForNextday)){
                                    int weekNumber = hours.getDayOfTheWeekNumber() + 1;
                                    if(weekNumber == 8){
                                        weekNumber = 1;
                                    }
                                    if(!timingsMap.containsKey(weekNumber)){
                                        Timings time = new Timings();
                                        List<Slots> slotListForNextDay = new ArrayList<>();
                                        slotListForNextDay.add(slotForNextday);
                                        time.setDay(WEEK_DAYS.get(hours.getDayOfTheWeekNumber()));
                                        time.setSlots(slotListForNextDay);
                                        timingsMap.put(weekNumber,time);
                                    }
                                    else{
                                        timingsMap.get(weekNumber).getSlots().add(slotForNextday);
                                    }
                                }
                            }
                            List<Timings> timeList = new ArrayList<>();
                            for(Timings time:timingsMap.values()){
                                timeList.add(time);
                            }
                            cafeTimings.setTimings(timeList);
                           return updateCafeTimings(cafeTimings, unitPartnerBrandMappingData,unitHours,oldBusinessHours,event,unit);
                        }
                    }
                }
            }

        } catch (Exception e) {
            LOG.error("Exception Caught While Checking Cafe Status for Unit " + outletId, e);
        }
        return false;
    }

    public static LocalTime roundToNearest15Minutes(LocalTime time) {
        int minute = time.getMinute();
        int roundedMinute = Math.round((float) minute / 15) * 15;

        if (roundedMinute == 60) {
            return time.plusHours(1).withMinute(0).withSecond(0).withNano(0);
        } else {
            return time.withMinute(roundedMinute).withSecond(0).withNano(0);
        }
    }



    public boolean updateCafeTimings(CafeTimings cafeTimings,UnitPartnerBrandMappingData unitPartnerBrandMappingData,List<UnitHours> unitHours,List<UnitHours> oldBusinessHours,
                                     String event,Unit unit) {
        ZomatoNotificationResponse response;
        String responseStatus = "FAILURE";
        String request = new Gson().toJson(cafeTimings);
        LOG.info("Value of Zomato Request Body::   ",request);
        try{
            response = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.UPDATE_CAFE_TIMINGS, HttpMethod.POST, cafeTimings, ZomatoNotificationResponse.class,unitPartnerBrandMappingData.getBrandId());
            responseStatus = response.getStatus();
            LOG.info("Called updateZomatoCafeTimings for Unit:::::{}",cafeTimings.getOutlet_id());

            if (response == null || response.getCode() != 200) {
                slackAndLogNotification("Zomato Update Cafe Timings API returned incorrect response:" + new Gson().toJson(response)
                        + "\n request: " + new Gson().toJson(request));
                sendZomatoBussinessHourChangeMail(unitHours,responseStatus,unit.getName() ,
                        PARTNER_NAME,unitPartnerBrandMappingData.getBrandId(),oldBusinessHours,event);
            }
            if(response.getCode()==200) {
                LOG.info("Successfully  Updated Delivery timing for unit:{}",cafeTimings.getOutlet_id());
                LOG.info("Zomato Response Message ::{}",response.getMessage());
                sendZomatoBussinessHourChangeMail(unitHours,responseStatus,unit.getName() ,
                        PARTNER_NAME,unitPartnerBrandMappingData.getBrandId(),oldBusinessHours,event);
                return true;
            }
        } catch (HttpStatusCodeException e) {
            String errorMessage = "Error Updating Cafe Timing for Zomato for unit:"+ cafeTimings.getOutlet_id();
            LOG.info(errorMessage);
            sendZomatoBussinessHourChangeMail(unitHours,responseStatus,unit.getName() ,
                    PARTNER_NAME,unitPartnerBrandMappingData.getBrandId(),oldBusinessHours,event);
            catchAndLogZomatoException(e,errorMessage );
        } catch (Exception e) {
            catchAndLogException(e, "Error Updating Cafe Timing for Zomato");
        }
        return false;
    }

	public void sendZomatoBussinessHourChangeMail(List<UnitHours> unitHours , String zomatoNotificationResponse ,
                                                  String unitName, String partnerName, Integer brandId,List<UnitHours> oldBusinessHours,
                                                  String event){
        List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
		try {
            if(Objects.isNull(oldBusinessHours)) {
                ZomatoBusinessHourChangeEmailTemplate template = new ZomatoBusinessHourChangeEmailTemplate(unitHours, zomatoNotificationResponse,
                        unitName, partnerName, brandId, environmentProperties.getBasePath(), event);
                ZomatoBussinessHourChange zomatoBussinessHourChange = new ZomatoBussinessHourChange(unitHours, zomatoNotificationResponse,
                        unitName, partnerName, brandId, toEmails, "Zomato Business Hours", environmentProperties.getEnvType(), template,null);
                zomatoBussinessHourChange.sendEmail();
            }
            else{
                ZomatoBusinessHourChangeOnUpdateEmailTemplate template = new ZomatoBusinessHourChangeOnUpdateEmailTemplate(unitHours, zomatoNotificationResponse,
                        unitName, partnerName, brandId, environmentProperties.getBasePath(), oldBusinessHours, event);
                ZomatoBussinessHourChange zomatoBussinessHourChange = new ZomatoBussinessHourChange(unitHours, zomatoNotificationResponse,
                        unitName, partnerName, brandId, toEmails, "Zomato Business Hours", environmentProperties.getEnvType(), null,template);
                zomatoBussinessHourChange.sendEmail();
            }

		}catch (Exception e){
			LOG.info("Error while Sending Email For Zomato Business Hours Updation for Unit : {} and Partner : {}",unitName,partnerName);
            LOG.info("EXCEPTION : {}" , e);
        }
    }


    public String changeSlots(Time time){
        int minutes =AppUtils.convertHourToMinute(time);
        int remainder = minutes%15;
        String updatedTime;
        if(remainder!=0) {
            int newTime = minutes - remainder;
            int hour = newTime / 60;
            int minute = newTime % 60;
            int newUpdatedTime = hour + minute;
            updatedTime = AppUtils.convertMinuteToHour(newUpdatedTime);
        }
        else {
            updatedTime = AppUtils.convertMinuteToHour(minutes);
        }
         return updatedTime;
    }

    public ZomatoOrderResponse getZomatoDuplicateOrderResponse(ZomatoOrderResponse zomatoOrderResponse,String orderId){
        LOG.info("ZOMATO duplicate order received for order id:::::::::::::::::::::::::::::: {}", orderId);
        zomatoOrderResponse.setCode(500);
        zomatoOrderResponse.setExternalOrderId(orderId);
        zomatoOrderResponse.setMessage("Duplicate order");
        zomatoOrderResponse.setStatus(ZomatoOrderStatus.FAILED);
        if (environmentProperties.slackDuplicateOrders()) {
            String message = ChannelPartnerUtils.getMessage("Zomato duplicate order received", ":::::For Request OrderId:::::" + orderId);
            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION,
                    message);
        }
        return zomatoOrderResponse;
    }

    public void addRecipeVariantsToStockEvent(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent stockEvent,
                                              Map<String, Set<String>> recipeVariantMap) {
        Set<String> variantIds = new HashSet<>();
        if (StockStatus.STOCK_IN.equals(stockEvent.getStatus()) && Objects.nonNull(recipeVariantMap)) {
            for (String productId : stockEvent.getProductIds()) {
                if (recipeVariantMap.containsKey(productId)) {
                    variantIds.addAll(recipeVariantMap.get(productId));
                }
            }
            LOG.info("Recipe Variants added in the stock request ::::: {}",new Gson().toJson(variantIds));
        }
        zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(variantIds);
    }


    public void addSplitProductToStockEvent(UnitPartnerBrandKey key, ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitProductsStockEvent stockEvent,
                                              Map<String, Set<String>> splitProductMap) {
        if (key.getBrandId().equals(ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID) && Objects.nonNull(splitProductMap)) {
            Set<String> entityIds = stockEvent.getProductDimensions().stream()
                    .filter(idName -> splitProductMap.containsKey(String.valueOf(idName.getId())) && Objects.nonNull(idName.getName()))
                    .flatMap(idName -> splitProductMap.get(String.valueOf(idName.getId())).stream()
                            .filter(splitProduct -> splitProduct.toLowerCase().contains(idName.getName().toLowerCase())))
                    .collect(Collectors.toSet());
            LOG.info("Entity ids added for split products :::: {}", new Gson().toJson(entityIds));
            zomatoUnitProductStock.getCatalogueVendorEntityIds().addAll(entityIds);
        }
    }

    @Override
    public ZomatoNotificationResponse rejectOrder(ZomatoOrderRejectRequest request, Integer brandId, Integer rejectedBy) {
        ZomatoNotificationResponse res = new ZomatoNotificationResponse();
        try {
            res = webServiceHelper.callZomatoApi(environmentProperties, ZomatoServiceEndpoints.REJECT_ORDER,
                    HttpMethod.POST, request, ZomatoNotificationResponse.class, brandId);
            if ("success".equals(res.getStatus())) {
                ZomatoOrderStatusRequest zomatoOrderStatusRequest = new ZomatoOrderStatusRequest();
                zomatoOrderStatusRequest.setOrderId(request.getOrder_id());
                zomatoOrderStatusRequest.setAction("UNIT_REJECTION");
                zomatoOrderStatusRequest.setReason(ZomatoRejectionCodes.getAllReasons().get(request.getRejection_message_id()));
                updateZomatoOrderStatus(zomatoOrderStatusRequest, rejectedBy);
            }
        } catch (Exception e) {
            LOG.info("Exception occurred while Rejecting Zomato Order: {}", e.getMessage());
        }
        return res;
    }

    @Override
    public Map<Integer, String> getRejectionReasons() {
        return ZomatoRejectionCodes.getAllReasons();
    }

    @Override
    public Boolean routeComplaintRequestToN8N(ZomatoOrderComplaint request) {
        try {
            webServiceHelper.postWithAuth(environmentProperties.getN8NWebhookPath(),"test"
                    ,  request,
                     ZomatoOrderComplaint.class);
           return true;
        } catch (Exception e) {
            LOG.info("Exception occurred while routing complaint request to N8N: {}", e.getMessage());
        }
        return false;
    }
}


