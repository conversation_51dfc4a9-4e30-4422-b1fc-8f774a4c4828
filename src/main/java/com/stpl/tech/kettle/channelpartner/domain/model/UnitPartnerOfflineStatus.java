package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.util.AppUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class UnitPartnerOfflineStatus {
    private Integer unitId;
    private String unitName;
    private Integer brandId;
    private String brandName;
    private List<PartnerOfflineDetails> partnerStatus;
    private Date latestUpdationDate;

    public UnitPartnerOfflineStatus() {
    }

    public UnitPartnerOfflineStatus(Integer unitId, String unitName, Integer brandId, String brandName,
                                    List<PartnerOfflineDetails> partnerStatus, Date latestUpdationDate) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.brandId = brandId;
        this.brandName = brandName;
        this.partnerStatus = partnerStatus;
        this.latestUpdationDate = latestUpdationDate;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public List<PartnerOfflineDetails> getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(List<PartnerOfflineDetails> partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public Date getLatestUpdationDate() {
        return this.latestUpdationDate;
    }

    public void setLatestUpdationDate(String latestUpdationDate) {
        this.latestUpdationDate = AppUtils.parseDate(latestUpdationDate, new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy"));

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitPartnerOfflineStatus that = (UnitPartnerOfflineStatus) o;
        return unitId.equals(that.unitId) && Objects.equals(unitName, that.unitName) && brandId.equals(that.brandId) &&
                Objects.equals(brandName, that.brandName) && partnerStatus.equals(that.partnerStatus) &&
                latestUpdationDate.equals(that.latestUpdationDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitId, unitName, brandId, brandName, partnerStatus, latestUpdationDate);
    }
}
