package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "discount_name",
        "discount_type",
        "discount_category",
        "discount_value",
        "discount_amount",
        "voucher_code",
        "discount_is_taxed",
        "discount_applied_on",
        "is_zomato_discount",
        "is_delivery_charge_discount"
        
})
public class ZomatoOrderDiscountV3 {

    @JsonProperty("discount_name")
    private String discountName;
    @JsonProperty("discount_type")
    private String discountType;
    @JsonProperty("discount_category")
    private String discountCategory;
    @JsonProperty("discount_value")
    private Float discountValue;
    @JsonProperty("discount_amount")
    private Float discountAmount;
    @JsonProperty("discount_is_taxed")
    private Boolean discountIsTaxed;
    @JsonProperty("discount_applied_on")
    private Float discountAppliedOn;
    @JsonProperty("is_zomato_discount")
    private Integer isZomatoDiscount;
    @JsonProperty("is_delivery_charge_discount")
    private Integer isDeliveryChargeDiscount;
    @JsonProperty("voucher_code")
    private String voucherCode;

    @JsonProperty("discount_name")
    public String getDiscountName() {
        return discountName;
    }

    @JsonProperty("discount_name")
    public void setDiscountName(String discountName) {
        this.discountName = discountName;
    }

    @JsonProperty("discount_type")
    public String getDiscountType() {
        return discountType;
    }

    @JsonProperty("discount_type")
    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    @JsonProperty("discount_category")
    public String getDiscountCategory() {
        return discountCategory;
    }

    @JsonProperty("discount_category")
    public void setDiscountCategory(String discountCategory) {
        this.discountCategory = discountCategory;
    }

    @JsonProperty("discount_value")
    public Float getDiscountValue() {
        return discountValue;
    }

    @JsonProperty("discount_value")
    public void setDiscountValue(Float discountValue) {
        this.discountValue = discountValue;
    }

    @JsonProperty("discount_amount")
    public Float getDiscountAmount() {
        return discountAmount;
    }

    @JsonProperty("discount_amount")
    public void setDiscountAmount(Float discountAmount) {
        this.discountAmount = discountAmount;
    }

    @JsonProperty("discount_is_taxed")
    public Boolean getDiscountIsTaxed() {
        return discountIsTaxed;
    }

    @JsonProperty("discount_is_taxed")
    public void setDiscountIsTaxed(Boolean discountIsTaxed) {
        this.discountIsTaxed = discountIsTaxed;
    }

    @JsonProperty("discount_applied_on")
    public Float getDiscountAppliedOn() {
        return discountAppliedOn;
    }

    @JsonProperty("discount_applied_on")
    public void setDiscountAppliedOn(Float discountAppliedOn) {
        this.discountAppliedOn = discountAppliedOn;
    }

    @JsonProperty("is_zomato_discount")
    public Integer getIsZomatoDiscount() {
        return isZomatoDiscount;
    }

    @JsonProperty("is_zomato_discount")
    public void setIsZomatoDiscount(Integer isZomatoDiscount) {
        this.isZomatoDiscount = isZomatoDiscount;
    }

    @JsonProperty("is_delivery_charge_discount")
    public Integer getIsDeliveryChargeDiscount() {
        return isDeliveryChargeDiscount;
    }

    @JsonProperty("is_delivery_charge_discount")
    public void setIsDeliveryChargeDiscount(Integer isDeliveryChargeDiscount) {
        this.isDeliveryChargeDiscount = isDeliveryChargeDiscount;
    }

    @JsonProperty("voucher_code")
    public String getVoucherCode() {
        return voucherCode;
    }

    @JsonProperty("voucher_code")
    public void setVoucherCode(String voucherCode) {
        this.voucherCode = voucherCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("discountName", discountName).append("discountType", discountType).append("discountCategory", discountCategory).append("discountValue", discountValue).append("discountAmount", discountAmount).append("discountIsTaxed", discountIsTaxed).append("voucherCode", voucherCode).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(discountType).append(discountCategory).append(discountName).append(discountIsTaxed).append(discountValue).append(discountAmount).append(voucherCode).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderDiscountV3) == false) {
            return false;
        }
        ZomatoOrderDiscountV3 rhs = ((ZomatoOrderDiscountV3) other);
        return new EqualsBuilder().append(discountType, rhs.discountType).append(discountCategory, rhs.discountCategory).append(discountName, rhs.discountName).append(discountIsTaxed, rhs.discountIsTaxed).append(discountValue, rhs.discountValue).append(discountAmount, rhs.discountAmount).append(voucherCode, rhs.voucherCode).isEquals();
    }

}
