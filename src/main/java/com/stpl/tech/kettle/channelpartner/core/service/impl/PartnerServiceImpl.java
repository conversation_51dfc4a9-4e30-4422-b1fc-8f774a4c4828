package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerCafeStatusHistoryDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;

import javax.mail.Part;

@Service
public class PartnerServiceImpl implements PartnerService {

    @Autowired
    private PartnerOrderDao partnerOrderDao;


    @Autowired
    private PartnerDao partnerDao;
    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerCafeStatusHistoryDao partnerCafeStatusHistoryDao;


    @Override
    public List<PartnerDetail> getPartners() {
        return partnerDao.findAll();
    }

    @Override
    public List<PartnerDetail> getAllPartners() {
        return partnerDao.findAll();
    }

    @Override
    public List<PartnerDetail> getActivePartners() {
        return new ArrayList<>(channelPartnerDataCache.getPartnerCache().values());
    }

    @Override
    public PartnerDetail addPartner(PartnerDetail partnerDetail) {
        if (partnerDetail.getPartnerName() != null && !partnerDetail.getPartnerName().trim().isEmpty()
            && partnerDetail.getPartnerAuthKey() != null) {
            partnerDetail = partnerDao.insert(partnerDetail);
            if (partnerDetail != null) {
                channelPartnerDataCache.clearPartnerCache();
                channelPartnerDataCache.initPartnerCache();
                return partnerDetail;
            }
        }
        return null;
    }

    @Override
    public PartnerDetail activatePartner(String partnerId) {
        if (partnerId != null) {
            Optional<PartnerDetail> partnerDetailData = partnerDao.findById(partnerId);
            if (partnerDetailData.isPresent()) {
            	PartnerDetail partnerDetail = partnerDetailData.get();
                partnerDetail .setPartnerStatus(PartnerStatus.ACTIVE);
                partnerDetail = partnerDao.save(partnerDetail);
                if (partnerDetail != null) {
                    return partnerDetail;
                }
            }
        }
        return null;
    }

    @Override
    public PartnerDetail deactivatePartner(String partnerId) {
        if (partnerId != null) {
            Optional<PartnerDetail> partnerDetailData = partnerDao.findById(partnerId);
            if (partnerDetailData.isPresent()) {
            	PartnerDetail partnerDetail =  partnerDetailData.get();
                partnerDetail.setPartnerStatus(PartnerStatus.IN_ACTIVE);
                partnerDetail = partnerDao.save(partnerDetail);
                if (partnerDetail != null) {
                    return partnerDetail;
                }
            }
        }
        return null;
    }

    @Override
    public List<PartnerCafeStatusHistory> getPartnerCafeStatusHistory(Integer brandId, Integer unitId, String partnerName) {

        return partnerCafeStatusHistoryDao.findTop15ByBrandIdAndUnitIdAndPartnerNameOrderByLastUpdatedTimeDesc(brandId,
            unitId, partnerName);
    }

    @Override
    public Map<String,String> getEditedOrderOriginalRequest(String partnerOrderId) {
        Map<String,String> resultMap = new HashMap<>();
        List<PartnerOrderErrorCode> errorCodes = new ArrayList<>(Arrays.asList(PartnerOrderErrorCode.STOCK_NOT_AVAILABLE,PartnerOrderErrorCode.STOCK_NOT_FOUND,
                PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT));
        List<PartnerOrderDetail> partnerOrderDetailList = partnerOrderDao.searchByPartnerOrderId(partnerOrderId);
        if(CollectionUtils.isEmpty(partnerOrderDetailList)){
            return resultMap;
        }
        PartnerOrderDetail partnerOrderDetail = partnerOrderDetailList.get(0);
        if(!CollectionUtils.isEmpty(partnerOrderDetail.getOrderErrors())){
            String partnerErrors = partnerOrderDetail.getOrderErrors().stream().filter(error -> errorCodes.contains(error.getErrorCode())).
                    map(PartnerOrderError::getErrorDescription).collect(Collectors.joining(","));
            resultMap.put("errors",partnerErrors);
        }
        if(partnerOrderDetail.getPartnerName().equals(AppConstants.SWIGGY)){
            SwiggyOrderRequest swiggyOrderRequest = (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
            String orderItems = "";
            orderItems = swiggyOrderRequest.getItems().stream().map(item -> item.getName() + "(" + item.getQuantity() + ")").collect(Collectors.joining(","));
            String modifiers = "";
            modifiers = swiggyOrderRequest.getItems().stream().map(
                    item-> item.getAddons().stream().filter(addon -> Arrays.stream(addon.getId().split("_")).toList().contains("PAIDADDON")
                            || Arrays.stream(addon.getId().split("_")).toList().contains("RECOM")).map(addon -> addon.getName() + "(" + item.getQuantity() + ")")
                            .collect(Collectors.joining(","))
            ).collect(Collectors.joining());

            resultMap.put("items" , orderItems);
            resultMap.put("modifiers",modifiers);



        }else if (partnerOrderDetail.getPartnerName().equals(AppConstants.ZOMATO)){
            ZomatoOrderRequestV3 zomatoOrderRequestV3 = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
            String orderItems = "";
            orderItems = zomatoOrderRequestV3.getDishes().stream().map(dish -> dish.getComposition().getCatalogueName() + "(" +
                    dish.getQuantity() + ")").collect(Collectors.joining(","));
            String modifiers = "";
            List<String> modifiersList = new ArrayList<>();
            zomatoOrderRequestV3.getDishes().forEach(dish ->{
                dish.getComposition().getModifierGroups().forEach(group ->{
                    group.getVariants().forEach(variant ->{
                        if(Arrays.stream(variant.getCatalogueId().split("_")).toList().contains("PAIDADDON")
                        || Arrays.stream(variant.getCatalogueId().split("_")).toList().contains("RECOM")){
                            modifiersList.add(variant.getCatalogueName() + "(" + dish.getQuantity() + ")");
                        }
                    });
                });
            });
            modifiers = String.join(",", modifiersList);
            resultMap.put("items",orderItems);
            resultMap.put("modifiers",modifiers);
        }

        return resultMap;

    }




}
