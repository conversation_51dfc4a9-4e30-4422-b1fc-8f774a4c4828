package com.stpl.tech.kettle.channelpartner.domain.model;

public enum PartnerActionCode {

    RECEIVED,
    CHECKED,
    PLACED,
    FAILED,
    SWIGGY_CALL_INITIATED,
    ORDER_CONFIRMED,
    <PERSON><PERSON><PERSON>_REQUESTED,
    <PERSON><PERSON><PERSON>_REQUEST_FAILED,
    <PERSON><PERSON><PERSON>_ACCEPTED,
    <PERSON><PERSON><PERSON>_REJECTED,
    <PERSON><PERSON><PERSON>LE<PERSON>,
    EDIT_<PERSON><PERSON><PERSON>_REQUESTED,
    EDIT_CANCELLED,
    MARK_RESOLVED,
    FORCE_PROCESSED,
    PICKED_BY_UPDATED,
    DELIVERED,
    CALL_SWIGGY_SUPPORT,
    <PERSON><PERSON><PERSON>_PROCESS,
    VIEW_INVENTORY,
    GET_CUSTOMER_DETAIL,
    SHOW_ITEMS,
    CALL_SWIGGY_CUSTOMER,
    P<PERSON>K_UP,
    K<PERSON><PERSON>E_ORDER_PLACED,
    CHECK_INVENTORY,
    PARTNER_RESPONSE;
}
