package com.stpl.tech.kettle.channelpartner.core.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;

@Component
public class PartnerOrderReconciliationScheduler {

	private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderCacheScheduler.class);

    @Autowired
    private RedisPublisher redisPublisher;

	@Scheduled(cron = "0 0 05 * * *", zone = "GMT+05:30")
	public void fetchLastBusinessDateOrdersOfPartner() {
		LOG.info("CRON JOB: fetchLastBusinessDateOrdersOfPartner : STARTED");
		 PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
		 partnerActionEvent.setEventType(PartnerActionEventType.ORDER_RECONCILIATION_DATA);
         partnerActionEvent.setEventData(new PartnerOrderDetail());
         partnerActionEvent.setPartner(false);
         redisPublisher.publish("COMMON", new Gson().toJson(partnerActionEvent));
		LOG.info("CRON JOB: fetchLastBusinessDateOrdersOfPartner : FINISHED");
	}

}
