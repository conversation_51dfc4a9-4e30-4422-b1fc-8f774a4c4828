package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CafeActivationTokenInfo implements TokenDao, Notification {
    private String requestId;
    private Integer partnerId;
    private Date timeStamp;

    public CafeActivationTokenInfo() {
    }

    public CafeActivationTokenInfo(String requestId, Integer partnerId, Date timeStamp) {
        this.requestId = requestId;
        this.partnerId = partnerId;
        this.timeStamp=timeStamp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public Date getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Date timeStamp) {
        this.timeStamp = timeStamp;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    @Override
    public String getNotificationMessage() {
        return null;
    }

    @Override
    public Map<String, Object> createClaims() {
        Map<String, Object> claims = new HashMap<>();
        claims.put("requestId", this.requestId);
        claims.put("partnerId", this.partnerId);
        claims.put("timeStamp", this.timeStamp);
        return claims;
    }

    @Override
    public void parseClaims(Claims claims) {
        this.requestId = claims.get("requestId", String.class);
        this.partnerId = claims.get("partnerId", Integer.class);
        this.timeStamp = claims.get("timeStamp", Date.class);
    }
}
