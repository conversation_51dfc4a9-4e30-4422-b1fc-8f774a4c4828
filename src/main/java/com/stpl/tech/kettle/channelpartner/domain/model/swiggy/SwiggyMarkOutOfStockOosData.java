package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "item"
})

public class SwiggyMarkOutOfStockOosData {
    @JsonProperty("item")
    private SwiggyMarkOutOfStockOosItem item;

    @JsonProperty("item")
    public SwiggyMarkOutOfStockOosItem getItem() {
        return item;
    }

    @JsonProperty("item")
    public void setItem(SwiggyMarkOutOfStockOosItem item) {
        this.item = item;
    }
}
