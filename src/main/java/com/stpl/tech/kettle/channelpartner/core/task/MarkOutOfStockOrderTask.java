package com.stpl.tech.kettle.channelpartner.core.task;

import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarkOutOfStockOrderTask implements Runnable {
    private static final Logger LOG = LoggerFactory.getLogger(MarkOutOfStockOrderTask.class);

    private OrderValidationService orderValidationService;

    private SwiggyService swiggyService;
    private volatile SwiggyOrderRequest swiggyOrderRequest;
    private volatile PartnerOrderDetail partnerOrderDetail;
    private volatile List<OrderItem> outOfStockItems;
    private volatile Order order;
    private volatile String requestId;


    @Override
    public void run() {
        try {
            MDC.put("request.id", requestId);
            if (!outOfStockItems.isEmpty()) {
                orderValidationService.sendMarkOutOfStockRequest(partnerOrderDetail, order, outOfStockItems,this.swiggyService);
            }
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error processing mark out of order stock request on swiggy ", ex);
        }
    }
}
