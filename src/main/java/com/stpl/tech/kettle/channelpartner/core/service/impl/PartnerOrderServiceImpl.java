package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.SyncLock;
import com.stpl.tech.kettle.channelpartner.core.service.MagicpinService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.util.StoreOpsServiceEndpoints;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.ActionCategory;
import com.stpl.tech.kettle.channelpartner.domain.model.FallbackReasonType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackLogDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackStatusDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackLog;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.StockRefreshAuditData;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.notification.email.OrderMismatchNotification;
import com.stpl.tech.kettle.channelpartner.core.notification.email.templates.OrderMismatchNotificationTemplate;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.EmailGenerationException;

@Service
public class PartnerOrderServiceImpl implements PartnerOrderService {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderServiceImpl.class);
    public static final String ORDER_REJECTED = "Order Rejected";

    @Autowired
    private SwiggyService swiggyService;

    @Autowired
    private TrackService trackService;

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private ZomatoService zomatoService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private CafeStatusChannelPartnerDao cafeStatusChannelPartnerDao;

    @Autowired
    private MagicpinService magicpinService;

    @Autowired
    private PartnerOrderConverterDependency partnerOrderConverterDependency;

    @Autowired
    private SyncLock lock;

    @Override
    public List<PartnerOrderDetail> getPartnerOrder(PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        if (partnerOrderDetail.getPartnerId() != null && partnerOrderDetail.getPartnerOrderId() != null) {
            return partnerOrderDao.searchByPartnerIdAndOrderId(partnerOrderDetail.getPartnerId(), partnerOrderDetail.getPartnerOrderId());
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Override
    public PartnerOrderDetail getPartnerOrder(String kettleOrderId) throws ChannelPartnerException {
        if (kettleOrderId != null) {
            return partnerOrderDao.searchByKettleOrderId(kettleOrderId);
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Override
    public Map<String, List<PartnerOrderDetail>> getPartnerPendingOrders(PartnerPendingOrderRequest request) throws ChannelPartnerException {
        List<PartnerOrderDetail> newOrders = new ArrayList<>();
        List<PartnerOrderDetail> changedOrders = new ArrayList<>();
        List<PartnerOrderDetail> finishedOrders = new ArrayList<>();
        Date time = ChannelPartnerUtils.getDateBeforeOrAfterInSeconds(ChannelPartnerUtils.getCurrentTimestamp(),
            -request.getHours() * 60 * 60);
        List<PartnerOrderDetail> orders ;
        if (request.getUnitId() != null) {
            LOG.info("Getting Pending  PartnerOrderDetail for Cafe and unitId  " + request.getUnitId());
            orders = partnerOrderDao.getCafePendingOrders(request.getUnitId(),time);
        } else {
//            LOG.info("Getting Pending  PartnerOrderDetail for callCenter ");
            orders = partnerOrderDao.getPartnerPendingOrders(time);
        }
        if (request.getPartnerOrderDetails() == null || request.getPartnerOrderDetails().keySet().size() == 0) {
            newOrders.addAll(orders);
        } else {
            orders.forEach(order -> {
                if (request.getPartnerOrderDetails().containsKey(order.getOrderId())) {
                    if (!request.getPartnerOrderDetails().get(order.getOrderId()).equals(order.getPartnerOrderStatus().name())) {
                        changedOrders.add(order);
                    }
                    request.getPartnerOrderDetails().remove(order.getOrderId());
                } else {
                    newOrders.add(order);
                }
            });
            request.getPartnerOrderDetails().keySet().forEach(key -> {
                PartnerOrderDetail partnerOrderDetail = new PartnerOrderDetail();
                partnerOrderDetail.setOrderId(key);
                finishedOrders.add(partnerOrderDetail);
            });
        }
        Map<String, List<PartnerOrderDetail>> stringListMap = new HashMap<>();
        stringListMap.put("NEW", newOrders);
        stringListMap.put("CHANGED", changedOrders);
        stringListMap.put("FINISHED", finishedOrders);
        return stringListMap;
    }

    @Override
    public List<PartnerOrderDetail> getOrdersByTime(Date startTime, Date endTime) {
        return partnerOrderDao.getOrdersByTime(startTime, endTime);
    }

    @Override
    public List<PartnerOrderDetail> getPartnerOrder(String partnerId, String orderStatus) throws ChannelPartnerException {
        if (!StringUtils.isEmpty(partnerId) && !StringUtils.isEmpty(orderStatus)) {
            return partnerOrderDao.searchByPartnerIdAndOrderStatus(partnerId, orderStatus);
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Autowired
    private PartnerOrderFallbackLogDao partnerOrderFallbackLogDao;

    @Autowired
    private PartnerOrderFallbackStatusDao partnerOrderFallbackStatusDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean callSwiggyPartnerSupport(String orderId ,String employeeId) throws ChannelPartnerException {
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(orderId);
        if (partnerOrderDetail.isPresent()) {
            PartnerOrderDetail orderDetail = partnerOrderDetail.get();
            boolean result = swiggyService.callSwiggyPartnerSupport(orderDetail);
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( orderDetail.getPartnerOrderId() );
            logFallbackData(orderDetail,String.valueOf(PartnerActionCode.CALL_SWIGGY_SUPPORT),"Swiggy Support Called",ActionCategory.BUTTON_CLICKED.name(),statusLogPrev,employeeId);
            return result;
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean markOrderResolved(String orderId,String employeeId) throws ChannelPartnerException {
    	Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(orderId);
        if (partnerOrderDetailData.isPresent()) {
        	PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            PartnerOrderStatus currentStatus = partnerOrderDetail.getPartnerOrderStatus();
            if (currentStatus == PartnerOrderStatus.CHECKED) {
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.RESOLVED);
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(currentStatus,
                        partnerOrderDetail.getPartnerOrderStatus(), true, null));
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.MARK_RESOLVED, "Order marked resolved", null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId(partnerOrderDetail.getPartnerOrderId());
                addFallbackOrderStatusLog(partnerOrderDetail,statusLogPrev);
                addFallbackOrderLog(partnerOrderDetail,String.valueOf(PartnerActionCode.MARK_RESOLVED),"Order Resolved",statusLogPrev,ActionCategory.BUTTON_CLICKED.name(),employeeId);
                if (partnerOrderDetail != null) {
                    return true;
                }
            }
            throw new ChannelPartnerException("Partner order cannot be marked as RESOLVED!");
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean addKettleOrder(String generatedOrderId, String partnerOrderId, Integer unitId,String employeeId) {
        Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(partnerOrderId);
        if (partnerOrderDetailData.isPresent()) {
            PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
            logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.KETTLE_ORDER_PLACED),"Kettle Order Punched",ActionCategory.ADD_KETTLE_ORDER.name(),statusLogPrev,employeeId);
            if (Objects.isNull(partnerOrderDetail.getKettleOrderId())) {
                AtomicInteger kettleOrderId = new AtomicInteger(0);
                AtomicBoolean isOrderFetched = new AtomicBoolean(true);
                Object orderJson = getKettleOrderDetail(generatedOrderId, kettleOrderId, isOrderFetched);
                if (Objects.nonNull(orderJson) && kettleOrderId.get() > 0 && isOrderFetched.get()) {
                    partnerOrderDetail.setKettleOrderId(String.valueOf(kettleOrderId.get()));
                    partnerOrderDetail.setKettleOrder(orderJson);
                    trackService.updatePartnerOrder(partnerOrderDetail);
                    setPartnerOrderDetailMySql(partnerOrderDetail, kettleOrderId.get());
                    return true;
                } else {
                    Object[] kettleOrderDetail = cafeStatusChannelPartnerDao.getOrderDetailByGeneratedOrderId(generatedOrderId);
                    if (Objects.nonNull(kettleOrderDetail)) {
                        kettleOrderId = new AtomicInteger((Integer) kettleOrderDetail[0]);
                        if (partnerOrderDetail.getPartnerOrderId().toString().equals(kettleOrderDetail[2])) {
                            if (Objects.nonNull(kettleOrderId)) {
                                partnerOrderDetail.setKettleOrderId(String.valueOf(kettleOrderId.get()));
                                trackService.updatePartnerOrder(partnerOrderDetail);
                                setPartnerOrderDetailMySql(partnerOrderDetail, kettleOrderId.get());
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private void setPartnerOrderDetailMySql(PartnerOrderDetail partnerOrderDetail , Integer kettleOrderId){
        if(AppConstants.SWIGGY.equals(partnerOrderDetail.getPartnerName())){
            swiggyService.savePartnerOrderDataMysql(partnerOrderDetail,kettleOrderId);
        }else if(AppConstants.ZOMATO.equals(partnerOrderDetail.getPartnerName())) {
            zomatoService.savePartnerOrderDataMysql(partnerOrderDetail, kettleOrderId);
        }

    }

    private Object getKettleOrderDetail(String generatedOrderId, AtomicInteger kettleOrderId, AtomicBoolean isOrderFetched) {
        ObjectMapper objectMapper = new ObjectMapper();
        Object orderJsonValue = null;
        StringBuilder endPoint = new StringBuilder(environmentProperties.getStoreOpsBasePath());
        endPoint.append(StoreOpsServiceEndpoints.GET_ORDER_DETAIL);
        try {
            Object orderDetail = webServiceHelper.postWithAuth(endPoint.toString(), environmentProperties.getChannelPartnerClientToken(),
                    new Gson().toJson(generatedOrderId).toString(), Object.class);
            JsonNode jsonNode = objectMapper.readTree(objectMapper.writeValueAsString(orderDetail));
            orderJsonValue = objectMapper.readValue(objectMapper.writeValueAsString(jsonNode.get("body")), Object.class);
            // Access the orderId attribute
            kettleOrderId.set(Integer.valueOf(jsonNode.get("body").get("orderId").asText()));
        } catch (Exception e) {
            LOG.info("Error while getting order detail for generated order id ::::: {}, {}", generatedOrderId, e);
            isOrderFetched.set(false);
            return null;
        }
        return orderJsonValue;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean manualProcess(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException {
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(orderId);
        Boolean result;
        result = lock.syncLock(orderId, () -> {
            if (Objects.nonNull(partnerOrderDetail) && Objects.nonNull(partnerOrderDetail.get())) {
                switch (partnerOrderDetail.get().getPartnerName()) {
                    case "SWIGGY":
                        swiggyService.manualProcessOrder(partnerOrderDetail.get(), false);
                        break;
                    case "ZOMATO":
                        zomatoService.manualProcessOrder(partnerOrderDetail.get(), false);
                        break;
                    case "MAGICPIN":
                        PartnerOrderServiceFactory.getInstance("MAGICPIN", partnerOrderConverterDependency).
                                manualProcessOrder(partnerOrderDetail.get(), false);
                        break;

                    default:
                        return false;
                }
            }
            logManualProcessEvent(partnerOrderDetail,employeeId);
            return true;
        });
        if(Objects.isNull(result)){
            throw new ChannelPartnerException("Partner order id not found!");
        }
        return result;
    }

    private void logManualProcessEvent(Optional<PartnerOrderDetail> partnerOrderDetail, String employeeId) {
        if (partnerOrderDetail.isPresent()) {
            PartnerOrderDetail orderDetail = partnerOrderDetail.get();
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( orderDetail.getPartnerOrderId() );
            logFallbackData(orderDetail,String.valueOf(PartnerActionCode.MANUAL_PROCESS),"Manual Procees ",ActionCategory.BUTTON_CLICKED.name(),statusLogPrev,employeeId);
        }
    }

    @Override
    public  List<PartnerItemData> getMagicPinOrderItems(String orderId){
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByPartnerOrderId(orderId).get(0);
        List<PartnerItemData> partnerItemDataList = new ArrayList<>();
        for(Object item : partnerOrderConverterDependency.getPartnerItemConverterService(ChannelPartnerServiceConstants.MAGICPIN).
                getItems(partnerOrderDetail.getPartnerOrder())){
            PartnerItemData partnerItemData = partnerOrderConverterDependency.getPartnerItemConverterService(ChannelPartnerServiceConstants.MAGICPIN).
                    getPartnerItemData(item);
            partnerItemDataList.add(partnerItemData);
        }
        return partnerItemDataList;
    }

    @Override
    public boolean markCancelled(String kettleOrderId,String employeeId) throws ChannelPartnerException {
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(kettleOrderId);
        //PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCache().ge t(partnerOrderDetail.getPartnerId());
        if (partnerOrderDetail != null) {
            PartnerOrderStatus partnerOrderStatus = partnerOrderDetail.getPartnerOrderStatus();
            if (partnerOrderStatus.equals(PartnerOrderStatus.EDIT_CANCEL_REQUESTED) || partnerOrderStatus.equals(PartnerOrderStatus.CANCEL_REQUESTED)) {
                if (partnerOrderStatus.equals(PartnerOrderStatus.EDIT_CANCEL_REQUESTED)) {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.EDIT_CANCELLED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.EDIT_CANCELLED, "Partner order cancelled", null));
                } else if (partnerOrderStatus.equals(PartnerOrderStatus.CANCEL_REQUESTED)) {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCELLED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCELLED, "Partner order cancelled", null));
                }
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderStatus, partnerOrderDetail.getPartnerOrderStatus(), true, null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
                logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.CANCELLED),"Order Cancelled",ActionCategory.BUTTON_CLICKED.name(),statusLogPrev,employeeId);
                if (partnerOrderDetail != null) {
                    return true;
                }
                throw new ChannelPartnerException("Error updating order status.");
            } else {
                throw new ChannelPartnerException("Partner order id not valid for cancellation!");
            }
        }
        throw new ChannelPartnerException("Partner order id not valid!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean manualProcessWithSkipInventory(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException {
        Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(orderId);
        if (partnerOrderDetailData.isPresent()) {
        	PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            boolean processOrder = true;
            for (PartnerOrderError partnerOrderError : partnerOrderDetail.getOrderErrors()) {
                if (!partnerOrderError.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT)
                		&& !partnerOrderError.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_AVAILABLE)) {
                    if (processOrder) {
                        processOrder = partnerOrderError.getErrorCode().isCanBeProcessed();
                    }
                }
            }
            if (processOrder && partnerOrderDetail.getPartnerOrderStatus().equals(PartnerOrderStatus.CHECKED)) {
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.FORCE_PROCESSED,
                        "Force process skipping inventory check!", null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                if (partnerOrderDetail != null) {
                    switch (partnerOrderDetail.getPartnerName()) {
                        case "SWIGGY":
                            swiggyService.manualProcessOrder(partnerOrderDetail, true);
                            break;
                        case "ZOMATO":
                            zomatoService.manualProcessOrder(partnerOrderDetail, true);
                            break;
                        case "MAGICPIN":
                            PartnerOrderServiceFactory.getInstance(partnerOrderDetail.getPartnerName(),partnerOrderConverterDependency)
                                    .manualProcessOrder(partnerOrderDetail,true);
                            break;
                        default:
                            throw new ChannelPartnerException("Error in force processing order. Partner is not valid!");
                    }
                    logForceProcessEvent(partnerOrderDetail,employeeId);
                    return true;
                } else {
                    throw new ChannelPartnerException("Error in force processing order. Please try again!");
                }
            }
            throw new ChannelPartnerException("Order not applicable for force manual process!");
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    private void logForceProcessEvent(PartnerOrderDetail partnerOrderDetail , String employeeId){
        PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
        logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.FORCE_PROCESSED),"Force Processed ",ActionCategory.BUTTON_CLICKED.name(),statusLogPrev,employeeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendOrderNotification(PartnerOrderDetail order) {
        OrderMismatchNotificationTemplate template = new OrderMismatchNotificationTemplate(order,
                order.getPartnerOrderId(), environmentProperties.getBasePath());
        OrderMismatchNotification notification = new OrderMismatchNotification(template, environmentProperties.getEnvType());
        try {
            notification.sendEmail();
        } catch (EmailGenerationException e) {
            e.printStackTrace();
        } catch (Exception e) {
            LOG.error("Error sending mismatch notification email:", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendOrderStatusUpdate(PartnerOrderStateUpdate request) {
        try {
            PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(request.getOrderId());
            if (partnerOrderDetail != null) {
                switch (partnerOrderDetail.getPartnerName()) {
                    case "SWIGGY":
                        swiggyService.sendOrderStatusUpdate(request, partnerOrderDetail);
                        break;
                    case "ZOMATO":
                        zomatoService.sendOrderStatusUpdate(request, partnerOrderDetail);
                        break;
                    case "MAGICPIN":
                        magicpinService.sendOrderStatusUpdate(request,partnerOrderDetail);
                    default:
                }
            }
        } catch (ChannelPartnerException ex) {
            LOG.error("Error updating partner order state to partner");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer getSwiggyRiderTimeOfArrival(String orderId) throws ChannelPartnerException {
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(orderId);
        if (partnerOrderDetail != null) {
            return swiggyService.getRiderTimeOfArrival(orderId, partnerOrderDetail);
        }
        throw new ChannelPartnerException("Invalid request", "Order id is not valid.");
    }

    @Override
	public void sendOrderNotPunchedNotification(PartnerOrderDetail detail) {
		try {
			UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
			String brandName = detail.getBrandId() != null
					? masterDataCache.getBrandMetaData().get(detail.getBrandId()).getBrandName()
					: "Unknown";
			String message = String.format(
					"Order Rejected from : %s for %s and cafe %s \n Partner Order Id : %s, Time : %s \nErrors : %s",
					detail.getPartnerName(), brandName, ubd.getName(), detail.getPartnerOrderId(),
					detail.getAddTimeIST(), StringUtils.join(detail.getOrderErrors(), ",\n"));
            String message1 = ChannelPartnerUtils.getMessage("Order Rejections", message);
			SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
<<<<<<< Updated upstream
					"Channel Partner", SlackNotification.ORDER_REJECTED, message1);
=======
					"Channel Partner",AppConstants.DOHFUL_BRAND_ID.equals(detail.getBrandId()) ? SlackNotification.ORDER_REJECTED :  SlackNotification.ORDER_REJECTED, message1);
>>>>>>> Stashed changes
			if (ubd.getUnitManagerId() != null) {
                SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getUnitManagerId() + "_notify"
										: ubd.getUnitManagerId() + "_notify",
								message1);
			}
			if (ubd.getCafeManagerId() != null) {
                SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getCafeManagerId() + "_notify"
										: ubd.getCafeManagerId() + "_notify",
								message1);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing slack for inventory down and order punched", e);
		}
	}

    @Override
    public void sendOrderNotPunchedNotificationToKnock(PartnerOrderDetail detail) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
            String brandName = detail.getBrandId() != null
                    ? masterDataCache.getBrandMetaData().get(detail.getBrandId()).getBrandName()
                    : "Unknown";
            String message = String.format(
                    "Order Rejected from : %s for %s and cafe %s \n Partner Order Id : %s, Time : %s \nErrors : %s",
                    detail.getPartnerName(), brandName, ubd.getName(), detail.getPartnerOrderId(),
                    detail.getAddTimeIST(), StringUtils.join(detail.getOrderErrors(), ",\n"));
            Map<String,String> params = new HashMap<>();
            params.put("message",message);
            params.put("title", ORDER_REJECTED);

            try {
                if (ubd.getUnitManagerId() != null) {
                    params.put("userId", ubd.getUnitManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() +  AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            }catch(Exception e){
                LOG.error("Error while publishing order rejection notification on knock ",e);
            }
            try {
                if (ubd.getCafeManagerId() != null) {
                    params.put("userId", ubd.getCafeManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            } catch (Exception e) {
                LOG.error("Error while publishing order rejection notification on knock ",e);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing slack for inventory down and order punched", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void logStockRefreshEvent(Integer unitId, Integer partnerId, Integer brandId,String logType, String status) {
        try{
            StockRefreshAuditData stockRefreshAuditData = StockRefreshAuditData.builder().unitId(unitId).channelPartnerId(partnerId)
                    .brandId(brandId).timestamp(AppUtils.getCurrentTimestamp()).logType(logType).status(status).build();
            cafeStatusChannelPartnerDao.add(stockRefreshAuditData);
        }catch (Exception e){
            LOG.error("Error While Logging Stock Refresh Event For  Unit Id :::: {} , Partner Id :::: {} ," +
                    " Brand Id :::: {}  ",unitId,partnerId,brandId);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean fallbackProcessedBy(String orderId, String fallbackProcessedBy) throws ChannelPartnerException {
            Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(orderId);
        if (partnerOrderDetailData.isPresent()) {
            PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            partnerOrderDetail.setFallbackProcessedBy(fallbackProcessedBy);
            partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.PICK_UP,
                "Order picked by updated to: " + fallbackProcessedBy, null));
            partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);

            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
            String actionCategory = ActionCategory.AGENT_CHANGED.name();
            if(statusLogPrev == null){
                actionCategory = ActionCategory.AGENT_ASSIGNED.name();
            }
            logFallbackData(partnerOrderDetail,String.valueOf(PartnerActionCode.PICK_UP),"Fallback picked up",actionCategory,statusLogPrev,fallbackProcessedBy);
            if (partnerOrderDetail != null) {
                return true;
            }
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean partnerResponse(PartnerOrderResponse response ,String employeeId){
        String orderId = response.getOrderId();
        String type = response.getType();
        String value = response.getValue();
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(orderId);
        if (partnerOrderDetail.isPresent()) {
            PartnerOrderDetail orderDetail = partnerOrderDetail.get();
            if(type.equals(FallbackReasonType.Cancellation_Reason.name())){
                orderDetail.setCancellationReason(value);
            }else if(type.equals(FallbackReasonType.Customer_Response.name())){
                orderDetail.setCustomerResponse(value);
            }else{
                orderDetail.setPartnerResponse(value);
            }
            PartnerOrderFallbackStatus statusLogPrev = partnerOrderFallbackStatusDao.findByOrderId( orderDetail.getPartnerOrderId() );
            logFallbackData(orderDetail,String.valueOf(PartnerActionCode.PARTNER_RESPONSE),"Partner Response from dropdown",ActionCategory.PARTNER_RESPONSE_SELECTED.name(),statusLogPrev,employeeId);
            return true;
        }
        return false;
    }

    @Override
    public PartnerOrderFallbackStatus addFallbackOrderStatusLog(PartnerOrderDetail partnerOrderDetail, PartnerOrderFallbackStatus statusLog){
        try{
            if(Objects.isNull(statusLog)){
                statusLog = partnerOrderFallbackStatusDao.findByOrderId( partnerOrderDetail.getPartnerOrderId() );
            }
            if( Objects.nonNull(statusLog) ) {
                statusLog.setEmployeeId(partnerOrderDetail.getFallbackProcessedBy());
                statusLog.setActionTime(ChannelPartnerUtils.getCurrentTimestamp());
                if(!StringUtils.isEmpty(partnerOrderDetail.getCancellationReason())){
                    statusLog.setCancellationReason(partnerOrderDetail.getCancellationReason());
                }
                if(!StringUtils.isEmpty(partnerOrderDetail.getPartnerResponse())){
                    statusLog.setPartnerResponse(partnerOrderDetail.getPartnerResponse());
                }
                if(!StringUtils.isEmpty(partnerOrderDetail.getCustomerResponse())){
                    statusLog.setCustomerResponse(partnerOrderDetail.getCustomerResponse());
                }
                statusLog.setFinalStatus(partnerOrderDetail.getPartnerOrderStatus().name());
                partnerOrderFallbackStatusDao.update(statusLog);
            } else {
                statusLog = ChannelPartnerUtils.buildFallbackStatusLog(partnerOrderDetail, partnerOrderDetail.getPartnerOrderStatus().name());
                partnerOrderFallbackStatusDao.add(statusLog);
            }

            return statusLog;
            } catch (Exception e) {
                 throw new RuntimeException(" Error Logging Swiggy Order Fallback Status " + e);
        }
    }

    @Override
    public void addFallbackOrderLog(
            PartnerOrderDetail partnerOrderDetail,
            String action,
            String comment,
            PartnerOrderFallbackStatus statusLog,
            String actionCategory,
            String statusPrev
    ) {
        try {
            PartnerOrderFallbackLog log = ChannelPartnerUtils.buildFallbackLog(
                    partnerOrderDetail,
                    action,
                    StringUtils.isEmpty(statusPrev) ? null : statusPrev,
                    comment,
                    statusLog,
                    actionCategory
            );

            LOG.info("Logging order resolution to PARTNER_ORDER_FALLBACK_LOGS: {}", log);
            partnerOrderFallbackLogDao.add(log);

        } catch (Exception e) {
            LOG.error("Error logging partner order fallback action", e);
        }
    }

    @Override
    public void logFallbackData(PartnerOrderDetail partnerOrderDetail, String action, String comment, String actionCategory,
                                PartnerOrderFallbackStatus statusLogPrev, String employeeId) {

        String prevEmployeeId = (statusLogPrev == null || "system".equals(statusLogPrev.getEmployeeId()))
                ? "system"
                : statusLogPrev.getEmployeeId();

        PartnerOrderFallbackStatus statusLog = addFallbackOrderStatusLog(partnerOrderDetail, statusLogPrev);

        addFallbackOrderLog(partnerOrderDetail, action, comment, statusLog, actionCategory, prevEmployeeId);
    }


}