package com.stpl.tech.kettle.channelpartner.core.service;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import lombok.extern.log4j.Log4j2;

import java.util.List;
@Log4j2
public  class PartnerActionEventPublisherLayerDecorator extends PartnerEventPublisherDecorator<PartnerActionEvent> {

    protected PartnerBaseDecorator partnerBaseDecorator;

    public PartnerActionEventPublisherLayerDecorator(PartnerMetadataBuilder partnerMetadataBuilder, PartnerBaseDecorator partnerBaseDecorator) {
        super(partnerMetadataBuilder);
        this.partnerBaseDecorator = partnerBaseDecorator;
    }

    @Override
    public <T,R> R preProcessData(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds, Class<R> returnType) throws ChannelPartnerException {
        R obj = this.partnerBaseDecorator.preProcessData(reqObj , requestType , partnerIds, returnType);
        if(obj instanceof PartnerActionEvent){
            PartnerActionEvent partnerActionEvent =new Gson().fromJson(new Gson().toJson(obj), PartnerActionEvent.class);
            pushRedisEventToQueue(partnerIds, partnerActionEvent);
        }
        return returnType.cast(obj);
    }

    @Override
    public void pushRedisEventToQueue(List<Integer> partnerIds, PartnerActionEvent event) {
        partnerIds.forEach(partnerId -> {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            if (partnerDetail != null && ChannelPartnerUtils.getNewChannelPartners().contains(partnerId)) {
                log.info("PUBLISHING REDIS EVENT " + event.getEventType().name() +"for partner::: "
                        + partnerDetail.getPartnerName()
                        +" topic: "
                        + ChannelPartnerServiceConstants.COMMON_PARTNER_ACTION_EVENT_CHANNEL );
                String message = new Gson().toJson(event);
                redisPublisher.publish(ChannelPartnerServiceConstants.COMMON_PARTNER_ACTION_EVENT_CHANNEL, message);
            } else {
                log.error("Error publishing event" + event.getEventType().name() + " for partner id " + partnerId + " is not valid.");
            }
        });
    }
}
