package com.stpl.tech.kettle.channelpartner.domain.model;

import java.util.Date;

import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.util.AppConstants;

public enum MenuType {
	DEFAULT, DAY_SLOT , DAY_SLOT_BREAKFAST, DAY_SLOT_LUNCH, DAY_SLOT_EVENING, DAY_SLOT_DINNER, DAY_SLOT_POST_DINNER,
	DAY_SLOT_OVERNIGHT, SINGLE_SERVE , DAY_SLOT_ALL;

	public static MenuType getCurrentMenuType(Date currentTime , Integer brandId) {
		if(AppConstants.CHAAYOS_BRAND_ID == brandId){
			return getCurrentMenuTypeForChaayos(currentTime);
		}else if(AppConstants.GNT_BRAND_ID == brandId){
			return getCurrentMenuTypeForGnt(currentTime);
		}else{
			return getCurrentMenuTypeForChaayos(currentTime);
		}

	}

	private static MenuType getCurrentMenuTypeForChaayos(Date currentTime){
		int hour = currentTime.getHours();
		if ((hour >= 5 && hour <= 11)) {
			return DAY_SLOT_BREAKFAST;
		} else if (hour >= 12 && hour < 14) {
			return DAY_SLOT_LUNCH;
		} else if (hour >= 14 && hour < 19) {
			return DAY_SLOT_EVENING;
		} else if (hour >= 19 && hour < 22) {
			return DAY_SLOT_DINNER;
		}  else if (hour >= 22 || hour <=4) {
			return DAY_SLOT_OVERNIGHT;
		}
		return DEFAULT;
	}

	private static MenuType getCurrentMenuTypeForGnt(Date currentTime){
		int hour = currentTime.getHours();
		if ((hour >= 10 && hour <= 12)) {
			return DAY_SLOT_BREAKFAST;
		} else if (hour >= 13 && hour <= 15) {
			return DAY_SLOT_LUNCH;
		} else if (hour >= 16 && hour <= 18) {
			return DAY_SLOT_EVENING;
		} else if (hour >= 19 && hour <= 22) {
			return DAY_SLOT_DINNER;
		} else if (hour == 23 || hour <= 1) {
			return DAY_SLOT_POST_DINNER;
		} else if (hour >= 2 && hour <= 4) {
			return DAY_SLOT_OVERNIGHT;
		}
		return DEFAULT;
	}
	
	public static MenuType fromValue(String v) {
        for (MenuType c: MenuType.values()) {
            if (c.toString().equalsIgnoreCase(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
