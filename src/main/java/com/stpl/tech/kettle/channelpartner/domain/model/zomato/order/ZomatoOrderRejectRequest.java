package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "rejection_message_id",
        "catalogue_vendor_entity_ids"
})
public class ZomatoOrderRejectRequest {

    @JsonProperty(value = "order_id")
    private String order_id;

    @JsonProperty(value = "rejection_message_id")
    private Integer rejection_message_id;

    @JsonProperty(value = "catalogue_vendor_entity_ids")
    private List<String> catalogue_vendor_entity_ids;

}
