package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.AbstractOrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderItemDecoratorBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderItemFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.OptionData;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
public class PaidAddonItemDecorator extends BaseAddonItemDecorator {


    private BaseOrderItemDecorator baseOrderItemDecorator;


    public PaidAddonItemDecorator(OrderItemDecoratorBuilder orderItemDecoratorBuilder, BaseOrderItemDecorator baseOrderItemDecorator) {
        super(orderItemDecoratorBuilder);
        this.baseOrderItemDecorator = baseOrderItemDecorator;
    }

    @Override
    public <R, I> Pair<PartnerItemData, List<OrderItem>> processItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts, int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) {
        final Pair<PartnerItemData, List<OrderItem>> menuItem = this.baseOrderItemDecorator.processItem(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap, cafeProducts);
        Product product = priceProfileProducts.get(menuItem.getKey().getProductId());
        ProductPrice productPrice = orderItemDecoratorBuilder.abstractOrderItemConverterService.getProductPriceMapping(item, menuItem.getKey(), product, false, null);
        addPaidAddons(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingMap, cafeProducts, product,
                menuItem.getKey(), productPrice, menuItem.getValue(), false, menuItem.getValue().get(0).getComposition());
        return menuItem;
    }

    private <I, R> void addPaidAddons(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail,
                                      Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                      Map<Integer, Map<String, BigDecimal>> pricingMap,
                                      Map<Integer, Product> cafeProducts, Product product, PartnerItemData partnerItemData, ProductPrice productPrice,
                                      List<OrderItem> orderItemList, boolean superCombo, OrderItemComposition parentItemComposition) {

        List<ProductPrice> matchedPriceObj = cafeProducts.get(partnerItemData.getOriginalProductId()).getPrices().stream().
                filter(price -> price.getDimension().equals(productPrice.getDimension())).toList();
        Boolean paidAddonFound = false;
        if (!superCombo && !matchedPriceObj.isEmpty() && Objects.nonNull(matchedPriceObj.get(0).getRecipe().getOptions())) {
            for (PartnerItemAddonData paidAddonPartnerData : partnerItemData.getPaidAddonProducts()) {
                for (OptionData optionData : matchedPriceObj.get(0).getRecipe().getOptions()) {
                    if ((paidAddonPartnerData.getProductId() > 0 && optionData.getId() == paidAddonPartnerData.getProductId())
                            || optionData.getName().equalsIgnoreCase(paidAddonPartnerData.getName().trim())) {

                        Product addonProduct = cafeProducts.get(paidAddonPartnerData.getProductId());

                        if (Objects.nonNull(addonProduct) && Objects.nonNull(addonProduct.getPrices()) && !addonProduct.getPrices().isEmpty()) {
                            parentItemComposition.getOptions().add(optionData.getName());
                            OrderItem orderItem = OrderItemFactory.createPaidAddonProduct(partnerOrderDetail.getPartnerName(), taxMap,
                                    addonProduct, partnerItemData, paidAddonPartnerData);
                            orderItemList.add(orderItem);
                            paidAddonFound = true;
                        }
                    }
                }
            }
        }
        if(!CollectionUtils.isEmpty(partnerItemData.getPaidAddonProducts()) && Boolean.FALSE.equals(paidAddonFound)){
            log.error("Paid Addon Not Found In Recipe For Partner Order Id : {} And Product Id : {}",partnerOrderDetail.getPartnerOrderId()
                    ,partnerItemData.getOriginalProductId());
        }
    }

}
