package com.stpl.tech.kettle.channelpartner.domain.model;


import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.util.Date;

public class DateSerializer implements JsonSerializer<Date> {

    @Override
    public JsonElement serialize(Date src, Type type, JsonSerializationContext jsonSerializationContext) {
        return src == null ? null : new JsonPrimitive(src.getTime());
    }

}
