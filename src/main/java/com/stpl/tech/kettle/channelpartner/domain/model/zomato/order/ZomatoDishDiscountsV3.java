package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "type",
        "amount"
})
public class ZomatoDishDiscountsV3 {
	
	@JsonProperty("type")
    private String type;
    @JsonProperty("amount")
    private String amount;
    
    @JsonProperty("type")
	public String getType() {
		return type;
	}
    
    @JsonProperty("type")
	public void setType(String type) {
		this.type = type;
	}
	
	@JsonProperty("amount")
	public String getAmount() {
		return amount;
	}
	
	@JsonProperty("amount")
	public void setAmount(String amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "ZomatoDishDiscountsV3 [type=" + type + ", amount=" + amount + "]";
	}
    
	 @Override
	    public int hashCode() {
	        return new HashCodeBuilder().append(type).append(amount).toHashCode();
	    }

	    @Override
	    public boolean equals(Object other) {
	        if (other == this) {
	            return true;
	        }
	        if ((other instanceof ZomatoDishDiscountsV3) == false) {
	            return false;
	        }
	        ZomatoDishDiscountsV3 rhs = ((ZomatoDishDiscountsV3) other);
	        return new EqualsBuilder().append(type, rhs.type).append(amount, rhs.amount).isEquals();
	    }

}
