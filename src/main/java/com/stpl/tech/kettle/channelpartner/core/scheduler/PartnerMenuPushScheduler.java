package com.stpl.tech.kettle.channelpartner.core.scheduler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventPublisherLayerDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerBuilderFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.service.impl.PartnerMetadataManagementProxyLayer;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

@Component
public class PartnerMenuPushScheduler {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerMenuPushScheduler.class);

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private PartnerMetadataManagementProxyLayer partnerMetadataManagementProxyLayer;

    private PartnerBaseDecorator partnerBaseDecorator ;
    @Autowired
    private PartnerMenuConverterDependency partnerMenuConverterDependency ;


    private void setPartnerBaseDecorator(EventType eventType){
        this.partnerBaseDecorator= ServiceFactory.getPartnerPreProcessorLayer(eventType.name(), PartnerBuilderFactory.getInstance(partnerMenuConverterDependency));
    };

    @Scheduled(cron = "0 01 12,19,22 * * *", zone = "GMT+05:30")
    public void pushMenuForDaySlot() throws ChannelPartnerException {
        LOG.info("CRON JOB: pushMenuForDaySlot : STARTED");
        Date date = ChannelPartnerUtils.getCurrentTimestamp();
        MenuType type = MenuType.getCurrentMenuType(date,AppConstants.CHAAYOS_BRAND_ID);
        if (properties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
            if(channelPartnerDataCache.getPartnerCache().containsKey("MAGICPIN")){
                partnerIds.add(channelPartnerDataCache.getPartnerCache().get("MAGICPIN").getKettlePartnerId());
            }
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);

            partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );
        }
        LOG.info("CRON JOB: pushMenuForDaySlot : FINISHED");
    }

    @Scheduled(cron = "0 43 06,14 * * *", zone = "GMT+05:30")
    public void pushMenuForDaySlot2() throws ChannelPartnerException {
        LOG.info("CRON JOB: pushMenuForDaySlot : STARTED");
        Date date = ChannelPartnerUtils.getCurrentTimestamp();
        MenuType type = MenuType.getCurrentMenuType(date,AppConstants.CHAAYOS_BRAND_ID);
        if (properties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
            if(channelPartnerDataCache.getPartnerCache().containsKey("MAGICPIN")){
                partnerIds.add(channelPartnerDataCache.getPartnerCache().get("MAGICPIN").getKettlePartnerId());
            }
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);

            partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );
        }
        LOG.info("CRON JOB: pushMenuForDaySlot : FINISHED");
    }
    

    @Scheduled(cron = "0 2 11,13,16,19,23 * * *", zone = "GMT+05:30")
    public void pushMenuForDaySlotForGNT() throws ChannelPartnerException {
        LOG.info("CRON JOB: pushMenuForDaySlot GNT : STARTED");
        Date date = ChannelPartnerUtils.getCurrentTimestamp();
        MenuType type = MenuType.getCurrentMenuType(date,AppConstants.GNT_BRAND_ID);
        if (properties.isScheduledMenuPushForGNT()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
            /*if(channelPartnerDataCache.getPartnerCache().containsKey("MAGICPIN")){
                partnerIds.add(channelPartnerDataCache.getPartnerCache().get("MAGICPIN").getKettlePartnerId());
            }*/
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setBrandId(AppConstants.GNT_BRAND_ID);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);

            partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );
        }
        LOG.info("CRON JOB: pushMenuForDaySlot GNT : FINISHED");
    }

/*    @Scheduled(cron = "0 45 22 * * *", zone = "GMT+05:30")
    public void pushMenuForMenu199() throws ChannelPartnerException {
        LOG.info("CRON JOB: Menu 199 : STARTED");
        MenuType type = MenuType.DAY_SLOT_BREAKFAST;
        List<Integer> unitIdsForMenu = properties.getUnitIdsForMenu199();
        List<Integer> partnerIds = new ArrayList<>();
        //partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
        partnerIds.add(-1);
        UnitMenuAddVO unitMenu = new UnitMenuAddVO();
        unitMenu.setMenuType(type);
        unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
        unitMenu.setUnitIdsForMenu(unitIdsForMenu);
        partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );

        LOG.info("CRON JOB: pushMenuForDaySlot : FINISHED");
    }*/



    @Scheduled(cron = "0 45 6 * * *", zone = "GMT+05:30")
    public void pushMenuForDaySlotBreakfast() throws ChannelPartnerException {
        LOG.info("CRON JOB Temp: pushMenuForDaySlot : STARTED");

        MenuType type = MenuType.DAY_SLOT_BREAKFAST;
/*        if (properties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> unitIdsForFilter = properties.getUnitIdsForMenu199();
            LOG.info("Filter Unit Ids : {}" , new Gson().toJson(unitIdsForFilter));
            unitIdsForMenu = unitIdsForMenu.stream().filter(unitId -> !unitIdsForFilter.contains(unitId)).collect(Collectors.toList());
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);
            partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );
        }*/
        LOG.info("CRON JOB Temp: pushMenuForDaySlot : FINISHED");
    }


 /*   @Scheduled(cron = "0 28 14 * * *", zone = "GMT+05:30")
    public void pushMenuForMenu299() throws ChannelPartnerException {
        LOG.info("CRON JOB: Menu 299 : STARTED");
        MenuType type = MenuType.DAY_SLOT_EVENING;
        List<Integer> unitIdsForMenu = properties.getUnitIdsForMenu299();
        List<Integer> partnerIds = new ArrayList<>();
        //partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
        partnerIds.add(-2);
        UnitMenuAddVO unitMenu = new UnitMenuAddVO();
        unitMenu.setMenuType(type);
        unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
        unitMenu.setUnitIdsForMenu(unitIdsForMenu);
        partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );

        LOG.info("CRON JOB: Menu 299 pushMenuForDaySlot : FINISHED");
    }*/

    /*@Scheduled(cron = "0 58 17 * * *", zone = "GMT+05:30")
    public void pushMenuForPostMenu299() throws ChannelPartnerException {
        LOG.info("CRON JOB: Post  Menu 299 : STARTED");
        MenuType type = MenuType.DEFAULT;
        List<Integer> unitIdsForMenu = properties.getUnitIdsForMenu299();
        List<Integer> partnerIds = new ArrayList<>();
        partnerIds.add(-2);
        UnitMenuAddVO unitMenu = new UnitMenuAddVO();
        unitMenu.setMenuType(type);
        unitMenu.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
        unitMenu.setUnitIdsForMenu(unitIdsForMenu);
        partnerMetadataManagementProxyLayer.sendRequestBasisRequestType(unitMenu , PartnerRequestType.SCHEDULED_MENU_PUSH, partnerIds );

        LOG.info("CRON JOB: Post  Menu 299 pushMenuForDaySlot : FINISHED");
    }*/



    private List<Integer> getUnitIdsForMenuPush() {
        List<Integer> unit = new ArrayList<>();
        if (EnvType.PROD.equals(properties.getEnvType()) || EnvType.SPROD.equals(properties.getEnvType())) {
            List<UnitBasicDetail> units = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : units) {
                if (unitBasicDetail.isLive() && UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus())) {
                    unit.add(unitBasicDetail.getId());
                }
            }
        } else {
            unit.add(10000);
        }
        return unit;
    }
}
