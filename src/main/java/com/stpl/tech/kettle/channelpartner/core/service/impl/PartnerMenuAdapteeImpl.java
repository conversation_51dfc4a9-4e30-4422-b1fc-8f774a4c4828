package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.modelmapper.PropertyMap;
import org.modelmapper.TypeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Log4j2
public class PartnerMenuAdapteeImpl implements PartnerAdaptee<MenuReqMetadata, MenuReqMetadata> {

    private final MasterDataCache masterDataCache ;
    private final PartnerMenuService partnerMenuService ;
    public PartnerMenuAdapteeImpl(MasterDataCache masterDataCache , PartnerMenuService partnerMenuService) {
        this.masterDataCache = masterDataCache;
        this.partnerMenuService = partnerMenuService;
    }

    @Override
    public MenuReqMetadata setRequestCommonData(MenuReqMetadata menuReqMetadata) {
        if (Objects.nonNull(menuReqMetadata)) {
            UnitMenuAddVO request = setInitialMenuData(menuReqMetadata);
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(menuReqMetadata.getUnitId(), menuReqMetadata.getBrandId(), menuReqMetadata.getKettlePartnerId());
            if (Objects.isNull(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key))) {
                return null;
            }
            menuReqMetadata.setUnitPartnerBrandKey(key);
            request.setNew(true);
            request.setRegion(masterDataCache.getUnit(menuReqMetadata.getUnitId()).getRegion());
            String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
            PartnerUnitMenuDetail partnerUnitMenuData;
            PartnerUnitMenuVersionMapping partnerUnitMenuVersionMapping = partnerMenuService
                    .getPartnerUnitVersionData(menuReqMetadata.getKettlePartnerId(), menuReqMetadata.getUnitId(), menuReqMetadata.getBrandId(), menuReqMetadata.getMenuType().name());
            if (partnerUnitMenuVersionMapping == null) {
                partnerUnitMenuVersionMapping = partnerMenuService.getPartnerUnitVersionData(menuReqMetadata.getKettlePartnerId(), menuReqMetadata.getUnitId(), menuReqMetadata.getBrandId(),
                        MenuType.DEFAULT.name());
                request.setMenuType(MenuType.DEFAULT);
            }
            if (partnerUnitMenuVersionMapping != null) {
                partnerUnitMenuData = partnerMenuService.getPartnerUnitVersionMenuDetail(menuReqMetadata.getKettlePartnerId(), menuReqMetadata.getUnitId(), menuReqMetadata.getBrandId(),
                        partnerUnitMenuVersionMapping.getMenuType().name(), partnerUnitMenuVersionMapping.getVersion());
                menuReqMetadata.setPartnerUnitMenuData(partnerUnitMenuData);
                menuReqMetadata.setPartnerUnitMenuVersionMapping(partnerUnitMenuVersionMapping);
                menuReqMetadata.setUnitMenuAddVO(request);
                menuReqMetadata.setRestaurantId(restaurantId);
                return menuReqMetadata;

            }
        }
        return null;
    }

    private UnitMenuAddVO setInitialMenuData(MenuReqMetadata menuReqMetadata) {
        UnitMenuAddVO unitMenuAddVO = new UnitMenuAddVO();
        unitMenuAddVO.setBrandId(menuReqMetadata.getBrandId());
        unitMenuAddVO.setKettlePartnerId(menuReqMetadata.getKettlePartnerId());
        unitMenuAddVO.setUnitId(menuReqMetadata.getUnitId());
        unitMenuAddVO.setEmployeeId(menuReqMetadata.getEmployeeId());
        unitMenuAddVO.setMenuType(MenuType.fromValue(menuReqMetadata.getMenuType().name()));
        return unitMenuAddVO ;
    }
}
