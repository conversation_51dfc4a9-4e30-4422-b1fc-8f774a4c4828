package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"id","thirdPartyId","qty","returnReason"})
public class Items {

    @JsonProperty("id")
    private int id;

    @JsonProperty("thirdPartyId")
    private String thirdPartyId;

    @JsonProperty("qty")
    private Integer qty;

    @JsonProperty("returnReason")
    private String returnReason;
}
