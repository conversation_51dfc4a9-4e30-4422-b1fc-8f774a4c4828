package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.CommonMetadataValidationService;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class CommonMetadataValidationServiceImpl implements CommonMetadataValidationService {

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Override
    public boolean validateUnitPartnerMapping(Integer unitId, String partnerName) {
        if (unitId != null && masterDataCache.getUnits().containsKey(unitId)) {
            Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerName).getKettlePartnerId();
            return masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                    .anyMatch(unitChannelPartnerMapping ->
                            unitChannelPartnerMapping.getChannelPartner().getId() == partnerId &&
                                    unitChannelPartnerMapping.getUnit().getId() == unitId);
        }
        return false;
    }

    @Override
    public boolean validateUnitChannelPartnerMappingFromCache(Integer kettlePartnerId, Integer unitId) {
        if (unitId != null && kettlePartnerId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId);
            boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                    .anyMatch(unitChannelPartnerMapping -> unitChannelPartnerMapping.getChannelPartner()
                            .getId() == kettlePartnerId && unitChannelPartnerMapping.getUnit().getId() == unitId);
            return partnerDetail != null && mappingValid;
        }
        return false;
    }
}
