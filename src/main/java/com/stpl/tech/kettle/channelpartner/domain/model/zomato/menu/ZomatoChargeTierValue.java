package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tier_id",
        "charge_value",
        "charge_applicable_below_order_amount",
        "charge_always_applicable"
})
public class ZomatoChargeTierValue {

    @JsonProperty("tier_id")
    private Integer tierId;
    @JsonProperty("charge_value")
    private Float chargeValue;
    @JsonProperty("charge_applicable_below_order_amount")
    private Float chargeApplicableBelowOrderAmount;
    @JsonProperty("charge_always_applicable")
    private Integer chargeAlwaysApplicable;

    @JsonProperty("tier_id")
    public Integer getTierId() {
        return tierId;
    }

    @JsonProperty("tier_id")
    public void setTierId(Integer tierId) {
        this.tierId = tierId;
    }

    @JsonProperty("charge_value")
    public Float getChargeValue() {
        return chargeValue;
    }

    @JsonProperty("charge_value")
    public void setChargeValue(Float chargeValue) {
        this.chargeValue = chargeValue;
    }

    @JsonProperty("charge_applicable_below_order_amount")
    public Float getChargeApplicableBelowOrderAmount() {
        return chargeApplicableBelowOrderAmount;
    }

    @JsonProperty("charge_applicable_below_order_amount")
    public void setChargeApplicableBelowOrderAmount(Float chargeApplicableBelowOrderAmount) {
        this.chargeApplicableBelowOrderAmount = chargeApplicableBelowOrderAmount;
    }

    @JsonProperty("charge_always_applicable")
    public Integer getChargeAlwaysApplicable() {
        return chargeAlwaysApplicable;
    }

    @JsonProperty("charge_always_applicable")
    public void setChargeAlwaysApplicable(Integer chargeAlwaysApplicable) {
        this.chargeAlwaysApplicable = chargeAlwaysApplicable;
    }
}