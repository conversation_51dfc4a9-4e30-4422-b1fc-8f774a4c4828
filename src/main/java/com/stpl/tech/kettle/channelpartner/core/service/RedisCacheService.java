package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockDetail;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public interface RedisCacheService {
    public Optional<SwiggyOrderOutOfStockData> getSwiggyOrderOutOfStockData(String partnerOrderId);
    public void saveSwiggyOrderOutOfStockData(SwiggyOrderOutOfStockDetail swiggyOrderOutOfStockData);

}
