package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "UnitPartnerLocalityMapping")
public class UnitPartnerLocalityMapping {

    @Id
    private String mappingId;
    private PartnerLocalityDetail partnerLocalityDetail;
    private Integer unitId;
    private String kettleCity;
    private String kettleLocality;
    private Boolean pending = true;

    public String getMappingId() {
        return mappingId;
    }

    public void setMappingId(String mappingId) {
        this.mappingId = mappingId;
    }

    public PartnerLocalityDetail getPartnerLocalityDetail() {
        return partnerLocalityDetail;
    }

    public void setPartnerLocalityDetail(PartnerLocalityDetail partnerLocalityDetail) {
        this.partnerLocalityDetail = partnerLocalityDetail;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getKettleCity() {
        return kettleCity;
    }

    public void setKettleCity(String kettleCity) {
        this.kettleCity = kettleCity;
    }

    public String getKettleLocality() {
        return kettleLocality;
    }

    public void setKettleLocality(String kettleLocality) {
        this.kettleLocality = kettleLocality;
    }

    public Boolean getPending() {
        return pending;
    }

    public void setPending(Boolean pending) {
        this.pending = pending;
    }
}
