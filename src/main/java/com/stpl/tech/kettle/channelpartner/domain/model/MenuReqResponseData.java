package com.stpl.tech.kettle.channelpartner.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuReqResponseData<T,R> implements Serializable {
    private static final long serialVersionUID = 490330964114542343L;
    private UnitMenuAddVO unitMenuAddVO;
    private R menuRes;
    private boolean status ;
    private String res ;
    private String errorBody ;
    private T partnerMenuReqObj;

}
