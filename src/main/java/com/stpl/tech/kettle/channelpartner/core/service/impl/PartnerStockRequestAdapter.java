package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerRequestTarget;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Log4j2
public class PartnerStockRequestAdapter implements PartnerRequestTarget<StockReqMetadata, StockReqMetadata> {
    private final PartnerAdaptee<StockReqMetadata, StockReqMetadata> partnerAdaptee;
    private final PartnerAbstractFactory partnerAbstractFactory;

    public PartnerStockRequestAdapter(PartnerAdaptee<StockReqMetadata, StockReqMetadata> partnerAdaptee, PartnerAbstractFactory partnerAbstractFactory) {
        this.partnerAdaptee = partnerAdaptee;
        this.partnerAbstractFactory = partnerAbstractFactory;
    }

    @Override
    public StockReqMetadata convertAndSendResponse(StockReqMetadata obj) throws ChannelPartnerException {
        if (Objects.nonNull(obj)) {
            StockReqMetadata stockReqMetadata = this.partnerAdaptee.setRequestCommonData(obj);
            return this.partnerAbstractFactory.convertToPartnerStockEvent(stockReqMetadata);
        }
        return null;
    }
}
