package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class ProcessOrderInKettle<R, T> extends OrderProcessingStep<R, T> {

    private final OrderProcessingStep<R, T> orderExceptionStep;

    public ProcessOrderInKettle(OrderProcessingChainBuilder<R, T> builder) {
        super(builder);
        super.setNextOrderProcessingStep(new OrderProcessingStep<>(builder));
        orderExceptionStep = new OrderExceptionHandler<>(builder);
    }

    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        long startTime = System.currentTimeMillis();
        try {
            partnerOrderManagementService.setOrderProcessingThread(partnerOrderDetail, (Order) response, isManual, false);
            log.info("\n----------- ,STEP 4, - ,Order Processed in Kettle in ---------------------- , milliseconds {}", (System.currentTimeMillis() - startTime));
            return super.checkNext(request, isManual, response, partnerPrimaryData, partnerOrderDetail, super.nextOrderProcessingStep);
        } catch (Exception e) {
            return orderExceptionStep.handleExceptionInProcessOrder(request, isManual, response, partnerPrimaryData, partnerOrderDetail, e, "PROCESS_ORDER_IN_KETTLE");
        }
    }
}
