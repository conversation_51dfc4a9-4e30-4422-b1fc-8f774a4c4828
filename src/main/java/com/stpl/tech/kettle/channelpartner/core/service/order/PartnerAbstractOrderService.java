package com.stpl.tech.kettle.channelpartner.core.service.order;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;

import java.util.Map;

public abstract class PartnerAbstractOrderService {


    public TrackService trackService;

    public ChannelPartnerDataCache channelPartnerDataCache;

    public PartnerOrderManagementService partnerOrderManagementService;

    public  PartnerAbstractOrderService(TrackService trackService , ChannelPartnerDataCache channelPartnerCacheService ,
                                    PartnerOrderManagementService partnerOrderManagementService){
        this.trackService = trackService;
        this.channelPartnerDataCache = channelPartnerCacheService;
        this.partnerOrderManagementService = partnerOrderManagementService;
    }

    public  abstract  <R,T> T addOrder(R request , boolean isManual) throws ChannelPartnerException;


    public abstract void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException;














}
