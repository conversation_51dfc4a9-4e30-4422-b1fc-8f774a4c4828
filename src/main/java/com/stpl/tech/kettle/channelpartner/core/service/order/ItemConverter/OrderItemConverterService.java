package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter;

import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderItemFactory;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class OrderItemConverterService extends AbstractOrderItemConverterService{
    public OrderItemConverterService(PartnerItemConverterService partnerItemConverterService, DiscountStrategy discountStrategy,
                                     MasterDataCache masterDataCache, TaxStrategy taxStrategy,
                                     EnvironmentProperties environmentProperties, PartnerOrderConverterDependency partnerOrderConverterDependency) {
        super(partnerItemConverterService, discountStrategy, masterDataCache, taxStrategy, environmentProperties, partnerOrderConverterDependency);
    }
}
