package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import javax.xml.bind.annotation.XmlRootElement;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@XmlRootElement(name = "PartnerMenuStatus")
public class PartnerMenuStatus {

	
	    @Id
	    protected String requestId;
	    protected boolean success;
	    protected String error;
		protected String storeId;
	    
		public String getRequestId() {
			return requestId;
		}
		public void setRequestId(String requestId) {
			this.requestId = requestId;
		}
		public boolean isSuccess() {
			return success;
		}
		public void setSuccess(boolean success) {
			this.success = success;
		}
		public String getError() {
			return error;
		}
		public void setError(String error) {
			this.error = error;
		}
		public String getStoreId() {return storeId;}
	    public void setStoreId(String storeId) {this.storeId = storeId;}
}
