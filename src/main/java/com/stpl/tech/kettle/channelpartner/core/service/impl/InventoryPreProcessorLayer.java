package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitRefreshInventoryEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
public class InventoryPreProcessorLayer extends PartnerBaseDecorator {


    public InventoryPreProcessorLayer(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
    }

    @Override
    public <T, R> R preProcessData(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds, Class<R> returnType) throws ChannelPartnerException {
        try {
            Object obj = processInventoryEvent((UnitRefreshInventoryEvent) reqObj, requestType, partnerIds);
            return returnType.cast(obj);
        } catch (ClassCastException e) {
            throw new ChannelPartnerException(new ChannelPartnerError("", "Class Cast Exception: Unable to cast obj to returnType: " + returnType + e));
        } catch (Exception e) {
            throw new ChannelPartnerException(new ChannelPartnerError("Unable to cast req Obj to UnitPartnerStatusVO ", "This exception has occurred because menu event req is received but req object is of type: " + reqObj.getClass().getName()+ e));
        }
    }

    private PartnerActionEvent processInventoryEvent(UnitRefreshInventoryEvent unitRefreshInventoryEvent, PartnerRequestType requestType, List<Integer> partnerIds) throws ChannelPartnerException {
        switch(requestType){
            case INVENTORY_UPDATE:
                return createInventoryUpdateEvent(unitRefreshInventoryEvent);
            default:
                throw new ChannelPartnerException("No Inventory Publish Event found for "+ requestType.name());
        }
    }

    private PartnerActionEvent createInventoryUpdateEvent(UnitRefreshInventoryEvent unitRefreshInventoryEvent) throws ChannelPartnerException {
        PartnerActionEvent event = new PartnerActionEvent() ;
        event.setPartner(true);
        event.setBrandId(unitRefreshInventoryEvent.getBrandId());
        event.getPartnerIds().addAll(unitRefreshInventoryEvent.getPartnerIds());
        event.setEventData(unitRefreshInventoryEvent);
        event.setEventType(PartnerActionEventType.INVENTORY_UPDATE);
        return event;
    }

}
