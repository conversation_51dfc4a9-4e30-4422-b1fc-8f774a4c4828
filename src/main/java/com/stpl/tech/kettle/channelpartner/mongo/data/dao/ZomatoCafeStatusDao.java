package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ZomatoCafeStatusDao extends MongoRepository<ZomatoCafeStatusData, String> {

    ZomatoCafeStatusData findByUnitId(Integer unitId);

    ZomatoCafeStatusData findByUnitIdAndBrandId(Integer unitId, Integer brandId);

    @Query(value = "{ 'unitId': { $in: ?0 }, 'partnerStatus': ?1, 'brandId': { $exists: ?2 } }",
            sort = "{ 'lastUpdatedTime': -1 }")
    List<ZomatoCafeStatusData> findAllByUnitIdInAndPartnerStatusAndBrandIdExists(Set<Integer> unitIds, boolean status, boolean brandExists);

}
