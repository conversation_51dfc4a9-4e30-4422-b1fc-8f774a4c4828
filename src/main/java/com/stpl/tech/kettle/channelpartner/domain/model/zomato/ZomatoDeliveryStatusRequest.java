package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "outlet_id",
        "status",
        "rider_data"
})
public class ZomatoDeliveryStatusRequest {

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("outlet_id")
    private String outletId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("rider_data")
    private ZomatoRiderData riderData;

    @JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("rider_data")
    public ZomatoRiderData getZomatoRiderData() {
        return riderData;
    }

    @JsonProperty("rider_data")
    public void setZomatoRiderData(ZomatoRiderData riderData) {
        this.riderData = riderData;
    }
}
