package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ZomatoOrderComplaint {
    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("reference_id")
    private String referenceId;

    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @JsonProperty("complaint_message")
    private String complaintMessage;

    @JsonProperty("complaint_reason")
    private String complaintReason;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("expired_at")
    private Long expiredAt;

    @JsonProperty("ordered_items")
    private List<ZomatoCustomerComplaintItem> orderedItems;

    @JsonProperty("customer_complaints_count")
    private int customerComplaintsCount;

    @JsonProperty("repeat_customer_count")
    private int repeatCustomerCount;

    @JsonProperty("refund_options")
    private List<ZomatoOrderComplaintRefundOption> refundOptions;

    @JsonProperty("min_custom_refund")
    private int minCustomRefund;
}
