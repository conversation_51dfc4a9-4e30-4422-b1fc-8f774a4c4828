package com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.impl;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.stpl.tech.kettle.channelpartner.core.service.impl.PartnerOrderServiceImpl.ORDER_REJECTED;


@Log4j2
public abstract class PartnerOrderManagementServiceImpl implements PartnerOrderManagementService {


    private OrderValidationService orderValidationService;

    private ChannelPartnerDataCache channelPartnerDataCache;

    private MasterDataCache masterDataCache;


    @Autowired
    private CommonPartnerEventNotificationService<String, SlackNotification, Boolean, String> commonPartnerEventNotificationService;

    @Autowired
    private TrackService trackService ;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private EnvironmentProperties environmentProperties ;


    private static String rejectOrFailureMessage = "";

    public  PartnerOrderManagementServiceImpl(OrderValidationService orderValidationService, ChannelPartnerDataCache channelPartnerDataCache,
                                              MasterDataCache masterDataCache) {
        this.orderValidationService = orderValidationService;
        this.channelPartnerDataCache = channelPartnerDataCache;
        this.masterDataCache  = masterDataCache;
    }

    @Override
    public <R, Q> PartnerOrderDetail primaryChecks(R request, PartnerOrderDetail partnerOrderDetail, Q response,
                                                   Integer kettlePartnerId, PartnerPrimaryData primaryData) {
        try {
            if (Objects.isNull(primaryData.getOutletId())) {
                orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_MISSING, "Unit Id is missing in the order.");
                //  response.setMessage(ChannelPartnerStringConstants.MISSING_OUTLET_ID);
            } else {
                validateUnit(request, partnerOrderDetail, response, primaryData.getOutletId(), kettlePartnerId);
               /* if (request.getDishes() == null || request.getDishes().isEmpty()) {
                    orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.EMPTY_ORDER, "No items in the order.");
                    response.setMessage("No items in order");
                }
                if (partnerOrderDetail.getOrderErrors().isEmpty() && ZomatoOrderType.DELIVERY.equals(request.getOrderType())) {
                    updateDeliveryDetailsV3(request, partnerOrderDetail);
                }*/
            }
           /* if (!partnerOrderDetail.getOrderErrors().isEmpty()) {
                partnerOrderDetail.setToBeProcessed(false);
                partnerOrderDetail.setToBeRejected(true);
             //   response.setCode(500);
             //   response.setExternalOrderId(partnerOrderDetail.getExternalOrderId());
             //   response.setStatus(ZomatoOrderStatus.FAILED);
            } else {
                partnerOrderDetail.setToBeProcessed(true);
                partnerOrderDetail.setToBeRejected(false);
            }*/
        } catch (Exception e) {
            partnerOrderDetail.setBeingProcessed(false);
        }
        return partnerOrderDetail;
    }

    @Override
    public void checkOrder(PartnerOrderDetail partnerOrderDetail, Order order,
                              Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual , PartnerPrimaryData partnerPrimaryData) {
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerPrimaryData.getPartnerName()).getKettlePartnerId();
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(partnerOrderDetail.getUnitId(),
                partnerOrderDetail.getBrandId(), partnerId);
        UnitPartnerBrandMappingData data = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        boolean inventoryCheckPasses = orderValidationService.runCommonValidations(order, partnerOrderDetail,
                partnerProductTaxMap, data, masterDataCache.getUnit(key.getUnitId()),null);
        log.info("Status of Inventory check {} for partner {} for partnerOrder Id {} ", inventoryCheckPasses,
                partnerOrderDetail.getPartnerName(), partnerOrderDetail.getPartnerOrderId());
    }



    @Override
    public <R> PartnerPrimaryData getPrimaryData(R reqest) {
        throw new NotImplementedException("Please Use Partner Specific Implementation");
    }

    private <T,R> void validateUnit(T request, PartnerOrderDetail partnerOrderDetail,
                              R response, String resId,Integer kettlePartnerId) {
        UnitPartnerBrandMappingData data = channelPartnerDataCache.getUnitPartnerBrandMappingData(resId, kettlePartnerId);
        Integer unitId = data.getUnitId();
        if (masterDataCache.getUnit(unitId) == null) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not valid.");
            //response.setMessage("Outlet id not valid");
        }
        if (masterDataCache.getUnit(unitId).getStatus().equals(UnitStatus.IN_ACTIVE)) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not active.");
            //response.setMessage("Outlet is not active");
        }
        if (!masterDataCache.getUnit(unitId).isLive()) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not live.");
            //response.setMessage("Outlet is not live");
        }
        if (!channelPartnerDataCache.validatePartnerAndMapping(kettlePartnerId, unitId)) {
            orderValidationService.addOrderError(partnerOrderDetail, PartnerOrderErrorCode.UNIT_INVALID, "Unit Id " + unitId + " is not mapped.");
            //response.setMessage("Outlet is not mapped");
        }
    }

    @Override
    public <R, T> T handlesRejectedOrder(R request, PartnerOrderDetail partnerOrderDetail, PartnerOrderStates partnerOrderError) {
        Set<String> errorCodes = new HashSet<>();
        if (partnerOrderDetail.getOrderErrors() != null
                && partnerOrderDetail.getOrderErrors().size() > 0) {
            for (PartnerOrderError error : partnerOrderDetail.getOrderErrors()) {
                if (error.getErrorCode().isToBeRejected()) {
                    errorCodes.add(error.getErrorCode().name());
                }
            }
        }
        UnitBasicDetail ubd = 	masterDataCache.getUnitBasicDetail(partnerOrderDetail.getUnitId());
        rejectOrFailureMessage = getRejectOrFailureMessage(partnerOrderError,partnerOrderDetail,ubd,errorCodes);
        log.error(rejectOrFailureMessage);
        return  commonHandlerForAnyErrorInOrder(partnerOrderDetail);
    }

    @Override
    public <T> T commonHandlerForAnyErrorInOrder(PartnerOrderDetail partnerOrderDetail){
        partnerOrderDetail.setBeingProcessed(false);
        partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
        return (T) sendOrderNotPunchedNotification(partnerOrderDetail);
    }

    private String getRejectOrFailureMessage(PartnerOrderStates partnerOrderStates, PartnerOrderDetail partnerOrderDetail, UnitBasicDetail ubd, Set<String> errorCodes){
        switch (partnerOrderStates){
            case REJECTED ->
                rejectOrFailureMessage = "Rejecting order for partner:::"+ partnerOrderDetail.getPartnerName() +"for cafe :;:::"+ubd.getName()
                        +"for partnerOrder Id::::::"+partnerOrderDetail.getPartnerOrderId()
                        +"with reasons ::::::::::::::"+StringUtils.join(errorCodes, ",");
            case FAILED ->
                rejectOrFailureMessage = "Failed" ;
        }
        return rejectOrFailureMessage;
    }

    @Override
    public <R, T> T handlesDuplicateOrder(R request, T response, PartnerPrimaryData partnerPrimaryData) {
        if (environmentProperties.slackDuplicateOrders()) {
            String message = ChannelPartnerUtils.getMessage("Duplicate Order received for partner ::::" +partnerPrimaryData.getPartnerName()+"and :::::For Request OrderId:::::"+partnerPrimaryData.getOrderId(),"");
            commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(message, SlackNotification.PARTNER_INTEGRATION, false ,partnerPrimaryData.getPartnerName() ,ApplicationName.KETTLE_SERVICE.name());
            return (T) message;
        }
        return null;
    }

    private String sendOrderNotPunchedNotification(PartnerOrderDetail detail) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
            String brandName = detail.getBrandId() != null
                    ? masterDataCache.getBrandMetaData().get(detail.getBrandId()).getBrandName()
                    : "Unknown";
            String message = String.format(
                    "Order Rejected from : %s for %s and cafe %s \n Partner Order Id : %s, Time : %s \nErrors : %s",
                    detail.getPartnerName(), brandName, ubd.getName(), detail.getPartnerOrderId(),
                    detail.getAddTimeIST(), StringUtils.join(detail.getOrderErrors(), ",\n"));
            String message1 = ChannelPartnerUtils.getMessage("Order Rejections", message);
            Map<String,String> params = new HashMap<>();
            params.put("message",message);
            params.put("title", ORDER_REJECTED);
            commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(message1, SlackNotification.ORDER_REJECTED, false, detail.getPartnerName(), ApplicationName.CHANNEL_PARTNER.name());
            try {
                if (ubd.getUnitManagerId() != null) {
                    commonPartnerEventNotificationService.publishPartnerOrderEventNotificationToUser(message1, false, ubd.getUnitManagerId() + "_notify", null);
                    params.put("userId", ubd.getUnitManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() +  AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            }catch(Exception e){
                log.error("Error while publishing order rejection notification on knock ",e);
            }
            try {
                if (ubd.getCafeManagerId() != null) {
                    commonPartnerEventNotificationService.publishPartnerOrderEventNotificationToUser(message1, false, ubd.getUnitManagerId() + "_notify", null);
                    params.put("userId", ubd.getCafeManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            } catch (Exception e) {
                log.error("Error while publishing order rejection notification on knock ",e);
            }
            return message1;
        } catch (Exception e) {
            log.error("Error while publishing slack for inventory down and order punched", e);
        }
        return null;
    }
}
