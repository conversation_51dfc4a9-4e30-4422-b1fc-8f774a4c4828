package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "variant_id",
        "catalogue_id",
        "catalogue_name",
        "unit_cost"
})

public class ZomatoOrderVariantsV3 {
	
	@JsonProperty("variant_id")
    private String variantId;
    @JsonProperty("catalogue_id")
    private String catalogueId;
    @JsonProperty("catalogue_name")
    private String catalogueName;
    @JsonProperty("unit_cost")
    private String unitCost;

	@JsonProperty("quantity")
	private Integer quantity;

	@JsonProperty("max_allowed_quantity")
	private Integer maxAllowedQuantity;

	@JsonProperty("total_cost")
	private Float totalCost;

    @JsonProperty("variant_id")
	public String getVariantId() {
		return variantId;
	}
    
    @JsonProperty("variant_id")
	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}
	
	@JsonProperty("catalogue_id")
	public String getCatalogueId() {
		return catalogueId;
	}
	
	@JsonProperty("catalogue_id")
	public void setCatalogueId(String catalogueId) {
		this.catalogueId = catalogueId;
	}
	
	@JsonProperty("catalogue_name")
	public String getCatalogueName() {
		return catalogueName;
	}
	
	@JsonProperty("catalogue_name")
	public void setCatalogueName(String catalogueName) {
		this.catalogueName = catalogueName;
	}
	
	@JsonProperty("unit_cost")
	public String getUnitCost() {
		return unitCost;
	}
	
	 @JsonProperty("unit_cost")
	public void setUnitCost(String unitCost) {
		this.unitCost = unitCost;
	}


	@JsonProperty("quantity")
	public Integer getQuantity() {
		return quantity;
	}


	@JsonProperty("quantity")
	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	@JsonProperty("max_allowed_quantity")
	public Integer getMaxAllowedQuantity() {
		return maxAllowedQuantity;
	}


	@JsonProperty("max_allowed_quantity")
	public void setMaxAllowedQuantity(Integer maxAllowedQuantity) {
		this.maxAllowedQuantity = maxAllowedQuantity;
	}


	@JsonProperty("total_cost")
	public Float getTotalCost() {
		return totalCost;
	}


	@JsonProperty("total_cost")
	public void setTotalCost(Float totalCost) {
		this.totalCost = totalCost;
	}

	@Override
	public String toString() {
		return "ZomatoOrderVariantsV3 [variantId=" + variantId + ", catalogueId=" + catalogueId + ", catalogueName="
				+ catalogueName + ", unitCost=" + unitCost + "]";
	}
    
	 
	 @Override
	    public int hashCode() {
	        return new HashCodeBuilder().append(variantId).append(catalogueId).append(catalogueName).append(unitCost).toHashCode();
	    }

	    @Override
	    public boolean equals(Object other) {
	        if (other == this) {
	            return true;
	        }
	        if ((other instanceof ZomatoOrderVariantsV3) == false) {
	            return false;
	        }
	        ZomatoOrderVariantsV3 rhs = ((ZomatoOrderVariantsV3) other);
	        return new EqualsBuilder().append(variantId, rhs.variantId).append(catalogueId, rhs.catalogueId).append(catalogueName, rhs.catalogueName).append(unitCost, rhs.unitCost).isEquals();
	    }
    

}
