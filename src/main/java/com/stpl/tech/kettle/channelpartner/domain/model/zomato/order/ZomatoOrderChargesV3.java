package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "charge_id",
    "charge_name",
    "charge_amount",
    "charge_type",
    "charge_value",
    "taxes",
    "charge_taxes_total",
    "is_delivery_charge"
})
public class ZomatoOrderChargesV3 {

    @JsonProperty("charge_id")
    private String chargeId;
    @JsonProperty("charge_name")
    private String chargeName;
    @JsonProperty("charge_amount")
    private Float chargeAmount;
    @JsonProperty("charge_type")
    private String chargeType;
    @JsonProperty("charge_value")
    private Float chargeValue;
    @JsonProperty("taxes")
    private List<ZomatoOrderTaxDetailsV3> chargeTaxes = null;
    @JsonProperty("charge_taxes_total")
    private Float chargeTaxesTotal;
    @JsonProperty("is_delivery_charge")
    private Integer isDeliveryCharge;

    @JsonProperty("charge_id")
    public String getChargeId() {
        return chargeId;
    }

    @JsonProperty("charge_id")
    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    @JsonProperty("charge_name")
    public String getChargeName() {
        return chargeName;
    }

    @JsonProperty("charge_name")
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    @JsonProperty("charge_amount")
    public Float getChargeAmount() {
        return chargeAmount;
    }

    @JsonProperty("charge_amount")
    public void setChargeAmount(Float chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    @JsonProperty("taxes")
    public List<ZomatoOrderTaxDetailsV3> getChargeTaxes() {
        return chargeTaxes;
    }

    @JsonProperty("taxes")
    public void setChargeTaxes(List<ZomatoOrderTaxDetailsV3> chargeTaxes) {
        this.chargeTaxes = chargeTaxes;
    }

    @JsonProperty("charge_type")
    public String getChargeType() {
        return chargeType;
    }

    @JsonProperty("charge_type")
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    @JsonProperty("charge_value")
    public Float getChargeValue() {
        return chargeValue;
    }

    @JsonProperty("charge_value")
    public void setChargeValue(Float chargeValue) {
        this.chargeValue = chargeValue;
    }

    @JsonProperty("charge_taxes_total")
    public Float getChargeTaxesTotal() {
        return chargeTaxesTotal;
    }

    @JsonProperty("charge_taxes_total")
    public void setChargeTaxesTotal(Float chargeTaxesTotal) {
        this.chargeTaxesTotal = chargeTaxesTotal;
    }

    @JsonProperty("is_delivery_charge")
    public Integer getIsDeliveryCharge() {
        return isDeliveryCharge;
    }

    @JsonProperty("is_delivery_charge")
    public void setIsDeliveryCharge(Integer isDeliveryCharge) {
        this.isDeliveryCharge = isDeliveryCharge;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("chargeId", chargeId).append("chargeName", chargeName).append("chargeAmount", chargeAmount).append("chargeTaxes", chargeTaxes).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(chargeAmount).append(chargeTaxes).append(chargeId).append(chargeName).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if (!(other instanceof ZomatoOrderChargesV3)) {
            return false;
        }
        ZomatoOrderChargesV3 rhs = ((ZomatoOrderChargesV3) other);
        return new EqualsBuilder().append(chargeAmount, rhs.chargeAmount).append(chargeTaxes, rhs.chargeTaxes).append(chargeId, rhs.chargeId).append(chargeName, rhs.chargeName).isEquals();
    }

}