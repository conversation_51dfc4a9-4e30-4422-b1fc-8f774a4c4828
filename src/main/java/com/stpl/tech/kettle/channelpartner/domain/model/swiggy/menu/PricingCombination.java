
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "variant_combination",
    "price",
    "addon_combination"
})
public class PricingCombination {

    @JsonProperty("variant_combination")
    private List<VariantCombination> variantCombination = null;
    @JsonProperty("price")
    private Double price = null;
    @JsonProperty("addon_combination")
    private List<AddonCombination> addonCombination = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("variant_combination")
    public List<VariantCombination> getVariantCombination() {
        return variantCombination;
    }

    @JsonProperty("variant_combination")
    public void setVariantCombination(List<VariantCombination> variantCombination) {
        this.variantCombination = variantCombination;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("addon_combination")
    public List<AddonCombination> getAddonCombination() {
        return addonCombination;
    }

    @JsonProperty("addon_combination")
    public void setAddonCombination(List<AddonCombination> addonCombination) {
        this.addonCombination = addonCombination;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("variantCombination", variantCombination).append("price", price).append("addonCombination", addonCombination).append("additionalProperties", additionalProperties).toString();
    }

}
