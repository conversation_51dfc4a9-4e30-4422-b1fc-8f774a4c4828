package com.stpl.tech.kettle.channelpartner.domain.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class UnitChannelPartnerStatus implements Serializable {

    private static final long serialVersionUID = -6803093800767067290L;
    private Integer unitId;
    private String unitName;
    private Integer brandId;
    private Map<String,String> employees;
    private boolean cafeStatus;
    private Integer partnerId;
    private String partnerName;
    private String message;
    private String url;

    public UnitChannelPartnerStatus() {
    }

    public UnitChannelPartnerStatus(int unitId, String unitName, Integer brandId, int cafeManagerEmpId, int areaManagerEmpId, boolean cafeStatus, Integer partnerId, String partnerName,
                String message,String url) {
        Map<String,String> employees = new HashMap<>();
        this.unitId = unitId;
        this.unitName = unitName;
        this.brandId = brandId;
        employees.put("areaManager",String.valueOf(areaManagerEmpId));
        employees.put("cafeManager",String.valueOf(cafeManagerEmpId));
        this.employees = employees;
        this.cafeStatus = cafeStatus;
        this.partnerId = partnerId;
        this.partnerName = partnerName;
        this.message=message;
        this.url=url;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Map<String, String> getEmployees() {
        return employees;
    }

    public void setEmployees(Map<String, String> employees) {
        this.employees = employees;
    }

    public boolean isCafeStatus() {
        return cafeStatus;
    }

    public void setCafeStatus(boolean cafeStatus) {
        this.cafeStatus = cafeStatus;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
