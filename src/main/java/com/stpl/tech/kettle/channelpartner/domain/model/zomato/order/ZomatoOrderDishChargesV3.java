package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "charge_id",
        "name",
        "amount",
        "value",
        "type",
        "taxes"
})
public class ZomatoOrderDishChargesV3 {

    @JsonProperty("charge_id")
    private String chargeId;
    @JsonProperty("name")
    private String chargeName;
    @JsonProperty("type")
    private String type;
    @JsonProperty("value")
    private Float value;
    @JsonProperty("amount")
    private Float chargeAmount;
    @JsonProperty("taxes")
    private List<ZomatoOrderTaxDetailsV3> chargeTaxes = null;

    @JsonProperty("charge_id")
    public String getChargeId() {
        return chargeId;
    }

    @JsonProperty("charge_id")
    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    @JsonProperty("name")
    public String getChargeName() {
        return chargeName;
    }

    @JsonProperty("name")
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    @JsonProperty("amount")
    public Float getChargeAmount() {
        return chargeAmount;
    }

    @JsonProperty("amount")
    public void setChargeAmount(Float chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    @JsonProperty("taxes")
    public List<ZomatoOrderTaxDetailsV3> getChargeTaxes() {
        return chargeTaxes;
    }

    @JsonProperty("taxes")
    public void setChargeTaxes(List<ZomatoOrderTaxDetailsV3> chargeTaxes) {
        this.chargeTaxes = chargeTaxes;
    }

    @JsonProperty("type")
    public String getType() {
		return type;
	}

    @JsonProperty("type")
	public void setType(String type) {
		this.type = type;
	}

    @JsonProperty("value")
    public Float getValue() {
		return value;
	}

    @JsonProperty("value")
	public void setValue(Float value) {
		this.value = value;
	}


	@Override
	public String toString() {
		return "ZomatoOrderDishChargesV3 [chargeId=" + chargeId + ", chargeName=" + chargeName + ", type=" + type
				+ ", value=" + value + ", chargeAmount=" + chargeAmount + ", chargeTaxes=" + chargeTaxes + "]";
	}

	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(chargeAmount).append(type).append(value).append(chargeTaxes).append(chargeId).append(chargeName).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderDishChargesV3) == false) {
            return false;
        }
        ZomatoOrderDishChargesV3 rhs = ((ZomatoOrderDishChargesV3) other);
        return new EqualsBuilder().append(chargeAmount, rhs.chargeAmount).append(value, rhs.value).append(type, rhs.type).append(chargeTaxes, rhs.chargeTaxes).append(chargeId, rhs.chargeId).append(chargeName, rhs.chargeName).isEquals();
    }

}