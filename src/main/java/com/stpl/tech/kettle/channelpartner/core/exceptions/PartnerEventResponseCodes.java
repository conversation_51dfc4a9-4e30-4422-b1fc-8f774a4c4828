package com.stpl.tech.kettle.channelpartner.core.exceptions;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum PartnerEventResponseCodes {

    MENU(Collections.singletonMap(200, "MENU GENERATION SUCCESSFULLY COMPLETED AFTER POST EVENT  PROCESSING !"),
            Collections.singletonMap(500, "MENU GENERATION FAILED DURING POST EVENT  PROCESSING !"),
    Collections.singletonMap(300, "MENU GENERATION INCOMPLETE AS PUSH REQUEST TO PARTNER FAILED !")),

    STOCK(Collections.singletonMap(200, "STOCK UPDATION SUCCESSFULLY COMPLETED AFTER POST EVENT  PROCESSING!"),
            Collections.singletonMap(500, "STOCK UPDATION FAILED DURING POST EVENT  PROCESSING ! "),
            Collections.singletonMap(300, "STOCK GENERATION INCOMPLETE AS PUSH REQUEST TO PARTNER FAILED !")),
    INVENTORY(Collections.singletonMap(200, "INVENTORY UPDATION SUCCESSFULLY COMPLETED AFTER POST EVENT  PROCESSING!"),
            Collections.singletonMap(500, "INVENTORY UPDATION FAILED DURING POST EVENT  PROCESSING !"),
            Collections.singletonMap(300, "INVENTORY GENERATION INCOMPLETE AS PUSH REQUEST TO PARTNER FAILED !"));

    private final Map<Integer, String> successMap;
    private final Map<Integer, String> errorMap;
    private final Map<Integer, String> otherMap ;
}
