package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public abstract class EventAbstractFactory<T,R> {
    protected ChannelPartnerDataCache channelPartnerDataCache;
    protected MasterDataCache masterDataCache;
    protected EnvironmentProperties environmentProperties;
    protected CommonMetadataValidationService commonMetadataValidationService;
    protected CommonPartnerEventNotificationService<String, SlackNotification,Boolean,String> commonPartnerEventNotificationService;
    protected PartnerMenuService partnerMenuService;

    public EventAbstractFactory(PartnerMetadataBuilder partnerMetadataBuilder) {
        this.channelPartnerDataCache = partnerMetadataBuilder.channelPartnerDataCache;
        this.masterDataCache = partnerMetadataBuilder.masterDataCache;
        this.environmentProperties = partnerMetadataBuilder.environmentProperties;
        this.commonMetadataValidationService = partnerMetadataBuilder.commonMetadataValidationService;
        this.commonPartnerEventNotificationService = partnerMetadataBuilder.commonPartnerEventNotificationService;
        this.partnerMenuService=partnerMetadataBuilder.partnerMenuService;
    }


    public abstract List<R> prepareMetadata(T reqObj, PartnerActionEventType eventType, String partnerName) throws ChannelPartnerException;


}
