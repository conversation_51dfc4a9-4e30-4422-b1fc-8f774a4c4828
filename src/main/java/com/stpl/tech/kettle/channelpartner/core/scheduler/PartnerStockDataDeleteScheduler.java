package com.stpl.tech.kettle.channelpartner.core.scheduler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.channelpartner.domain.model.ProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.AppUtils;

@Component
public class PartnerStockDataDeleteScheduler {


    @Autowired
    PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;

    @Autowired
    private MasterDataCache masterDataCache;

    private static final Logger LOG = LoggerFactory.getLogger(PartnerStockDataDeleteScheduler.class);


    @Scheduled(cron = "0 30 1 * * *", zone = "GMT+05:30")
    public void deleteStockRecordsAfterTwoWeeks() {
        LOG.info("CRON JOB: deleteStockRecordsAfterTwoWeeks : STARTED");
        Date afterTwoWeekDate = AppUtils.getOldDate(AppUtils.getCurrentDate(), 15);
        LOG.info("deleting data for date before {}",afterTwoWeekDate);
        masterDataCache.getAllChannelPartners().forEach(channelPartnerDetail -> {
            LOG.info("searching data for {}",channelPartnerDetail.getName());
            Slice<PartnerUnitProductStockSnapshot> slice = null;
            Pageable pageable = PageRequest.of(0, 100);
            while (true) {
                slice = partnerUnitStockSnapshotDao.findAllByPartnerId(channelPartnerDetail.getId(), pageable);
                List<PartnerUnitProductStockSnapshot> snapshotsList = slice.getContent();
                snapshotsList.forEach(partnerUnitProductStockSnapshot -> {
                    if (partnerUnitProductStockSnapshot != null && partnerUnitProductStockSnapshot.getProductStockSnapshot().size() > 0) {
                        List<ProductStockSnapshot> toBeSavedList = new ArrayList<>();
                        partnerUnitProductStockSnapshot.getProductStockSnapshot().forEach(productStockSnapshot -> {
                            if (!AppUtils.isBefore(AppUtils.getDate(productStockSnapshot.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"), afterTwoWeekDate)) {
//                                LOG.info("saving value  for unit {} for product {} for partner {}", partnerUnitProductStockSnapshot.getUnitId(), partnerUnitProductStockSnapshot.getProductId(), partnerUnitProductStockSnapshot.getPartnerId());
                                toBeSavedList.add(productStockSnapshot);
                            }
                        });
                        partnerUnitProductStockSnapshot.setProductStockSnapshot(toBeSavedList);
                        partnerUnitStockSnapshotDao.save(partnerUnitProductStockSnapshot);
                    }
                });
                if (!slice.hasNext()) {
                    break;
                }
                pageable = slice.nextPageable();
            }
        });
        LOG.info("CRON JOB: deleteStockRecordsAfterTwoWeeks : FINISHED");
    }


}
