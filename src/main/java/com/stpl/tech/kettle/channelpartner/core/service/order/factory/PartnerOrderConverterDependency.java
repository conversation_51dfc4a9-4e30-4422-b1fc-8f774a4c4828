package com.stpl.tech.kettle.channelpartner.core.service.order.factory;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.common.factory.CommonPartnerDependency;
import com.stpl.tech.kettle.channelpartner.core.service.order.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class PartnerOrderConverterDependency extends CommonPartnerDependency {

    @Autowired
    @Qualifier("magicpinItemConverter")
    private PartnerItemConverterService magicpintemConverterService;


    @Autowired
    private OrderValidationService orderValidationService;


    @Autowired
    private TrackService trackService;

    @Autowired
    @Qualifier("magicpinTaxStrategy")
    private TaxStrategy magicPinTaxStrategy;

    @Autowired
    @Qualifier("magicpinDiscountStrategy")
    private DiscountStrategy magicPinDiscountStrategy;

    @Autowired
    @Qualifier("magicpinOrderService")
    private PartnerOrderManagementService magicPinOrderManagementService;

    @Autowired
    private PartnerOrderService partnerOrderService ;


    public OrderValidationService getOrderValidationService() {
        return orderValidationService;
    }


    public PartnerItemConverterService getPartnerItemConverterService(String partnerName){
        switch (partnerName){
            case "MAGICPIN":
                return magicpintemConverterService;
            default:
                throw  new IllegalArgumentException("In Valid Partner Name");
        }
    }

    public PartnerOrderService getPartnerOrderService() {
        return partnerOrderService;
    }

    public DiscountStrategy getdiscountStrategy(String partnerName) {
        switch (partnerName){
            case "MAGICPIN":
                return magicPinDiscountStrategy;
            default:
                throw  new IllegalArgumentException("In Valid Partner Name");
        }
    }

    public TaxStrategy getTaxStrategy(String partnerName){
        switch (partnerName){
            case "MAGICPIN" :
                return magicPinTaxStrategy;
            default:
                throw new IllegalArgumentException("In Valid Partner Name");
        }
    }

    public PartnerOrderManagementService getPartnerOrderManagementService(String partnerName) {
        switch (partnerName){
            case "MAGICPIN" :
                return magicPinOrderManagementService;
            default:
                throw new IllegalArgumentException("In Valid Partner Name");
        }
    }

    public TrackService getTrackService() {
        return trackService;
    }

}
