package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "penalty_amount_message",
        "penalty_time_message"
})
public class ZomatoOrderPenaltyMessageV3 {
	
	@JsonProperty("penalty_amount_message")
    private Integer penaltyAmountMessage;
    @JsonProperty("penalty_time_message")
    private String penaltyTimeMessage;
    
    @JsonProperty("penalty_amount_message")
	public Integer getPenaltyAmountMessage() {
		return penaltyAmountMessage;
	}

    @JsonProperty("penalty_amount_message")
	public void setPenaltyAmountMessage(Integer penaltyAmountMessage) {
		this.penaltyAmountMessage = penaltyAmountMessage;
	}

    @JsonProperty("penalty_time_message")
	public String getPenaltyTimeMessage() {
		return penaltyTimeMessage;
	}

    @JsonProperty("penalty_time_message")
	public void setPenaltyTimeMessage(String penaltyTimeMessage) {
		this.penaltyTimeMessage = penaltyTimeMessage;
	}

	@Override
	public String toString() {
		return "ZomatoOrderPenaltyMessageV3 [penaltyAmountMessage=" + penaltyAmountMessage + ", penaltyTimeMessage="
				+ penaltyTimeMessage + "]";
	}
    
	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(penaltyAmountMessage).append(penaltyTimeMessage).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderPenaltyMessageV3) == false) {
            return false;
        }
        ZomatoOrderPenaltyMessageV3 rhs = ((ZomatoOrderPenaltyMessageV3) other);
        return new EqualsBuilder().append(penaltyAmountMessage, rhs.penaltyAmountMessage).append(penaltyTimeMessage, rhs.penaltyTimeMessage).isEquals();
    }

}
