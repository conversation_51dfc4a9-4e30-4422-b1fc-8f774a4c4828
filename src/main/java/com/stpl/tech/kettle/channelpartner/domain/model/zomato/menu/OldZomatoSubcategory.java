
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "subcategory_id",
        "subcategory_name",
        "subcategory_description",
        "subcategory_is_active",
        "subcategory_image_url",
        "subcategory_tags",
        "subcategory_order",
        "items"
})
public class OldZomatoSubcategory {

    @JsonProperty("subcategory_id")
    private String subcategoryId;
    @JsonProperty("subcategory_name")
    private String subcategoryName;
    @JsonProperty("subcategory_description")
    private String subcategoryDescription;
    @JsonProperty("subcategory_is_active")
    private Integer subcategoryIsActive;
    @JsonProperty("subcategory_image_url")
    private String subcategoryImageUrl;
    @JsonProperty("subcategory_tags")
    private List<Integer> subcategoryTags = null;
    @JsonProperty("subcategory_order")
    private Integer subcategoryOrder;
    @JsonProperty("items")
    private List<ZomatoMenuItem> items = null;

    @JsonProperty("subcategory_id")
    public String getSubcategoryId() {
        return subcategoryId;
    }

    @JsonProperty("subcategory_id")
    public void setSubcategoryId(String subcategoryId) {
        this.subcategoryId = subcategoryId;
    }

    @JsonProperty("subcategory_name")
    public String getSubcategoryName() {
        return subcategoryName;
    }

    @JsonProperty("subcategory_name")
    public void setSubcategoryName(String subcategoryName) {
        this.subcategoryName = subcategoryName;
    }

    @JsonProperty("subcategory_description")
    public String getSubcategoryDescription() {
        return subcategoryDescription;
    }

    @JsonProperty("subcategory_description")
    public void setSubcategoryDescription(String subcategoryDescription) {
        this.subcategoryDescription = subcategoryDescription;
    }

    @JsonProperty("subcategory_is_active")
    public Integer getSubcategoryIsActive() {
        return subcategoryIsActive;
    }

    @JsonProperty("subcategory_is_active")
    public void setSubcategoryIsActive(Integer subcategoryIsActive) {
        this.subcategoryIsActive = subcategoryIsActive;
    }

    @JsonProperty("subcategory_image_url")
    public String getSubcategoryImageUrl() {
        return subcategoryImageUrl;
    }

    @JsonProperty("subcategory_image_url")
    public void setSubcategoryImageUrl(String subcategoryImageUrl) {
        this.subcategoryImageUrl = subcategoryImageUrl;
    }

    @JsonProperty("subcategory_tags")
    public List<Integer> getSubcategoryTags() {
        return subcategoryTags;
    }

    @JsonProperty("subcategory_tags")
    public void setSubcategoryTags(List<Integer> subcategoryTags) {
        this.subcategoryTags = subcategoryTags;
    }

    @JsonProperty("subcategory_order")
    public Integer getSubcategoryOrder() {
        return subcategoryOrder;
    }

    @JsonProperty("subcategory_order")
    public void setSubcategoryOrder(Integer subcategoryOrder) {
        this.subcategoryOrder = subcategoryOrder;
    }

    @JsonProperty("items")
    public List<ZomatoMenuItem> getItems() {
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<ZomatoMenuItem> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return "ZomatoSubcategory{" +
                "subcategoryId='" + subcategoryId + '\'' +
                ", subcategoryName='" + subcategoryName + '\'' +
                ", subcategoryDescription='" + subcategoryDescription + '\'' +
                ", subcategoryIsActive=" + subcategoryIsActive +
                ", subcategoryImageUrl='" + subcategoryImageUrl + '\'' +
                ", subcategoryTags=" + subcategoryTags +
                ", subcategoryOrder=" + subcategoryOrder +
                ", items=" + items +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OldZomatoSubcategory that = (OldZomatoSubcategory) o;

        return new EqualsBuilder()
                .append(subcategoryId, that.subcategoryId)
                .append(subcategoryName, that.subcategoryName)
                .append(subcategoryDescription, that.subcategoryDescription)
                .append(subcategoryIsActive, that.subcategoryIsActive)
                .append(subcategoryImageUrl, that.subcategoryImageUrl)
                .append(subcategoryTags, that.subcategoryTags)
                .append(subcategoryOrder, that.subcategoryOrder)
                .append(items, that.items)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(subcategoryId)
                .append(subcategoryName)
                .append(subcategoryDescription)
                .append(subcategoryIsActive)
                .append(subcategoryImageUrl)
                .append(subcategoryTags)
                .append(subcategoryOrder)
                .append(items)
                .toHashCode();
    }
}
