package com.stpl.tech.kettle.channelpartner.domain.model;

public enum TaxPayingEntity {
    SELF("DEFAULT", "RESTAURANT", "VENDOR","merchant"),
    PARTNER("SOURCE_TAX", "SWIGGY", "SWIGGY","magicpin");

    private final String zomato;
    private final String swiggy;
    private final String swiggyNew;

    private final String magicPin;

    TaxPayingEntity(String zomato, String swiggy, String swiggyNew , String magicPin) {
        this.zomato = zomato;
        this.swiggy = swiggy;
        this.swiggyNew = swiggyNew;
        this.magicPin = magicPin;
    }

    public String getZomato() {
        return zomato;
    }

    public String getSwiggy() {
        return swiggy;
    }

	public String getSwiggyNew() {
		return swiggyNew;
	}

    public String getMagicPin(){return magicPin;}


    
}
