
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"vendorEntityId",
	"name",
	"order",
	"subCategories" 
})
public class ZomatoMenuTiming {

    @JsonProperty("service")
    private String service;
	@JsonProperty("time1From")
    private String time1From;
	@JsonProperty("time1To")
	private String time1To;
	@JsonProperty("time2From")
	private String time2From;
	@JsonProperty("time2To")
	private String time2To;
	@JsonProperty("time3From")
	private String time3From;
	@JsonProperty("time3To")
	private String time13To;
	@JsonProperty("dayMonday")
	private boolean dayMonday;
	@JsonProperty("dayTuesday")
	private boolean dayTuesday;
	@JsonProperty("dayWednesday")
	private boolean dayWednesday;
	@JsonProperty("dayThursday")
	private boolean dayThursday;
	@JsonProperty("dayFriday")
	private boolean dayFriday;
	@JsonProperty("daySaturday")
	private boolean daySaturday;
	@JsonProperty("daySunday")
	private boolean daySunday;
	@JsonProperty("startDate")
	private String startDate;
	@JsonProperty("endDate")
	private String endDate;

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getTime1From() {
		return time1From;
	}

	public void setTime1From(String time1From) {
		this.time1From = time1From;
	}

	public String getTime1To() {
		return time1To;
	}

	public void setTime1To(String time1To) {
		this.time1To = time1To;
	}

	public String getTime2From() {
		return time2From;
	}

	public void setTime2From(String time2From) {
		this.time2From = time2From;
	}

	public String getTime2To() {
		return time2To;
	}

	public void setTime2To(String time2To) {
		this.time2To = time2To;
	}

	public String getTime3From() {
		return time3From;
	}

	public void setTime3From(String time3From) {
		this.time3From = time3From;
	}

	public String getTime13To() {
		return time13To;
	}

	public void setTime13To(String time13To) {
		this.time13To = time13To;
	}

	public boolean isDayMonday() {
		return dayMonday;
	}

	public void setDayMonday(boolean dayMonday) {
		this.dayMonday = dayMonday;
	}

	public boolean isDayTuesday() {
		return dayTuesday;
	}

	public void setDayTuesday(boolean dayTuesday) {
		this.dayTuesday = dayTuesday;
	}

	public boolean isDayWednesday() {
		return dayWednesday;
	}

	public void setDayWednesday(boolean dayWednesday) {
		this.dayWednesday = dayWednesday;
	}

	public boolean isDayThursday() {
		return dayThursday;
	}

	public void setDayThursday(boolean dayThursday) {
		this.dayThursday = dayThursday;
	}

	public boolean isDayFriday() {
		return dayFriday;
	}

	public void setDayFriday(boolean dayFriday) {
		this.dayFriday = dayFriday;
	}

	public boolean isDaySaturday() {
		return daySaturday;
	}

	public void setDaySaturday(boolean daySaturday) {
		this.daySaturday = daySaturday;
	}

	public boolean isDaySunday() {
		return daySunday;
	}

	public void setDaySunday(boolean daySunday) {
		this.daySunday = daySunday;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoMenuTiming that = (ZomatoMenuTiming) o;

		return new EqualsBuilder()
			.append(service, that.service)
			.append(time1From, that.time1From)
			.append(time1To, that.time1To)
			.append(time2From, that.time2From)
			.append(time2To, that.time2To)
			.append(time3From, that.time3From)
			.append(time13To, that.time13To)
			.append(dayMonday, that.dayMonday)
			.append(dayTuesday, that.dayTuesday)
			.append(dayWednesday, that.dayWednesday)
			.append(dayThursday, that.dayThursday)
			.append(dayFriday, that.dayFriday)
			.append(daySaturday, that.daySaturday)
			.append(daySunday, that.daySunday)
			.append(startDate, that.startDate)
			.append(endDate, that.endDate)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(service)
			.append(time1From)
			.append(time1To)
			.append(time2From)
			.append(time2To)
			.append(time3From)
			.append(time13To)
			.append(dayMonday)
			.append(dayTuesday)
			.append(dayWednesday)
			.append(dayThursday)
			.append(dayFriday)
			.append(daySaturday)
			.append(daySunday)
			.append(startDate)
			.append(endDate)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoMenuTiming{" +
			"service='" + service + '\'' +
			", time1From='" + time1From + '\'' +
			", time1To='" + time1To + '\'' +
			", time2From='" + time2From + '\'' +
			", time2To='" + time2To + '\'' +
			", time3From='" + time3From + '\'' +
			", time13To='" + time13To + '\'' +
			", dayMonday='" + dayMonday + '\'' +
			", dayTuesday='" + dayTuesday + '\'' +
			", dayWednesday='" + dayWednesday + '\'' +
			", dayThursday='" + dayThursday + '\'' +
			", dayFriday='" + dayFriday + '\'' +
			", daySaturday='" + daySaturday + '\'' +
			", daySunday='" + daySunday + '\'' +
			", startDate='" + startDate + '\'' +
			", endDate='" + endDate + '\'' +
			'}';
	}
}
