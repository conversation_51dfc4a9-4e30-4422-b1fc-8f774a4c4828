package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoMenuRequestStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderDataV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderMaskV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.*;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoRatingRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRejectRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoFetchOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ZomatoService {

    //ZomatoOrderResponse addZomatoOrder(ZomatoOrderRequest zomatoOrderRequest, boolean isManual);

    void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException;

    void confirmZomatoOrder(ZomatoNotificationRequest request, boolean isManual);

    void updateZomatoMenu(Integer unitId, boolean isNew, PartnerActionEventType eventType, Integer brandId, Integer integer) throws ChannelPartnerException;

    void updateZomatoMenuNew(Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) throws ChannelPartnerException;

    //void updateZomatoMenuOld(Integer unitId, PartnerActionEventType eventType, Integer brandId) throws ChannelPartnerException;

    //UnitMenuAddVO createZomatoMenuRequestObjOld(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId) throws ChannelPartnerException;

    UnitMenuAddVO createZomatoMenuRequestObj(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) throws ChannelPartnerException;

    void updateZomatoStock(UnitProductsStockEvent event);

    //void updateZomatoStockV1(UnitProductsStockEvent event);

    void updateZomatoStockV3(UnitProductsStockEvent event);

    void refreshUnitInventory(List<Integer> unitIds);

    void refreshUnitInventory(List<Integer> unitIds,Integer brandId);

    void setUnitDeliveryStatus(List<Integer> unitIds, Boolean status, Integer brandId) throws ChannelPartnerException;

    ZomatoLogisticsStatusResponse getOutletLogisticsStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    ZomatoNotificationResponse updateOutletLogisticsStatus(ZomatoLogisticsChangeRequest request) throws ChannelPartnerException;

    ZomatoDeliveryStatusResponse getOutletDeliveryStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    ZomatoNotificationResponse updateOutletDeliveryStatus(ZomatoDeliveryChangeRequest request) throws ChannelPartnerException;

    ZomatoTakeawayStatusResponse getOutletTakeawayStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    void updateOutletTakeawayStatus(UnitPartnerStatusVO request) throws ChannelPartnerException;

    void updateZomatoOrderDeliveryStatus(ZomatoDeliveryStatusRequest request);

    void updateZomatoOrderStatus(ZomatoOrderStatusRequest request, Integer approvedBy);

    void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck);

    void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual);

    void updateMenuStatus(ZomatoMenuRequestStatus request);

    ZomatoOrderResponse addZomatoOrderV3(ZomatoOrderRequestV3 order, boolean isManual);

    boolean sendMenuOffers(PartnerOfferDetail partnerOfferDetail, List<PartnerOfferDetail> activePartnerOfferDetails) throws IllegalArgumentException, IllegalAccessException, ChannelPartnerException;

    boolean updateRiderDetails(ZomatoOrderRiderDataV3 request);

    boolean updateRiderMask(ZomatoOrderRiderMaskV3 request);

    //boolean addSingleServeMenu(UnitMenuAddVO request);

    void pushMenuToUnits(List<Integer> unitIdsForMenu, Integer brandId,
                         Integer employeeId, Integer kettlePartnerId, MenuType menuType) throws ChannelPartnerException;

    void scheduledMenuPush(List<Integer> unitIdsForMenu, MenuType menuType , Integer brandId) throws ChannelPartnerException, IOException;

    MACRespose macRelay(MACRequest request);

    void updateZomatoOutletStatus(Integer brandId, ZomatoDeliveryChangeRequest request);

    boolean updateCafeDeliveryTimeZomato(CafeTimingChangeRequest cafeTimingChangeRequest);

    void uploadZomatoOrderRating();

    boolean getZomatoOutletOrderRating(ZomatoRatingRequest zomatoRatingRequest);

    boolean updateZomatoCafeTimings(Integer outletId, String event, List<UnitHours> oldBusinessHours);

    void updateAllZomatoCafeTimings();

    void savePartnerOrderDataMysql(PartnerOrderDetail partnerOrderDetail , Integer kettleOrderId);

    void updateZomatoStockV3ForScheduleStockUpdate(UnitProductsStockEvent event , PartnerStockUpdateSchedule partnerStockUpdateSchedule);

    public void postMenuProcessNotification(ZomatoMenuRequestStatus request, PartnerMenuStatus partnerMenuStatus);

    public ZomatoNotificationResponse rejectOrder(ZomatoOrderRejectRequest request, Integer brandId, Integer rejectedBy);

    public Map<Integer, String> getRejectionReasons();


    public ZomatoFetchOrderResponse fetchOrderStatus(String zomatoOrderId);

    public Boolean routeComplaintRequestToN8N(ZomatoOrderComplaint request);
}
