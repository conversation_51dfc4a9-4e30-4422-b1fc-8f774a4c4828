package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.impl;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicPinAdditionalCharges;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinChargesDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItemAddons;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItems;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.OrderItemAddonOptions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Log4j2
@Qualifier("magicpinItemConverter")
public class MagicPinItemConverterServiceImpl implements PartnerItemConverterService {

    private static String PAID_ADDON_IDENTIFIER = "Paid_Addons";
    private static String SIZE_IDENTIFIER = "Size";
    private static String RECOM_IDENTIFIER = "_Recommendations";

    private static String MILK_IDENTIFIER = "Milk Option";

    @Override
    public <I> PartnerItemData getPartnerItemData(I item) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        String[] itemIds = ((MagicpinOrderItems) item).getThirdPartyId().split("_");
        Integer productId = 0;
        try {
            productId = Integer.valueOf(itemIds[0]);
        } catch (Exception e) {
            log.error("Couldn't cast item id to Product id For  {} ", magicpinOrderItem.getThirdPartyId());
        }
        return PartnerItemData.builder().itemId(magicpinOrderItem.getThirdPartyId()).itemName(magicpinOrderItem.getDisplayText())
                .quantity(magicpinOrderItem.getQuantity().intValue()).originalProductId(productId).productId(updateDesiChaiProductId(magicpinOrderItem, productId))
                .dimensionCode(getDimensionCode(magicpinOrderItem)).paidAddonProducts(getPaidAddonItems(magicpinOrderItem))
                .addonProducts(getAddonItems(magicpinOrderItem)).recommendedProducts(getRecommendedItems(magicpinOrderItem))
                .taxPayingEntity(magicpinOrderItem.getTaxLiability()).unitPrice(getItemPrice(item, magicpinOrderItem.getThirdPartyId()))
                .totalItemPrice(getItemTotalPrice(item, magicpinOrderItem.getThirdPartyId())).discountWeight(((MagicpinOrderItems) item)
                        .getDistributedDiscountWieght())
                .build();
    }


    private String getMilkPropertyValue(MagicpinOrderItems magicpinOrderItem) {
        String sizeProperty = "";
        for (MagicpinOrderItemAddons magicpinOrderItemAddonGroup : magicpinOrderItem.getMagicpinOrderItemAddonsList()) {
            if (magicpinOrderItemAddonGroup.getName().equalsIgnoreCase(SIZE_IDENTIFIER)) {
                for (OrderItemAddonOptions addonOption : magicpinOrderItemAddonGroup.getOrderItemAddonsDetails()) {
                    if(!CollectionUtils.isEmpty(addonOption.getOptionGroups())){
                        for(MagicpinOrderItemAddons modifierAddonGroup : addonOption.getOptionGroups()){
                            if(modifierAddonGroup.getName().equalsIgnoreCase(MILK_IDENTIFIER)){
                                for(OrderItemAddonOptions modifierAddon : modifierAddonGroup.getOrderItemAddonsDetails()){
                                    if(modifierAddon.isSelected()){
                                        sizeProperty = modifierAddon.getName();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if(StringUtils.isEmpty(sizeProperty)){
            return getMilkPropertyValueForSingleDimension(magicpinOrderItem);
        }
        return sizeProperty;
    }

    private String getMilkPropertyValueForSingleDimension(MagicpinOrderItems magicpinOrderItem){
        String sizeProperty = "";
        for (MagicpinOrderItemAddons magicpinOrderItemAddonGroup : magicpinOrderItem.getMagicpinOrderItemAddonsList()) {
            if (magicpinOrderItemAddonGroup.getName().equalsIgnoreCase(MILK_IDENTIFIER)) {
                for (OrderItemAddonOptions addonOption : magicpinOrderItemAddonGroup.getOrderItemAddonsDetails()) {
                    if(addonOption.isSelected()){
                        sizeProperty = addonOption.getName();
                        break;
                    }
                    }
                }
        }

        return sizeProperty;
    }

    private Integer updateDesiChaiProductId(MagicpinOrderItems magicpinOrderItem, Integer productId) {
        Integer finalProductId = productId;
        if (productId.equals(10)) {
            finalProductId = getDesiChaiMilkProductId(magicpinOrderItem, productId);
        }else if(productId.equals(1282)){
            finalProductId = getLemonGrassMilkProductId(magicpinOrderItem,productId);
        }
        return finalProductId;
    }


    private Integer getDesiChaiMilkProductId(MagicpinOrderItems item, Integer productId) {
        if (ChannelPartnerUtils.getDesiChaiMilkMap().get(getMilkPropertyValue(item)) != null) {
            return ChannelPartnerUtils.getDesiChaiMilkMap().get(getMilkPropertyValue(item).trim());
        }
        return productId;
    }

    private Integer getLemonGrassMilkProductId(MagicpinOrderItems item, Integer productId) {
        if (ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(getMilkPropertyValue(item)) != null) {
            return ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(getMilkPropertyValue(item).trim());
        }
        return productId;
    }

    @Override
    public <I> PartnerItemData getConstituentProductPartnerItemData(I item, Integer constituentProductId,
                                                                    Integer quantity, String dimension, String name) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        return PartnerItemData.builder().itemId(String.valueOf(constituentProductId)).productId(updateDesiChaiProductId(magicpinOrderItem,constituentProductId))
                .quantity(quantity).addonProducts(getConstituentAddonItems(magicpinOrderItem, constituentProductId))
                .paidAddonProducts(getConstituentPaidAddonItems(magicpinOrderItem, constituentProductId))
                .recommendedProducts(getConstituentRecommendedItems(magicpinOrderItem, constituentProductId))
                .dimensionCode(dimension).itemName(name).taxPayingEntity(magicpinOrderItem.getTaxLiability()).build();
    }

    private String getDimensionCode(MagicpinOrderItems item) {
        String dimnensionCode = "None";
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (magicpinOrderItemAddon.getName().equalsIgnoreCase(SIZE_IDENTIFIER)) {
                    dimnensionCode = magicpinOrderItemAddon.getOrderItemAddonsDetails().get(0).getName();
                }
            }
        }
        return dimnensionCode;
    }

    private List<PartnerItemAddonData> getPaidAddonItems(MagicpinOrderItems item) {
        List<PartnerItemAddonData> paidAddonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (magicpinOrderItemAddon.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER)) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if(orderItemAddonOption.isSelected()){
                            paidAddonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                        }
                    }
                } else {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if (!CollectionUtils.isEmpty(orderItemAddonOption.getOptionGroups())) {
                            for (MagicpinOrderItemAddons modifierAddonGroup : orderItemAddonOption.getOptionGroups()) {
                                if (modifierAddonGroup.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER)) {
                                    for (OrderItemAddonOptions modifierGroupAddon : modifierAddonGroup.getOrderItemAddonsDetails()) {
                                        if(modifierGroupAddon.isSelected()){
                                            paidAddonsList.add(getPartnerItemAddonData(modifierGroupAddon, item));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return paidAddonsList;
    }

    private List<PartnerItemAddonData> getConstituentPaidAddonItems(MagicpinOrderItems item, Integer constituentProductId) {
        List<PartnerItemAddonData> paidAddonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (magicpinOrderItemAddon.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER)) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if((orderItemAddonOption.getThirdPartyId().startsWith(constituentProductId + "_")
                                || orderItemAddonOption.getThirdPartyId().endsWith("_" + constituentProductId))){
                            if(orderItemAddonOption.isSelected()){
                                paidAddonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                            }
                        }
                    }
                }
            }
        }
        return paidAddonsList;
    }

    private PartnerItemAddonData getPartnerItemAddonData(OrderItemAddonOptions orderItemAddonOption, MagicpinOrderItems magicpinOrderItem) {
        String[] itemIds = orderItemAddonOption.getThirdPartyId().split("_");
        Integer productId = 0;
        try {
            productId = Integer.valueOf(itemIds[0]);
        } catch (Exception e) {
            log.error("Couldn't cast item id to Product id For  {} ", orderItemAddonOption.getThirdPartyId());
        }
        return PartnerItemAddonData.builder().partnerItemId(orderItemAddonOption.getThirdPartyId())
                .name(orderItemAddonOption.getName()).quantity(magicpinOrderItem.getQuantity().intValue())
                .unitPrice(BigDecimal.valueOf(orderItemAddonOption.getMrp())).productId(productId)
                .build();

    }

    private List<PartnerItemAddonData> getAddonItems(MagicpinOrderItems item) {
        List<PartnerItemAddonData> addonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (!magicpinOrderItemAddon.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER) &&
                        !magicpinOrderItemAddon.getThirdPartyId().contains(RECOM_IDENTIFIER)) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if(orderItemAddonOption.isSelected()){
                            addonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                            if (!CollectionUtils.isEmpty(orderItemAddonOption.getOptionGroups())) {
                                for (MagicpinOrderItemAddons modifierAddonGroup : orderItemAddonOption.getOptionGroups()) {
                                    if (!modifierAddonGroup.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER)
                                            && !modifierAddonGroup.getThirdPartyId().contains(RECOM_IDENTIFIER)) {
                                        for (OrderItemAddonOptions modifierGroupAddon : modifierAddonGroup.getOrderItemAddonsDetails()) {
                                            if(modifierGroupAddon.isSelected()){
                                                addonsList.add(getPartnerItemAddonData(modifierGroupAddon, item));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return addonsList;
    }

    private List<PartnerItemAddonData> getConstituentAddonItems(MagicpinOrderItems item, Integer constituentProductId) {
        List<PartnerItemAddonData> addonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if ((!magicpinOrderItemAddon.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER) &&
                        !magicpinOrderItemAddon.getThirdPartyId().contains(RECOM_IDENTIFIER))) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if((orderItemAddonOption.getThirdPartyId().startsWith(constituentProductId + "_")
                                || orderItemAddonOption.getThirdPartyId().endsWith("_" + constituentProductId))
                        ||(magicpinOrderItemAddon.getThirdPartyId().startsWith(constituentProductId + "_")
                                || magicpinOrderItemAddon.getThirdPartyId().endsWith("_" + constituentProductId))
                        ){
                            if(orderItemAddonOption.isSelected()){
                                addonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                            }
                        }
                    }
                }
            }
        }
        return addonsList;
    }

    private List<PartnerItemAddonData> getRecommendedItems(MagicpinOrderItems item) {
        List<PartnerItemAddonData> recommAddonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (magicpinOrderItemAddon.getThirdPartyId().contains(RECOM_IDENTIFIER)) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if(orderItemAddonOption.isSelected()){
                            recommAddonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                        }
                    }
                } else {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        //recommAddonsList.add(getPartnerItemAddonData(orderItemAddonOption));
                        if (!CollectionUtils.isEmpty(orderItemAddonOption.getOptionGroups())) {
                            for (MagicpinOrderItemAddons modifierAddonGroup : orderItemAddonOption.getOptionGroups()) {
                                if (modifierAddonGroup.getThirdPartyId().contains(RECOM_IDENTIFIER)) {
                                    for (OrderItemAddonOptions modifierGroupAddon : modifierAddonGroup.getOrderItemAddonsDetails()) {
                                        if(modifierGroupAddon.isSelected()){
                                            recommAddonsList.add(getPartnerItemAddonData(modifierGroupAddon, item));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return recommAddonsList;
    }

    private List<PartnerItemAddonData> getConstituentRecommendedItems(MagicpinOrderItems item,
                                                                      Integer constituentProductId) {
        List<PartnerItemAddonData> recommAddonsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if (magicpinOrderItemAddon.getThirdPartyId().contains(RECOM_IDENTIFIER)) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if((orderItemAddonOption.getThirdPartyId().startsWith(constituentProductId + "_")
                                || orderItemAddonOption.getThirdPartyId().endsWith("_" + constituentProductId))){
                            if(orderItemAddonOption.isSelected()){
                                recommAddonsList.add(getPartnerItemAddonData(orderItemAddonOption, item));
                            }
                        }
                    }
                }
            }
        }
        return recommAddonsList;
    }


    @Override
    public <I> BigDecimal getItemPrice(I item, String itemId) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        if (magicpinOrderItem.getThirdPartyId().equalsIgnoreCase(itemId)) {
            BigDecimal totalModifiersPrice = getTotalModifiersPrice(magicpinOrderItem);
            return ChannelPartnerUtils.subtract(BigDecimal.valueOf(magicpinOrderItem.getAmount()), totalModifiersPrice);
        }
        return getOptionsPrice(magicpinOrderItem, itemId);
    }

    private <I> BigDecimal getItemTotalPrice(I item, String itemId) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        if (magicpinOrderItem.getThirdPartyId().equalsIgnoreCase(itemId)) {
            return BigDecimal.valueOf(magicpinOrderItem.getAmount());
        }
        return getOptionsPrice(magicpinOrderItem, itemId);
    }

    private BigDecimal getTotalModifiersPrice(MagicpinOrderItems item) {
        BigDecimal totalModifiersPrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                if ((magicpinOrderItemAddon.getThirdPartyId().contains(PAID_ADDON_IDENTIFIER) ||
                        magicpinOrderItemAddon.getThirdPartyId().contains(RECOM_IDENTIFIER))) {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if (orderItemAddonOption.isSelected() && BigDecimal.valueOf(orderItemAddonOption.getMrp()).compareTo(BigDecimal.ZERO) > 0) {
                            totalModifiersPrice = ChannelPartnerUtils.add(totalModifiersPrice, BigDecimal.valueOf(orderItemAddonOption.getMrp()));
                        }

                    }
                } else {
                    for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                        if (!CollectionUtils.isEmpty(orderItemAddonOption.getOptionGroups())) {
                            for (MagicpinOrderItemAddons modifierAddonGroup : orderItemAddonOption.getOptionGroups()) {
                                for (OrderItemAddonOptions modifierAddon : modifierAddonGroup.getOrderItemAddonsDetails()) {
                                    if (modifierAddon.isSelected() && BigDecimal.valueOf(modifierAddon.getMrp()).compareTo(BigDecimal.ZERO) > 0) {
                                        totalModifiersPrice = ChannelPartnerUtils.add(totalModifiersPrice, BigDecimal.valueOf(modifierAddon.getMrp()));
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }
        return totalModifiersPrice;

    }


    private BigDecimal getOptionsPrice(MagicpinOrderItems item, String itemId) {
        BigDecimal itemPrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(item.getMagicpinOrderItemAddonsList())) {
            for (MagicpinOrderItemAddons magicpinOrderItemAddon : item.getMagicpinOrderItemAddonsList()) {
                for (OrderItemAddonOptions orderItemAddonOption : magicpinOrderItemAddon.getOrderItemAddonsDetails()) {
                    if (orderItemAddonOption.getThirdPartyId().equalsIgnoreCase(itemId)) {
                        return BigDecimal.valueOf(orderItemAddonOption.getMrp());
                    } else {
                        if (!CollectionUtils.isEmpty(orderItemAddonOption.getOptionGroups())) {
                            for (MagicpinOrderItemAddons modifierAddonGroup : orderItemAddonOption.getOptionGroups()) {
                                for (OrderItemAddonOptions modifierAddon : modifierAddonGroup.getOrderItemAddonsDetails()) {
                                    if (modifierAddon.getThirdPartyId().equalsIgnoreCase(itemId)) {
                                        return BigDecimal.valueOf(modifierAddon.getMrp());
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
        return itemPrice;

    }

    @Override
    public <I, R> List<I> getItems(R request) {
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        return (List<I>) distributeDiscountWeight(magicpinOrderRequest.getMagicpinOrderItems());
    }

    private List<MagicpinOrderItems> distributeDiscountWeight(List<MagicpinOrderItems> magicpinOrderItems){
        BigDecimal totalAmount = BigDecimal.ZERO;
        for(MagicpinOrderItems magicpinOrderItem :  magicpinOrderItems){
            totalAmount = totalAmount.add(BigDecimal.valueOf(magicpinOrderItem.getAmount() * magicpinOrderItem.getQuantity()));
        }
        for(MagicpinOrderItems magicpinOrderItem : magicpinOrderItems){
            BigDecimal weight = ChannelPartnerUtils.divide(BigDecimal.valueOf(magicpinOrderItem.getAmount()*magicpinOrderItem.getQuantity())
            ,totalAmount);
            magicpinOrderItem.setDistributedDiscountWieght(weight);
        }
        return magicpinOrderItems;
    }

    @Override
    public <R> PartnerItemData getOrderLevelPackagingCharge(R request) {
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        BigDecimal packagingCharges = BigDecimal.ZERO;
        Boolean taxDeductedByPartner = false;
        String taxPayingEntity = "";
        BigDecimal taxRate = BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(magicpinOrderRequest.getAdditionalCharges())){
            MagicPinAdditionalCharges magicPinAdditionalCharge = magicpinOrderRequest.getAdditionalCharges().get(0);
            packagingCharges = BigDecimal.valueOf(magicPinAdditionalCharge.getAmount());
            taxDeductedByPartner = TaxPayingEntity.PARTNER.getMagicPin().
                    equalsIgnoreCase(magicPinAdditionalCharge.getTaxLiability());
            taxPayingEntity = magicPinAdditionalCharge.getTaxLiability();
            if(!CollectionUtils.isEmpty(magicPinAdditionalCharge.getMagicpinTaxDetails())){
                taxRate = BigDecimal.valueOf(magicPinAdditionalCharge.getMagicpinTaxDetails().get(0).getRate());
            }

        }
        return PartnerItemData.builder().unitPrice(packagingCharges).
                taxDeductedByPartner(taxDeductedByPartner).taxPayingEntity(taxPayingEntity)
                .taxRate(taxRate).build();
    }

    @Override
    public <I> PartnerItemData getPackagingCharge(I item) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        BigDecimal packagingCharges = BigDecimal.ZERO;
        Boolean taxDeductedByPartner = false;
        String taxPayingEntity = "";
        BigDecimal taxRate = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(magicpinOrderItem.getMagicpinChargesDetails())) {
            for (MagicpinChargesDetails magicpinChargesDetail : magicpinOrderItem.getMagicpinChargesDetails()) {
                if (magicpinChargesDetail.getName().equalsIgnoreCase("Packaging Charges")) {
                    packagingCharges = ChannelPartnerUtils.add(packagingCharges,
                            BigDecimal.valueOf(magicpinChargesDetail.getAmount()));
                    taxDeductedByPartner = TaxPayingEntity.PARTNER.getMagicPin().
                            equalsIgnoreCase(magicpinOrderItem.getTaxLiability());
                    taxPayingEntity = magicpinOrderItem.getTaxLiability();
                    if(!CollectionUtils.isEmpty(magicpinChargesDetail.getMagicpinTaxDetails())){
                        taxRate = BigDecimal.valueOf(magicpinChargesDetail.getMagicpinTaxDetails().get(0).getRate());
                    }

                }
            }
        }
        return PartnerItemData.builder().unitPrice(packagingCharges).
                taxDeductedByPartner(taxDeductedByPartner).taxPayingEntity(taxPayingEntity)
                .taxRate(taxRate).build();
    }

}
