package com.stpl.tech.kettle.channelpartner.core.queue.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class DelayedPartnerActionEvent extends PartnerActionEvent implements Serializable, Delayed {

    private long time;
    private Integer partnerId;

    public DelayedPartnerActionEvent(long time, Integer partnerId) {
        this.time = time;
        this.partnerId = partnerId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        long diff = time - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MICROSECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        if (this.time < ((DelayedPartnerActionEvent)o).time) {
            return -1;
        }
        if (this.time > ((DelayedPartnerActionEvent)o).time) {
            return 1;
        }
        return 0;
    }
}
