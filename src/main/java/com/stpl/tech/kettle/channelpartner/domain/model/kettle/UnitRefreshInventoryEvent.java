package com.stpl.tech.kettle.channelpartner.domain.model.kettle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitRefreshInventoryEvent implements Serializable {
    @Builder.Default
    private List<Integer> unitIds = new ArrayList<>();
    @Builder.Default
    private List<Integer> partnerIds= new ArrayList<>();
    private Integer brandId ;
}
