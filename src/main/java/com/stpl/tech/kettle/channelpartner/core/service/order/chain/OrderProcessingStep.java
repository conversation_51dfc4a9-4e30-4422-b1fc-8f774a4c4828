package com.stpl.tech.kettle.channelpartner.core.service.order.chain;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderProcessingChainBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class OrderProcessingStep<R, T> extends OrderProcessingChain<R, T> {
    protected OrderProcessingStep<R, T> nextOrderProcessingStep;

    protected TrackService trackService;
    protected PartnerOrderManagementService partnerOrderManagementService;
    protected ChannelPartnerDataCache channelPartnerDataCache;
    protected MasterDataCache masterDataCache;
    protected WebServiceHelper webServiceHelper;
    protected EnvironmentProperties environmentProperties;

    public OrderProcessingStep(OrderProcessingChainBuilder<R, T> builder) {
        this.partnerOrderManagementService = builder.partnerOrderManagementService;
        this.channelPartnerDataCache = builder.channelPartnerDataCache;
        this.masterDataCache = builder.masterDataCache;
        this.webServiceHelper = builder.webServiceHelper;
        this.environmentProperties = builder.environmentProperties;
        this.trackService = builder.trackService;
    }


    @Override
    public T process(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        if(nextOrderProcessingStep!=null){
            return nextOrderProcessingStep.process(request, isManual, response, partnerPrimaryData, partnerOrderDetail);
        }else{
            return response;
        }
    }

    @SafeVarargs
    public final OrderProcessingStep<R, T> linkNextStep(OrderProcessingStep<R, T>... orderProcessingSteps) {
        if (orderProcessingSteps == null || orderProcessingSteps.length == 0) {
            throw new IllegalArgumentException("At least one processing step must be provided.");
        }

        OrderProcessingStep<R, T> head = orderProcessingSteps[0];
        OrderProcessingStep<R, T> current = head;

        for (int i = 1; i < orderProcessingSteps.length; i++) {
            current.nextOrderProcessingStep = orderProcessingSteps[i];
            current = orderProcessingSteps[i];
        }
        return head;
    }

    T checkNext(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail, OrderProcessingStep<R, T> nextOrderProcessingStep) throws ChannelPartnerException {
        if (nextOrderProcessingStep == null) {
            return response;
        }
        return nextOrderProcessingStep.process(request, isManual, response, partnerPrimaryData, partnerOrderDetail);
    }

    public T handleExceptionInProcessOrder(R request, boolean isManual, T response, PartnerPrimaryData partnerPrimaryData, PartnerOrderDetail partnerOrderDetail, Exception e, String exceptionEvent) throws ChannelPartnerException {
        return null;
    }

    public OrderProcessingStep<R, T> getNextOrderProcessingStep() {
        return nextOrderProcessingStep;
    }

    public void setNextOrderProcessingStep(OrderProcessingStep<R, T> nextOrderProcessingStep) {
        this.nextOrderProcessingStep = nextOrderProcessingStep;
    }
}
