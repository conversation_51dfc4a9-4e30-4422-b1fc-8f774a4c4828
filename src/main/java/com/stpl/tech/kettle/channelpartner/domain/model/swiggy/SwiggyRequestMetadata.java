package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "updated_prep_time"
})
public class SwiggyRequestMetadata {

    @JsonProperty("updated_prep_time")
    private float updatedPrepTime;

    @JsonProperty("updated_prep_time")
    public float getUpdatedPrepTime() {
        return updatedPrepTime;
    }

    @JsonProperty("updated_prep_time")
    public void setUpdatedPrepTime(float updatedPrepTime) {
        this.updatedPrepTime = updatedPrepTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SwiggyRequestMetadata that = (SwiggyRequestMetadata) o;
        return Float.compare(that.updatedPrepTime, updatedPrepTime) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(updatedPrepTime);
    }

    @Override
    public String toString() {
        return "SwiggyRequestMetadata{" +
                "updatedPrepTime=" + updatedPrepTime +
                '}';
    }
}
