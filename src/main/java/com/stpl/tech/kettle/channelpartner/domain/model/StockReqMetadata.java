package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUpsellingSuperCombosProdIdDetail;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
//This will be a common pojo for all partners - unitProduct stock event
// 1 . For Zomato before sending request to partner ---> catalogueVendorEntityIds and variantVendorEntiyIds  will be processed
// 2. For MagicPIn before sending update stock call to partner -----> stockEventProductIds ,
public class StockReqMetadata implements Serializable {
    private static final long serialVersionUID = -6349776002703202879L;
    private UnitPartnerBrandKey unitPartnerBrandKey;
    private List<String> catalogueVendorEntityIds ;
    private List<String> variantVendorEntityIds ;
    private List<String> stockEventProductIds ; //eg vada pav etc  eg - catalogueVendorEntityIds in zomato
    @Builder.Default
    private Map<String, List<String>> productDimensionLevelStockMap= new HashMap<>();
    @Builder.Default
    private Map<String, List<String>> productDimensionAndVariantLevelStockMap= new HashMap<>();
    private PartnerUpsellingSuperCombosProdIdDetail unitUpsellCombosMapping;
    private UnitProductsStockEvent unitProductsStockEvent ;
    private String outletId ;
    private Object partnerProductStockEventReqObj ;
    private StockStatus stockStatus ;
    private List<String> heroAndUpsellingProductIds ;
    private List<String> addOnProductIds ;
}

