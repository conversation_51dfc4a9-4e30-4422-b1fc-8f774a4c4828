package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.kie.api.definition.rule.All;
import org.springframework.beans.factory.annotation.Autowired;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderRejectionData {
    private SwiggyRejectOrderDetail swiggyRejectOrderDetail;

    private RejectionMetadata rejectionMetadata;

    private ReasonMetaData reasonMetaData;
}
