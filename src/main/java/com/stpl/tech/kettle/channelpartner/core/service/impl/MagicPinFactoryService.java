package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.exceptions.MagicpinError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.PartnerEventResponseCodes;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.core.util.MagicpinServiceEndPoints;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuReqResponseData;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqResponseData;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinCallBackData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinItem;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinMenuRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinMenuResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinModifier;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinOption;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu.MagicPinStoreData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinUnitAndInventoryUpdateResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.stock.MagicPinOptionStockData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.stock.MagicPinSkuStockData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.HttpStatusCodeException;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.stock.MagicPinUnitProductStock;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
public class MagicPinFactoryService extends PartnerAbstractFactory {

    public MagicPinFactoryService(PartnerMetadataBuilder partnerMetadataBuilder){
        super(partnerMetadataBuilder);
    }
    private static final String PARTNER_NAME = "MAGICPIN";


    @Override
    public <T> T convertToPartnerStockEvent(T obj) throws ChannelPartnerException {
        if (obj instanceof StockReqMetadata ) {
            StockReqMetadata stockReqMetadata = (StockReqMetadata) obj;
            List<String> stockEventProductIds = stockReqMetadata.getStockEventProductIds();
            List<String> heroAndUpsellingProductIds = stockReqMetadata.getHeroAndUpsellingProductIds();
            List<String> addOnProductIds = stockReqMetadata.getAddOnProductIds();
            MagicPinUnitProductStock magicPinUnitProductStock = new MagicPinUnitProductStock();
            magicPinUnitProductStock.setPartnerStoreId(String.valueOf(masterDataCache.getUnitPartnerBrandMappingMetaData().get(stockReqMetadata.getUnitPartnerBrandKey()).getRestaurantId()));
            if(Objects.nonNull(stockEventProductIds) && !stockEventProductIds.isEmpty()){
                stockEventProductIds.forEach(stockEventProductId -> {
                    magicPinUnitProductStock.getSkus().add(MagicPinSkuStockData.builder()
                            .partnerSku(stockEventProductId)
                            .availability(stockReqMetadata.getStockStatus().getEnable())
                            .build());
                });
            }

            if(Objects.nonNull(heroAndUpsellingProductIds) && !heroAndUpsellingProductIds.isEmpty()){
                heroAndUpsellingProductIds.forEach(heroAndUpsellingProductId -> {
                    magicPinUnitProductStock.getOptions().add(MagicPinOptionStockData.builder()
                            .availability(stockReqMetadata.getStockStatus().getEnable())
                            .id(heroAndUpsellingProductId)
                            .build());
                });
            }

            if(Objects.nonNull(addOnProductIds) && !addOnProductIds.isEmpty()){
                addOnProductIds.forEach(addOnProductId -> {
                    magicPinUnitProductStock.getOptions().add(MagicPinOptionStockData.builder()
                            .id(addOnProductId).availability(stockReqMetadata.getStockStatus().getEnable())
                            .build());
                });
            }

            //Till date this will work for GNT for product Like Gjar ka halwa having teo dimenison - Single serve , and serves two
            /*if( Objects.nonNull(stockReqMetadata.getProductDimensionLevelStockMap()) && !stockReqMetadata.getProductDimensionLevelStockMap().isEmpty()){
                for(Map.Entry<String,List<String>> productDimensionLevelStockEntry : stockReqMetadata.getProductDimensionLevelStockMap().entrySet()){
                    magicPinUnitProductStock.getOptions().addAll(productDimensionLevelStockEntry.getValue().stream().map(productDimensionId ->
                         MagicPinOptionStockData.builder().id(productDimensionId).availability(stockReqMetadata.getStockStatus().getEnable()).build()
                    ).collect(Collectors.toList()));
                }
            }*/

            if (Objects.nonNull(stockReqMetadata.getProductDimensionAndVariantLevelStockMap()) && !stockReqMetadata.getProductDimensionAndVariantLevelStockMap().isEmpty()) {
                for (Map.Entry<String, List<String>> productDimVariantLevelEntry : stockReqMetadata.getProductDimensionAndVariantLevelStockMap().entrySet()) {
                    magicPinUnitProductStock.getOptions().addAll(productDimVariantLevelEntry.getValue().stream().map(productDimensionId ->
                            MagicPinOptionStockData.builder().id(productDimensionId).availability(stockReqMetadata.getStockStatus().getEnable()).build()
                    ).collect(Collectors.toList()));
                }
            }
            stockReqMetadata.setPartnerProductStockEventReqObj(magicPinUnitProductStock);
            return (T) stockReqMetadata;
        } else {
            throw new ChannelPartnerException(" Event recieved is not of type StockReqMetadata . Actual type of event recieved is :::::::::::" + obj.getClass().getName());
        }
    }

    @Override
    public <R> R sendMenuPushReqToPartner(UnitMenuAddVO request) {
        Object menuRequestObject = request.getMenuRequest();
        Gson gson = new Gson();
        MagicPinMenuRequest menuRequest = gson.fromJson(gson.toJson(menuRequestObject), MagicPinMenuRequest.class);
        boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                unitChannelPartnerMapping.getChannelPartner().getId() == 24 &&
                        unitChannelPartnerMapping.getUnit().getId() == request.getUnitId());
        MenuReqResponseData<MagicPinMenuRequest, MagicPinMenuResponse> magicPinMenuReqResponseData =
                MenuReqResponseData.<MagicPinMenuRequest, MagicPinMenuResponse>builder()
                        .unitMenuAddVO(request)
                        .partnerMenuReqObj(menuRequest)
                        .status(false)
                        .build();
        if (mappingValid) {
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(request.getUnitId(), request.getBrandId(), 24);
            String restaurantId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getRestaurantId();
            menuRequest.setPartnerStoreId(restaurantId);

            String res = "";
            String errorStr = "";
            updateStockValueForMenuPush(menuRequest, new UnitPartnerBrandKey(request.getUnitId(), request.getBrandId(), AppConstants.CHANNEL_PARTNER_ZOMATO));
            if (menuRequest != null) {
                menuRequest.setPartnerStoreId(getTestOutletForDev(request.getBrandId(), menuRequest.getPartnerStoreId()));
                try {
                    MagicPinMenuResponse response = webServiceHelper.callMagicpinApi(environmentProperties,
                            MagicpinServiceEndPoints.UPDATE_CATALOG, HttpMethod.POST, menuRequest,
                            MagicPinMenuResponse.class, request.getBrandId());
                    magicPinMenuReqResponseData.setMenuRes(response);
                    magicPinMenuReqResponseData.setStatus(true);
//                menuPushPostProcessing(response, request, request.getEmployeeId());
                    String responseJson = new Gson().toJson(response);
                    res = "Magic Pin menu added response:\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                            + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n" + responseJson;

                } catch (HttpStatusCodeException e) {
                    magicPinMenuReqResponseData.setStatus(false);
                    errorStr = e.getResponseBodyAsString();
                    res = "Error adding menu to Magic Pin :\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                            + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n";
                } catch (Exception e) {
                    magicPinMenuReqResponseData.setStatus(false);
                    errorStr = e.getMessage();
                    res = "Error adding menu to Magic Pin:\n" + "unit:" + masterDataCache.getUnit(request.getUnitId()).getName()
                            + "\n brand: " + masterDataCache.getBrandMetaData().get(request.getBrandId()).getBrandName() + "\n";
                }
                magicPinMenuReqResponseData.setErrorBody(errorStr);
                magicPinMenuReqResponseData.setRes(res);
            }
        }
        return (R) magicPinMenuReqResponseData;

    }

    private String getTestOutletForDev(Integer brandId, String outletId) {
        if (!ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            outletId = ChannelPartnerUtils.getMagicPinTestOutletId();
        }
        return outletId;
    }

    @Override
    public <T, R> R convertToPartnerMenu(T obj, Class<R> returnType) throws ChannelPartnerException {
        if (obj instanceof MenuReqMetadata) {
            MenuReqMetadata menuReqMetadata = (MenuReqMetadata) obj;
            PartnerUnitMenuDetail partnerUnitMenuData = menuReqMetadata.getPartnerUnitMenuData();
            if (partnerUnitMenuData == null) {
                log.error("No Menu Mapping found for Unit: {} with brandId: {} for {}", menuReqMetadata.getUnitId(), menuReqMetadata.getBrandId(), menuReqMetadata.getKettlePartnerId());
                return null;
            }

            UnitMenuAddVO unitMenuAddVO = menuReqMetadata.getUnitMenuAddVO();
            Object menuData = partnerUnitMenuData.getMenuData();
            Gson gson = new Gson();
            MagicPinMenuRequest magicPinMenuObj = gson.fromJson(gson.toJson(partnerUnitMenuData.getMenuData()), MagicPinMenuRequest.class);

            if (menuData != null) {
                MagicPinMenuRequest magicPinMenuRequest = createMagicPinMenuRequest(  magicPinMenuObj, menuReqMetadata);
                unitMenuAddVO.setMenuRequest(magicPinMenuRequest);
                unitMenuAddVO.setVersion(menuReqMetadata.getPartnerUnitMenuVersionMapping().getVersion());
            }
            return returnType.cast(unitMenuAddVO);
        }
        return null;
    }

    private MagicPinMenuRequest createMagicPinMenuRequest(MagicPinMenuRequest magicPinMenuRequest, MenuReqMetadata menuReqMetadata) {
        magicPinMenuRequest.setStore(createMagicPinStoreData(menuReqMetadata));
        magicPinMenuRequest.setMagicPinCallBackData(new MagicPinCallBackData());
        magicPinMenuRequest.setOutletId(menuReqMetadata.getUnitPartnerBrandKey().getUnitId().toString());
        magicPinMenuRequest.setPartnerStoreId(menuReqMetadata.getRestaurantId());

        return magicPinMenuRequest;
    }

    private <U> List<U> deserializeListToJson(List<Object> list, Class<U> type) {
        Gson gson = new Gson();
        Type listType = TypeToken.getParameterized(List.class, type).getType();
        String json = gson.toJson(list, listType);
        return gson.fromJson(json, listType);
    }

    private MagicPinStoreData createMagicPinStoreData(MenuReqMetadata menuReqMetadata) {
        return MagicPinStoreData.builder()
                .id(masterDataCache.getUnitPartnerBrandMappingMetaData().get(menuReqMetadata.getUnitPartnerBrandKey())
                        .getPartnerSourceSystemId().toString())
                .integrationCode(masterDataCache.getBrandMetaData().get(menuReqMetadata.getBrandId()).getBrandName())
                .build();
    }

    @Override
    public <T> List<PartnerEventResponse> postEventProcessing(List<T> obj, EventType eventType) throws ChannelPartnerException {
        switch (eventType) {
            case Menu:
                final List<PartnerEventResponse> postMenuEventProcessingResList = postMenuEventProcessing(obj, eventType);
                break ;
            case Stock:
                final List<PartnerEventResponse> postStockEventProcessingResList  = postStockEventProcessing(obj, eventType);
                break;
            default: throw new ChannelPartnerException("Unknown type of post event recieved ::::::::{}"+ eventType.name());

        }
        return null;
    }

    private <T,R> List<R> postStockEventProcessing(List<T> obj, EventType eventType) throws ChannelPartnerException {
        Gson gson = new Gson();
        List<PartnerEventResponse> partnerEventResponses = new ArrayList<>();
        for (T req : obj) {
            StockReqResponseData stockReqResponseData = (StockReqResponseData) req;
            StockReqMetadata stockReqMetadata = (StockReqMetadata) stockReqResponseData.getStockReqToPartner();
            UnitProductsStockEvent stockEvent = stockReqMetadata.getUnitProductsStockEvent();
            MagicPinUnitProductStock magicPinUnitProductStock = (MagicPinUnitProductStock)stockReqMetadata.getPartnerProductStockEventReqObj();
            if (stockReqResponseData.isStatus()) {
                try{
                    logStockUpdateSnapshot(magicPinUnitProductStock, stockReqMetadata.getUnitProductsStockEvent());
                    addPartnerEventRes(partnerEventResponses, 200, PartnerEventResponseCodes.STOCK.getErrorMap().get(0), null, eventType);
                }catch(Exception e){
                    log.error("Exception cuaght while logging stock update snapshot  FOR STOCK EVENT ::::::::::{}", new Gson().toJson(stockEvent), e);
                    addPartnerEventRes(partnerEventResponses, 500, PartnerEventResponseCodes.STOCK.getErrorMap().get(0), e, eventType);
                }
            } else {
                String message = ChannelPartnerUtils.getMessage(stockReqResponseData.getRes(), stockReqResponseData.getErrorBody());
                commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(message, SlackNotification.PARTNER_INTEGRATION, false, PARTNER_NAME, ApplicationName.KETTLE_SERVICE.name());
                partnerOrderService.logStockRefreshEvent(stockEvent.getUnitId(), stockReqMetadata.getUnitPartnerBrandKey().getPartnerId(),
                        stockEvent.getBrandId(),stockEvent.getStatus().name(),
                        AppConstants.FAILED);
                addPartnerEventRes(partnerEventResponses, 300, PartnerEventResponseCodes.STOCK.getOtherMap().get(0), null, eventType);
            }
        }
        return (List<R>) partnerEventResponses;
    }

    private void addPartnerEventRes(List<PartnerEventResponse> partnerEventResponses, int code, String s, Exception e, EventType eventType) {
        PartnerEventResponse partnerEventResponse = PartnerEventResponse.builder()
                .eventType(eventType.name())
                .code(code)
                .response(s)
                .error(Objects.nonNull(e) ? e.toString():"")
                .build();
        partnerEventResponses.add(partnerEventResponse);
    }

    private <T> List<PartnerEventResponse> postMenuEventProcessing(List<T> obj, EventType eventType) {
        Gson gson = new Gson();
        List<PartnerEventResponse> partnerEventResponses = new ArrayList<>();
        for (T req : obj) {
            MenuReqResponseData<MagicPinMenuRequest, MagicPinMenuResponse> menuReqResponseData = (MenuReqResponseData<MagicPinMenuRequest, MagicPinMenuResponse>) req;
            if (menuReqResponseData.isStatus()) {
                try{
                    commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(menuReqResponseData.getRes(), SlackNotification.PARTNER_MENU, true, PARTNER_NAME, ApplicationName.KETTLE_SERVICE.name());
                    UnitMenuAddVO unitMenuAddVO =  menuReqResponseData.getUnitMenuAddVO();
                    MagicPinMenuRequest magicPinMenuRequest =  gson.fromJson(gson.toJson(menuReqResponseData.getPartnerMenuReqObj()), MagicPinMenuRequest.class);
                    MagicPinMenuResponse magicPinMenuResponse =  gson.fromJson(gson.toJson(menuReqResponseData.getMenuRes()), MagicPinMenuResponse.class);
                    menuPushPostProcessing(magicPinMenuResponse, unitMenuAddVO, magicPinMenuRequest);
                    addPartnerEventRes(partnerEventResponses, 200, PartnerEventResponseCodes.MENU.getErrorMap().get(0), null, eventType);
                }catch (Exception e ){
                    log.error("Exception cuaght while logging MENU POST  PROCESSING FOR MENU EVENT ::::::::::" ,e);
                    addPartnerEventRes(partnerEventResponses, 500, PartnerEventResponseCodes.MENU.getErrorMap().get(0), e, eventType);
                }

            } else {
                String message = ChannelPartnerUtils.getMessage(menuReqResponseData.getRes(), menuReqResponseData.getErrorBody());
                commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(message, SlackNotification.PARTNER_INTEGRATION, false, PARTNER_NAME,ApplicationName.KETTLE_SERVICE.name());
                addPartnerEventRes(partnerEventResponses, 300, PartnerEventResponseCodes.MENU.getOtherMap().get(0), null, eventType);

            }
        }
        return partnerEventResponses;
    }

    private void menuPushPostProcessing(MagicPinMenuResponse response, UnitMenuAddVO request, MagicPinMenuRequest magicPinMenuRequest) {
//        if (response.getMenuResponse().getProcessed() != null && response.getMenuResponse().getProcessed()) {
        partnerMenuService.updatePartnerMenuAuditData(request.getKettlePartnerId(), request.getUnitId(),
                request.getBrandId(), request.getEmployeeId(), request.getMenuType().toString(), request.getVersion(), PARTNER_NAME, response);
        Map<MagicPinItem, List<MagicPinOption>> productsDimensionVariantMap = updateUnitProductMappingCache(magicPinMenuRequest, request);
        updatePartnerUnitProductPricing(request.getKettlePartnerId(), request.getUnitId(), request.getBrandId(),
                request.getEmployeeId(), magicPinMenuRequest, productsDimensionVariantMap);
        channelPartnerDataCache.loadUnitProductComboMappings(masterDataCache.getUnitBasicDetail(request.getUnitId()));
    }

    private Map<MagicPinItem, List<MagicPinOption>> updateUnitProductMappingCache(MagicPinMenuRequest request, UnitMenuAddVO unitMenuAddVO) {
        List<String> upsellingProductIds = new ArrayList<>();
        //Todo - Make changes in future for SuperCombo and HeroCombo as currently they are not live in our system .Ishman
        List<String> superComboProductIds = new ArrayList<>();
        List<String> addOnProductIds = new ArrayList<>();
        Map<String, List<String>> productVariantsMap = new HashMap<>();
        Map<MagicPinItem, List<MagicPinOption>> productDimensionVariantMap = new HashMap<>();
        prepareProductVariantsMap(request, productVariantsMap);
        setUpsellAndAddonProductIds(request.getModifiers(), upsellingProductIds,addOnProductIds);
        getAddonProductIds(request.getModifiers(), addOnProductIds);
        /*for (ZomatoCombo combos : menuRequest.getMenu().getCombos()) {
            superComboProductIds.add(combos.getVendorEntityId());
        }*/
        log.info("Product variants map is not empty. Updating value to db.");
        if (!upsellingProductIds.isEmpty() || !superComboProductIds.isEmpty() || !productVariantsMap.isEmpty()) {
            partnerMenuService.addPartnerUnitMenuMappingForUpsellingProds(new ArrayList<>(), unitMenuAddVO, upsellingProductIds,
                    superComboProductIds, productVariantsMap, addOnProductIds, null,null);
            channelPartnerDataCache.loadUnitUpsellingSuperComboMapping(unitMenuAddVO.getKettlePartnerId(), unitMenuAddVO.getUnitId(), unitMenuAddVO.getBrandId());
        }
        return productDimensionVariantMap;
    }

    private void prepareProductVariantsMap(MagicPinMenuRequest request, Map<String, List<String>> productVariantsMap) {
        request.getMenu().getCategories().forEach(magicPinCategory -> {
            magicPinCategory.getSubCategories().forEach(magicPinSubCategory -> {
                magicPinSubCategory.getItems().forEach(magicPinItem -> {
                    List<String> variantIds = new ArrayList<>();
                    magicPinItem.getCustomizations().forEach(magicPinCustomization -> {
                        if (ChannelPartnerServiceConstants.MAGIC_PIN_SIZE_IDENTIFIIER.equalsIgnoreCase(magicPinCustomization.getTitle())) {
                            magicPinCustomization.getOptions().forEach(magicPinOption -> {
                                variantIds.add(magicPinOption.getId());
                            });
                        }
                    });
                    if (!productVariantsMap.isEmpty() && productVariantsMap.containsKey(magicPinItem.getId())) {
                        productVariantsMap.get(magicPinItem.getId()).addAll(variantIds);
                    } else {
                        productVariantsMap.put(magicPinItem.getId(), variantIds);
                    }
                });
            });
        });
    }

    private void getAddonProductIds(List<MagicPinModifier> modifiers, List<String> addOnProductIds) {
        for (MagicPinModifier magicPinModifier : modifiers) {
            if (!magicPinModifier.getOptions().isEmpty()) {
                magicPinModifier.getOptions().forEach(magicPinOption -> {
                    try {
                        if (masterDataCache.getProduct(Integer.parseInt(magicPinOption.getId())).isInventoryTracked()) {
                            addOnProductIds.add(magicPinOption.getId());
                        }
                    } catch (NumberFormatException ignored) {

                    }
                });
            }
        }
    }

    private void updatePartnerUnitProductPricing(Integer kettlePartnerId, Integer unitId, Integer brandId,
                                                 Integer employeeId, MagicPinMenuRequest magicPinMenuRequest, Map<MagicPinItem, List<MagicPinOption>> productsVariantMap) {
        Map<Integer, Map<String, BigDecimal>> pricing = new HashMap<>();
        Integer pricingUnit = masterDataCache.getUnitPartnerBrandMappingMetaData().get(new UnitPartnerBrandKey(unitId, brandId,
                kettlePartnerId)).getPriceProfileUnitId();
        Collection<Product> products = masterDataCache.getUnitProductDetails(pricingUnit);
        Map<Integer, Product> productMap = new HashMap<>();
        products.forEach(product -> productMap.put(product.getId(), product));
        for (Map.Entry<MagicPinItem, List<MagicPinOption>> entry : productsVariantMap.entrySet()) {
            Integer productId = entry.getKey().getId().contains("_") ?
                    Integer.parseInt(entry.getKey().getId().split("_")[0]) : Integer.parseInt(entry.getKey().getId());
            if (masterDataCache.getProduct(productId) != null &&
                    ProductClassification.MENU.equals(masterDataCache.getProduct(productId).getClassification())) {
                if (!pricing.containsKey(productId)) {
                    pricing.put(productId, new HashMap<>());
                }
                findDimensionAndAddPrice(entry, productMap, pricing, productId);
            }
        }
        PartnerUnitProductPricingDetail pricingDetail = partnerMenuService.updatePartnerUnitProductPricing(kettlePartnerId,
                unitId, brandId, employeeId, pricing, PARTNER_NAME);
        channelPartnerDataCache.loadPartnerUnitProductPricingMap(pricingDetail);
    }

    private void findDimensionAndAddPrice(Map.Entry<MagicPinItem, List<MagicPinOption>> productDimensionVariant, Map<Integer, Product> productMap, Map<Integer, Map<String, BigDecimal>> pricing, Integer productId) {
        for (MagicPinOption magicPinOption : productDimensionVariant.getValue()) {
            String dimension = magicPinOption.getId().trim().split("_")[1];
            pricing.get(productId).put(dimension, BigDecimal.valueOf(magicPinOption.getPrice()));
        }
    }


    private void setUpsellAndAddonProductIds(List<MagicPinModifier> modifiers,
                                                   List<String> upsellingProductIds, List<String> addOnProductIds) {
        for (MagicPinModifier magicPinModifier : modifiers) {
            processMagicPinModifier(magicPinModifier, upsellingProductIds,addOnProductIds);
        }
    }

    private void processMagicPinModifier(MagicPinModifier magicPinModifier,
                                                List<String> upsellingProductIds, List<String> addOnProductIds) {
        if (isValidMagicPinModifier(magicPinModifier)) {
            String title = magicPinModifier.getTitle();
            /*if (ChannelPartnerServiceConstants.MAGIC_PIN_SIZE_IDENTIFIIER.equalsIgnoreCase(title)) {
                processSizeModifier(magicPinModifier, productVariantsMap);
            } */
            if(ChannelPartnerServiceConstants.ADDON_ITEM_IDENTIFIER.endsWith(magicPinModifier.getId())){
                processAddonOrPaidAddonModifier(magicPinModifier,addOnProductIds);
            }
            if (ChannelPartnerServiceConstants.MAGIC_PIN_RECOM_IDENTIFIIER.equalsIgnoreCase(title)) {
                processRecomModifier(magicPinModifier, upsellingProductIds);
            }
        }
    }

    private  boolean isValidMagicPinModifier(MagicPinModifier magicPinModifier) {
        return magicPinModifier != null &&
                magicPinModifier.getTitle() != null &&
                !magicPinModifier.getTitle().isEmpty();
    }

    private void processSizeModifier(MagicPinModifier magicPinModifier,
                                            Map<String, List<String>> productVariantsMap) {
        List<String> variantIds = magicPinModifier.getOptions().stream()
                .filter(Objects::nonNull)
                .map(MagicPinOption::getId)
                .toList();

        String modifierId = magicPinModifier.getId();
        productVariantsMap.computeIfAbsent(modifierId, k -> new ArrayList<>())
                .addAll(variantIds);
    }

    private void processRecomModifier(MagicPinModifier magicPinModifier,
                                             List<String> upsellingProductIds) {
        upsellingProductIds.addAll(magicPinModifier.getOptions().stream()
                .filter(Objects::nonNull)
                .map(MagicPinOption::getId)
                .toList());
    }
    private void processAddonOrPaidAddonModifier(MagicPinModifier magicPinModifier, List<String> addonProductIds) {
        addonProductIds.addAll(
                magicPinModifier.getOptions().stream()
                        .filter(this::isInventoryTrackedOption)
                        .map(MagicPinOption::getId)
                        .toList()
        );
    }

    private boolean isInventoryTrackedOption(MagicPinOption magicPinOption) {
        try {
            return masterDataCache.getProduct(Integer.parseInt(magicPinOption.getId())).isInventoryTracked();
        } catch (NumberFormatException | NullPointerException ignored) {
            return false;
        }
    }
    @Override
    public <R> R sendStockUpdateReqToPartner(StockReqMetadata stockReqMetadata) {
        StockReqResponseData stockReqResponseData = StockReqResponseData.builder().stockReqToPartner(stockReqMetadata).status(false).build();
        String res = "";
        String errorStr = "";
        if (Objects.nonNull(stockReqMetadata)) {
            try {
                MagicPinUnitProductStock magicPinUnitProductStock = (MagicPinUnitProductStock) stockReqMetadata.getPartnerProductStockEventReqObj();
                String magicPinUnitProductStockJson = new Gson().toJson(magicPinUnitProductStock);
                try {
                    if (Objects.nonNull(magicPinUnitProductStock) && (!magicPinUnitProductStock.getSkus().isEmpty() || !magicPinUnitProductStock.getOptions().isEmpty())) {
                        MagicpinUnitAndInventoryUpdateResponse response = webServiceHelper.callMagicpinApi(environmentProperties,
                                MagicpinServiceEndPoints.UPDATE_INVENORY, HttpMethod.POST, magicPinUnitProductStock, MagicpinUnitAndInventoryUpdateResponse.class,
                                stockReqMetadata.getUnitPartnerBrandKey().getBrandId());
                        String responseJson = new Gson().toJson(response);
                        log.info("STOCK API REQUEST PROCESSED :::: (MAGICPIN) :::: REQUEST :::: {} :::: RESPONSE :::: {}", magicPinUnitProductStockJson, responseJson);
                        if (response == null || response.getResponseCode() != 200) {
                            stockReqResponseData.setStatus(false);
                            res = "MAGICPIN stock API returned incorrect response";
                            errorStr = ":::::REQUEST:::::" + magicPinUnitProductStockJson + "\n" + ":::::RESPONSE:::::" + responseJson;
                        } else {
                            stockReqResponseData.setStockResponse(response);
                            stockReqResponseData.setStatus(true);
                        }
                    }
                } catch (HttpStatusCodeException e) {
                    stockReqResponseData.setStatus(false);
                    String errorBody = e.getResponseBodyAsString();
                    MagicpinError magicpinError = new Gson().fromJson(e.getResponseBodyAsString(), MagicpinError.class);
                    if (magicpinError != null && magicpinError.getMessage() != null) {
                        errorBody = magicpinError.getMessage();
                    }
                    res= "Error updating stock to Zomato:::::\n request : " + magicPinUnitProductStockJson
                            + "\n" + masterDataCache.getUnit(stockReqMetadata.getUnitProductsStockEvent().getUnitId()).getName() + "\n";
                    errorStr= errorBody;
                } catch (Exception e) {
                   stockReqResponseData.setStatus(false);
                    res= "Error updating stock to Zomato:::::\n request : " + magicPinUnitProductStockJson
                            + "\n" + masterDataCache.getUnit(stockReqMetadata.getUnitProductsStockEvent().getUnitId()).getName() + "\n";
                    errorStr+=e;
                }
            } catch (Exception e) {
                log.error("Exception while sending stock request to partner ::::::::{}", PARTNER_NAME, e);
            }

            stockReqResponseData.setRes(res);
            stockReqResponseData.setErrorBody(errorStr);
            return (R) stockReqResponseData;
        }
        return null;
    }

    private void logStockUpdateSnapshot(MagicPinUnitProductStock magicPinUnitProductStock, UnitProductsStockEvent unitProductsStockEvent)
            throws ChannelPartnerException {
        log.info("Updating MAGICPIN unit product stock snapshot");
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(PARTNER_NAME).getKettlePartnerId();
        Set<String> productIds = magicPinUnitProductStock.getSkus().stream().map(MagicPinSkuStockData :: getPartnerSku).collect(Collectors.toSet());
        for (String productId : productIds) {
            // r_ for upselling and rd_ for Hero product and ss for single serve menu
            if (!productId.endsWith(ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER) &&
                    !productId.endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER) && !productId.startsWith("ss_")) {
                Integer product = Integer.valueOf(productId);
                PartnerUnitProductStockSnapshot snapshot = partnerUnitStockSnapshotDao
                        .findByUnitIdAndPartnerIdAndProductId(magicPinUnitProductStock.getPartnerStoreId(), partnerId, product);
                if (snapshot == null) {
                    snapshot = new PartnerUnitProductStockSnapshot();
                    snapshot.setPartnerId(partnerId);
                    snapshot.setUnitId(magicPinUnitProductStock.getPartnerStoreId());
                    snapshot.setProductId(product);
                    snapshot.setProductStockSnapshot(new ArrayList<>());
                }
                ProductStockSnapshot stockSnapshot = new ProductStockSnapshot();
                stockSnapshot.setStockStatus(unitProductsStockEvent.getStatus());
                stockSnapshot.setUpdateTime(ChannelPartnerUtils.getFormattedTime(currentTime, "yyyy-MM-dd HH:mm:ss"));
                snapshot.getProductStockSnapshot().add(stockSnapshot);
                snapshot = partnerUnitStockSnapshotDao.save(snapshot);
                if (snapshot == null) {
                    throw new ChannelPartnerException("Error updating stock snapshot!");
                }
            }
        }
    }


    @Override
    public <T> T updateStockValueForMenuPush(T menuRequestObj, UnitPartnerBrandKey key) {
        try {
            if (Objects.isNull(key.getBrandId())) {
                return menuRequestObj;
            }
            log.info("Trying To update In Stock Value IN Menu For Unit Id :::: {} , Brand Id :::: {} , Partner Id :::: {} ", key.getUnitId(), key.getBrandId(), key.getPartnerId());
            UnitProductsStockEvent stockOut = new UnitProductsStockEvent();
            stockOut.setStatus(StockStatus.STOCK_OUT);
            stockOut.setUnitId(key.getUnitId());
            UnitProductsStockEvent stockIn = new UnitProductsStockEvent();
            stockIn.setStatus(StockStatus.STOCK_IN);
            stockIn.setUnitId(key.getUnitId());
            orderValidationService.refreshLiveUnitInventory(key.getUnitId(), stockIn, stockOut);
            Set<String> stockOutProductIds = new HashSet<>();

            for (String productId : stockOut.getProductIds()) {
                Map<Integer, Set<Integer>> map = channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.
                        getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId());
                if (map.containsKey(Integer.valueOf(productId))) {
                    Set<String> comboIds = map.get(Integer.valueOf(productId)).stream().map(String::valueOf).collect(Collectors.toSet());
                    if (!CollectionUtils.isEmpty(comboIds)) {
                        stockOutProductIds.addAll(comboIds);
                    }
                }
            }
            stockOutProductIds.addAll(stockOut.getProductIds());

            log.info("Stock Out Products For Unit Id : {} , partner Id :: {}  During Menu Push :::: {}", key.getUnitId(), key.getPartnerId(), new Gson().toJson(stockOutProductIds));

            Map<String, Boolean> stockOuts = stockOutProductIds.stream().collect(Collectors.toMap(Function.identity(), (value) -> {
                return true;
            }));

            if (menuRequestObj instanceof MagicPinMenuRequest) {
                updateMenuStock((MagicPinMenuRequest) menuRequestObj, stockOutProductIds);
            }

        } catch (Exception e) {
            log.info("Error While Updating Stock Values During Menu Push for unit :::{} for partner ::::{} for brand :::{}  :::::::::::: ", key.getUnitId(), key.getPartnerId(), key.getBrandId(), e);
        }
        return menuRequestObj;
    }

    private void updateMenuStock(MagicPinMenuRequest magicPinMenuRequest, Set<String> stockOutProductIds) {
        magicPinMenuRequest.getMenu().getCategories().forEach(category ->
                category.getSubCategories().forEach(subCategory ->
                        subCategory.getItems().forEach(item -> {
                            String productKey = item.getId().split("_")[0];
                            if (stockOutProductIds.contains(productKey)) {
                                item.setInStock(false);
                            }
                        })
                )
        );
    }

    private Set<String> getStockOutProductIds(UnitProductsStockEvent stockOut, UnitPartnerBrandKey key) {
        Set<String> stockOutProductIds = new HashSet<>(stockOut.getProductIds());

        channelPartnerDataCache.getUnitProductComboMappings(masterDataCache.getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId())
                .forEach((productId, comboIds) -> stockOutProductIds.addAll(comboIds.stream().map(String::valueOf).collect(Collectors.toSet())));

        return stockOutProductIds;
    }
}
