package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeRaiseRequestApprovalDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.domain.model.ActionRequest;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Repository
public class CafeRaiseRequestApprovalDaoImpl extends AbstractMasterDaoImpl implements CafeRaiseRequestApprovalDao {

    private static final Logger LOG = LoggerFactory.getLogger(CafeRaiseRequestApprovalDaoImpl.class);

    @Override
    public List<CafeRaiseRequestApproval> getAllPendingCafeSwitchOffRequests(String actionTaken){
        Query query = manager.createQuery("FROM CafeRaiseRequestApproval c left join fetch c.cafeRaiseRequestApprovalProducts WHERE c.actionTaken= :actionTaken");
        query.setParameter("actionTaken",actionTaken);
        return query.getResultList();
    }

    @Override
    public Boolean isActiveCafeClosureRequestExists(Integer unitId , Integer brandId , Integer partnerId){
        Query query = manager.createQuery("select count(c) FROM CafeRaiseRequestApproval c WHERE c.actionTaken= :actionTaken and c.unitId = :unitId " +
                " and c.brandId = :brandId and c.channelPartnerId = :channelPartnerId" +
                " and  actionTimestamp > :thresholdDate and c.requestType = :requestType ");
        query.setParameter("actionTaken", AppConstants.APPROVED_REQUEST).setParameter("unitId" ,unitId)
                .setParameter("brandId",brandId).setParameter("channelPartnerId",partnerId)
                .setParameter("thresholdDate",AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-2)).
        setParameter("requestType", AppConstants.BATCH_CODE_KEY_TYPE_UNIT);
        Long count = (Long) query.getSingleResult();
        return count > 0;
    }

    @Override
    public CafeRaiseRequestApproval getActiveProductClosureRequest(Integer unitId , Integer brandId , Integer partnerId){
        try{
            Query query = manager.createQuery("FROM CafeRaiseRequestApproval c WHERE c.actionTaken in  (:actionTakens) and c.unitId = :unitId " +
                    " and c.brandId = :brandId and c.channelPartnerId = :channelPartnerId" +
                    " and  actionTimestamp > :thresholdDate and c.requestType = :requestType");
            query.setParameter("actionTakens",new ArrayList<>(Arrays.asList(AppConstants.APPROVED_REQUEST,AppConstants.INITIATED))).
                    setParameter("unitId" ,unitId)
                    .setParameter("brandId",brandId).setParameter("channelPartnerId",partnerId)
                    .setParameter("thresholdDate",AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-2)).
                    setParameter("requestType", ChannelPartnerServiceConstants.PRODUCT_REQUEST_TYPE);
            return (CafeRaiseRequestApproval) query.getSingleResult();
        }catch (NoResultException nre){
            //
        }catch (Exception e){
            LOG.error("Error in fetching active product closure request",e);
        }
        return null;


    }

    @Override
    public boolean updateActionTakenOnRequest(Integer id,String actionTaken){
        Query query = manager.createQuery("UPDATE CafeRaiseRequestApproval c SET c.actionTaken = :actionTaken , " +
                "c.completionTimestamp = :completionTime " + " WHERE c.id = :id");
        query.setParameter("actionTaken", actionTaken);
        query.setParameter("id", id);
        query.setParameter("completionTime", AppUtils.getCurrentTimestamp());
        int count = query.executeUpdate();
        return count > 0;
    }

    @Override
    public CafeRaiseRequestApproval getCafeRequestById(Integer id , String status){
        Query query = manager.createQuery("FROM CafeRaiseRequestApproval  WHERE id = :id and actionTaken = :actionTaken");
        query.setParameter("id", id);
        query.setParameter("actionTaken", status);
        return (CafeRaiseRequestApproval) query.getSingleResult();

    }


}
