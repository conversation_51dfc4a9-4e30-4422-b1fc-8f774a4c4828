package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.domain.model.BrandProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.DealOfTheDayRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuTrackResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductAliasVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductsTagsVU;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerUnitListVO;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappingVU;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitAutoSwitchOff;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuVersionData;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitProductPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayStatusResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPushLog;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DealOfTheDay;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfileMappings;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdNameGroup;
import com.stpl.tech.master.domain.model.IdValueUnit;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

public interface PartnerMetadataManagementService {
    public boolean setUnitAvailability(UnitPartnerStatusVO request) throws ChannelPartnerException;

    boolean setUnitProductStock(UnitProductPartnerStatusVO request) throws ChannelPartnerException;

    List<PartnerDeliveryStatus> getDeliveryStatus(String kettleOrderId) throws ChannelPartnerException;

    boolean setPartnerProductFilter(UnitProductPartnerStatusVO request);

    boolean setPartnerProductTags(PartnerProductTagsVO request);

    boolean setPartnerMeatTags(PartnerProductTagsVO request);

    boolean setPartnerProductTagsMappings(PartnerProductTagsMappings request);

    boolean setPartnerProductMeatTagsMappings(PartnerProductTagsMappings request);

    boolean setPartnerBogoProducts(UnitProductPartnerStatusVO request);

    boolean setPartnerProductAliases(PartnerProductAliasVO request);

    List<Integer> getPartnerProductFilter(Integer partnerId, Integer brandId);

    List<Integer> getPartnerBogoProducts(Integer partnerId);

    List<IdNameGroup> getPartnerProductTags(Integer partnerId);

    List<IdName> getPartnerMeatTags(Integer partnerId);

    List<ProductTagsMappings> getPartnerProductTagsMappings(Integer partnerId, Integer brandId);

    List<ProductTagsMappings> getPartnerProductMeatTagsMappings(Integer partnerId, Integer brandId);

    List<BrandProductTagsMappings> getPartnerProductTagsMappingsDetail(@RequestBody Integer partnerId, @RequestParam Integer brandId);

    List<BrandProductTagsMappings> getPartnerProductMeatTagsMappingsDetail(Integer partnerId, Integer brandId);

    List<IdName> getPartnerProductQVMTags(Integer partnerId);

    List<ProductAlias> getPartnerProductAliases(Integer partnerId);

    boolean setPartnerQVMtTags(PartnerProductTagsVO request);

    /*MenuSequence getMenuSequence(UnitMenuAddVO request) throws ChannelPartnerException;*/

    UnitMenuAddVO getPartnerUnitMenu(UnitMenuAddVO request, PartnerActionEventType eventType) throws ChannelPartnerException;

    @Deprecated
    boolean addPartnerRegionMenu(UnitMenuAddVO request) throws ChannelPartnerException;

    boolean addPartnerUnitMenu(UnitMenuAddVO request) throws ChannelPartnerException;

    List<PartnerUnitMenuDetail> getActiveMenuForPartnerUnits(PartnerUnitListVO request) throws ChannelPartnerException;

    boolean refreshPartnerUnitMenu(UnitMenuAddVO request) throws ChannelPartnerException;

    IdCodeName getZomatoTreatsItem();

    boolean setZomatoTreatsItem(Integer productId) throws ChannelPartnerException;

    boolean removeZomatoTreatsItem();

    List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnits(PartnerUnitListVO request) throws ChannelPartnerException;

    PartnerOfferDetail addPartnerOffer(PartnerUnitListVO request) throws ChannelPartnerException;

    void publishInventoryToPartner(UnitPartnerStatusVO request);

    Boolean deactivatePartnerOffer(String partnerOfferId) throws ChannelPartnerException;

    Boolean activatePartnerOffer(String partnerOfferId) throws ChannelPartnerException;

    IdCodeName getSwiggySuperItem();

    boolean setSwiggySuperItem(Integer productId) throws ChannelPartnerException;

    boolean removeSwiggySuperItem();

    List<Integer> getSwiggyRecommendedProducts();

    boolean setSwiggyRecommendedProducts(List<Integer> productIds);

    MenuTrackResponse getSwiggyMenuStatus(Integer unitId, Integer partnerId, Integer brandId);

    boolean updateCafeDeliveryTimeSwiggy(CafeTimingChangeRequest cafeTimingChangeRequest);

    ZomatoLogisticsStatusResponse getOutletLogisticsStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    ZomatoNotificationResponse updateOutletLogisticsStatus(ZomatoLogisticsChangeRequest request) throws ChannelPartnerException;

    ZomatoNotificationResponse updateOutletDeliveryStatus(ZomatoDeliveryChangeRequest request) throws ChannelPartnerException;

    void updateZomatoOutletStatusByRestaurantId(Integer brandId, ZomatoDeliveryChangeRequest request) throws ChannelPartnerException;

    ZomatoDeliveryStatusResponse getOutletDeliveryStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    ZomatoTakeawayStatusResponse getOutletTakeawayStatus(Integer unitId, Integer brandId) throws ChannelPartnerException;

    boolean updateOutletTakeawayStatus(UnitPartnerStatusVO request) throws ChannelPartnerException;

    boolean updateCafeDeliveryTimeZomato(CafeTimingChangeRequest cafeTimingChangeRequest);

    List<UnitPartnerLocalityMapping> getPendingLocalityMappings();

    boolean updateLocalityMappings(List<UnitPartnerLocalityMapping> mappings);

    boolean sendPartnerOffer(PartnerOfferDetail partnerOfferId) throws ChannelPartnerException, IllegalArgumentException, IllegalAccessException;

    public List<String> getPartnerBogoProductsforOffer(Integer kettlePartnerId);

    //public boolean addPartnerUnitSingleServeMenu(UnitMenuAddVO request);

    public boolean getSwiggyStockVersionStatus();

    public boolean updateSwiggyStockVersionStatus(boolean status);

    public String addMenuVersionForPartner(UnitMenuAddVO request) throws ChannelPartnerException;

    public List<UnitMenuVersionData> getUnitMenuVersionData(Integer unitId, Integer kettlePartnerId, Integer brandId, String menuType);

    public List<UnitMenuVersionData> markUnitMenuVersionDef(Integer unitId, Integer kettlePartnerId, Integer brandId, String version, String status, String menuType);

    public PartnerUnitMenuDetail showVersionMenu(Integer unitId, Integer kettlePartnerId, Integer brandId, String version, String menuType);

    public boolean pushMenuToUnits(UnitMenuAddVO request) throws ChannelPartnerException;

    PartnerUnitProductStockSnapshot getUnitProductStockSnapshot(UnitPartnerBrandKey key, Integer productId);

    public List<CafeMenuAutoPush> menuAutoPush(List<CafeMenuAutoPush> data);

    public List<CafeMenuAutoPush> showMenuData(Integer brandId);

    public List<CafeMenuAutoPushLog> menuAutoPushHistory(Integer brandId, Integer unitId);

    List<DesiChaiCustomProfiles> getDesiChaiCustomProfiles();

    DesiChaiCustomProfiles addDesiChaiCustomProfile(DesiChaiCustomProfiles profile) throws ChannelPartnerException;

    List<DesiChaiCustomProfileMappings> addDesiChaiCustomProfileMappings(List<DesiChaiCustomProfileMappings> mappings);

    List<DesiChaiCustomProfileMappings> getDesiChaiCustomProfileMappings(PartnerUnitListVO request);

    DesiChaiCustomProfileMappings updateDesiChaiCustomProfileMappingStatus(DesiChaiCustomProfileMappings mapping) throws ChannelPartnerException;

    List<DesiChaiCustomProfiles> getDesiChaiCustomProfilesForUnit(Integer unitId,Integer partnerId) throws ChannelPartnerException;

    void setUnitAvailability(String requestId, Integer partnerId, Boolean forceUpdate);

    boolean activateCafeUnitDashboard( Integer id,Integer partnerId, Integer unitId, Integer brandId);

    boolean setPartnerAllergenTags(PartnerProductTagsVO request);

    public  List<IdName> getPartnerAllergenTags(Integer partnerId);

    List<ProductTagsMappings> getPartnerProductAllergenTagsMappings(Integer partnerId, Integer brandId);

    public boolean setPartnerProductAllergenTagsMapping(PartnerProductTagsMappings request);

    public List<BrandProductTagsMappings> getPartnerProductAllergenTagsMappingsDetail(Integer partnerId, Integer brandId);

    public boolean setPartnerProductServingInfoTagsMapping(PartnerProductTagsMappings request);

    public List<ProductTagsMappings> getPartnerProductServingInfoTagsMappings(Integer partnerId, Integer brandId);

    public List<BrandProductTagsMappings> getPartnerProductServingInfoTagsMappingsDetail(Integer partnerId, Integer brandId);

    boolean setPartnerServingInfoTags(PartnerProductTagsVO request);

    public  List<IdName> getPartnerServingInfoTags(Integer partnerId);

    public List<IdValueUnit> getPartnerServingSize(Integer partnerId);

    public boolean setPartnerServingSize(PartnerProductsTagsVU request);

    public List<ProductTagsMappingVU> getPartnerProductServingSizeMappings(Integer partnerId, Integer brandId);

    public boolean setPartnerProductServingSizeMappings(PartnerProductTagsMappings request);

    public List<BrandProductTagsMappings> getPartnerProductServingSizeMappingsDetail(Integer partnerId,Integer brandId);

    boolean setOffUnitAvailibilityFromKnockApp(UnitAutoSwitchOff request,boolean status);

    public DealOfTheDay getDotdProducts(Integer kettlePartnerId);

    public boolean setProductsForDOTD(DealOfTheDayRequest dealOfTheDayRequest);

    public <T> boolean sendRequestBasisRequestType(T reqObj , PartnerRequestType requestType, List<Integer> partnerIds ) throws ChannelPartnerException;
    public boolean scheduledMenuPush(UnitMenuAddVO request, List<Integer> partnerIds);

    public List<Integer> getFilteredProductForNoPackagingCharges(Integer partnerId,Integer brandId);

    public Set<Integer> getProductIdsForSplitDimension();

}

