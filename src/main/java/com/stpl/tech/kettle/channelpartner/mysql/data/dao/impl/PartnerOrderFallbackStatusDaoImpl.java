package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.AbstractMasterDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackStatusDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class PartnerOrderFallbackStatusDaoImpl extends AbstractMasterDaoImpl implements PartnerOrderFallbackStatusDao {

    public PartnerOrderFallbackStatus findByOrderId(String orderId) {
        Query query = manager.createQuery("FROM PartnerOrderFallbackStatus p WHERE p.orderId= :orderId ");
        query.setParameter("orderId",orderId);
        List<PartnerOrderFallbackStatus> data = query.getResultList();
        return data.isEmpty() ? null : data.get(0);
    }

}
