package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "slug"
})
public class ZomatoTaxGroup {
	
	@JsonProperty("slug")
    private String slug;
    
    @JsonProperty("slug")
    public String getSlug() {
		return slug;
	}

    @JsonProperty("slug")
	public void setSlug(String slug) {
		this.slug = slug;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		ZomatoTaxGroup that = (ZomatoTaxGroup) o;
		return Objects.equals(slug, that.slug);
	}

	@Override
	public int hashCode() {

		return Objects.hash(slug);
	}

	@Override
	public String toString() {
		return "ZomatoTaxGroup{" +
				"slug='" + slug + '\'' +
				'}';
	}
}