package com.stpl.tech.kettle.channelpartner.core.service.order.builder;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.order.chain.OrderProcessingStep;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.PartnerOrderManagementService;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class OrderProcessingChainBuilder<R, T> {
    public PartnerOrderManagementService partnerOrderManagementService;
    public ChannelPartnerDataCache channelPartnerDataCache;
    public MasterDataCache masterDataCache;
    public WebServiceHelper webServiceHelper;
    public EnvironmentProperties environmentProperties;
    public TrackService trackService;

    public OrderProcessingChainBuilder<R, T> withPartnerOrderManagementService(PartnerOrderConverterDependency partnerOrderConverterDependency, String partnerName) {
        this.partnerOrderManagementService = partnerOrderConverterDependency.getPartnerOrderManagementService(partnerName);
        return this;
    }

    public OrderProcessingChainBuilder<R, T> withMasterDataCache(PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.masterDataCache = partnerOrderConverterDependency.getMasterDataCache();
        return this;
    }

    public OrderProcessingChainBuilder<R, T> withWebServiceHelper(PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.webServiceHelper = partnerOrderConverterDependency.getWebServiceHelper();
        return this;
    }

    public OrderProcessingChainBuilder<R, T> withEnvironmentProperties(PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.environmentProperties = partnerOrderConverterDependency.getEnvironmentProperties();
        return this;
    }

    public OrderProcessingChainBuilder<R, T> withChannelPartnerDataCache(PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.channelPartnerDataCache = partnerOrderConverterDependency.getChannelPartnerDataCache();
        return this;
    }

    public OrderProcessingChainBuilder<R, T> withTrackService(PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.trackService = partnerOrderConverterDependency.getTrackService();
        return this;
    }

    public OrderProcessingChainBuilder<R, T> defaultBuilderForProcessOrder(PartnerOrderConverterDependency partnerOrderConverterDependency, String partnerName) {
        this.trackService = partnerOrderConverterDependency.getTrackService();
        this.channelPartnerDataCache = partnerOrderConverterDependency.getChannelPartnerDataCache();
        this.masterDataCache = partnerOrderConverterDependency.getMasterDataCache();
        this.environmentProperties = partnerOrderConverterDependency.getEnvironmentProperties();
        this.webServiceHelper = partnerOrderConverterDependency.getWebServiceHelper();
        this.partnerOrderManagementService = partnerOrderConverterDependency.getPartnerOrderManagementService(partnerName);
        return this;
    }

    public OrderProcessingStep<R, T> buildOrderProcessingStep() {
        OrderProcessingStep<R,T> orderProcessingStep = new OrderProcessingStep<>(this);
        orderProcessingStep.setNextOrderProcessingStep(orderProcessingStep);
        return orderProcessingStep;
    }


}
