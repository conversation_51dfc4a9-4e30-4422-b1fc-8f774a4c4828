package com.stpl.tech.kettle.channelpartner.core.service.order.factory;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.OrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.BaseOrderItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.ChargesItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.MenuItemBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.PaidAddonItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.RecomItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.builder.OrderItemDecoratorBuilder;
import org.apache.commons.lang.NotImplementedException;

import java.util.HashMap;
import java.util.Map;

public class OrderItemDecoratorFactory {

    private static Map<String, BaseOrderItemDecorator> orderItemDecoratorMap = new HashMap<>();

    public static BaseOrderItemDecorator getInstance(String partnerName,
                                                     PartnerOrderConverterDependency partnerOrderConverterDependency) {
        if (!orderItemDecoratorMap.containsKey(partnerName)) {
            orderItemDecoratorMap.put(partnerName, getPartnerItemConverterInstance(partnerName, partnerOrderConverterDependency));
        }
        return orderItemDecoratorMap.get(partnerName);
    }

    private static BaseOrderItemDecorator getPartnerItemConverterInstance(String partnerName,
                                                                          PartnerOrderConverterDependency partnerOrderConverterDependency) {

        OrderItemDecoratorBuilder builder = new OrderItemDecoratorBuilder();
        BaseOrderItemDecorator baseOrderItemDecorator;
        BaseOrderItemDecorator menuItemDecorator = new MenuItemBaseDecorator(builder);
        builder = builder.defaultBuilder(partnerOrderConverterDependency, partnerName, new OrderItemConverterService(
                partnerOrderConverterDependency.getPartnerItemConverterService(partnerName),
                partnerOrderConverterDependency.getdiscountStrategy(partnerName),
                partnerOrderConverterDependency.getMasterDataCache(),
                partnerOrderConverterDependency.getTaxStrategy(partnerName),
                partnerOrderConverterDependency.getEnvironmentProperties(),
                partnerOrderConverterDependency
        ));
        switch (partnerName) {
            case "MAGICPIN" -> {
                baseOrderItemDecorator = new ChargesItemDecorator(builder,
                        new RecomItemDecorator(builder,new PaidAddonItemDecorator(builder,menuItemDecorator),menuItemDecorator));
            }
            default -> {
                throw new NotImplementedException("Not Implemented yet For This Partner");
            }
        }
        return baseOrderItemDecorator;
    }

}
