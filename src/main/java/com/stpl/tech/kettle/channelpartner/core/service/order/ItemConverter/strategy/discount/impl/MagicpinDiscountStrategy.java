package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.impl;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItems;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.master.recipe.model.BasicInfo;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component
@Qualifier("magicpinDiscountStrategy")
public class MagicpinDiscountStrategy implements DiscountStrategy {
    @Override
    public <R, T> void applyDiscount(R request, T item, List<OrderItem> items, PartnerItemData partnerItemData) {
        BigDecimal catalogueDistributedDiscount = getTotalCatalogueItemDiscount((MagicpinOrderRequest) request, (MagicpinOrderItems) item) ;
        BigDecimal totalItemAmount = getTotalAmount(items);
        for (OrderItem oItem : items) {
            oItem.setDiscountDetail(getTotalItemDiscount(request, item, partnerItemData, oItem,catalogueDistributedDiscount,totalItemAmount));
            oItem.setAmount(ChannelPartnerUtils.subtract(oItem.getTotalAmount(), oItem.getDiscountDetail().getPromotionalOffer()));
        }
    }

    private BigDecimal getTotalAmount(List<OrderItem> orderItemList){
        BigDecimal totalAmount =  BigDecimal.ZERO;
        for(OrderItem orderItem : orderItemList){
            totalAmount = ChannelPartnerUtils.add(totalAmount,ChannelPartnerUtils.multiply(orderItem.getTotalAmount(),
                    BigDecimal.valueOf(orderItem.getQuantity())));
        }
        return totalAmount;
    }


   private <R,T> BigDecimal getTotalCatalogueItemDiscount(MagicpinOrderRequest magicpinOrderRequest ,MagicpinOrderItems magicpinOrderItem){
       return ChannelPartnerUtils.multiply(magicpinOrderItem.getDistributedDiscountWieght(),
                BigDecimal.valueOf(magicpinOrderRequest.getMerchantFundedDiscount()));
    }

    @Override
    public <R, T> PercentageDetail getTotalOrderDiscount(R request) {
        return null;
    }

    @Override
    public <R, T> DiscountDetail getTotalItemDiscount(R request, T item, PartnerItemData partnerItemData, OrderItem oItem ,
                                                      BigDecimal catalogueItemDistributedDiscount , BigDecimal totalParentAmount) {
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        DiscountDetail discountDetail = new DiscountDetail();
        BigDecimal totalPrice = ChannelPartnerUtils.multiply(oItem.getTotalAmount(),
                BigDecimal.valueOf(oItem.getQuantity()));
        BigDecimal discountWeight = ChannelPartnerUtils.divide(totalPrice,totalParentAmount);
        BigDecimal orderlevelDiscount = ChannelPartnerUtils.multiply(discountWeight,catalogueItemDistributedDiscount);
        if (Objects.equals(partnerItemData.getProductId(), oItem.getProductId())) {
            discountDetail = calculateDiscountPercentForParentItem(partnerItemData, magicpinOrderRequest,orderlevelDiscount);
        } else {
            Pair<Boolean, PartnerItemAddonData> recommendedAddonPair = isAddonProduct(partnerItemData.getRecommendedProducts(), oItem);
            Pair<Boolean, PartnerItemAddonData> paidAddonPair = isAddonProduct(partnerItemData.getPaidAddonProducts(), oItem);

            if (recommendedAddonPair.getKey() || paidAddonPair.getKey()) {
                PartnerItemAddonData addonData = recommendedAddonPair.getKey() ? recommendedAddonPair.getValue() : paidAddonPair.getValue();
                discountDetail = calculateDiscountPercent(addonData, magicpinOrderRequest,orderlevelDiscount);
            }
        }
        return discountDetail;
    }

    private Pair<Boolean, PartnerItemAddonData> isAddonProduct(List<PartnerItemAddonData> partnerItemAddonData, OrderItem orderItem) {
        return Objects.nonNull(partnerItemAddonData) && !partnerItemAddonData.isEmpty()
                ? partnerItemAddonData.stream()
                .filter(addonData -> addonData.getProductId() == orderItem.getProductId())
                .findFirst()
                .map(addon -> Pair.of(true, addon))
                .orElse(Pair.of(false, null))
                : Pair.of(false, null);
    }

    private DiscountDetail calculateDiscountPercentForParentItem(PartnerItemData partnerItemData, MagicpinOrderRequest magicpinOrderRequest
    , BigDecimal orderLevelDiscount) {
        PercentageDetail pDetail = new PercentageDetail();
        pDetail.setPercentage(BigDecimal.ZERO);
        pDetail.setValue(BigDecimal.ZERO);
        DiscountDetail discountDetail = new DiscountDetail();
        BigDecimal totalPriceOfItem = ChannelPartnerUtils.multiply(partnerItemData.getUnitPrice(), BigDecimal.valueOf(partnerItemData.getQuantity()));
        BigDecimal totalDiscount = ChannelPartnerUtils.add(partnerItemData.getDiscount(), orderLevelDiscount);
        BigDecimal totalDiscountPercentage = ChannelPartnerUtils.divide(totalDiscount, totalPriceOfItem);
        discountDetail.setPromotionalOffer(totalDiscount);
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        pDetail.setPercentage(totalDiscountPercentage);
        discountDetail.setDiscount(pDetail);
        return discountDetail;
    }

    private DiscountDetail calculateDiscountPercent(PartnerItemAddonData partnerItemAddonData, MagicpinOrderRequest magicpinOrderRequest
    ,BigDecimal orderLevelDiscount) {
        PercentageDetail pDetail = new PercentageDetail();
        pDetail.setPercentage(BigDecimal.ZERO);
        pDetail.setValue(BigDecimal.ZERO);
        DiscountDetail discountDetail = new DiscountDetail();
        BigDecimal totalPriceOfItem = ChannelPartnerUtils.multiply(partnerItemAddonData.getUnitPrice(), BigDecimal.valueOf(partnerItemAddonData.getQuantity()));
        BigDecimal totalDiscount = ChannelPartnerUtils.add(partnerItemAddonData.getDiscount(), orderLevelDiscount);
        BigDecimal totalDiscountPercentage = ChannelPartnerUtils.divide(totalDiscount, totalPriceOfItem);
        discountDetail.setPromotionalOffer(totalDiscount);
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        pDetail.setPercentage(totalDiscountPercentage);
        discountDetail.setDiscount(pDetail);
        return discountDetail;
    }
}
