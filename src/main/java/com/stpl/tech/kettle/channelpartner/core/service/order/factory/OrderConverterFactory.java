package com.stpl.tech.kettle.channelpartner.core.service.order.factory;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.OrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.orderConverter.OrderConverterService;
import org.apache.commons.lang3.NotImplementedException;

import java.util.HashMap;
import java.util.Map;

public class OrderConverterFactory {

    private static Map<String, OrderConverterService> orderConverterServiceMap = new HashMap<>();

    private OrderConverterFactory(){};

    public static OrderConverterService  getInstance(String partnerName ,
                                                             PartnerOrderConverterDependency partnerOrderConverterDependency){
        if(!orderConverterServiceMap.containsKey(partnerName)){
            orderConverterServiceMap.put(partnerName,getPartnerConverterInstance(partnerName,partnerOrderConverterDependency));
        }
        return orderConverterServiceMap.get(partnerName);
    }

    private static OrderConverterService getPartnerConverterInstance(String partnerName,
                                                                             PartnerOrderConverterDependency partnerOrderConverterDependency){
        switch (partnerName){
            case "MAGICPIN":
                return  new OrderConverterService(
                        OrderItemConverterFactory.getInstance(partnerName,partnerOrderConverterDependency),
                        partnerOrderConverterDependency.getEnvironmentProperties(),
                        partnerOrderConverterDependency.getChannelPartnerDataCache(),
                        partnerOrderConverterDependency.getWebServiceHelper(),
                        partnerOrderConverterDependency.getMasterDataCache(),
                        partnerOrderConverterDependency.getOrderValidationService());
            default:
                throw new NotImplementedException("Not Implemented yet For This Partner");
        }
    }
}
