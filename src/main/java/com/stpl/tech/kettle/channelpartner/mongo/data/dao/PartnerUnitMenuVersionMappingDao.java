package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import java.util.List;

import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;

@Repository
public interface PartnerUnitMenuVersionMappingDao extends MongoRepository<PartnerUnitMenuVersionMapping, String> {

	List<PartnerUnitMenuVersionMapping> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(Integer unitId,
			Integer kettlePartnerId, Integer brandId, MenuType menuType);

	Page<PartnerUnitMenuVersionMapping> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeOrderByIdDesc(Integer unitId,
		Integer kettlePartnerId, Integer brandId, MenuType menuType, Pageable pageable);

	@Query(value = "{ 'unitId' : ?0, 'partnerId' : ?1,'brandId' : ?2,'menuType' : ?3 }")
	public Page<PartnerUnitMenuVersionMapping> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(Integer unitId,
            Integer partnerId, Integer brandId, MenuType menuType, Pageable pageRequest);

	PartnerUnitMenuVersionMapping findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeAndStatus(Integer unitId,
			Integer partnerId, Integer brandId, MenuType menuType, String status);

	List<PartnerUnitMenuVersionMapping> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeAndVersion(Integer unitId,
		Integer partnerId, Integer brandId, MenuType menuType, String version);

}
