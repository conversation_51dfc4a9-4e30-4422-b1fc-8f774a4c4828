package com.stpl.tech.kettle.channelpartner.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.Date;
import java.util.List;
import java.util.Objects;

public class UnitMenuAddVO {

    @JsonProperty("unitId")
    private Integer unitId;
    @JsonProperty("region")
    private String region;
    @JsonProperty("kettlePartnerId")
    private Integer kettlePartnerId;
    @JsonProperty("employeeId")
    private Integer employeeId;
    @JsonProperty("menuRequest")
    private Object menuRequest;
    @JsonProperty("charges")
    private List<Object> charges;
    @JsonProperty("isNew")
	private Boolean isNew;
    @JsonProperty("addTime")
    private Date addTime;
    @JsonProperty("brandId")
    private Integer brandId;
    @JsonProperty("unitIdsForMenu")
    private List<Integer> unitIdsForMenu;
    @JsonProperty("version")
    private String version;
    @JsonProperty("status")
    private String status;
    @JsonProperty("menuType")
    private MenuType menuType;
    @JsonProperty("menuSequenceId")
    private Integer menuSequenceId;
    @JsonProperty("menuSequenceName")
    private String menuSequenceName;
    

    @JsonProperty("unitId")
    public Integer getUnitId() {
        return unitId;
    }

    @JsonProperty("unitId")
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @JsonProperty("region")
    public String getRegion() {
        return region;
    }

    @JsonProperty("region")
    public void setRegion(String region) {
        this.region = region;
    }

    @JsonProperty("kettlePartnerId")
    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    @JsonProperty("kettlePartnerId")
    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }

    @JsonProperty("menuRequest")
    public Object getMenuRequest() {
        return menuRequest;
    }

    @JsonProperty("menuRequest")
    public void setMenuRequest(Object menuRequest) {
        this.menuRequest = menuRequest;
    }

    @JsonProperty("employeeId")
    public Integer getEmployeeId() {
        return employeeId;
    }

    @JsonProperty("employeeId")
    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    @JsonProperty("addTime")
    public Date getAddTime() {
        return addTime;
    }

    @JsonProperty("addTime")
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    @JsonProperty("isNew")
    public Boolean getNew() {
        return isNew;
    }

    @JsonProperty("isNew")
    public void setNew(Boolean aNew) {
        isNew = aNew;
    }

    @JsonProperty("charges")
    public List<Object> getCharges() {
		return charges;
	}

    @JsonProperty("charges")
	public void setCharges(List<Object> charges) {
		this.charges = charges;
	}

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }
    
	public List<Integer> getUnitIdsForMenu() {
		return unitIdsForMenu;
	}

	public void setUnitIdsForMenu(List<Integer> unitIdsForMenu) {
		this.unitIdsForMenu = unitIdsForMenu;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	public MenuType getMenuType() {
		return menuType;
	}

	public void setMenuType(MenuType menuType) {
		this.menuType = menuType;
	}
	
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    public String getMenuSequenceName() {
        return menuSequenceName;
    }

    public void setMenuSequenceName(String menuSequenceName) {
        this.menuSequenceName = menuSequenceName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        UnitMenuAddVO that = (UnitMenuAddVO) o;

        return new EqualsBuilder()
                .append(unitId, that.unitId)
                .append(region, that.region)
                .append(kettlePartnerId, that.kettlePartnerId)
                .append(employeeId, that.employeeId)
                .append(menuRequest, that.menuRequest)
                .append(charges, that.charges)
                .append(isNew, that.isNew)
                .append(addTime, that.addTime)
                .append(brandId, that.brandId)
                .append(unitIdsForMenu, that.unitIdsForMenu)
                .append(version, that.version)
                .append(status, that.status)
                .append(menuType, that.menuType)
                .append(menuSequenceId, that.menuSequenceId)
                .append(menuSequenceName, that.menuSequenceName)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(unitId)
                .append(region)
                .append(kettlePartnerId)
                .append(employeeId)
                .append(menuRequest)
                .append(charges)
                .append(isNew)
                .append(addTime)
                .append(brandId)
                .append(unitIdsForMenu)
                .append(version)
                .append(status)
                .append(menuType)
                .append(menuSequenceId)
                .append(menuSequenceName)
                .toHashCode();
    }
}
