package com.stpl.tech.kettle.channelpartner.core.redis.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class SwiggyListenerImpl implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyListenerImpl.class);

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Autowired
    private SwiggyService swiggyService;

    private ChannelTopic channelTopic = new ChannelTopic("SWIGGY");

    @PostConstruct
    public void subscribe() {
        redisMessageListenerContainer.addMessageListener(this, channelTopic);
    }

    @Override
    public void onMessage(Message message, byte[] bytes) {
        PartnerActionEvent event = new Gson().fromJson(message.toString(), PartnerActionEvent.class);
        if (event != null) {
            LOG.info("Processing Swiggy action: " + new Gson().toJson(event));
            try {
                ObjectMapper mapper = new ObjectMapper();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                switch (event.getEventType()) {
                    case UNIT_PRODUCT_STOCK:
                        LOG.info("Processing SWIGGY UNIT_PRODUCT_STOCK EVENT");
                        UnitProductsStockEvent unitProductsStockEvent = mapper.readValue(new Gson().toJson(event.getEventData()), UnitProductsStockEvent.class);
                        if (unitProductsStockEvent.getProductIds() != null && unitProductsStockEvent.getProductIds().size() > 0) {
                            swiggyService.updateSwiggyStock(unitProductsStockEvent);
                        }
                        break;
                    case INVENTORY_UPDATE:
                        LOG.info("Processing SWIGGY INVENTORY_UPDATE EVENT");
                        List data = (List)event.getEventData();
                        List<Integer> unitIds = new ArrayList<>();
                        for(Object o : data) {
                            unitIds.add((int)Double.parseDouble(o.toString()));
                        }
                        if (Objects.nonNull((Integer)event.getBrandId())) {
                            swiggyService.refreshUnitInventory(unitIds,(Integer)event.getBrandId());
                        }
                        else {
                            swiggyService.refreshUnitInventory(unitIds);
                        }
                        break;
                    case UPDATE_ALL_UNIT_MENU:
                    	LOG.info("Processing Zomato UPDATE_UNIT_MENU EVENT");
                        UnitMenuAddVO eventD = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                        LOG.info("Units For Menu Push  ::::: {} " ,eventD.getUnitIdsForMenu());
                        swiggyService.pushMenuToUnits(eventD.getUnitIdsForMenu(), eventD.getBrandId(), eventD.getEmployeeId(), eventD.getKettlePartnerId(), eventD.getMenuType());
                        break;
                    case UPDATE_UNIT_MENU:
                        LOG.info("Processing SWIGGY UPDATE_UNIT_MENU EVENT");
                        UnitMenuAddVO eventData = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                        //To be enabled once fully integrated and tested
                        swiggyService.updateSwiggyMenu(eventData.getUnitId(), event.getEventType(), eventData.getBrandId(), eventData.getKettlePartnerId(), eventData.getEmployeeId());
                        break;
                    case SCHEDULED_MENU_PUSH:
                        LOG.info("Processing Scheduled SWIGGY UPDATE_UNIT_MENU EVENT");
                        UnitMenuAddVO unitMenuAddVO = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                        swiggyService.scheduledMenuPush(unitMenuAddVO.getUnitIdsForMenu(), unitMenuAddVO.getMenuType(),unitMenuAddVO.getBrandId());
                        break;
                    default:
                        LOG.info(new Gson().toJson(event.getEventData()));
                }
            } catch (Exception e) {
                LOG.error("Error processing swiggy redis event::::::::::::::: " + event.toString(), e);
            }
        }
    }
}
