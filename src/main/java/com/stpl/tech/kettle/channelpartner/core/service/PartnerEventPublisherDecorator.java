package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;

import java.util.List;

public abstract class PartnerEventPublisherDecorator<T> extends PartnerBaseDecorator{
    public PartnerEventPublisherDecorator(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
    }

    abstract void pushRedisEventToQueue(List<Integer> partnerIds , T event);
}
