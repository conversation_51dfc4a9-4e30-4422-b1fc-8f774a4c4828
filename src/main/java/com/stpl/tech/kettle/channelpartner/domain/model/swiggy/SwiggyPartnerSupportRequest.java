package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "timestamp",
        "timestamp_outlet",
        "swiggy_order_id",
        "external_order_id",
        "metadata"
})
public class SwiggyPartnerSupportRequest {

    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("timestamp_outlet")
    private String timestampOutlet;
    @JsonProperty("swiggy_order_id")
    private long swiggyOrderId;
    @JsonProperty("external_order_id")
    private String externalOrderId;
    @JsonProperty("metadata")
    private SwiggyRequestMetadata metadata;

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("timestamp_outlet")
    public String getTimestampOutlet() {
        return timestampOutlet;
    }

    @JsonProperty("timestamp_outlet")
    public void setTimestampOutlet(String timestampOutlet) {
        this.timestampOutlet = timestampOutlet;
    }

    @JsonProperty("swiggy_order_id")
    public long getSwiggyOrderId() {
        return swiggyOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public void setSwiggyOrderId(long swiggyOrderId) {
        this.swiggyOrderId = swiggyOrderId;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("metadata")
    public SwiggyRequestMetadata getMetadata() {
        return metadata;
    }

    @JsonProperty("metadata")
    public void setMetadata(SwiggyRequestMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("timestamp", timestamp).append("timestampOutlet", timestampOutlet).append("swiggyOrderId", swiggyOrderId).append("externalOrderId", externalOrderId).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(timestamp).append(externalOrderId).append(swiggyOrderId).append(timestampOutlet).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyPartnerSupportRequest) == false) {
            return false;
        }
        SwiggyPartnerSupportRequest rhs = ((SwiggyPartnerSupportRequest) other);
        return new EqualsBuilder().append(timestamp, rhs.timestamp).append(externalOrderId, rhs.externalOrderId).append(swiggyOrderId, rhs.swiggyOrderId).append(timestampOutlet, rhs.timestampOutlet).isEquals();
    }

}
