package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "type", "amount" })
public class ZomatoOrderDishDiscountV3 {

	@JsonProperty("type")
	private String discountType;
	@JsonProperty("amount")
	private Float discountValue;

	public String getDiscountType() {
		return discountType;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}

	public Float getDiscountValue() {
		return discountValue;
	}

	public void setDiscountValue(Float discountValue) {
		this.discountValue = discountValue;
	}

	@Override
	public String toString() {
		return "ZomatoOrderDishDiscountV3 [discountType=" + discountType + ", discountValue=" + discountValue + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(discountType).append(discountValue).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderDishDiscountV3) == false) {
			return false;
		}
		ZomatoOrderDishDiscountV3 rhs = ((ZomatoOrderDishDiscountV3) other);
		return new EqualsBuilder().append(discountType, rhs.discountType).append(discountValue, rhs.discountValue)
				.isEquals();
	}

}
