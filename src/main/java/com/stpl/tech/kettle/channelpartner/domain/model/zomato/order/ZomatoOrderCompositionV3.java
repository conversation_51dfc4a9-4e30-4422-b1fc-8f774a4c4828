package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "variant_id", "catalogue_id", "catalogue_name", "properties", "unit_cost", "modifier_groups",
		"category_id", "category_name", "sub_category_id", "sub_category_name", "combo_id", "combo_name", "selections", "instructions" })
public class ZomatoOrderCompositionV3 {

	@JsonProperty("variant_id")
	private String variantId;
	@JsonProperty("catalogue_id")
	private String catalogueId;
	@JsonProperty("catalogue_name")
	private String catalogueName;
	@JsonProperty("catalogue_description")
	private String catalogueDescription;
	@JsonProperty("properties")
	private List<ZomatoOrderCompositionPropV3> properties;
	@JsonProperty("unit_cost")
	private Float unitCost;
	@JsonProperty("modifier_groups")
	private List<ZomatoOrderModifierGroupsV3> modifierGroups;
	@JsonProperty("instructions")
	private List<ZomatoOrderItemInstructions> instructions;
	@JsonProperty("category_id")
	private String categoryId;
	@JsonProperty("category_name")
	private String categoryName;
	@JsonProperty("sub_category_id")
	private String subCategoryId;
	@JsonProperty("sub_category_name")
	private String subCategoryName;
	@JsonProperty("combo_id")
	private String comboId;
	@JsonProperty("combo_name")
	private String comboName;
	@JsonProperty("selections")
	private List<ZomatoOrderCompSelectionV3> selections;


	public String getVariantId() {
		return variantId;
	}

	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}

	public String getCatalogueId() {
		return catalogueId;
	}

	public void setCatalogueId(String catalogueId) {
		this.catalogueId = catalogueId;
	}

	public String getCatalogueName() {
		return catalogueName;
	}

	public void setCatalogueName(String catalogueName) {
		this.catalogueName = catalogueName;
	}

	public String getCatalogueDescription() {
		return catalogueDescription;
	}

	public void setCatalogueDescription(String catalogueDescription) {
		this.catalogueDescription = catalogueDescription;
	}

	public List<ZomatoOrderCompositionPropV3> getProperties() {
		return properties;
	}

	public void setProperties(List<ZomatoOrderCompositionPropV3> properties) {
		this.properties = properties;
	}

	public Float getUnitCost() {
		return unitCost;
	}

	public void setUnitCost(Float unitCost) {
		this.unitCost = unitCost;
	}

	public List<ZomatoOrderModifierGroupsV3> getModifierGroups() {
		return modifierGroups;
	}

	public void setModifierGroups(List<ZomatoOrderModifierGroupsV3> modifierGroups) {
		this.modifierGroups = modifierGroups;
	}

	public List<ZomatoOrderItemInstructions> getInstructions() {
		return instructions;
	}

	public void setInstructions(List<ZomatoOrderItemInstructions> instructions) {
		this.instructions = instructions;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getSubCategoryId() {
		return subCategoryId;
	}

	public void setSubCategoryId(String subCategoryId) {
		this.subCategoryId = subCategoryId;
	}

	public String getSubCategoryName() {
		return subCategoryName;
	}

	public void setSubCategoryName(String subCategoryName) {
		this.subCategoryName = subCategoryName;
	}

	public String getComboId() {
		return comboId;
	}

	public void setComboId(String comboId) {
		this.comboId = comboId;
	}

	public String getComboName() {
		return comboName;
	}

	public void setComboName(String comboName) {
		this.comboName = comboName;
	}

	public List<ZomatoOrderCompSelectionV3> getSelections() {
		return selections;
	}

	public void setSelections(List<ZomatoOrderCompSelectionV3> selections) {
		this.selections = selections;
	}

	@Override
	public String toString() {
		return "ZomatoOrderCompositionV3 [variantId=" + variantId + ", catalogueId=" + catalogueId + ", catalogueName="
				+ catalogueName + ", properties=" + properties + ", unitCost=" + unitCost + ", modifierGroups="
				+ modifierGroups + ", categoryId=" + categoryId + ", categoryName=" + categoryName + ", subCategoryId="
				+ subCategoryId + ", subCategoryName=" + subCategoryName + ", comboId=" + comboId + ", comboName="
				+ comboName + ", selections=" + selections + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(variantId).append(catalogueId).append(catalogueName).append(properties)
				.append(unitCost).append(modifierGroups).append(categoryId).append(categoryName).append(subCategoryId)
				.append(subCategoryName).append(comboId).append(comboName).append(selections).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderCompositionV3) == false) {
			return false;
		}
		ZomatoOrderCompositionV3 rhs = ((ZomatoOrderCompositionV3) other);
		return new EqualsBuilder().append(variantId, rhs.variantId).append(catalogueId, rhs.catalogueId)
				.append(catalogueName, rhs.catalogueName).append(properties, rhs.properties)
				.append(unitCost, rhs.unitCost).append(modifierGroups, rhs.modifierGroups)
				.append(categoryId, rhs.categoryId).append(categoryName, rhs.categoryName)
				.append(subCategoryId, rhs.subCategoryId).append(subCategoryName, rhs.subCategoryName)
				.append(comboId, rhs.comboId).append(comboName, rhs.comboName).append(selections, rhs.selections)
				.isEquals();
	}

}
