package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.kie.api.definition.rule.All;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MagicpinMerchantData {

    @JsonProperty("integration_partner_name")
    private String integrationPartnerName;

    @JsonProperty("client_id")
    private String clientId;
    @JsonProperty("client_id2")
    private String clientId2;

    @JsonProperty("client_id3")
    private String clientId3;
}
