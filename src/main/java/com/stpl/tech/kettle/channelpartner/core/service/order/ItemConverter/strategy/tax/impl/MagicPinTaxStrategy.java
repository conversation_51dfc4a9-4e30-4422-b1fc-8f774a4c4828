package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.impl;

import com.stpl.tech.kettle.channelpartner.controller.CafeLookUpResources;
import com.stpl.tech.kettle.channelpartner.core.converters.AbstractConverters;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicPinAdditionalCharges;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinChargesDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderItems;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinTaxDetails;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTaxDetailsV3;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Component
@Qualifier("magicpinTaxStrategy")
public class MagicPinTaxStrategy implements TaxStrategy {

    @Override
    public <I> void addItemTax(I item, List<OrderItem> orderItems, Map<String, TaxDataVO> taxMap) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        for (OrderItem orderItem : orderItems) {
            orderItem.setTax(setTaxForOrderItem(orderItem, magicpinOrderItem, taxMap));
            orderItem.setTaxDeductedByPartner(TaxPayingEntity.PARTNER.getMagicPin().
                    equalsIgnoreCase(magicpinOrderItem.getTaxLiability()));
            orderItem.setPartnerTaxType(magicpinOrderItem.getTaxLiability());
        }
    }

    @Override
    public <I> BigDecimal getItemPackagingTax(OrderItem orderItem, I item) {
        MagicpinOrderItems magicpinOrderItem = (MagicpinOrderItems) item;
        BigDecimal itemPackagingTax = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(magicpinOrderItem.getMagicpinChargesDetails())) {
            for (MagicpinChargesDetails magicpinChargesDetail : magicpinOrderItem.getMagicpinChargesDetails()) {
                if (magicpinChargesDetail.getName().equalsIgnoreCase("Packaging Charges")) {
                    for (MagicpinTaxDetails magicpinTaxDetail : magicpinChargesDetail.getMagicpinTaxDetails()) {
                        if (false) { //interstate is not supported
                            if (magicpinTaxDetail.getName().equalsIgnoreCase("IGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("IGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                        } else {
                            if (magicpinTaxDetail.getName().equalsIgnoreCase("CGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                            if ((magicpinTaxDetail.getName().equalsIgnoreCase("SGST") ||
                                    magicpinTaxDetail.getName().equalsIgnoreCase("UTGST")) && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                        }
                    }

                }
            }
        }
        return itemPackagingTax;
    }

    @Override
    public <R> BigDecimal getOrderLevelPackagingTax(OrderItem orderItem, R request){
        MagicpinOrderRequest magicpinOrderRequest = (MagicpinOrderRequest) request;
        BigDecimal itemPackagingTax = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(magicpinOrderRequest.getAdditionalCharges())) {
            for (MagicPinAdditionalCharges magicpinChargesDetail : magicpinOrderRequest.getAdditionalCharges()) {
                if (magicpinChargesDetail.getTitle().equalsIgnoreCase("Packaging Charges")) {
                    for (MagicpinTaxDetails magicpinTaxDetail : magicpinChargesDetail.getMagicpinTaxDetails()) {
                        if (false) { //interstate is not supported
                            if (magicpinTaxDetail.getName().equalsIgnoreCase("IGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("IGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                        } else {
                            if (magicpinTaxDetail.getName().equalsIgnoreCase("CGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                            if ((magicpinTaxDetail.getName().equalsIgnoreCase("SGST") ||
                                    magicpinTaxDetail.getName().equalsIgnoreCase("UTGST")) && Objects.nonNull(magicpinTaxDetail.getRate())) {
                                BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
                                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST",
                                        taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                                        orderItem.getPrice(), 1, BigDecimal.ZERO);
                                orderItem.getTaxes().add(taxDetail);
                                itemPackagingTax = ChannelPartnerUtils.add(itemPackagingTax, taxDetail.getValue());
                            }
                        }
                    }

                }
            }
        }
        return itemPackagingTax;
    }

    private static BigDecimal getTaxAmount(BigDecimal taxableAmount, Float taxPercentage) {
        return ChannelPartnerUtils.divide(ChannelPartnerUtils.multiply(taxableAmount, BigDecimal.valueOf(taxPercentage)), BigDecimal.valueOf(100));
    }

    private static BigDecimal setTaxForOrderItem(OrderItem orderItem, MagicpinOrderItems magicpinOrderItem, Map<String, TaxDataVO> taxMap) {
        BigDecimal totalItemTax = BigDecimal.ZERO;
        Integer qty = orderItem.getQuantity();
        DiscountDetail discountDetail = orderItem.getDiscountDetail();
        if (CollectionUtils.isEmpty(magicpinOrderItem.getTaxCharges())) {
            List<MagicpinTaxDetails> magicpinTaxDetails = new ArrayList<>();

            MagicpinTaxDetails magicpinTaxDetailsForCGST = new MagicpinTaxDetails();
            magicpinTaxDetailsForCGST.setName("CGST");
            magicpinTaxDetailsForCGST.setRate(taxMap.get(orderItem.getCode()).getState().getCgst().floatValue());
            magicpinTaxDetailsForCGST.setAmount(getTaxAmount(orderItem.getAmount(), magicpinTaxDetailsForCGST.getRate()).floatValue());
            magicpinTaxDetails.add(magicpinTaxDetailsForCGST);

            MagicpinTaxDetails magicpinTaxDetailsForSGST = new MagicpinTaxDetails();
            magicpinTaxDetailsForSGST.setName("SGST");
            magicpinTaxDetailsForSGST.setRate(taxMap.get(orderItem.getCode()).getState().getSgst().floatValue());
            magicpinTaxDetailsForSGST.setAmount(getTaxAmount(orderItem.getAmount(), magicpinTaxDetailsForSGST.getRate()).floatValue());
            magicpinTaxDetails.add(magicpinTaxDetailsForSGST);

            magicpinOrderItem.setTaxCharges(magicpinTaxDetails);
        }
        totalItemTax = processMagicPinOrderItems(orderItem, magicpinOrderItem, totalItemTax, qty, discountDetail);
        return totalItemTax;
    }

    private static BigDecimal processMagicPinOrderItems(OrderItem orderItem, MagicpinOrderItems magicpinOrderItem,
                                                                BigDecimal totalItemTax, Integer qty, DiscountDetail discountDetail) {
        for (MagicpinTaxDetails magicpinTaxDetail : magicpinOrderItem.getTaxCharges()) {
            if (false) { //interstate is not supported
                if (magicpinTaxDetail.getName().equalsIgnoreCase("IGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                    totalItemTax = setTotalTaxForOrderItem(orderItem, magicpinTaxDetail, qty, totalItemTax, "IGST", discountDetail);
                }
            } else {
                if (magicpinTaxDetail.getName().equalsIgnoreCase("CGST") && Objects.nonNull(magicpinTaxDetail.getRate())) {
                    totalItemTax = setTotalTaxForOrderItem(orderItem, magicpinTaxDetail, qty, totalItemTax, "CGST", discountDetail);
                }
                if ((magicpinTaxDetail.getName().equalsIgnoreCase("SGST") ||
                        magicpinTaxDetail.getName().equalsIgnoreCase("UTGST")) && Objects.nonNull(magicpinTaxDetail.getRate())) {
                    totalItemTax = setTotalTaxForOrderItem(orderItem, magicpinTaxDetail, qty, totalItemTax, "SGST/UTGST", discountDetail);
                }
            }
        }
        return totalItemTax;
    }

    private static BigDecimal setTotalTaxForOrderItem(OrderItem orderItem, MagicpinTaxDetails magicpinTaxDetail,
                                                Integer qty, BigDecimal totalItemTax, String taxName, DiscountDetail discountDetail) {
        BigDecimal taxAmount = getTaxAmount(orderItem.getAmount(), magicpinTaxDetail.getRate());
        TaxDetail taxDetail = AbstractConverters.createTaxDetailV3(taxName, "GST",
                taxAmount.setScale(2, RoundingMode.HALF_UP), BigDecimal.valueOf(magicpinTaxDetail.getRate()),
                orderItem.getPrice(), qty, discountDetail.getPromotionalOffer());
        orderItem.getTaxes().add(taxDetail);
        totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
        return totalItemTax;
    }
}
