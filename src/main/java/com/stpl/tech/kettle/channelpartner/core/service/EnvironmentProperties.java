package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.util.EnvType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.RecursiveTask;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Service
@RefreshScope
public class EnvironmentProperties {

	@Autowired
	Environment environment;

	public String getRedisHost() {
		return environment.getProperty("redis.host", "localhost");
	}

	public int getRedisPort() {
		return Integer.valueOf(environment.getProperty("redis.port", "6379"));
	}

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type", "DEV"));
	}

	public String getChannelPartnerClientToken() {
		return environment.getProperty("cp.client.token");
	}

	public String getMongoURI() {
		return environment.getProperty("cp.mongo.uri");
	}

	public String getMongoHost() {
		return environment.getProperty("cp.mongo.host", "localhost");
	}

	public int getMongoPort() {
		return Integer.valueOf(environment.getProperty("cp.mongo.port", "27017"));
	}

	public String getMongoSchema() {
		return environment.getProperty("cp.mongo.schema", "neo");
	}

	public String getMongoUser() {
		return environment.getProperty("cp.mongo.user", "root");
	}

	public String getMongoPass() {
		return environment.getProperty("cp.mongo.pass", "root");
	}

	public String getKettleCRMBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public String getKettleServiceBasePath() {
		return environment.getProperty("base.path.kettle.service");
	}

	public String getMasterServiceBasePath() {
		return environment.getProperty("base.path.master.service");
	}

	public String getCRMServiceBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public int getDefaultCustomerId() {
		return Integer.parseInt(environment.getProperty("default.customer.id"));
	}

	public int getSwiggyConfirmationDelayInSeconds() {
		return Integer.parseInt(environment.getProperty("swiggy.confirmation.delay","5"));
	}

    public int getZomatoConfirmationDelayInSeconds() {
        return Integer.parseInt(environment.getProperty("zomato.confirmation.delay","5"));
    }

	public boolean getSendOtp() {
		return Boolean.valueOf(environment.getProperty("send.otp", "true"));
	}

	public String getSwiggyCustomerContact() {
		return environment.getProperty("swiggy.customer.contact","**********");
	}

	public String getZomatoCustomerContact(){
		return environment.getProperty("zomato.customer.contact", "**********");
	}

	public String getPartnerCreditAccount(String partnerName){
		String key = partnerName + ".credit.account";
		return environment.getProperty(key);
	}

	public String getPartnerCustomerContact(String partnerName){
		String key = partnerName.toLowerCase() + ".customer.contact";
		return environment.getProperty(key,"**********");
	}

	public String getSwiggyAPIKey() {
		return environment.getProperty("swiggy.api.key","Dssd2NfASDr34DTDAa9HlP8MPURVGN5bI0edwedsuay5sada");
	}

	public String getSwiggyMenuAuthorizationKey() {
		return environment.getProperty("swiggy.menu.authorization.key","dXNlcjpjaGVjaw==");
	}

	public String getSwiggyMenuTokenId() {
		return environment.getProperty("swiggy.menu.token.id","1002651b75492a31");
	}

    public String getZomatoAPIKey(int brandId) {
		if(brandId == 0) {
			brandId = 1; //Chaayos brand id
		}
        return environment.getProperty("zomato.api.key"+brandId,"90a38a546af580c03cfd2cc39146daf1");
    }

	public String getBasePath() {
		return environment.getProperty("server.base.dir");
	}

	public boolean slackDuplicateOrders(){
		return Boolean.valueOf(environment.getProperty("slack.duplicate.orders", "false"));
	}

	public String getEmailFilterDomains() {
		return environment.getProperty("email.filter.domains","");
	}

	public String getAwsQueueRegion() {
		return environment.getProperty("aws.queue.region","EU_WEST_1");
	}
	
	public String getDevUrl() {
		return environment.getProperty("base.path.dev.url");
	}

	public String getChannelPartnerRedirectToken() {
		return environment.getProperty("cp.redirect.client.token");
	}
	
	public boolean getCpCondimentFlag() {
		return  Boolean.valueOf(environment.getProperty("channelpartner.condiment.flag"));
	}

	public boolean isScheduledMenuPush() {
		return  Boolean.valueOf(environment.getProperty("scheduled.menu.push", "false"));
	}
	public boolean isScheduledMenuPushForGNT() {
		return  Boolean.valueOf(environment.getProperty("scheduled.menu.push,gnt", "false"));
	}

    public String getChannelPartnerBasePath() {
		return environment.getProperty("base.path.channel.partner");
    }

    public String getKnockNotificationUrl() {
		return  environment.getProperty("knock.notification.url");
    }

	public String getKnockMasterToken() {
		return environment.getProperty("knock.master.token");
	}

	public String getKnockBaseUrl() {
		return  environment.getProperty("knock.base.url");
	}

	public boolean isTestingModeforNotification(){
		return  Boolean.valueOf(environment.getProperty("notification.testingMode.flag","false"));
	}

	public boolean isTestingModeEnabled(){
		return Boolean.valueOf(environment.getProperty("cp.test.mode", "false"));
	}
	public String getLiveOrderBaseUrl() {
		return environment.getProperty("live.orders.base.url","http://dev.kettle.chaayos.com:8080");
	}

	public String getLiveOrdersToken(){
		return  environment.getProperty("live.orders.token");
	}

	public boolean isDefaultZomatoCustomerFlow(){
		return Boolean.valueOf(environment.getProperty("default.zomato.customer.flow", "true"));
	}

	public boolean isDefaultPartnerCustomerFlow(String partnerName){
		String key = "default." + partnerName.toLowerCase() + ".customer.flow";
		return Boolean.valueOf(environment.getProperty(key,"true"));
	}

	public boolean getEditOrderCompositionBasisRemark(){
		return Boolean.valueOf(environment.getProperty("edit.order.composition.basis.delivery.remark", "false"));
	}

	public Integer getSwiggyCustomerId() {
		return Integer.parseInt(environment.getProperty("default.swiggy.customer.id", "67456"));
	}

	public Integer getZomatoCustomerId() {
		return Integer.parseInt(environment.getProperty("default.zomato.customer.id", "3527255"));
	}

	public boolean isDefaultSwiggyCustomerFlow(){
		return Boolean.valueOf(environment.getProperty("default.swiggy.customer.flow", "false"));
	}

	public String getInventoryBaseUrl(){
		return environment.getProperty("inventory.base.url", "https://internal.chaayos.com");
	}
	public Boolean callInventoryForWebOrders(){
		return Boolean.valueOf(environment.getProperty("call.inventory.for.web.orders", "true"));
	}

	public String getMagicpinApiKey() {
		return environment.getProperty("magicpin.api.key","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiY2hhYXlvcyIsImlhdCI6MTcwNTMwMzQ3NX0.z25UJqMUnJm3kotaZeZMGrvcP-EbRUi2GcbfWU48ywk");
	}

	public String getDesiChaiProfileNameForSuperCombo(){
		return environment.getProperty("desi.chai.profile.name.super.combo","");
	}
	public Boolean isStockInProductsDuringMenuPushEnabled(){
		return Boolean.valueOf(environment.getProperty("menu.stock.in.product.filter","true"));
	}

	public Boolean isStockInProductsDuringMenuUnitIds(Integer unitId){
		String propValue = environment.getProperty("menu.stock.in.product.filter.unit.ids","26091");
		if("ALL".equalsIgnoreCase(propValue)){
			return true;
		}else{
			List<String> unitIds =  Arrays.stream(propValue.split(",")).toList();
			return  unitIds.contains(String.valueOf(unitId));
		}
	}

	public Boolean isAcquireLockUsed(){
		return Boolean.valueOf(environment.getProperty("acquire.lock.used","true"));
	}

	public Integer lockTimeInMilliSecond(){
		return Integer.valueOf(environment.getProperty("lock.time.in.ms", String.valueOf(500)));
	}

	public String getStoreOpsBasePath() {
		return environment.getProperty("base.path.store.ops");
	}

	public String getStoreOpsKettleAuth(){
		return environment.getProperty("store.ops.cp.client.token");
	}

	public List<Integer> getUnitIdsForMenu199(){
		List<Integer> unitIds  = new ArrayList<>();
		try{
			if(!StringUtils.isEmpty(environment.getProperty("unit.ids.menu.199",""))){
				unitIds = Arrays.stream(environment.getProperty("unit.ids.menu.199","").
						split(",")).map(s ->Integer.parseInt(s)).toList();
			}
		}catch (Exception e){

		}

		return unitIds;
	}

	public List<Integer> getUnitIdsForMenu299(){
		List<Integer> unitIds  = new ArrayList<>();
		try{
			if(!StringUtils.isEmpty(environment.getProperty("unit.ids.menu.299",""))){
				unitIds = Arrays.stream(environment.getProperty("unit.ids.menu.299","").
						split(",")).map(s ->Integer.parseInt(s)).toList();
			}
		}catch (Exception e){

		}

		return unitIds;
	}

	public Integer getNumberOfDaysForLastOrderCheck(){
		return Integer.valueOf(environment.getProperty("days.to.check.for.last.order","2"));
	}

	public Boolean toSendStockOutReportAfterMenuPush(){
		return Boolean.valueOf(environment.getProperty("send.stock.out.report","false"));
	}

	public List<String>  getEmailsForStockOutReport() {
		List<String> emailIds = new ArrayList<>();
		try {
			if (!StringUtils.isEmpty(environment.getProperty("stock.out.report.emails", "<EMAIL>"))) {
				emailIds = Arrays.stream(environment.getProperty("stock.out.report.emails", "").split(",")).toList();
			}
		} catch (Exception e) {

		}
		return emailIds;
	}

	public String getProductIdsForSplitDimension(){
		return environment.getProperty("split.dimension.product.ids");
	}

	public Boolean multipleQuantityZomatoModifierEnabled(){
		return Boolean.valueOf(environment.getProperty("zomato.multiquantity.modifier.enabled","true"));
	}

	public String getSwiggyBoltIdentifier(){
		return environment.getProperty("swiggy.bolt.key","Bolt-10 min delivery");
	}

	public String getZomatoExpressIndentifier(){
		return environment.getProperty("zomato.express.identifier","EXPRESS");
	}

	public Boolean runCronForZomatoCafeStatus() {
		return Boolean.valueOf(environment.getProperty("zomato.status.cron.enabled","true"));
	}

	public String getComplaintApiResponse() {
		return environment.getProperty("zomato.complaint.relay.response","Complaints request acknowledged successfully");
	}

	public String getN8NWebhookPath() {
		return environment.getProperty("zomato.complaint.n8n.webhook","https://n8n.chaayos.com/webhook-test/orderComplaintWebhook");
	}
}

