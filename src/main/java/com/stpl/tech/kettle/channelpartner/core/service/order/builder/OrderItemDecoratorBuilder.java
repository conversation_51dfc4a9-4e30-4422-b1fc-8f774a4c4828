package com.stpl.tech.kettle.channelpartner.core.service.order.builder;

import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.AbstractOrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class OrderItemDecoratorBuilder {

    public PartnerItemConverterService partnerItemConverterService;
    public MasterDataCache masterDataCache;
    public TaxStrategy taxStrategy;
    public DiscountStrategy discountStrategy;

    public AbstractOrderItemConverterService abstractOrderItemConverterService;

    public OrderItemDecoratorBuilder defaultBuilder(PartnerOrderConverterDependency partnerOrderConverterDependency, String partnerName, AbstractOrderItemConverterService abstractOrderItemConverterService) {
        this.partnerItemConverterService = partnerOrderConverterDependency.getPartnerItemConverterService(partnerName);
        this.masterDataCache = partnerOrderConverterDependency.getMasterDataCache();
        this.taxStrategy = partnerOrderConverterDependency.getTaxStrategy(partnerName);
        this.abstractOrderItemConverterService=abstractOrderItemConverterService;
        this.discountStrategy=partnerOrderConverterDependency.getdiscountStrategy(partnerName);
        return this;
    }


}
