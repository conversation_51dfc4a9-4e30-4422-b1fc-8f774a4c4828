
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.PaymentStatus;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "order_id",
    "restaurant_id",
    "restaurant_name",
    "outlet_id",
    "order_date_time",
    "enable_delivery",
    "net_amount",
    "gross_amount",
    "payment_mode",
    "payment_status",
    "amount_paid",
    "amount_balance",
    "order_type",
    "order_additional_charges",
    "takeaway_details",
    "order_discounts",
    "customer_details",
    "order_items",
    "cash_to_be_collected"
})
public class ZomatoOrderRequest implements ZomatoOrderable {

    @JsonProperty("order_id")
    private Long orderId;
    @JsonProperty("restaurant_id")
    private Long restaurantId;
    @JsonProperty("restaurant_name")
    private String restaurantName;
    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("order_date_time")
    private String orderDateTime;
    @JsonProperty("enable_delivery")
    private Integer enableDelivery;
    @JsonProperty("net_amount")
    private Float netAmount;
    @JsonProperty("gross_amount")
    private Float grossAmount;
    @JsonProperty("payment_mode")
    private String paymentMode;
    @JsonProperty("payment_status")
    private PaymentStatus paymentStatus;
    @JsonProperty("amount_paid")
    private Float amountPaid;
    @JsonProperty("amount_balance")
    private Float amountBalance;
    @JsonProperty("order_type")
    private ZomatoOrderType orderType;
    @JsonProperty("order_instructions")
    private String orderInstructions;
    @JsonProperty("order_additional_charges")
    private List<ZomatoChargeDetails> orderAdditionalCharges = null;
    @JsonProperty("takeaway_details")
    private ZomatoOrderTakeAwayDetails takeAwayDetails;
    @JsonProperty("order_discounts")
    private List<ZomatoOrderDiscount> orderDiscounts = null;
    @JsonProperty("customer_details")
    private CustomerDetails customerDetails;
    @JsonProperty("order_items")
    private List<ZomatoOrderItem> orderItems = null;
    @JsonProperty("cash_to_be_collected")
    private Float cashToBeCollected = 0f;
    @JsonProperty("total_merchant")
    private Float totalMerchant = 0f;
    @JsonProperty("is_special_combo_order")
    private Integer isSpecialComboOrder;
    @JsonProperty("order_status")
    private String orderStatus;

    @JsonProperty("order_id")
    public Long getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("restaurant_id")
    public Long getRestaurantId() {
        return restaurantId;
    }

    @JsonProperty("restaurant_id")
    public void setRestaurantId(Long restaurantId) {
        this.restaurantId = restaurantId;
    }

    @JsonProperty("restaurant_name")
    public String getRestaurantName() {
        return restaurantName;
    }

    @JsonProperty("restaurant_name")
    public void setRestaurantName(String restaurantName) {
        this.restaurantName = restaurantName;
    }

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("order_date_time")
    public String getOrderDateTime() {
        return orderDateTime;
    }

    @JsonProperty("order_date_time")
    public void setOrderDateTime(String orderDateTime) {
        this.orderDateTime = orderDateTime;
    }

    @JsonProperty("enable_delivery")
    public Integer getEnableDelivery() {
        return enableDelivery;
    }

    @JsonProperty("enable_delivery")
    public void setEnableDelivery(Integer enableDelivery) {
        this.enableDelivery = enableDelivery;
    }

    @JsonProperty("net_amount")
    public Float getNetAmount() {
        return netAmount;
    }

    @JsonProperty("net_amount")
    public void setNetAmount(Float netAmount) {
        this.netAmount = netAmount;
    }

    @JsonProperty("gross_amount")
    public Float getGrossAmount() {
        return grossAmount;
    }

    @JsonProperty("gross_amount")
    public void setGrossAmount(Float grossAmount) {
        this.grossAmount = grossAmount;
    }

    @JsonProperty("payment_mode")
    public String getPaymentMode() {
        return paymentMode;
    }

    @JsonProperty("payment_mode")
    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @JsonProperty("payment_status")
    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    @JsonProperty("payment_status")
    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @JsonProperty("amount_paid")
    public Float getAmountPaid() {
        return amountPaid;
    }

    @JsonProperty("amount_paid")
    public void setAmountPaid(Float amountPaid) {
        this.amountPaid = amountPaid;
    }

    @JsonProperty("amount_balance")
    public Float getAmountBalance() {
        return amountBalance;
    }

    @JsonProperty("amount_balance")
    public void setAmountBalance(Float amountBalance) {
        this.amountBalance = amountBalance;
    }

    @JsonProperty("order_type")
    public ZomatoOrderType getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(ZomatoOrderType orderType) {
        this.orderType = orderType;
    }

    @JsonProperty("order_instructions")
    public String getOrderInstructions() {
        return orderInstructions;
    }

    @JsonProperty("order_instructions")
    public void setOrderInstructions(String orderInstructions) {
        this.orderInstructions = orderInstructions;
    }

    @JsonProperty("order_additional_charges")
    public List<ZomatoChargeDetails> getOrderAdditionalCharges() {
        return orderAdditionalCharges;
    }

    @JsonProperty("order_additional_charges")
    public void setOrderAdditionalCharges(List<ZomatoChargeDetails> orderAdditionalCharges) {
        this.orderAdditionalCharges = orderAdditionalCharges;
    }

    @JsonProperty("takeaway_details")
    public ZomatoOrderTakeAwayDetails getTakeAwayDetails() {
        return takeAwayDetails;
    }

    @JsonProperty("takeaway_details")
    public void setTakeAwayDetails(ZomatoOrderTakeAwayDetails takeAwayDetails) {
        this.takeAwayDetails = takeAwayDetails;
    }

    @JsonProperty("order_discounts")
    public List<ZomatoOrderDiscount> getOrderDiscounts() {
        return orderDiscounts;
    }

    @JsonProperty("order_discounts")
    public void setOrderDiscounts(List<ZomatoOrderDiscount> orderDiscounts) {
        this.orderDiscounts = orderDiscounts;
    }

    @JsonProperty("customer_details")
    public CustomerDetails getCustomerDetails() {
        return customerDetails;
    }

    @JsonProperty("customer_details")
    public void setCustomerDetails(CustomerDetails customerDetails) {
        this.customerDetails = customerDetails;
    }

    @JsonProperty("order_items")
    public List<ZomatoOrderItem> getOrderItems() {
        return orderItems;
    }

    @JsonProperty("order_items")
    public void setOrderItems(List<ZomatoOrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    @JsonProperty("cash_to_be_collected")
    public Float getCashToBeCollected() {
        return cashToBeCollected;
    }

    @JsonProperty("cash_to_be_collected")
    public void setCashToBeCollected(Float cashToBeCollected) {
        this.cashToBeCollected = cashToBeCollected;
    }

    @JsonProperty("total_merchant")
    public Float getTotalMerchant() {
        return totalMerchant;
    }

    @JsonProperty("total_merchant")
    public void setTotalMerchant(Float totalMerchant) {
        this.totalMerchant = totalMerchant;
    }

    @JsonProperty("is_special_combo_order")
    public Integer getIsSpecialComboOrder() {
        return isSpecialComboOrder;
    }

    @JsonProperty("is_special_combo_order")
    public void setIsSpecialComboOrder(Integer isSpecialComboOrder) {
        this.isSpecialComboOrder = isSpecialComboOrder;
    }

    @JsonProperty("order_status")
    public String getOrderStatus() {
        return orderStatus;
    }

    @JsonProperty("order_status")
    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("orderId", orderId).append("restaurantId", restaurantId).append("restaurantName", restaurantName).append("outletId", outletId).append("orderDateTime", orderDateTime).append("enableDelivery", enableDelivery).append("netAmount", netAmount).append("grossAmount", grossAmount).append("paymentMode", paymentMode).append("paymentStatus", paymentStatus).append("amountPaid", amountPaid).append("amountBalance", amountBalance).append("orderType", orderType).append("orderAdditionalCharges", orderAdditionalCharges).append("orderDiscounts", orderDiscounts).append("customerDetails", customerDetails).append("orderItems", orderItems).append("totalMerchant", totalMerchant).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(paymentMode).append(orderDateTime).append(orderType).append(paymentStatus).append(orderItems).append(grossAmount).append(enableDelivery).append(amountBalance).append(outletId).append(customerDetails).append(orderDiscounts).append(amountPaid).append(restaurantName).append(restaurantId).append(orderId).append(orderAdditionalCharges).append(netAmount).append(totalMerchant).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderRequest) == false) {
            return false;
        }
        ZomatoOrderRequest rhs = ((ZomatoOrderRequest) other);
        return new EqualsBuilder().append(paymentMode, rhs.paymentMode).append(orderDateTime, rhs.orderDateTime).append(orderType, rhs.orderType).append(paymentStatus, rhs.paymentStatus).append(orderItems, rhs.orderItems).append(grossAmount, rhs.grossAmount).append(enableDelivery, rhs.enableDelivery).append(amountBalance, rhs.amountBalance).append(outletId, rhs.outletId).append(customerDetails, rhs.customerDetails).append(orderDiscounts, rhs.orderDiscounts).append(amountPaid, rhs.amountPaid).append(restaurantName, rhs.restaurantName).append(restaurantId, rhs.restaurantId).append(orderId, rhs.orderId).append(orderAdditionalCharges, rhs.orderAdditionalCharges).append(netAmount, rhs.netAmount).append(totalMerchant, rhs.totalMerchant).isEquals();
    }

}
