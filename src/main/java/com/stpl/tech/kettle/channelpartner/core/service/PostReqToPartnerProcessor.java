package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqResponseData;

import java.util.List;

public abstract class PostReqToPartnerProcessor extends PartnerActionEventDecorator {

    public abstract <T> List<?> postReqToPartner(List<T> request) throws ChannelPartnerException;

    @Override
    public <T, R> List<R> processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
       return super.processRedisEvent(partnerName, partnerActionEvent, res, eventAbstractFactory);
    }

}
