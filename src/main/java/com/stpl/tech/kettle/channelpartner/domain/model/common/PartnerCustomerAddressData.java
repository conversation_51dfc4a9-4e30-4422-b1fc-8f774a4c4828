package com.stpl.tech.kettle.channelpartner.domain.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerCustomerAddressData {
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String locality;

    private String landmark;

    private String city;

    private String state;

    private String country;

    private String latitude;

    private String longitude;

    private String zipcode;
}
