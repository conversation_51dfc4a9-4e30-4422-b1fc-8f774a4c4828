package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PostEventProcessor;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqResponseData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Objects;


@Log4j2
public class PostStockEventProcessor extends PostEventProcessor {
    private final PartnerAbstractFactory partnerAbstractFactory;
    private final PartnerActionEventDecorator partnerActionEventDecorator;
    protected final PartnerMetadataBuilder partnerMetadataBuilder;

    public PostStockEventProcessor(PartnerAbstractFactory partnerAbstractFactory, PartnerActionEventDecorator partnerActionEventDecorator ,PartnerMetadataBuilder partnerMetadataBuilder) {
        this.partnerAbstractFactory = partnerAbstractFactory;
        this.partnerActionEventDecorator = partnerActionEventDecorator;
        this.partnerMetadataBuilder = partnerMetadataBuilder;
    }

    @Override
    public <T, R> List processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        List<StockReqResponseData> resultList = this.partnerActionEventDecorator.processRedisEvent(partnerName, partnerActionEvent, res, eventAbstractFactory);
        if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
            return postEventProcessing(resultList, EventType.Stock);
        }
        return null;
    }


    @Override
    public <StockReqResponseData> List<PartnerEventResponse> postEventProcessing(List<StockReqResponseData> obj, EventType eventType) throws ChannelPartnerException {
        return this.partnerAbstractFactory.postEventProcessing(obj, eventType);
    }
}
