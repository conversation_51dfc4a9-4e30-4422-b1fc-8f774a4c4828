package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PostReqToPartnerProcessor;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.task.stock.StockEventTask;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqResponseData;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Log4j2
public class PostInventoryReqToPartner extends PostReqToPartnerProcessor {

    private PartnerAbstractFactory partnerAbstractFactory;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public PostInventoryReqToPartner(PartnerAbstractFactory partnerAbstractFactory) {
        this.partnerAbstractFactory = partnerAbstractFactory;
    }

    @Override
    public <T, UnitProductsStockEvent> List<UnitProductsStockEvent> processRedisEvent(String partnerName, PartnerActionEvent partnerActionEvent, Class<T> res, EventAbstractFactory eventAbstractFactory) throws ChannelPartnerException {
        return super.processRedisEvent(partnerName, partnerActionEvent, res, eventAbstractFactory);
    }

    @Override
    public <StockReqMetadata> List<?> postReqToPartner(List<StockReqMetadata> request) throws ChannelPartnerException {
        Gson gson = new Gson();
        List<StockReqResponseData> stockReqResponseList = new ArrayList<>();
        ExecutorService executor = Executors.newCachedThreadPool();
        List<Future<StockReqResponseData>> futures = new ArrayList<>();
        for (StockReqMetadata stockReqMetadata : request) {
            Callable<StockReqResponseData> task = new StockEventTask(partnerAbstractFactory, (com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata) stockReqMetadata, ChannelPartnerUtils.generateID());
            Future<StockReqResponseData> future = executor.submit(task);
            futures.add(future);
        }
        // wait for all tasks to complete
        for (Future<StockReqResponseData> future : futures) {
            try {
                StockReqResponseData response = future.get();
                // Perform post-processing with the response
                if (response != null) {
                    stockReqResponseList.add(response);
                }
            } catch (InterruptedException | ExecutionException e) {
                throw new ChannelPartnerException("Throwing interrupted exception while processing menu on multi threads ");
            }
        }
        executor.shutdown();
        return stockReqResponseList;
    }
}
