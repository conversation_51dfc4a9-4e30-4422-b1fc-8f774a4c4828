package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.PartnerOrderFallbackLogDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackLog;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

@Repository
public class PartnerOrderFallbackLogDaoImpl extends AbstractMasterDaoImpl implements PartnerOrderFallbackLogDao {

    public PartnerOrderFallbackLog findByOrderId(String orderId) {
        try{
            Query query = manager.createQuery("FROM PartnerOrderFallbackLog p WHERE p.orderId= :orderId");
            query.setParameter("orderId",orderId);
            List<PartnerOrderFallbackLog> data = query.getResultList();
            return data.isEmpty() ? null : data.get(0);
        }catch (NoResultException nre){
            return  null;

        }
    }

    public boolean checkSwiggyCxSupport(String orderId){
        try{
            Query query = manager.createQuery("FROM PartnerOrderFallbackLog p WHERE p.orderId= :orderId  and (p.action = 'CALL_SWIGGY_CUSTOMER' or p.action = 'CALL_SWIGGY_SUPPORT' ) ");
            query.setParameter("orderId",orderId);
            List<PartnerOrderFallbackLog> data = query.getResultList();
            return !data.isEmpty();
        }catch (Exception e){
            return false;
        }
    }
}
