package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "PARTNER_ORDER_COMPLAINT_TRACK")
public class PartnerOrderComplaintTrack {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "PARTNER_ORDER_ID", nullable = false)
    private String partnerOrderId;

    @Column(name = "JSON_DATA", nullable = false, columnDefinition = "json")
    private String jsonData;

    @Column(name = "ADD_TIME")
    private Date addTime;

    @Column(name = "STATUS")
    private String status;
}
