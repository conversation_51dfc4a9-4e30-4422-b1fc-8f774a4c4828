package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_type",
        "charges"
})
public class ZomatoMenuItemCharge {

    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("charges")
    private List<Integer> charges;

    @JsonProperty("order_type")
    public String getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<Integer> getCharges() {
        if(charges == null){
            charges = new ArrayList<>();
        }
        return charges;
    }

    public void setCharges(List<Integer> charges) {
        this.charges = charges;
    }
}