package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinItem implements Serializable {
    private String id;
    private String title;
    private String imageUrl;
    private String description;
    private Integer foodType;
    private double price;
    private boolean inStock;
    private List<MagicPinCustomization> customizations;
    private List<String> modifierIds;
    private List<String> chargeIds;
    private List<String> taxIds;
    private List<Object> tags;
}
