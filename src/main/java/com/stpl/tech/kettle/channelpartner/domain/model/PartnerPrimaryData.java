package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerData;
import com.stpl.tech.master.recipe.model.BasicInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerPrimaryData {
    String orderId;
    String outletId;

    String partnerName;

    String discountName;

    String customerName;

    String customerPhoneNumber;

    BigDecimal cashToCollect = BigDecimal.ZERO;

    String paymentMode;

    String orderInstructions;

    String orderType;

    boolean enableDelivery;

    PartnerCustomerData partnerCustomerData;
}
