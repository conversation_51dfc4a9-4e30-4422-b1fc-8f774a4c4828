package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "PARTNER_STOCK_UPDATE_SCHEDULE")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PartnerStockUpdateSchedule implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STOCK_UPDATE_ID")
    protected Integer stockUpdateId;

    @Column(name = "PARTNER_ID")
    protected Integer partnerId;

    @Column(name = "BRAND_ID")
    protected Integer brandId;

    @Column(name = "UNIT_IDS")
    protected String unitIds;

    @Column(name = "PRODUCT_IDS")
    protected String productIds;

    @Column(name = "STOCK_IN_TIME")
    protected String stockInTime;

    @Column(name = "STOCK_OUT_TIME")
    protected String stockOutTime;

    @Column(name = "LAST_EXECUTION_TIME")
    protected Date lastExecutionTime;



    @Column(name = "START_TIME")
    protected Date startTime;

    @Column(name = "STOCK_OUT_END_TIME")
    protected Date stockOutEndTime;
 @Column(name = "STOCK_OUT_START_TIME")
    protected Date stockOutStartTime;

    @Column(name = "STATUS")
    protected String status;
}
