package com.stpl.tech.kettle.channelpartner.core.redis.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class CommonListenerImpl implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(CommonListenerImpl.class);

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    PartnerMenuService partnerMenuService;

    private ChannelTopic channelTopic = new ChannelTopic("COMMON");

    @PostConstruct
    public void subscribe() {
        redisMessageListenerContainer.addMessageListener(this, channelTopic);
    }

    @Override
    public void onMessage(Message message, byte[] bytes) {
        PartnerActionEvent event = new Gson().fromJson(message.toString(), PartnerActionEvent.class);
        if (event != null) {
            LOG.info("Processing action: " + new Gson().toJson(event));
            try {
                switch (event.getEventType()) {
                    case ORDER_MISMATCH:
                        LOG.info("PROCESSING ORDER_MISMATCH EVENT "+event.getEventType().name());
                        PartnerOrderDetail detail = new Gson().fromJson(new Gson().toJson(event.getEventData()), PartnerOrderDetail.class);
                        partnerOrderService.sendOrderNotification(detail);
                        break;
                    case ORDER_STATUS_UPDATE:
                        LOG.info("PROCESSING ORDER_STATUS_UPDATE EVENT "+event.getEventType().name());
                        PartnerOrderStateUpdate stateUpdate = new Gson().fromJson(new Gson().toJson(event.getEventData()), PartnerOrderStateUpdate.class);
                        partnerOrderService.sendOrderStatusUpdate(stateUpdate);
                        break;
                    case ORDER_RECONCILIATION_DATA:
                        LOG.info("PROCESSING ORDER_DATA EVENT OF "+event.getEventType().name());
                        //partnerMenuService.fetchOrderReconciliationData();
                        break;
                    default:
                }
            } catch (Exception e) {
                LOG.error("Error processing common redis event::::::::::::::: " + event.toString() + " \n" + e);
            }
        }
    }
}
