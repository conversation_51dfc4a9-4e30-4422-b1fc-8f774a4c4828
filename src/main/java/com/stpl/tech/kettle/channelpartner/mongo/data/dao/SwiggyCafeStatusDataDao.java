package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface SwiggyCafeStatusDataDao extends MongoRepository<SwiggyCafeStatusData, String> {
    SwiggyCafeStatusData findByUnitId(Integer unitId);

    SwiggyCafeStatusData findByUnitIdAndBrandId(Integer unitId,Integer brandId);

    @Query(value = "{ 'unitId': { $in: ?0 }, 'partnerStatus': ?1, 'brandId': { $exists: ?2 } }",
            sort = "{ 'lastUpdatedTime': -1 }")
    List<SwiggyCafeStatusData> findAllByUnitIdInAndPartnerStatusAndBrandIdExists(Set<Integer> unitIds, boolean status, boolean brandExists);

}
