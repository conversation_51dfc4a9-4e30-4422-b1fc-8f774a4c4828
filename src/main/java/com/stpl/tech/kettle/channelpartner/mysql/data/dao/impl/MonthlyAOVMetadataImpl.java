package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MonthlyAOVDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.MonthlyAOVMetadataDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.MonthlyAOVMetadata;
import com.stpl.tech.master.core.exception.DataUpdationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

@Slf4j
@Repository
public class MonthlyAOVMetadataImpl extends AbstractMasterDaoImpl implements MonthlyAOVMetadataDao {
    @Override
    public MonthlyAOVMetadata getPreviousMonthAov(int year, int month, int brandId) {
        Query query = manager.createQuery("FROM MonthlyAOVMetadata z where z.year = :year and z.month = :month and z.brandId= :brandId");
        query.setParameter("year",year);
        query.setParameter("month",month);
        query.setParameter("brandId",brandId);
        return  query.getResultList().isEmpty() ? null : (MonthlyAOVMetadata) query.getResultList().get(0);
    }

    @Override
    public List<MonthlyAOVMetadata> getBrandWisePreviousMonthAov(int year, int month, List<Integer> brandId) {
        try{
            Query query = manager.createQuery("FROM  MonthlyAOVMetadata z where z.year =:year and z.month=:month and z.brandId in (:brandId)");
            query.setParameter("year", year);
            query.setParameter("month", month);
            query.setParameter("brandId", brandId);
            List<MonthlyAOVMetadata> monthlyAOVMetadata= query.getResultList();
            return monthlyAOVMetadata;
        }catch(Exception e ){
            log.error("Exception while getting brand wise previous month aov ::::", e);
            return null;
        }
    }

    @Override
    public void updateAovValues(MonthlyAOVMetadata monthlyAOVMetadata, Optional<MonthlyAOVDetail> obj) throws DataUpdationException {
        monthlyAOVMetadata.setAov(obj.get().getAov());
        monthlyAOVMetadata.setCalculationStartTime(obj.get().getCalculationStartTime());
        monthlyAOVMetadata.setCalculatedAt(obj.get().getCalculatedAt());
        manager.flush();
    }
}
