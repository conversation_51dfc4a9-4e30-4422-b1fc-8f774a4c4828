package com.stpl.tech.kettle.channelpartner.core.task.stock.schedule;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl.PartnerStockSchedulerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@Log4j2
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemStockScheduleTask implements Runnable {

    //private UnitProductsStockEvent unitProductsStockEvent;

    private PartnerStockUpdateSchedule partnerStockUpdateSchedule;

    private PartnerStockSchedulerDao partnerStockSchedulerDao;

    private StockStatus stockStatus;

    private ZomatoService zomatoService;

    private SwiggyService swiggyService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void run() {
        List<UnitProductsStockEvent> unitProductsStockEvents  = getStockEvents(this.partnerStockUpdateSchedule,this.stockStatus);
        try{
            switch (this.partnerStockUpdateSchedule.getPartnerId()){
                case 3 :
                    for(UnitProductsStockEvent event : unitProductsStockEvents){
                        try{
                            zomatoService.updateZomatoStockV3ForScheduleStockUpdate(event,this.partnerStockUpdateSchedule);
                        }catch (Exception e){
                            log.error("Error While updating Stock Schedule For unit Id : {}  task Id : {}",event.getUnitId(), this.partnerStockUpdateSchedule.getStockUpdateId());
                        }
                    }
                    break;
                case 6:
                    for(UnitProductsStockEvent event : unitProductsStockEvents){
                        swiggyService.updateSwiggyStockForScheduledStockUpdate(event,this.partnerStockUpdateSchedule);
                    }
                    break;
            }
            partnerStockUpdateSchedule.setLastExecutionTime(AppUtils.getCurrentTimestamp());
        }catch (Exception e){
            log.error("Error which executing scheduled stock update task task id ::::: {}  ::::: {} " , this.partnerStockUpdateSchedule.getStockUpdateId()
            ,e);
        }
        if(this.stockStatus.equals(StockStatus.STOCK_OUT)){
            partnerStockUpdateSchedule.setStockOutStartTime(AppUtils.addDays(partnerStockUpdateSchedule.getStockOutStartTime(),1));
            partnerStockUpdateSchedule.setStockOutEndTime(AppUtils.addDays(partnerStockUpdateSchedule.getStockOutEndTime(),1));
        }
        this.partnerStockSchedulerDao.save(partnerStockUpdateSchedule);

    }

    private List<UnitProductsStockEvent> getStockEvents(PartnerStockUpdateSchedule event, StockStatus stockStatus){
        List<UnitProductsStockEvent> unitProductsStockEvents = new ArrayList<>();
        Set<Integer> unitIds = Arrays.stream(event.getUnitIds().split(",")).map(id -> Integer.parseInt(id))
                .collect(Collectors.toSet());
        List<String> productIds = Arrays.stream(event.getProductIds().split(","))
                .collect(Collectors.toList());
        for(Integer unitId : unitIds){
            UnitProductsStockEvent stockEvent = new UnitProductsStockEvent();
            stockEvent.setPartnerId(event.getPartnerId());
            stockEvent.setBrandId(event.getBrandId());
            stockEvent.setUnitId(unitId);
            stockEvent.setProductIds(productIds);
            stockEvent.setStatus(stockStatus);
            unitProductsStockEvents.add(stockEvent);
        }
        return unitProductsStockEvents;
    }
}
