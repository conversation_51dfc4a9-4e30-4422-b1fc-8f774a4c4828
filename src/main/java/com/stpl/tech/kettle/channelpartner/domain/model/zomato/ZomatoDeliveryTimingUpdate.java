package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outlet_id",
        "timings"
})
public class ZomatoDeliveryTimingUpdate {

    @JsonProperty("outlet_id")
    String outlet_id;

    @JsonProperty("timings")
    List<ZomatoTimings> timings;

    @JsonProperty("outlet_id")
    public String getOutlet_id() {
        return outlet_id;
    }

    @JsonProperty("outlet_id")
    public void setOutlet_id(String outlet_id) {
        this.outlet_id = outlet_id;
    }

    @JsonProperty("timings")
    public List<ZomatoTimings> getTimings() {
        return timings;
    }

    @JsonProperty("timings")
    public void setTimings(List<ZomatoTimings> timings) {
        this.timings = timings;
    }
}
