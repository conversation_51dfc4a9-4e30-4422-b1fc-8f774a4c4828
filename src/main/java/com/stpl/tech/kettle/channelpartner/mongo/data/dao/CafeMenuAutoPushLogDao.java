package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPushLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CafeMenuAutoPushLogDao extends MongoRepository<CafeMenuAutoPushLog,String> {
   List<CafeMenuAutoPushLog> findTop15ByBrandIdAndUnitIdOrderByLastUpdatedTimeStringDesc(Integer brandId, Integer unitId );
}
