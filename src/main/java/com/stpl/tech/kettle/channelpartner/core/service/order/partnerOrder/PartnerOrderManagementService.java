package com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStates;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerCustomerData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderUpdate;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;

import java.util.Map;

public interface PartnerOrderManagementService {

    public <R,Q> PartnerOrderDetail primaryChecks(R request, PartnerOrderDetail partnerOrderDetail,
                                       Q response, Integer kettlePartnerId , PartnerPrimaryData primaryData);

    public void checkOrder(PartnerOrderDetail partnerOrderDetail, Order order,
                             Map<Integer, StateTaxVO> partnerProductTaxMap, boolean isManual , PartnerPrimaryData partnerPrimaryData);


    public void setOrderProcessingThread(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual,
                                         boolean skipInventoryCheck);


    public <R> PartnerPrimaryData getPrimaryData(R reuest);

    <T> T commonHandlerForAnyErrorInOrder(PartnerOrderDetail partnerOrderDetail);

    public <R,T> T handlesDuplicateOrder(R request , T response, PartnerPrimaryData partnerPrimaryData);

    public <R,T> T handlesFailedOrder(R request , T response, PartnerOrderDetail partnerOrderDetail);


    public <R,T> T handlesRejectedOrder(R request  , PartnerOrderDetail partnerOrderDetail, PartnerOrderStates rejected);
    public <R,T> T handlesSuccessFullOrder(R request  , PartnerOrderDetail partnerOrderDetail);
    public <R,T> T handlesExceptionInOrder(R request , T response , PartnerOrderDetail partnerOrderDetail, Exception e, PartnerPrimaryData partnerPrimaryData);

    public <T> T getResponseObject();

    public  <R> PartnerCustomerData getPartnerCustomerData(R request);

    public Order convertOrder(PartnerOrderDetail partnerOrderDetail , boolean isManual,
                              PartnerPrimaryData partnerPrimaryData);

    void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck);
    void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual);
    void confirmMagicpinOrder(MagicpinOrderUpdate magicpinOrderUpdate, boolean isManual);
}
