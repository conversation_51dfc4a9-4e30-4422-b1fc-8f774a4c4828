package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitRefreshInventoryEvent;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum EventType {
    Menu(event("UPDATE_MENU", "UPDATE_UNIT_MENU", "UPDATE_ALL_UNIT_MENU", "SCHEDULED_MENU_PUSH"), UnitMenuAddVO.class),
    Stock(event("UNIT_PRODUCT_STOCK"), UnitProductsStockEvent.class),
    Inventory(event("INVENTORY_UPDATE"), UnitRefreshInventoryEvent.class),
    TakeAwayStatus(event("TAKEAWAY_STATUS_UPDATE"), UnitPartnerStatusVO.class);

    private final List<String> events;

    private final Class<?> associatedClass;  // The associated class for the enum constant

    private static List<String> event(String... names) {
        return Arrays.asList(names);
    }
}

