package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
//
//@Entity
//@Table(name = "PARTNER_ORDER_RECONCILIATION_DATA")
public class PartnerOrderReconciliationData {

    private int id;
    private String partnerOrderId;
    private String kettleOrderId;
    private String partnerName;
    private Integer kettlepartnerId;
    private String unitId;
    private BigDecimal partnerGrossAmount;
    private BigDecimal kettleTotalAmount;
    private BigDecimal amountDifference;
    private Date orderTime;
    private Integer brandId;
    private List<PartnerOrderReconciliationErrorData> partnerOrderError;

//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @Column(name = "PARTNER_ORDER_RECONCILIATION_ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

//    @Column(name = "PARTNER_ORDER_ID")
    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(String partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

//    @Column(name = "KETTLE_ORDER_ID")
    public String getKettleOrderId() {
        return kettleOrderId;
    }

    public void setKettleOrderId(String kettleOrderId) {
        this.kettleOrderId = kettleOrderId;
    }

//    @Column(name = "PARTNER_NAME")
    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

//    @Column(name = "KETTLE_PARTNER_ID")
    public Integer getPartnerId() {
        return kettlepartnerId;
    }

    public void setPartnerId(Integer kettlepartnerId) {
        this.kettlepartnerId = kettlepartnerId;
    }

//    @Column(name = "UNIT_ID")
    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

//    @Column(name = "PARTNER_GROSS_AMOUNT")
    public BigDecimal getPartnerGrossAmount() {
        return partnerGrossAmount;
    }

    public void setPartnerGrossAmount(BigDecimal partnerGrossAmount) {
        this.partnerGrossAmount = partnerGrossAmount;
    }

//    @Column(name = "KETTLE_TOTAL_AMOUNT")
    public BigDecimal getKettleTotalAmount() {
        return kettleTotalAmount;
    }

    public void setKettleTotalAmount(BigDecimal kettleTotalAmount) {
        this.kettleTotalAmount = kettleTotalAmount;
    }

//    @Column(name = "AMOUNT_DIFFERENCE")
    public BigDecimal getAmountDifference() {
        return amountDifference;
    }

    public void setAmountDifference(BigDecimal amountDifference) {
        this.amountDifference = amountDifference;
    }

//    @Column(name = "ORDER_TIME")
    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

//    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

//    @OneToMany(fetch = FetchType.LAZY, mappedBy = "partnerOrderReconciliations")
    public List<PartnerOrderReconciliationErrorData> getPartnerOrderError() {
        return partnerOrderError;
    }

    public void setPartnerOrderError(List<PartnerOrderReconciliationErrorData> partnerOrderError) {
        this.partnerOrderError = partnerOrderError;
    }



}
