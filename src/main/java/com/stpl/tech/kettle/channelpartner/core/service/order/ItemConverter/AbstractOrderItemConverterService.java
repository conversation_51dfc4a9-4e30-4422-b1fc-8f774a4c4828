package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter;

import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.converters.AbstractConverters;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter.PartnerItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.BaseOrderItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.MenuItemBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.PaidAddonItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.decorator.RecomItemDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.discount.DiscountStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.strategy.tax.TaxStrategy;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderItemDecoratorFactory;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderItemFactory;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.PartnerOrderConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemAddonData;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.OptionData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.mail.Part;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;



@Log4j2
public abstract class AbstractOrderItemConverterService {

    private static final String REGULAR_SUGAR = "Regular Sugar";
    private PartnerItemConverterService partnerItemConverterService;

    private DiscountStrategy discountStrategy;

    private TaxStrategy taxStrategy;

    private MasterDataCache masterDataCache;


    private EnvironmentProperties environmentProperties;

    private PartnerOrderConverterDependency partnerOrderConverterDependency;


    AbstractOrderItemConverterService(PartnerItemConverterService partnerItemConverterService, DiscountStrategy discountStrategy,
                                      MasterDataCache masterDataCache, TaxStrategy taxStrategy,
                                      EnvironmentProperties environmentProperties, PartnerOrderConverterDependency partnerOrderConverterDependency) {
        this.partnerItemConverterService = partnerItemConverterService;
        this.discountStrategy = discountStrategy;
        this.masterDataCache = masterDataCache;
        this.taxStrategy = taxStrategy;
        this.environmentProperties = environmentProperties;
        this.partnerOrderConverterDependency =partnerOrderConverterDependency;
    }


    public <R, I> void addItemsToOrder(Order order, R request, PartnerOrderDetail partnerOrderDetail,
                                       Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                       int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) {
        for (Object item : partnerItemConverterService.getItems(request)) {
            final Pair<PartnerItemData, List<OrderItem>> orderList = OrderItemDecoratorFactory.getInstance(partnerOrderDetail.getPartnerName(), partnerOrderConverterDependency).processItem(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap
                    , cafeProducts);
            order.getOrders().addAll(orderList.getValue());
        }
    }


    public <I, R> Pair<PartnerItemData, List<OrderItem>> createMenuItem(Order order, R request, I item,
                                                                        PartnerOrderDetail partnerOrderDetail,
                                                                        Map<String, TaxDataVO> taxMap,
                                                                        Map<Integer, Product> priceProfileProducts,
                                                                        int pricingUnitId,
                                                                        Map<Integer, Map<String, BigDecimal>> pricingMap,
                                                                        Map<Integer, Product> cafeProducts) {
        PartnerItemData partnerItemData = partnerItemConverterService.getPartnerItemData(item);
        checkIsComboProduct(cafeProducts, partnerItemData);
        Product product = priceProfileProducts.get(partnerItemData.getProductId());
        List<OrderItem> orderItemList = new ArrayList<>();
        log.info("Adding Menu item  {} To order ", product.getName());
        addMenuItem(order, request, item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap,
                cafeProducts, product, partnerItemData, false, null, orderItemList);
        return Pair.of(partnerItemData,orderItemList);
    }

    private void checkIsComboProduct(Map<Integer, Product> cafeProducts, PartnerItemData partnerItemData) {
        for(Map.Entry<Integer , Product> cafeProductEntry : cafeProducts.entrySet()){
            if(Objects.equals(partnerItemData.getProductId() , cafeProductEntry.getKey())){
                partnerItemData.setComboProduct(cafeProductEntry.getValue().getType() == ChannelPartnerServiceConstants.COMBO_CATEGORY_ID);
            }
        }
    }

    public  <I, R> void addMenuItem(Order order, R request, I item, PartnerOrderDetail partnerOrderDetail,
                                    Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                    int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap,
                                    Map<Integer, Product> cafeProducts, Product product, PartnerItemData partnerItemData,
                                    boolean isComboConstituentItem, String comboConstituentDimesnion,
                                    List<OrderItem> orderItemList) {
        List<Integer> validProductIds = new ArrayList<>();
        validateItemsMapping(item, partnerOrderDetail, taxMap, priceProfileProducts, pricingUnitId, pricingMap, cafeProducts,
                new ArrayList<>(Arrays.asList(partnerItemData.getProductId())), validProductIds);
        if (!CollectionUtils.isEmpty(validProductIds)) {
            OrderItem orderItem = OrderItemFactory.createOrderItem(partnerOrderDetail.getPartnerName(), product, item);
            if(Boolean.TRUE.equals(isComboConstituentItem)){
                orderItem.setQuantity(partnerItemData.getQuantity());
            }
            ProductPrice productPrice = getProductPriceMapping(item, partnerItemData, product, isComboConstituentItem, comboConstituentDimesnion);
            setItemPrice(orderItem, item, productPrice, product, partnerItemData);
            OrderItemComposition orderItemComposition = new OrderItemComposition();
            setRecipeAddons(false, item, partnerItemData, orderItemComposition, productPrice.getRecipe());
            setRecipeIngredientVariants(false, item, partnerItemData, orderItemComposition, productPrice.getRecipe());
            setRecipeIngredientProducts(false, item, partnerItemData, orderItemComposition, productPrice.getRecipe());
            // Setting default ingredient variants for missing ones
            setMissingRecipeIngredientVariants(item, partnerItemData, orderItemComposition, productPrice.getRecipe());
            // Setting default ingredient products for missing ones
            setMissingRecipeIngredientProducts(item, partnerItemData, orderItemComposition, productPrice.getRecipe());
            if (environmentProperties.getEditOrderCompositionBasisRemark() && product.getType() == 5) {
                editSugarRecipeIngredientVarientsBasisRemark(item,partnerItemData ,orderItemComposition,
                        productPrice.getRecipe(), order.getOrderRemark());
            }
            if(productPrice.getRecipe().getIngredient().getCompositeProduct() != null){
                addComboConstituentItems(order,request,item,partnerOrderDetail,taxMap,priceProfileProducts,pricingUnitId
                        ,pricingMap,cafeProducts,product,partnerItemData,productPrice
                ,orderItemList,orderItemComposition);
            }
            orderItem.setComposition(orderItemComposition);
            orderItemList.add(orderItem);

        }


    }

    private <I,R>void addComboConstituentItems(Order order ,R request ,I item ,PartnerOrderDetail partnerOrderDetail,
                                               Map<String, TaxDataVO> taxMap,
                                               Map<Integer, Product> priceProfileProducts,
                                             Integer priceProfileUnitId,
                                             Map<Integer, Map<String, BigDecimal>> pricingMap,
                                             Map<Integer, Product> cafeProducts, Product product, PartnerItemData partnerItemData,
                                               ProductPrice productPrice,
                                             List<OrderItem> orderItemList, OrderItemComposition parentItemComposition ){
        for (CompositeIngredientData compositeIngredientData : productPrice.getRecipe().getIngredient().getCompositeProduct()
                .getDetails()) {
            IngredientProductDetail defaultMenuProduct = null;
            for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                if(ingredientProductDetail.isDefaultSetting()){
                    defaultMenuProduct = ingredientProductDetail;
                }
            }
            if (defaultMenuProduct == null) {
                defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
            }
            String comboConstituentDimension = defaultMenuProduct.getDimension().getCode();
            PartnerItemData constituentPartnerItemData = partnerItemConverterService.getConstituentProductPartnerItemData(item,
                    defaultMenuProduct.getProduct().getProductId(),defaultMenuProduct.getQuantity().intValue(),defaultMenuProduct.getDimension().getName(),
                    defaultMenuProduct.getProduct().getName());
            Product comboConstituent = priceProfileProducts.get(constituentPartnerItemData.getProductId());
            addMenuItem(order,request,item,partnerOrderDetail,taxMap,priceProfileProducts,
                    priceProfileUnitId,pricingMap,cafeProducts,comboConstituent,constituentPartnerItemData,true,
                    comboConstituentDimension,parentItemComposition.getMenuProducts());
        }

    }

    public  <R> void  addOrderLevelChargeItemToOrder(Order order, R request, PartnerOrderDetail partnerOrderDetail, boolean isInterState,
                                                     Map<String, TaxDataVO> taxMap, Map<Integer, Product> products,
                                                     int pricingUnitId, List<OrderItem> orderItemList){
        Product product = products.get(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID);
        PartnerItemData packagingItemMetaData = partnerItemConverterService.getOrderLevelPackagingCharge(request);
        BigDecimal packagingCharge = packagingItemMetaData.getUnitPrice();
        if (packagingCharge.compareTo(BigDecimal.ZERO) > 0) {
              OrderItem  packagingItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                        masterDataCache, isInterState, taxMap, false, BigDecimal.ZERO, "",
                        product.getPrices().get(0).getDimension(), pricingUnitId, BigDecimal.ZERO,
                        packagingItemMetaData.getTaxDeductedByPartner(), packagingItemMetaData.getTaxPayingEntity());
                packagingItem.setPrice(packagingCharge);
                packagingItem.setAmount(packagingCharge);
                packagingItem.setTotalAmount(packagingCharge);

            packagingItem.getTaxes().clear();
            BigDecimal totalItemTax = BigDecimal.ZERO;
            totalItemTax = taxStrategy.getOrderLevelPackagingTax(packagingItem,request);
            packagingItem.setTax(totalItemTax);
            orderItemList.add(packagingItem);

        }
    }


    private <I> void setItemPrice(OrderItem orderItem, I item, ProductPrice productPrice, Product product,
                                  PartnerItemData partnerItemData) {
        orderItem.setPrice(partnerItemConverterService.getItemPrice(item, partnerItemData.getItemId()));
        orderItem.setOriginalPrice(productPrice.getPrice());
        orderItem.setTotalAmount(ChannelPartnerUtils.multiply(orderItem.getPrice(), BigDecimal.valueOf(orderItem.getQuantity())));
        orderItem.setAmount(orderItem.getTotalAmount());
        orderItem.setDimension(productPrice.getDimension());
        RecipeDetail recipeDetail = productPrice.getRecipe();
        orderItem.setRecipeId(recipeDetail.getRecipeId());


    }

    private <I> void setRecipeAddons(boolean superCombo, I item, PartnerItemData partnerItemData,
                                     OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        if (!superCombo) {
            for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getAddonProducts())
                for (IngredientProductDetail ingredientProductDetail : recipeDetail.getAddons()) {
                    if ((partnerItemAddonData.getProductId() > 0 &&
                            ingredientProductDetail.getProduct().getProductId() == partnerItemAddonData.getProductId())) {
                        orderItemComposition.getAddons().add(ingredientProductDetail);
                    }
                }
        }
    }

    private <I> void setRecipeIngredientVariants(boolean superCombo, I item, PartnerItemData partnerItemData,
                                                 OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {

        //Filter based On Modifer Item Name
        if (!superCombo) {
            for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getAddonProducts()) {
                recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.getAlias()
                                .equalsIgnoreCase(partnerItemAddonData.getName().trim())) {
                            orderItemComposition.getVariants().add(ingredientVariantDetail);
                        }
                    }
                });
            }
        }
    }

    private <I> void setRecipeIngredientProducts(boolean superCombo, I item, PartnerItemData partnerItemData,
                                                 OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        if (!superCombo) {
            for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getAddonProducts()) {
                recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        if (ingredientProductDetail.getProduct().getName()
                                .equalsIgnoreCase(partnerItemAddonData.getName().trim())) {
                            orderItemComposition.getProducts().add(ingredientProductDetail);
                        }
                    }
                });
            }
        }
    }

    private <I> void setMissingRecipeIngredientVariants(I item, PartnerItemData partnerItemData,
                                                        OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
            boolean found = false;
            for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getAddonProducts()) {
                    if (ingredientVariantDetail.getAlias().equalsIgnoreCase(partnerItemAddonData.getName().trim())) {
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                    if (ingredientVariantDetail.isDefaultSetting()) {
                        orderItemComposition.getVariants().add(ingredientVariantDetail);
                    }
                });
            }
        });
    }


    private <I> void setMissingRecipeIngredientProducts(I item, PartnerItemData partnerItemData,
                                                        OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        try {
            recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                if (Objects.nonNull(ingredientProduct) && ingredientProduct.getDetails().isEmpty() &&
                        ingredientProduct.getDetails().size() > 0) {
                    boolean found = false;
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        for (PartnerItemAddonData partnerItemAddonData : partnerItemData.getAddonProducts()) {
                            if (ingredientProductDetail.getProduct().getName()
                                    .equalsIgnoreCase(partnerItemAddonData.getName().trim())) {
                                found = true;
                                break;
                            }
                        }
                    }
                    if (!found) {
                        ingredientProduct.getDetails().forEach(ingredientProductDetail -> {
                            if (ingredientProductDetail.isDefaultSetting()) {
                                orderItemComposition.getProducts().add(ingredientProductDetail);
                            }
                        });
                    }
                }
            });
        } catch (Exception e) {
            log.error("RecipeDetail does not contain any products :{}", e.toString());
        }
        ;
    }

    private <I> void editSugarRecipeIngredientVarientsBasisRemark(I item, PartnerItemData partnerItemData,
                                                                  OrderItemComposition orderItemComposition,
                                                                  RecipeDetail recipeDetail, String orderInstructions) {
        Map<String, String> orderItemRemarkMap = ChannelPartnerUtils.getOrderItemRemarkMap();
        AtomicReference<String> key = new AtomicReference<>("");
        try {
            recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                boolean editApplicable = false;
                if (!orderItemRemarkMap.isEmpty() && orderInstructions != null && orderInstructions.length() > 0) {
                    for (Map.Entry<String, String> entry : orderItemRemarkMap.entrySet()) {
                        String instructions = orderInstructions.trim().toLowerCase();
                        String matcher = entry.getKey().trim().toLowerCase();
                        if (instructions.contains(matcher)) {
                            editApplicable = true;
                            key.set(orderItemRemarkMap.get(matcher));
                            break;
                        }
                    }
                }
                if (editApplicable) {
                    ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(key.get())) {
                            replaceExistingSugarVariant(ingredientVariantDetail, orderItemComposition, key);
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("Exception while editing order item composition for sugar", e);
        }

    }

    private static void replaceExistingSugarVariant(IngredientVariantDetail ingredientVariantDetail,
                                                    OrderItemComposition orderItemComposition, AtomicReference<String> key) {
        if (Objects.nonNull(orderItemComposition) && Objects.nonNull(orderItemComposition.getVariants()) && !orderItemComposition.getVariants().isEmpty()) {
            for (int i = 0; i < orderItemComposition.getVariants().size(); i++) {
                if (orderItemComposition.getVariants().get(i).getAlias().equalsIgnoreCase(REGULAR_SUGAR)) {
                    orderItemComposition.getVariants().set(i, ingredientVariantDetail);
                }
            }
        }
    }


    public  <I> ProductPrice getProductPriceMapping(I item, PartnerItemData partnerItemData, Product product, boolean isComboConstituentItem,
                                                    String comboConstituentDimension) {
        if (!isComboConstituentItem) {
            return getMenuItemPriceMapping(item, partnerItemData, product);
        } else {
            return getComboConstituentItemPriceMapping(item, partnerItemData, product, comboConstituentDimension);
        }
    }

    private <I> ProductPrice getMenuItemPriceMapping(I item, PartnerItemData partnerItemData, Product product) {
        ProductPrice productPrice = null;
        //for products with only single dimension like food category items
        if (product.getPrices().size() == 1) {
            productPrice = product.getPrices().get(0);
        } else if (product.getPrices().size() > 1) {
            //for products with multiple dimensions where size property is present
            String dimensionCode = partnerItemData.getDimensionCode();
            List<ProductPrice> productPrices = product.getPrices().stream().filter(productPrice1 ->
                    productPrice1.getDimension().equalsIgnoreCase(dimensionCode)).collect(Collectors.toList());
            if (!productPrices.isEmpty()) {
                productPrice = productPrices.get(0);
            }
        }
        if (productPrice == null) {
            BigDecimal price = BigDecimal.ZERO;
            BigDecimal unitCost = partnerItemConverterService.getItemPrice(item, partnerItemData.getItemId());
            if (unitCost.compareTo(BigDecimal.ZERO) > 0) {
                price = unitCost;
            }
            TreeMap<BigDecimal, ProductPrice> priceDifference = new TreeMap<>();
            for (ProductPrice pPrice : product.getPrices()) {
                priceDifference.put(ChannelPartnerUtils.subtract(price, pPrice.getPrice()).abs(), pPrice);
            }
            productPrice = priceDifference.get(priceDifference.firstKey());
        }
        return productPrice;
    }

    private <I> ProductPrice getComboConstituentItemPriceMapping(I item, PartnerItemData partnerItemData, Product product,
                                                                 String comboConstituentDimension) {
        ProductPrice productPrice = null;
        for (ProductPrice pPrice : product.getPrices()) {
            if (pPrice.getDimension().equalsIgnoreCase(comboConstituentDimension)) {
                productPrice = pPrice;
            }
        }
        if (productPrice == null) {
            productPrice = product.getPrices().get(0);
        }
        return productPrice;
    }




    private <I> Boolean validateItemsMapping(I item, PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, Map<Integer, Product> priceProfileProducts,
                                             int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap,
                                             Map<Integer, Product> cafeProducts, List<Integer> productIds, List<Integer> foundProducts) {
        productIds.forEach(productId -> {
            if (priceProfileProducts.containsKey(productId) && cafeProducts.containsKey(productId)) {
                foundProducts.add(productId);
            } else {
                addProductNotFoundError(partnerOrderDetail, productId);
            }
        });

        return true;

    }


    private void addProductNotFoundError(PartnerOrderDetail partnerOrderDetail, Integer productId) {
        PartnerOrderError partnerOrderError = new PartnerOrderError();
        partnerOrderError.setErrorCode(PartnerOrderErrorCode.PRODUCT_MISMATCH);
        String errorDescription = "Product " + masterDataCache.getProduct(productId).getName() + " with id " + productId + " not found.";
        partnerOrderError.setErrorDescription(errorDescription);
        partnerOrderDetail.getOrderErrors().add(partnerOrderError);
    }


}
