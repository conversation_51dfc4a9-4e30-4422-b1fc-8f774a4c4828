package com.stpl.tech.kettle.channelpartner.core.queue.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.amazonaws.util.Base64;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventPublisherLayerDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerBuilderFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageFormatException;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UnitProductStockListener implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(UnitProductStockListener.class);

    private MessageProducer errorQueue;

    private RedisPublisher redisPublisher;

    private Map<Integer, PartnerDetail> partnerMap;
    private PartnerBaseDecorator partnerBaseDecorator;

    private PartnerMenuConverterDependency partnerMenuConverterDependency ;

    public UnitProductStockListener(MessageProducer errorQueue, RedisPublisher publisher, Map<Integer, PartnerDetail> partnerMap,PartnerMenuConverterDependency partnerMenuConverterDependency) {
        this.errorQueue = errorQueue;
        this.redisPublisher = publisher;
        this.partnerMap = partnerMap;
        this.partnerMenuConverterDependency = partnerMenuConverterDependency;
    }

    @Override
    public void onMessage(Message message) {
        try {
            LOG.info("On Message " + message.getJMSMessageID());
            if (message instanceof SQSObjectMessage) {
                SQSObjectMessage object = (SQSObjectMessage) message;
                if (object.getObject() instanceof UnitProductsStockEvent) {
                    //message.acknowledge();
                    UnitProductsStockEvent event = (UnitProductsStockEvent)object.getObject();
                    LOG.info("STOCK EVENT RECEIVED OBJECT::::", new Gson().toJson(event));
                    if (processMessage(event)) {
                        message.acknowledge();
                    }
                }
            }
            if (message instanceof SQSTextMessage) {
                SQSTextMessage object = (SQSTextMessage) message;
                if (object.getText() != null) {
                    //message.acknowledge();
                    String response = object.getText();
                    UnitProductsStockEvent event = deserialize(response);
                    LOG.info("STOCK EVENT RECEIVED TEXT::::"+ new Gson().toJson(event));
                    if (processMessage(event)) {
                        message.acknowledge();
                    }
                }
            }
        } catch (JMSException e) {
            LOG.error("Error while saving the message", e);
            try {
                LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
                errorQueue.send(message);
            } catch (JMSException e1) {
                LOG.error("Error while saving the message to error queue", e);
            }
        }
    }

    private boolean processMessage(UnitProductsStockEvent event) {
        LOG.info("Got Message " + new Gson().toJson(event));
        PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
        partnerActionEvent.setEventData(event);
        partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
        List<Integer> partnerIds = new ArrayList<>();
        if (event.getPartnerId() != null) {
            partnerActionEvent.getPartnerIds().add(event.getPartnerId());
        }
        if (event.getPartnerId() != null) {
            if (!ChannelPartnerUtils.getNewChannelPartners().contains(event.getPartnerId())) {
                if (partnerMap.containsKey(event.getPartnerId())) {
                    redisPublisher.publish(partnerMap.get(event.getPartnerId()).getPartnerName(), new Gson().toJson(partnerActionEvent));
                } else {
                    LOG.error("Error in stock event listener: Partner id " + event.getPartnerId() + " does not exist.");
                }
            } else {
                partnerIds.add(event.getPartnerId());
            }
        } else {
            partnerMap.values().stream()
                    .filter(partnerDetail -> !ChannelPartnerUtils.getNewChannelPartnerNames().contains(partnerDetail.getPartnerName()))
                    .forEach(partnerDetail -> redisPublisher.publish(partnerDetail.getPartnerName(), new Gson().toJson(partnerActionEvent)));

            partnerIds.addAll(ChannelPartnerUtils.getNewChannelPartners());
        }
        if(!partnerIds.isEmpty()){
            sendUnitProductStockUpdateRequest(partnerIds,event);
        }
        return true;
    }

    private void sendUnitProductStockUpdateRequest(List<Integer> partnerIds, UnitProductsStockEvent event) {
        PartnerMetadataBuilder partnerMetadataBuilder = PartnerBuilderFactory.getInstance(partnerMenuConverterDependency);
        setPartnerBaseDecorator(EventType.Stock,partnerMetadataBuilder);
        PartnerBaseDecorator baseDecorator = new PartnerActionEventPublisherLayerDecorator(partnerMetadataBuilder, this.partnerBaseDecorator);
        try {
            baseDecorator.preProcessData(event,PartnerRequestType.UNIT_PRODUCT_STOCK,partnerIds, PartnerActionEvent.class);
        } catch (Exception e) {
            LOG.error("Exception caught while sending request of :::::{} for event :::::{} ", EventType.Stock.name(), PartnerRequestType.UNIT_PRODUCT_STOCK.getRequestType());
        }
    }

    private static UnitProductsStockEvent deserialize(String data) throws JMSException {
        if (data == null) {
            return null;
        } else {
            UnitProductsStockEvent obj;
            try {
                byte[] b = Base64.decode(data.getBytes());
                ByteArrayInputStream bi = new ByteArrayInputStream(b);
                ObjectInputStream si = new ObjectInputStream(bi);
                obj = (UnitProductsStockEvent) si.readObject();
                return obj;
            } catch (IOException ex) {
                LOG.error("IOException: cannot serialize objectMessage", ex);
                throw convertExceptionToMessageFormatException(ex);
            } catch (Exception ex) {
                LOG.error("IOException: cannot serialize objectMessage", ex);
            }
        }
        return null;
    }

    private static MessageFormatException convertExceptionToMessageFormatException(Exception e) {
        MessageFormatException ex = new MessageFormatException(e.getMessage());
        ex.initCause(e);
        return ex;
    }

    private void setPartnerBaseDecorator(EventType eventType, PartnerMetadataBuilder partnerMetadataBuilder){
        this.partnerBaseDecorator= ServiceFactory.getPartnerPreProcessorLayer(eventType.name() , partnerMetadataBuilder);
    };
}
