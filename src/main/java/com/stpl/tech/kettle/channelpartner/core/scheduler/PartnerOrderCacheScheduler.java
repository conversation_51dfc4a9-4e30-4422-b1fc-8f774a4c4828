package com.stpl.tech.kettle.channelpartner.core.scheduler;

import java.io.IOException;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.atomic.AtomicInteger;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventPublisherLayerDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerBaseDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerBuilderFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitRefreshInventoryEvent;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderV3;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.poi.ss.formula.functions.T;
import org.joda.time.DateTime;
import org.joda.time.LocalTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.queue.model.DelayedPartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import org.springframework.util.CollectionUtils;

@Component
public class PartnerOrderCacheScheduler {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderCacheScheduler.class);

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired


    private MasterDataCache masterDataCache;

    @Autowired
    private WebServiceHelper webServiceHelper;

    private static BlockingQueue<DelayedPartnerActionEvent> delayQueue = new DelayQueue<>();

    private PartnerBaseDecorator partnerBaseDecorator;

    @Autowired
    private PartnerMenuConverterDependency partnerMenuConverterDependency ;

    @Scheduled(cron = "0 0 05 * * *", zone = "GMT+05:30")
    public void clearPastOrdersFromCache() {
        LOG.info("CRON JOB: clearPastOrdersFromCache : STARTED");
        Date date = ChannelPartnerUtils.getDateBeforeOrAfterInSeconds(ChannelPartnerUtils.getCurrentTimestamp(), -12 * 60 * 60);
        Map<String, PartnerOrderCacheDetail> map = new HashMap<>();
        if (channelPartnerDataCache.getPartnerOrderCache() != null && !channelPartnerDataCache.getPartnerOrderCache().isEmpty()) {
            channelPartnerDataCache.getPartnerOrderCache().values().forEach(partnerOrderCacheDetail -> {
                if (partnerOrderCacheDetail.getAddTime().compareTo(date) <= 0) {
                    map.put(partnerOrderCacheDetail.getPartnerOrderId(), partnerOrderCacheDetail);
                    //channelPartnerDataCache.getPartnerOrderCache().remove(partnerOrderCacheDetail.getPartnerOrderId());
                }
            });
        }
        channelPartnerDataCache.getPartnerOrderCache().clear();
        channelPartnerDataCache.getPartnerOrderCache().putAll(map);
        LOG.info("CRON JOB: clearPastOrdersFromCache : FINISHED");
    }

    //@Scheduled(cron = "0 0 7-23 * * *", zone = "GMT+05:30")
    @Scheduled(cron = "0 0/15 6-13 * * *", zone = "GMT+05:30")
    public void refreshStock() {
        LOG.info("CRON JOB: refreshStock : STARTED");
        try {
            //    if (ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            LOG.info("Starting unit inventory refresh hourly cron:::");
            AtomicInteger index = new AtomicInteger(1);

            Set<Integer> unitIds = channelPartnerDataCache.getChaayosUnitTimings().get(getNearestTime(AppUtils.getCurrentTimeIST()));
            if (CollectionUtils.isEmpty(unitIds)) {
                return;
            }
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                if (unitIds.contains(unitChannelPartnerMapping.getUnit().getId())) {
                    runRefreshStock(index, unitChannelPartnerMapping, 1);
                    //runRefreshStock(index, unitChannelPartnerMapping, 6);
                    runRefreshStock(index, unitChannelPartnerMapping, 5);
                }
            });
            //}
        } catch (Exception e) {
            LOG.error("Error in stock refresh hourly cron.", e);
        }
        LOG.info("CRON JOB: refreshStock : FINISHED");
    }

    @Scheduled(cron = "0 0/15 6-12 * * *", zone = "GMT+05:30")
    public void refreshStockForDohful() {
        LOG.info("CRON JOB: refreshStock Dohful : STARTED");
        try {
            AtomicInteger index = new AtomicInteger(1);

            Set<Integer> unitIds = channelPartnerDataCache.getChaayosUnitTimings().get(getNearestTime(AppUtils.getCurrentTimeIST()));
            if (CollectionUtils.isEmpty(unitIds)) {
                return;
            }
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                if (unitIds.contains(unitChannelPartnerMapping.getUnit().getId())) {
                    UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitChannelPartnerMapping.getUnit().getId(),
                            AppConstants.DOHFUL_BRAND_ID, unitChannelPartnerMapping.getChannelPartner().getId());
                    if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                        runRefreshStock(index, unitChannelPartnerMapping, 6);
                    }
                }
            });
        } catch (Exception e) {
            LOG.error("Error in stock refresh hourly cron.", e);
        }
        LOG.info("CRON JOB: refreshStock Dohful: FINISHED");
    }

    public void refreshAllUnitsStock() {
        LOG.info("refreshStock All Units : STARTED");
        try {
            LOG.info("Starting All unit inventory refresh:::");
            AtomicInteger index = new AtomicInteger(1);
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                runRefreshStock(index, unitChannelPartnerMapping, 1);
                runRefreshStock(index, unitChannelPartnerMapping, 6);
            });
        } catch (Exception e) {
            LOG.error("Error in stock refresh All Units Stock", e);
        }
        LOG.info("Refresh Of All Units Stock : FINISHED");
    }

    @Scheduled(cron = "0 0/15 7-13 * * *", zone = "GMT+05:30")
    public void refreshStockForGNT() {
        LOG.info("CRON JOB: refreshStock for GNT : STARTED");
        try {
            //    if (ChannelPartnerUtils.isProd(environmentProperties.getEnvType())) {
            LOG.info("Starting GNT unit inventory refresh hourly cron:::");
            AtomicInteger index = new AtomicInteger(1);
            Set<Integer> unitIds = channelPartnerDataCache.getGntUnitTimings().get(getNearestTime(AppUtils.getCurrentTimeIST()));
            if (CollectionUtils.isEmpty(unitIds)) {
                return;
            }
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                if (unitIds.contains(unitChannelPartnerMapping.getUnit().getId())) {
                    runRefreshStock(index, unitChannelPartnerMapping, 3);
                }
            });
        } catch (Exception e) {
            LOG.error("Error in stock refresh hourly cron.", e);
        }
        LOG.info("CRON JOB: refreshStock for GNT : FINISHED");
    }

    public void refreshAllUnitsStockForGNT() {
        LOG.info("refreshStock All Units for GNT : STARTED");
        try {
            LOG.info("Starting GNT ALl Units unit inventory refresh :::");
            AtomicInteger index = new AtomicInteger(1);
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                runRefreshStock(index, unitChannelPartnerMapping, 3);
            });
        } catch (Exception e) {
            LOG.error("Error in All Units GNT stock refresh ", e);
        }
        LOG.info("All Units refreshStock for GNT : FINISHED");
    }


    private void runRefreshStock(AtomicInteger index, UnitChannelPartnerMapping unitChannelPartnerMapping, Integer brandId) {

        Integer partnerId = unitChannelPartnerMapping.getChannelPartner().getId();
        DelayedPartnerActionEvent partnerActionEvent = new DelayedPartnerActionEvent(
                System.currentTimeMillis() + (index.get() * 2000), partnerId);
        partnerActionEvent.setPartner(true);
        partnerActionEvent.getPartnerIds().add(partnerId);
        partnerActionEvent.setBrandId(brandId);
        UnitRefreshInventoryEvent unitRefreshInventoryEvent = new UnitRefreshInventoryEvent();
        unitRefreshInventoryEvent.setUnitIds(List.of(unitChannelPartnerMapping.getUnit().getId()));
        unitRefreshInventoryEvent.getPartnerIds().add(partnerId);
        unitRefreshInventoryEvent.setBrandId(brandId);
        List<Integer> data = new ArrayList<>();
        data.add(unitChannelPartnerMapping.getUnit().getId());
        if(!ChannelPartnerUtils.getNewChannelPartners().contains(partnerId)){
            partnerActionEvent.setEventData(data);
        }else{
            partnerActionEvent.setEventData(unitRefreshInventoryEvent);
        }
        //todo Ishman - to make Necessary Changes for MagicPin here
//         partnerActionEvent.setBrandId(unitPartnerBrandMappingData.getBrandId());
        partnerActionEvent.setEventType(PartnerActionEventType.INVENTORY_UPDATE);
        delayQueue.add(partnerActionEvent);
        index.getAndIncrement();

    }

    @Scheduled(fixedRate = 1000)
    public void consumeDelayedStockRefreshEvent() {
//        LOG.info("CRON JOB: consumeDelayedStockRefreshEvent : STARTED");
        try {
            if (delayQueue.peek() != null) {
                boolean enablePartnerCallBlocking = Boolean.parseBoolean(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.PARTNER_CALL_BLOCK));
                if ((enablePartnerCallBlocking && webServiceHelper.getRateLimiter().allowRequest()) || !enablePartnerCallBlocking) {
                    DelayedPartnerActionEvent event = delayQueue.poll();
                    if (event != null) {
                        Integer partnerId = event.getPartnerId();
                        if (channelPartnerDataCache.getPartnerCacheById().containsKey(partnerId)) {
                            if(!ChannelPartnerUtils.getNewChannelPartnerNames().contains(channelPartnerDataCache.getPartnerCacheById().get(partnerId).getPartnerName())){
                                List data = (List) event.getEventData();
                                String unitList = new Gson().toJson(data);
                                LOG.info("Publishing unit inventory refresh INVENTORY_UPDATE event: partner id {} unit id: {}", partnerId, unitList);
                                redisPublisher.publish(channelPartnerDataCache.getPartnerCacheById().get(partnerId).getPartnerName(), new Gson().toJson(event));
                            }else{
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                try {
                                    UnitRefreshInventoryEvent unitRefreshInventoryEvent = mapper.readValue(new Gson().toJson(event.getEventData()), UnitRefreshInventoryEvent.class);
                                    LOG.info("Publishing unit inventory refresh INVENTORY_UPDATE event: partner id {} unit id: {}", partnerId, unitRefreshInventoryEvent.getUnitIds());
                                    PartnerMetadataBuilder partnerMetadataBuilder = PartnerBuilderFactory.getInstance(partnerMenuConverterDependency);
                                    setPartnerBaseDecorator(EventType.Inventory,partnerMetadataBuilder);
                                    PartnerBaseDecorator baseDecorator = new PartnerActionEventPublisherLayerDecorator(partnerMetadataBuilder, this.partnerBaseDecorator);
                                    try {
                                        baseDecorator.preProcessData(unitRefreshInventoryEvent, PartnerRequestType.INVENTORY_UPDATE,ChannelPartnerUtils.getNewChannelPartners(), PartnerActionEvent.class);
                                    } catch (Exception e) {
                                        LOG.error("Exception caught while sending request of :::::{} ", EventType.Inventory.name(), e);
                                    }
                                } catch (Exception e) {
                                    LOG.error("error parsing  order request for partner ::::{}", partnerId, e);
                                }
                            }
                        } else {
                            LOG.error("Error publishing INVENTORY_UPDATE event: partner id {} is not valid.", partnerId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error in stock refresh delay queue consumption.", e);
        }
//        LOG.info("CRON JOB: consumeDelayedStockRefreshEvent : FINISHED");
    }

    public static BlockingQueue<DelayedPartnerActionEvent> getDelayQueue() {
        return delayQueue;
    }

    public void refreshStock(int unitId) {
        try {
            LOG.info("Starting unit inventory refresh hourly cron:::");
            AtomicInteger index = new AtomicInteger(1);
            Map<Integer, List<Integer>> unitTimingMappings = new HashMap<>();
            masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                if (unitId == unitChannelPartnerMapping.getUnit().getId()) {
                    runRefreshStock(index, unitChannelPartnerMapping, null);
                }
            });
        } catch (Exception e) {
            LOG.error("Error in stock refresh hourly cron.", e);
        }
    }

    private String getNearestTime(DateTime time) {
        Integer modMinutes = (time.toLocalTime().getMinuteOfHour()) % 15;
        Time times;
        String openingTime;
            String newTime = String.valueOf(time.toLocalTime().minusMinutes(modMinutes));
            times = Time.valueOf(List.of(newTime.split("\\.")).subList(0, 1).get(0));
            openingTime = channelPartnerDataCache.convertToString(times);
        LOG.info("time is {}", openingTime);
        return openingTime;
    }

    private void setPartnerBaseDecorator(EventType eventType, PartnerMetadataBuilder partnerMetadataBuilder){
        this.partnerBaseDecorator= ServiceFactory.getPartnerPreProcessorLayer(eventType.name() , partnerMetadataBuilder);
    };

}