package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
@Data
@AllArgsConstructor
@Builder
@RedisHash("SwiggyOrderOutOfStockData")
public class SwiggyOrderOutOfStockData implements Serializable {
    private static final long serialVersionUID = 3187335552474724190L;

    @Id
    private String partnerOrderId ;

    @Builder.Default
    private List<String> itemIds = new ArrayList<>();
    @Builder.Default

    private List<String> addOnIds=new ArrayList<>();
    @Builder.Default
    private List<String> variantIds=new ArrayList<>();
}
