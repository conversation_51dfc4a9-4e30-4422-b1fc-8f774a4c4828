package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderFallbackStatus;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PartnerOrderService {

    List<PartnerOrderDetail> getPartnerOrder(PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    PartnerOrderDetail getPartnerOrder(String kettleOrderId) throws ChannelPartnerException;

    Map<String, List<PartnerOrderDetail>> getPartnerPendingOrders(PartnerPendingOrderRequest request) throws ChannelPartnerException;

    List<PartnerOrderDetail> getOrdersByTime(Date startTime, Date endTime);

    List<PartnerOrderDetail> getPartnerOrder(String partnerId, String orderStatus) throws ChannelPartnerException;

    boolean callSwiggyPartnerSupport(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException;

    boolean markOrderResolved(String orderId,String employeeId) throws ChannelPartnerException;

    boolean manualProcess(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException;

    boolean markCancelled(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException;

    boolean manualProcessWithSkipInventory(String orderId , String employeeId) throws ChannelPartnerException, URISyntaxException;

    void sendOrderNotification(PartnerOrderDetail order);

    void sendOrderStatusUpdate(PartnerOrderStateUpdate request);

    Integer getSwiggyRiderTimeOfArrival(String orderId) throws ChannelPartnerException;

    void sendOrderNotPunchedNotification(PartnerOrderDetail detail);

    void sendOrderNotPunchedNotificationToKnock(PartnerOrderDetail detail);

    void logStockRefreshEvent(Integer unitId , Integer partnerId , Integer brandId,String logType, String status);

    public boolean addKettleOrder(String kettleOrderId,String partnerOrderId , Integer unitId,String employeeId);

    public  List<PartnerItemData> getMagicPinOrderItems(String orderId);

    boolean fallbackProcessedBy(String orderId, String fallbackProcessedBy) throws ChannelPartnerException;

    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", propagation = Propagation.REQUIRED)
    boolean partnerResponse(PartnerOrderResponse response , String employeeId);

    PartnerOrderFallbackStatus addFallbackOrderStatusLog(PartnerOrderDetail partnerOrderDetail, PartnerOrderFallbackStatus statusLog);

    void addFallbackOrderLog(
            PartnerOrderDetail partnerOrderDetail,
            String action,
            String comment,
            PartnerOrderFallbackStatus statusLog,
            String actionCategory,
            String employeeId
    );


    void logFallbackData(PartnerOrderDetail partnerOrderDetail, String action, String comment, String actionCategory,
                         PartnerOrderFallbackStatus statusLogPrev, String employeeId);
}
