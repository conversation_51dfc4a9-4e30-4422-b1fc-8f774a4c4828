package com.stpl.tech.kettle.channelpartner.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockReqResponseData<T,R> implements Serializable {
    private static final long serialVersionUID = 1952892066856942235L;
    private T stockReqToPartner ;
    private R stockResponse ;
    private boolean status ;
    private String res ;
    private String errorBody ;
}
