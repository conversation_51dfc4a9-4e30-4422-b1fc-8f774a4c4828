package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.CommonMetadataValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAdaptee;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerRequestTarget;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Log4j2
public class StockEventFactoryService extends EventAbstractFactory<UnitProductsStockEvent, StockReqMetadata> {

    private static PartnerAbstractFactory partnerAbstractFactory;
    private  PartnerMetadataBuilder partnerMetadataBuilder;

    public StockEventFactoryService(PartnerMetadataBuilder partnerMetadataBuilder) {
        super(partnerMetadataBuilder);
        this.partnerMetadataBuilder=partnerMetadataBuilder;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<StockReqMetadata> prepareMetadata(UnitProductsStockEvent event, PartnerActionEventType eventType, String partnerName) throws ChannelPartnerException {
        if (Objects.nonNull(event)  && Objects.nonNull(event.getUnitId()) && masterDataCache.getUnits().containsKey(event.getUnitId())) {
            partnerAbstractFactory= ServiceFactory.getPartnerAbstractFactory(partnerName, partnerMetadataBuilder);
            boolean mappingValid = commonMetadataValidationService.validateUnitPartnerMapping(event.getUnitId(), partnerName);
            if (mappingValid) {
                return getStockReqMetadataList(event, partnerName);
            }
        } else {
            commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(getMessage(event,"Incorrect Event Data" ), SlackNotification.PARTNER_INTEGRATION, false, partnerName, ApplicationName.KETTLE_SERVICE.name());
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    private List<StockReqMetadata> getStockReqMetadataList(UnitProductsStockEvent event, String partnerName) {
        List<StockReqMetadata> stockReqMetadataList = new ArrayList<>();
        Integer partnerId = channelPartnerDataCache.getPartnerCache().get(partnerName).getKettlePartnerId();
        if (masterDataCache.getUnit(event.getUnitId()) != null &&
                commonMetadataValidationService.validateUnitChannelPartnerMappingFromCache(partnerId, event.getUnitId()) && masterDataCache.getUnit(event.getUnitId()).isLive() &&
                UnitStatus.ACTIVE.equals(masterDataCache.getUnit(event.getUnitId()).getStatus())) {
            List<Integer> brandId = new ArrayList<>();
            if (Objects.nonNull(event.getBrandId())) {
                brandId = Arrays.asList(event.getBrandId());
            } else {
                brandId = Arrays.asList(AppConstants.CHAAYOS_BRAND_ID, AppConstants.GNT_BRAND_ID);
            }
            for (Integer brand : brandId) {
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(event.getUnitId(), brand, partnerId);
                if (masterDataCache.getUnitPartnerBrandMappingMetaData().containsKey(key)) {
                     stockReqMetadataList.add(processUnitPartnerBrandMappingEvents(event, key));
                }
            }
        } else {
            commonPartnerEventNotificationService.publishPartnerEventNotificationToChannel(getMessage(event,"UnitProductsStockEvent unit partner mapping not found:::::"), SlackNotification.PARTNER_INTEGRATION, false, partnerName, ApplicationName.KETTLE_SERVICE.name() );
        }
        return stockReqMetadataList;
    }

    private StockReqMetadata processUnitPartnerBrandMappingEvents(UnitProductsStockEvent event, UnitPartnerBrandKey key) {
        StockReqMetadata stockReqMetadata = StockReqMetadata.builder().unitProductsStockEvent(event).unitPartnerBrandKey(key).stockStatus(event.getStatus()).build();
        PartnerAdaptee<StockReqMetadata, StockReqMetadata> partnerAdaptee = new PartnerStockAdapteeImpl(this.partnerMetadataBuilder.webServiceHelper, this.partnerMetadataBuilder.environmentProperties, this.masterDataCache, this.channelPartnerDataCache ,this.partnerMetadataBuilder.orderValidationService);
        PartnerRequestTarget<StockReqMetadata, StockReqMetadata> partnerRequestTarget = new PartnerStockRequestAdapter(partnerAdaptee, partnerAbstractFactory);
        try {
            return partnerRequestTarget.convertAndSendResponse(stockReqMetadata) ;
        } catch (ChannelPartnerException e) {
            throw new RuntimeException(e);
        }
    }

    private String getMessage(UnitProductsStockEvent unitProductsStockEvent, String title){
        String eventJson = new Gson().toJson(unitProductsStockEvent);
        return ChannelPartnerUtils.getMessage("Incorrect Event Data", eventJson);
    }
}
