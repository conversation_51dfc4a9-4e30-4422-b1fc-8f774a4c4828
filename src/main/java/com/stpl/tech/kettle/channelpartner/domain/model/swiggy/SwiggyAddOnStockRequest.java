package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "restaurantId", "externalAddonId", "inStock" })
public class SwiggyAddOnStockRequest {

	@JsonProperty("restaurantId")
	private String restaurantId;
	@JsonProperty("externalAddonId")
	private String externalAddonId;
	@JsonProperty("inStock")
	private boolean inStock;

	public String getRestaurantId() {
		return restaurantId;
	}

	public void setRestaurantId(String restaurantId) {
		this.restaurantId = restaurantId;
	}

	public String getExternalAddonId() {
		return externalAddonId;
	}

	public void setExternalAddonId(String externalAddonId) {
		this.externalAddonId = externalAddonId;
	}

	public boolean isInStock() {
		return inStock;
	}

	public void setInStock(boolean inStock) {
		this.inStock = inStock;
	}

	@Override
	public String toString() {
		return "SwiggyAddOnVariantStockRequest [restaurantId=" + restaurantId + ", externalAddonId=" + externalAddonId
				+ ", inStock=" + inStock + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(restaurantId).append(externalAddonId).append(inStock).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof SwiggyStockRequest) == false) {
			return false;
		}
		SwiggyAddOnStockRequest rhs = ((SwiggyAddOnStockRequest) other);
		return new EqualsBuilder().append(externalAddonId, rhs.externalAddonId).append(restaurantId, rhs.restaurantId)
				.append(inStock, rhs.inStock).isEquals();
	}

}
