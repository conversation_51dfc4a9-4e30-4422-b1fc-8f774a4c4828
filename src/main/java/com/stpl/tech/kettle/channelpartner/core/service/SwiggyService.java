package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuTrackResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyPartnerSupportRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.ReasonMetaData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.RejectionMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderReject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.ConnectCustomerObject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyCustomerConnect;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyRejectOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyHolidaySlotData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerStockUpdateSchedule;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;

public interface SwiggyService {

    boolean callSwiggyPartnerSupport(PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    void updateSwiggyStock(UnitProductsStockEvent event) throws ChannelPartnerException;

    void refreshUnitInventory(List<Integer> unitIds);

    void refreshUnitInventory(List<Integer> unitIds,Integer brandId);

    boolean setUnitAvailability(List<Integer> unitIds, Boolean status, Date startDate, Date endDate, Integer brandId) throws ChannelPartnerException;

    SwiggyRiderStatusResponse updateSwiggyOrderDeliveryStatus(SwiggyRiderStatusRequest request);

    SwiggyCancelResponse addSwiggyHolidaySlot(SwiggyHolidaySlotData request);

    SwiggyCancelResponse cancelSwiggyOrder(SwiggyCancelRequest request, boolean isManual,String employeeId) throws ChannelPartnerException;

    SwiggyOrderResponse addSwiggyOrder(SwiggyOrderRequest request, boolean isManual) throws URISyntaxException, ChannelPartnerException;

    void manualProcessOrder(PartnerOrderDetail partnerOrderDetail, boolean skipInventoryCheck) throws ChannelPartnerException;

    void confirmSwiggyOrder(SwiggyPartnerSupportRequest request, boolean isManual);

    Integer getRiderTimeOfArrival(String orderId, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    void sendOrderStatusUpdate(PartnerOrderStateUpdate request, PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    void placeOrder(PartnerOrderDetail partnerOrderDetail, Order order, boolean isManual, boolean skipInventoryCheck);

    void notifyOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual);

    void updateSwiggyMenu(Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer kettlePartnerId, Integer EmployeeId) throws ChannelPartnerException;

    UnitMenuAddVO createSwiggyMenuRequestObj(Integer partnerId, Integer unitId, PartnerActionEventType eventType, Integer brandId, Integer employeeId) throws ChannelPartnerException;

	SwiggyOrderResponse addSwiggyOrderInDev(Object request);

	void pushMenuToUnits(List<Integer> unitIdsForMenu, Integer brandId, Integer employeeId, Integer kettlePartnerId,
			MenuType menuType);

    void scheduledMenuPush(List<Integer> unitIdsForMenu, MenuType menuType ,Integer brandId) throws ChannelPartnerException, IOException;

    MenuTrackResponse trackSwiggyMenuPushStatus(Integer unitId, Integer partnerId, Integer brandId);

    boolean updateCafeDeliveryTimeSwiggy(CafeTimingChangeRequest cafeTimingChangeRequest);

    SwiggyOrderReject orderReject(SwiggyRejectOrderDetail swiggyRejectOrderDetail, RejectionMetadata rejectionMetadata,ReasonMetaData reasonMetaData);

    String getRestaurantId(String orderId);

    SwiggyCustomerConnect connectWithCustomer(ConnectCustomerObject connectCustomerObject);

    public Boolean markOOSItemsStockIn(String partnerOrderid, UnitPartnerBrandKey key);

    public void runOOSItemsStockInTask(String partnerOrderId , UnitPartnerBrandKey key);

    public void savePartnerOrderDataMysql(PartnerOrderDetail partnerOrderDetail , Integer kettleOrderId);

    void updateSwiggyStockForScheduledStockUpdate(UnitProductsStockEvent event , PartnerStockUpdateSchedule partnerStockUpdateSchedule);
}
