package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

public enum SwiggyOrderRejectionCodes {
    RESTAURANT_CLOSED(101, "Restaurant Closed"),
    SEMI_ITEMS_UNAVAILABLE(102, "Semi Items Unavailable"),
    ITEMS_UNAVAILABLE(103, "Items Unavailable"),
    SWIGGY_RIDER_ISSUE(104, "Swiggy Rider Issue"),
    OUTLET_ID_NOT_EXIST(105, "Outlet_id does not exist"),
    ORDER_ID_ALREADY_EXIST(106, "Order_id already exist"),
    ITEM_ID_NOT_EXIST(131, "Item_id does not exist"),
    VARIANT_ID_NOT_EXIST(132, "Variant_id does not exist"),
    ADDON_ID_NOT_EXIST(133, "addon_id does not exist"),
    ITEM_PRICE_MISMATCH(134, "Item_price mismatch"),
    REQUEST_PARTNER_CALL(135, "Request Swiggy to Call Partner"),
    OTHER_ERROR(0, "");

    int code;
    String message;

    SwiggyOrderRejectionCodes(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
