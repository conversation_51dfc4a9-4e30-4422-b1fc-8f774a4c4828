package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import org.codehaus.jackson.annotate.JsonValue;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public enum ZomatoRejectionCodes {
    // Some rejection code commented after discussion
    ITEMS_OUT_OF_STOCK(1,"Items out of stock"),
//    NO_DELIVERY_BOYS(2,"No delivery boys available"),
    NEARING_CLOSING_TIME(3,"Nearing closing time"),
//    OUT_OF_SUBZONE_AREA(4,"Out of Subzone/Area"),
    KITCHEN_IS_FULL(5,"Kitchen is Full"),
    MERCHANT_DEVICE_ISSUE(6, "Merchant device issue"),
    OUTLET_CLOSED(7, "Outlet Closed");

    int id;
    String message;

    ZomatoRejectionCodes(int id, String message) {
        this.id = id;
        this.message = message;
    }

    public int getId() {
        return id;
    }

    public String getMessage() {
        return message;
    }

    @JsonValue
    public Object toJson() {
        return new Object() {
            public final Integer id = getId();
            public final String message = getMessage();
        };
    }

    public static Map<Integer, String> getAllReasons() {
        Map<Integer, String> res = new HashMap<>();
        Arrays.stream(ZomatoRejectionCodes.values()).forEach(v -> {
            res.put(v.getId(), v.getMessage());
        });
        return res;
    }
}
