package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.jms.JMSException;

import com.stpl.tech.kettle.channelpartner.core.service.CheckedFunction;
import com.stpl.tech.kettle.channelpartner.core.service.MagicpinService;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerRequestType;
import com.stpl.tech.kettle.channelpartner.core.service.CheckedBiFunction;
import com.stpl.tech.kettle.channelpartner.domain.model.DealOfTheDayRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.DotdProductDimensionsRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.DotdProductsRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.DealOfTheDayDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DealOfTheDay;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DotdProduct;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DotdProductDimensions;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.BrandProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.FilteredProductsVO;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuTrackResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductAliasVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductsTagsVU;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerUnitListVO;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappingVU;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitAutoSwitchOff;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuVersionData;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitProductPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayStatusResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.CafeMenuAutoPushDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.CafeMenuAutoPushLogDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.DesiChaiCustomProfileDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.DesiChaiCustomProfileMappingsDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMetadataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOfferDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.SwiggyCafeStatusDataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.UnitPartnerLocalityMappingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.ZomatoCafeStatusDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPushLog;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfileMappings;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadata;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadataKey;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.StockEventService;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdNameGroup;
import com.stpl.tech.master.domain.model.IdValueUnit;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.util.AppConstants;
import org.springframework.util.CollectionUtils;

@Service
@Primary
public class PartnerMetadataManagementServiceImpl implements PartnerMetadataManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerMetadataManagementServiceImpl.class);

    @Autowired
    private SwiggyService swiggyService;

    @Autowired
    private ZomatoService zomatoService;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private StockEventService stockEventService;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private PartnerMenuService partnerMenuService;

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    private PartnerMetadataDao partnerMetadataDao;

    @Autowired
    private PartnerOfferDao partnerOfferDao;

    @Autowired
    private UnitPartnerLocalityMappingDao unitPartnerLocalityMappingDao;

    @Autowired
    private PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private CafeMenuAutoPushDao cafeMenuAutoPushDao;

    @Autowired
    private CafeMenuAutoPushLogDao cafeMenuAutoPushLogDao;

    @Autowired
    private DesiChaiCustomProfileDao desiChaiCustomProfileDao;

    @Autowired
    private DesiChaiCustomProfileMappingsDao desiChaiCustomProfileMappingsDao;

    @Autowired
    private ZomatoCafeStatusDao zomatoCafeStatusDao;

    @Autowired
    private SwiggyCafeStatusDataDao swiggyCafeStatusDataDao;

    @Autowired
    private CafeStatusChannelPartnerService cafeStatusChannelPartnerService;

    @Autowired
    private DealOfTheDayDao dealOfTheDayDao;

    @Autowired
    private MagicpinService magicpinService;

    @Override
    public boolean setUnitAvailability(UnitPartnerStatusVO request) throws ChannelPartnerException {
        for (Integer partnerId : request.getPartnerIds()) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            if (partnerDetail != null) {
                switch (partnerDetail.getPartnerName()) {
                    case "SWIGGY":
                        swiggyService.setUnitAvailability(request.getUnitIds(), request.getStatus(), request.getStartDate(), request.getEndDate(), request.getBrandId());
                        break;
                    case "ZOMATO":
                        zomatoService.setUnitDeliveryStatus(request.getUnitIds(), request.getStatus(), request.getBrandId());
                        break;
                    case "MAGICPIN":
                        magicpinService.setUnitDeliveryStatus(request.getUnitIds(), request.getStatus(), request.getBrandId());


                }
            }
        }
        return true;
    }

    @Override
    public boolean setUnitProductStock(UnitProductPartnerStatusVO request) throws ChannelPartnerException {
        if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
            if (request.getPartnerIds() != null && request.getPartnerIds().size() > 0) {
                request.getPartnerIds().forEach(partnerId -> {
                    PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
                    boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                            unitChannelPartnerMapping.getChannelPartner().getId() == partnerId && unitChannelPartnerMapping.getUnit().getId() == request.getUnitId());
                    if (partnerDetail != null && mappingValid) {
                        UnitProductsStockEvent event = new UnitProductsStockEvent();
                        event.setUnitId(request.getUnitId());
                        event.setStatus(request.getStatus() ? StockStatus.STOCK_IN : StockStatus.STOCK_OUT);
                        event.setPartnerId(partnerId);
                        event.setBrandId(request.getBrandId());
                        event.setForceStockOut(request.getForceStockOut());
                        request.getProductIds().forEach(productId -> event.getProductIds().add(productId.toString()));
                        try {
                            stockEventService.publishStockEvent(environmentProperties.getEnvType().name(), event);
                        } catch (JMSException e) {
                            LOG.error("Error publishing stock event:::::::::::" + new Gson().toJson(event));
                            e.printStackTrace();
                        }
                    }
                });
                return true;
            } else {
                throw new ChannelPartnerException("Partner id is not valid!");
            }
        } else {
            throw new ChannelPartnerException("Unit id is not valid!");
        }
    }

    @Override
    public List<PartnerDeliveryStatus> getDeliveryStatus(String kettleOrderId) throws ChannelPartnerException {
        if (kettleOrderId != null && !kettleOrderId.isEmpty()) {
            PartnerOrderDetail orderDetail = partnerOrderService.getPartnerOrder(kettleOrderId);
            if (orderDetail != null) {
                return orderDetail.getDeliveryStatuses();
            }
            throw new ChannelPartnerException("Invalid request", "Order id not valid");
        }
        throw new ChannelPartnerException("Invalid request", "Please send order id in request.");
    }

    @Override
    public boolean setPartnerProductFilter(UnitProductPartnerStatusVO request) {
        Integer partnerId = request.getPartnerIds().get(0);
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
            PartnerMetadata metadata = new PartnerMetadata();
            metadata.setKettlePartnerId(partnerId);
            metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(partnerId).getPartnerName());
            if (metadata.getMetadata() == null) {
                metadata.setMetadata(new HashMap<>());
            }
            List<FilteredProductsVO> filteredProductsList = new ArrayList<>();
            FilteredProductsVO vo = new FilteredProductsVO();
            vo.setBrandId(request.getBrandId());
            vo.setProductIds(request.getProductIds());
            filteredProductsList.add(vo);
            metadata.getMetadata().put(PartnerMetadataKey.PRODUCT_FILTER_LIST, new Gson().toJson(filteredProductsList));
            partnerMetadataDao.save(metadata);
        } else {
            for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                if (partnerMetadata.getKettlePartnerId().equals(partnerId)) {
                    String filterProducts = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_FILTER_LIST);
                    List<FilteredProductsVO> updatedList = new ArrayList<>();
                    if (filterProducts != null) {
                        try {
                            JsonArray filterList = new Gson().fromJson(filterProducts, JsonArray.class);
                            for (JsonElement item : filterList) {
                                FilteredProductsVO vo = new Gson().fromJson(new Gson().toJson(item), FilteredProductsVO.class);
                                if (!vo.getBrandId().equals(request.getBrandId())) {
                                    updatedList.add(vo);
                                }
                            }
                            FilteredProductsVO vo = new FilteredProductsVO();
                            vo.setBrandId(request.getBrandId());
                            vo.setProductIds(request.getProductIds());
                            updatedList.add(vo);
                        } catch (Exception e) {
                            LOG.error("Error setting partner product filters", e);
                        }
                    } else {
                        FilteredProductsVO vo = new FilteredProductsVO();
                        vo.setBrandId(request.getBrandId());
                        vo.setProductIds(request.getProductIds());
                        updatedList.add(vo);
                    }
                    partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_FILTER_LIST, new Gson().toJson(updatedList));
                    partnerMetadataDao.save(partnerMetadata);
                }
            }
        }
        return true;
    }

    @Override
    public boolean setPartnerProductTags(PartnerProductTagsVO request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata metadata = new PartnerMetadata();
                metadata.setKettlePartnerId(request.getPartnerId());
                metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (metadata.getMetadata() == null) {
                    metadata.setMetadata(new HashMap<>());
                }
                metadata.getMetadata().put(PartnerMetadataKey.PRODUCT_TAGS_LIST, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(metadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_TAGS_LIST, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setPartnerMeatTags(PartnerProductTagsVO request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata metadata = new PartnerMetadata();
                metadata.setKettlePartnerId(request.getPartnerId());
                metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (metadata.getMetadata() == null) {
                    metadata.setMetadata(new HashMap<>());
                }
                metadata.getMetadata().put(PartnerMetadataKey.MEAT_TAGS_LIST, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(metadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.MEAT_TAGS_LIST, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setPartnerProductTagsMappings(PartnerProductTagsMappings request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata metadata = new PartnerMetadata();
                metadata.setKettlePartnerId(request.getPartnerId());
                metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (metadata.getMetadata() == null) {
                    metadata.setMetadata(new HashMap<>());
                }
                List<BrandProductTagsMappings> mappings = new ArrayList<>();
                mappings.add(request.getMappings());
                metadata.getMetadata().put(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS, new Gson().toJson(mappings));
                partnerMetadataDao.save(metadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS);
                        List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
                        if (data != null) {
                            try {
                                JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                                for (JsonElement mappings : brandProductTagsMappings) {
                                    BrandProductTagsMappings bpm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                                    if (!bpm.getBrandId().equals(request.getMappings().getBrandId())) {
                                        updatedMappings.add(bpm);
                                    }
                                }
                                updatedMappings.add(request.getMappings());
                            } catch (Exception e) {
                                LOG.error("Error setting partner product tag mappings", e);
                            }
                        } else {
                            updatedMappings.add(request.getMappings());
                        }
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS, new Gson().toJson(updatedMappings));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setPartnerProductMeatTagsMappings(PartnerProductTagsMappings request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata metadata = new PartnerMetadata();
                metadata.setKettlePartnerId(request.getPartnerId());
                metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (metadata.getMetadata() == null) {
                    metadata.setMetadata(new HashMap<>());
                }
                List<BrandProductTagsMappings> mappings = new ArrayList<>();
                mappings.add(request.getMappings());
                metadata.getMetadata().put(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS, new Gson().toJson(mappings));
                partnerMetadataDao.save(metadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS);
                        List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
                        if (data != null) {
                            try {
                                JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                                for (JsonElement mappings : brandProductTagsMappings) {
                                    BrandProductTagsMappings bpm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                                    if (!bpm.getBrandId().equals(request.getMappings().getBrandId())) {
                                        updatedMappings.add(bpm);
                                    }
                                }
                                updatedMappings.add(request.getMappings());
                            } catch (Exception e) {
                                LOG.error("Error setting partner product meat tag mappings", e);
                            }
                        } else {
                            updatedMappings.add(request.getMappings());
                        }
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS, new Gson().toJson(updatedMappings));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setPartnerAllergenTags(PartnerProductTagsVO request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (partnerMetadata.getMetadata() == null || partnerMetadata.getMetadata().isEmpty()) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                partnerMetadata.getMetadata().put(PartnerMetadataKey.ALLERGEN_TAGS_LIST, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.ALLERGEN_TAGS_LIST, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<IdName> getPartnerAllergenTags(Integer partnerId) {
        List<IdName> tags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.ALLERGEN_TAGS_LIST);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    IdName tag = new Gson().fromJson(item, IdName.class);
                    tags.add(tag);
                }
            }
        }
        return tags;
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductAllergenTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_ALLERGEN_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (Objects.nonNull(tagsArray)) {
                for (JsonElement data : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(data.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }

        }
        return new ArrayList<>();
    }

    @Override
    public List<BrandProductTagsMappings> getPartnerProductAllergenTagsMappingsDetail(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        for (PartnerMetadata partnerMetadata : partnerMetadataList) {
            String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_ALLERGEN_TAGS_MAPPINGS);
            List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
            if (data != null) {
                try {
                    JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                    for (JsonElement mappings : brandProductTagsMappings) {
                        BrandProductTagsMappings bptm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                        if (bptm.getBrandId().equals(brandId)) {
                            updatedMappings.add(bptm);
                        }
                    }
                    return updatedMappings;
                } catch (Exception e) {
                    LOG.error("Error in fetching data", e);
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public boolean setPartnerProductAllergenTagsMapping(PartnerProductTagsMappings request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (Objects.isNull(partnerMetadata.getMetadata())) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                List<BrandProductTagsMappings> brandProductTagsMappingsList = new ArrayList<>();
                brandProductTagsMappingsList.add(request.getMappings());
                partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_ALLERGEN_TAGS_MAPPINGS, new Gson().toJson(brandProductTagsMappingsList));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_ALLERGEN_TAGS_MAPPINGS);
                        List<BrandProductTagsMappings> updatedMap = new ArrayList<>();
                        if (data != null) {
                            try {
                                JsonArray brandProductTagsMapping = new Gson().fromJson(data, JsonArray.class);
                                for (JsonElement item : brandProductTagsMapping) {
                                    BrandProductTagsMappings bptm = new Gson().fromJson(new Gson().toJson(item), BrandProductTagsMappings.class);
                                    if (!bptm.getBrandId().equals(request.getMappings().getBrandId())) {
                                        updatedMap.add(bptm);
                                    }

                                }
                                updatedMap.add(request.getMappings());
                            } catch (Exception e) {
                                LOG.error("Error occured while updating mappings for Allergen Types", e);
                            }
                        } else {
                            updatedMap.add(request.getMappings());
                        }
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_ALLERGEN_TAGS_MAPPINGS, new Gson().toJson(updatedMap));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setPartnerServingInfoTags(PartnerProductTagsVO request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (partnerMetadata.getMetadata() == null || partnerMetadata.getMetadata().isEmpty()) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                partnerMetadata.getMetadata().put(PartnerMetadataKey.SERVING_INFO_TAGS_LIST, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.SERVING_INFO_TAGS_LIST, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<IdName> getPartnerServingInfoTags(Integer partnerId) {
        List<IdName> tags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (Objects.nonNull(partnerMetadataList) && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.SERVING_INFO_TAGS_LIST);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    IdName tag = new Gson().fromJson(item, IdName.class);
                    tags.add(tag);
                }
            }
        }
        return tags;
    }

    @Override
    public boolean setPartnerProductServingInfoTagsMapping(PartnerProductTagsMappings request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (Objects.isNull(partnerMetadataList) || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (Objects.isNull(partnerMetadata.getMetadata())) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                List<BrandProductTagsMappings> brandProductTagsMappingsList = new ArrayList<>();
                brandProductTagsMappingsList.add(request.getMappings());
                partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_SERVING_INFO_TAGS_MAPPINGS, new Gson().toJson(brandProductTagsMappingsList));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_INFO_TAGS_MAPPINGS);
                        List<BrandProductTagsMappings> updatedMap = new ArrayList<>();
                        if (data != null) {
                            try {
                                JsonArray brandProductTagsMapping = new Gson().fromJson(data, JsonArray.class);
                                for (JsonElement item : brandProductTagsMapping) {
                                    BrandProductTagsMappings bptm = new Gson().fromJson(new Gson().toJson(item), BrandProductTagsMappings.class);
                                    if (!bptm.getBrandId().equals(request.getMappings().getBrandId())) {
                                        updatedMap.add(bptm);
                                    }

                                }
                                updatedMap.add(request.getMappings());
                            } catch (Exception e) {
                                LOG.error("Error occured while updating mappings for Serving Info", e);
                            }
                        } else {
                            updatedMap.add(request.getMappings());
                        }
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_SERVING_INFO_TAGS_MAPPINGS, new Gson().toJson(updatedMap));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductServingInfoTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_INFO_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (Objects.nonNull(tagsArray)) {
                for (JsonElement data : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(data.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<BrandProductTagsMappings> getPartnerProductServingInfoTagsMappingsDetail(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        for (PartnerMetadata partnerMetadata : partnerMetadataList) {
            String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_INFO_TAGS_MAPPINGS);
            List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
            if (data != null) {
                try {
                    JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                    for (JsonElement mappings : brandProductTagsMappings) {
                        BrandProductTagsMappings bptm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                        if (bptm.getBrandId().equals(brandId)) {
                            updatedMappings.add(bptm);
                        }
                    }
                    return updatedMappings;
                } catch (Exception e) {
                    LOG.error("Error in fetching data", e);
                }

            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<IdValueUnit> getPartnerServingSize(Integer partnerId) {
        List<IdValueUnit> tags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.SERVING_SIZE_TAGS_LIST);
            if (Objects.isNull(productTags) || productTags.equals("null")) {
                return tags;
            }
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            LOG.info("List of Serving Size {}", tagsArray);
            if (tagsArray != null) {
                for (JsonElement data : tagsArray) {
                    IdValueUnit tag = new Gson().fromJson(data, IdValueUnit.class);
                    tags.add(tag);
                }
            }
        }
        return tags;
    }

    @Override
    public boolean setPartnerServingSize(PartnerProductsTagsVU request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (Objects.isNull(partnerMetadata.getMetadata())) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                partnerMetadata.getMetadata().put(PartnerMetadataKey.SERVING_SIZE_TAGS_LIST, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.SERVING_SIZE_TAGS_LIST, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<ProductTagsMappingVU> getPartnerProductServingSizeMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_SIZE_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);

            if (Objects.nonNull(tagsArray)) {
                for (JsonElement data : tagsArray) {

                    BrandProductTagsMappings mappings = new Gson().fromJson(data.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappingsVU();
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public boolean setPartnerProductServingSizeMappings(PartnerProductTagsMappings request) {
        if (Objects.nonNull(request) && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata partnerMetadata = new PartnerMetadata();
                partnerMetadata.setKettlePartnerId(request.getPartnerId());
                partnerMetadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (partnerMetadata.getMetadata() == null) {
                    partnerMetadata.setMetadata(new HashMap<>());
                }
                List<BrandProductTagsMappings> mappings = new ArrayList<>();
                mappings.add(request.getMappings());
                partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_SERVING_SIZE_TAGS_MAPPINGS, new Gson().toJson(mappings));
                partnerMetadataDao.save(partnerMetadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_SIZE_TAGS_MAPPINGS);
                        List<BrandProductTagsMappings> updateMap = new ArrayList<>();
                        if (data != null) {
                            try {
                                JsonArray brandProductTagsMapping = new Gson().fromJson(data, JsonArray.class);
                                for (JsonElement mappings : brandProductTagsMapping) {
                                    String ans = new Gson().toJson(mappings);
                                    BrandProductTagsMappings bptm = new Gson().fromJson(ans, BrandProductTagsMappings.class);
                                    if (!bptm.getBrandId().equals(request.getMappings().getBrandId())) {
                                        updateMap.add(bptm);
                                    }
                                }
                                updateMap.add(request.getMappings());
                            } catch (Exception e) {
                                LOG.info("Error occured while updating mappings for serving size", e);
                            }
                        } else {
                            updateMap.add(request.getMappings());
                        }
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_SERVING_SIZE_TAGS_MAPPINGS, new Gson().toJson(updateMap));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<BrandProductTagsMappings> getPartnerProductServingSizeMappingsDetail(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        for (PartnerMetadata partnerMetadata : partnerMetadataList) {
            String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_SERVING_SIZE_TAGS_MAPPINGS);
            List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
            if (data != null) {
                try {
                    JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                    for (JsonElement item : brandProductTagsMappings) {
                        BrandProductTagsMappings bptm = new Gson().fromJson(new Gson().toJson(item), BrandProductTagsMappings.class);
                        if (bptm.getBrandId().equals(brandId)) {
                            updatedMappings.add(bptm);
                        }
                    }
                    return updatedMappings;
                } catch (Exception e) {
                    LOG.info("Error in fetching data");
                }
            }
        }
        return new ArrayList<>();
    }


    @Override
    public boolean setPartnerBogoProducts(UnitProductPartnerStatusVO request) {
        Integer partnerId = request.getPartnerIds().get(0);
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
            PartnerMetadata metadata = new PartnerMetadata();
            metadata.setKettlePartnerId(partnerId);
            metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(partnerId).getPartnerName());
            if (metadata.getMetadata() == null) {
                metadata.setMetadata(new HashMap<>());
            }
            metadata.getMetadata().put(PartnerMetadataKey.BOGO_PRODUCT_LIST, new Gson().toJson(request.getProductIds()));
            partnerMetadataDao.save(metadata);
        } else {
            for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                partnerMetadata.getMetadata().put(PartnerMetadataKey.BOGO_PRODUCT_LIST, new Gson().toJson(request.getProductIds()));
                partnerMetadataDao.save(partnerMetadata);
            }
        }
        return true;
    }

    @Override
    public boolean setPartnerProductAliases(PartnerProductAliasVO request) {
        Integer partnerId = request.getPartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
            PartnerMetadata metadata = new PartnerMetadata();
            metadata.setKettlePartnerId(partnerId);
            metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(partnerId).getPartnerName());
            if (metadata.getMetadata() == null) {
                metadata.setMetadata(new HashMap<>());
            }
            metadata.getMetadata().put(PartnerMetadataKey.PRODUCT_ALIAS_LIST, new Gson().toJson(request.getProductAliases()));
            partnerMetadataDao.save(metadata);
        } else {
            for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                if (partnerMetadata.getKettlePartnerId().equals(partnerId)) {
                    partnerMetadata.getMetadata().put(PartnerMetadataKey.PRODUCT_ALIAS_LIST, new Gson().toJson(request.getProductAliases()));
                    partnerMetadataDao.save(partnerMetadata);
                }
            }
        }
        channelPartnerDataCache.loadPartnerProductAliases();
        return true;
    }

    @Override
    public List<Integer> getPartnerProductFilter(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productFilters = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_FILTER_LIST);
            JsonArray tagsArray = new Gson().fromJson(productFilters, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    try {
                        FilteredProductsVO mapping = new Gson().fromJson(item, FilteredProductsVO.class);
                        if (mapping.getBrandId().equals(brandId)) {
                            return mapping.getProductIds();
                        }
                    } catch (Exception e) {
                        return null;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<Integer> getPartnerBogoProducts(Integer partnerId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productList = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.BOGO_PRODUCT_LIST);
            JsonArray productIds = new Gson().fromJson(productList, JsonArray.class);
            if (productIds != null) {
                List<Integer> products = new ArrayList<>();
                for (JsonElement item : productIds) {
                    products.add(item.getAsInt());
                }
                return products;
            }
        }
        return null;
    }

    @Override
    public List<String> getPartnerBogoProductsforOffer(Integer partnerId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productList = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.BOGO_PRODUCT_LIST);
            JsonArray productIds = new Gson().fromJson(productList, JsonArray.class);
            if (productIds != null) {
                List<String> products = new ArrayList<>();
                for (JsonElement item : productIds) {
                    products.add(String.valueOf(item.getAsInt()));
                }
                return products;
            }
        }
        return null;
    }

    @Override
    public List<IdNameGroup> getPartnerProductTags(Integer partnerId) {
        List<IdNameGroup> tags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_TAGS_LIST);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    IdNameGroup tag = new Gson().fromJson(item, IdNameGroup.class);
                    tags.add(tag);
                }
            }
        }
        return tags;
    }

    @Override
    public List<IdName> getPartnerMeatTags(Integer partnerId) {
        List<IdName> tags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            LOG.info("###{}", partnerMetadataList);
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.MEAT_TAGS_LIST);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    IdName tag = new Gson().fromJson(item, IdName.class);
                    tags.add(tag);
                }
            }
        }
        return tags;
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(item.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductMeatTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(item.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<BrandProductTagsMappings> getPartnerProductTagsMappingsDetail(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        for (PartnerMetadata partnerMetadata : partnerMetadataList) {
            String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS);
            List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
            if (data != null) {
                try {
                    JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                    for (JsonElement mappings : brandProductTagsMappings) {
                        BrandProductTagsMappings bpm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                        if (bpm.getBrandId().equals(brandId)) {
                            updatedMappings.add(bpm);
                        }
                    }
                    return updatedMappings;
                } catch (Exception e) {
                    LOG.info("Error in fetching data");
                }
            }

        }

        return new ArrayList<>();
    }


    @Override
    public List<BrandProductTagsMappings> getPartnerProductMeatTagsMappingsDetail(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        for (PartnerMetadata partnerMetadata : partnerMetadataList) {
            String data = partnerMetadata.getMetadata().get(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS);
            List<BrandProductTagsMappings> updatedMappings = new ArrayList<>();
            if (data != null) {
                try {
                    JsonArray brandProductTagsMappings = new Gson().fromJson(data, JsonArray.class);
                    for (JsonElement mappings : brandProductTagsMappings) {
                        BrandProductTagsMappings bpm = new Gson().fromJson(new Gson().toJson(mappings), BrandProductTagsMappings.class);
                        if (bpm.getBrandId().equals(brandId)) {
                            updatedMappings.add(bpm);
                        }
                    }
                    return updatedMappings;
                } catch (Exception e) {
                    LOG.info("Error in fetching data");
                }
            }

        }

        return new ArrayList<>();
    }

    @Override
    public List<IdName> getPartnerProductQVMTags(Integer partnerId) {
        List<IdName> qvmTags = new ArrayList<>();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PARTNER_QVM_TAGS);
            if (productTags != null) {
                JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
                if (tagsArray != null) {
                    for (JsonElement item : tagsArray) {
                        IdName mapping = new Gson().fromJson(item, IdName.class);
                        qvmTags.add(mapping);
                    }
                }
            }
        }
        return qvmTags;
    }

    @Override
    public List<ProductAlias> getPartnerProductAliases(Integer partnerId) {
        return new ArrayList<>(channelPartnerDataCache.getProductAliasMap(partnerId).values());
    }

    @Override
    public boolean setPartnerQVMtTags(PartnerProductTagsVO request) {
        if (request != null && request.getPartnerId() != null) {
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(request.getPartnerId());
            if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
                PartnerMetadata metadata = new PartnerMetadata();
                metadata.setKettlePartnerId(request.getPartnerId());
                metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId()).getPartnerName());
                if (metadata.getMetadata() == null) {
                    metadata.setMetadata(new HashMap<>());
                }
                metadata.getMetadata().put(PartnerMetadataKey.PARTNER_QVM_TAGS, new Gson().toJson(request.getTags()));
                partnerMetadataDao.save(metadata);
            } else {
                for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                    if (partnerMetadata.getKettlePartnerId().equals(request.getPartnerId())) {
                        partnerMetadata.getMetadata().put(PartnerMetadataKey.PARTNER_QVM_TAGS, new Gson().toJson(request.getTags()));
                        partnerMetadataDao.save(partnerMetadata);
                    }
                }
            }
            return true;
        }
        return false;
    }


    /*@Override
    public MenuSequence getMenuSequence(UnitMenuAddVO request) throws ChannelPartnerException {
        if (request.getUnitId() != null && request.getKettlePartnerId() != null) {
            if (Arrays.stream(UnitRegion.values()).map(Enum::name).collect(Collectors.toList()).contains(request.getRegion())) {
                PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
                if (partnerDetail != null) {
                    List<UnitPartnerMenuMapping> menuMappings = channelPartnerDataCache.getMenuMappingsByUnitPartnerBrand(request.getUnitId(), request.getKettlePartnerId(), request.getBrandId());
                    if(menuMappings != null) {
                        for(UnitPartnerMenuMapping unitPartnerMenuMapping : menuMappings) {
                            if(unitPartnerMenuMapping.getStatus().equalsIgnoreCase(AppConstants.ACTIVE) &&
                                    MenuApp.CHANNEL_PARTNER.equals(unitPartnerMenuMapping.getMenuApp()) && MenuType.DEFAULT.equals(unitPartnerMenuMapping.getMenuType())) {
                                return channelPartnerDataCache.getMenuSequenceMap().get(unitPartnerMenuMapping.getMenuSequence().getId());
                            }
                        }
                    }
                    throw new ChannelPartnerException("No menu is mapped to this unit!");
                } else {
                    throw new ChannelPartnerException("Partner id is not valid!");
                }
            }
            throw new ChannelPartnerException("Unit region is not valid!");
        } else {
            throw new ChannelPartnerException("Request is not valid!");
        }
    }*/

    @Override
    public UnitMenuAddVO getPartnerUnitMenu(UnitMenuAddVO request, PartnerActionEventType eventType) throws
            ChannelPartnerException {
        PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
        if (request.getKettlePartnerId() != null && partnerDetail != null) {
            if (PartnerActionEventType.UPDATE_UNIT_MENU.equals(eventType)) {
                if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
                    switch (partnerDetail.getPartnerName()) {
                        case "SWIGGY":
                            return null;
                        case "ZOMATO":
                            return zomatoService.createZomatoMenuRequestObj(request.getKettlePartnerId(), request.getUnitId(),
                                    eventType, request.getBrandId(), request.getEmployeeId());
                    }
                }
                throw new ChannelPartnerException("Unit region is not valid!");
            } else {
                //TODO case for region wise menu which has been deprecated
                return null;
            }
        } else {
            throw new ChannelPartnerException("Partner id is not valid!");
        }
    }

    @Override
    public boolean addPartnerRegionMenu(UnitMenuAddVO request) throws ChannelPartnerException {
        if (request.getRegion() != null) {
            if (Arrays.stream(masterDataCache.getAllRegions().toArray()).collect(Collectors.toList()).contains(request.getRegion())) {
                PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
                if (partnerDetail != null) {
                    partnerMenuService.addPartnerMenuDetail(request);
                    //Adding menu update request to queue
                    List<Integer> partnerIds = new ArrayList<>(1);
                    partnerIds.add(partnerDetail.getKettlePartnerId());
                    masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
                        if (unitChannelPartnerMapping.getChannelPartner().getId() == partnerDetail.getKettlePartnerId()) {
                            Unit unit = masterDataCache.getUnit(unitChannelPartnerMapping.getUnit().getId());
                            if (unit != null && unit.getStatus().equals(UnitStatus.ACTIVE) && unit.getRegion().equalsIgnoreCase(request.getRegion())) {
                                pushMenuEventToQueue(partnerIds, unit.getId(), PartnerActionEventType.UPDATE_MENU, request.getBrandId());
                            }
                        }
                    });
                } else {
                    throw new ChannelPartnerException("Partner id is not valid!");
                }
            }
            return true;
        } else {
            throw new ChannelPartnerException("Unit id is not valid!");
        }
    }

    @Override
    public boolean addPartnerUnitMenu(UnitMenuAddVO request) throws ChannelPartnerException {
        if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
            if (partnerDetail != null) {
                partnerMenuService.addPartnerUnitMenuDetail(request);
                List<Integer> partnerIds = new ArrayList<>(1);
                partnerIds.add(partnerDetail.getKettlePartnerId());
                boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                        unitChannelPartnerMapping.getChannelPartner().getId() == partnerDetail.getKettlePartnerId() &&
                                unitChannelPartnerMapping.getUnit().getId() == request.getUnitId());
                if (mappingValid) {
                    Unit unit = masterDataCache.getUnit(request.getUnitId());
                    if (unit != null && unit.getStatus().equals(UnitStatus.ACTIVE) && request.getNew()) {
                        pushMenuEventToQueue(partnerIds, request, PartnerActionEventType.UPDATE_UNIT_MENU, request.getBrandId());
                    }
                }
            } else {
                throw new ChannelPartnerException("Partner id is not valid!");
            }
            return true;
        } else {
            throw new ChannelPartnerException("Unit id is not valid!");
        }
    }

    @Override
    public List<PartnerUnitMenuDetail> getActiveMenuForPartnerUnits(PartnerUnitListVO request) throws
            ChannelPartnerException {
        List<PartnerUnitMenuDetail> menuList = new ArrayList<>();
        PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getPartnerId());
        if (partnerDetail != null) {
            for (Integer unitId : request.getUnitIds()) {
                if (unitId != null && masterDataCache.getUnit(unitId) != null) {
                    AtomicBoolean unitMapped = new AtomicBoolean(false);
                    boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                            unitChannelPartnerMapping.getChannelPartner().getId() == partnerDetail.getKettlePartnerId() &&
                                    unitChannelPartnerMapping.getUnit().getId() == unitId);
                    if (mappingValid) {
                        Unit unit = masterDataCache.getUnit(unitId);
                        if (unit != null && unit.getStatus().equals(UnitStatus.ACTIVE)) {
                            unitMapped.set(true);
                        }
                    }
                    if (unitMapped.get()) {
                        PartnerUnitMenuDetail partnerUnitMenuDetail = partnerMenuService.getActivePartnerUnitMenuDetail(request.getPartnerId(),
                                unitId, request.getNew(), request.getBrandId());
                        if (partnerUnitMenuDetail != null) {
                            partnerUnitMenuDetail.setMenuData(null);
                            menuList.add(partnerUnitMenuDetail);
                            //return partnerUnitMenuDetail;
                        }
                    }
                }
            }
            return menuList;
        } else {
            throw new ChannelPartnerException("Partner id is not valid!");
        }
    }

    @Override
    public boolean refreshPartnerUnitMenu(UnitMenuAddVO request) throws ChannelPartnerException {
        if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
            if (partnerDetail != null) {
                List<Integer> partnerIds = new ArrayList<>(1);
                partnerIds.add(partnerDetail.getKettlePartnerId());
                PartnerActionEvent event = new PartnerActionEvent();
                event.setPartnerIds(partnerIds);
                event.setPartner(true);
                event.setEventType(PartnerActionEventType.UPDATE_UNIT_MENU);
                event.setEventData(request);
                redisPublisher.publish(partnerDetail.getPartnerName(), new Gson().toJson(event));
            } else {
                throw new ChannelPartnerException("Partner id is not valid!");
            }
            return true;
        } else {
            throw new ChannelPartnerException("Unit id is not valid!");
        }
    }

    @Override
    public List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnits(PartnerUnitListVO request) throws
            ChannelPartnerException {
        return partnerMenuService.getAllActiveOffersForPartnerAndUnits(request);
    }

    @Override
    public PartnerOfferDetail addPartnerOffer(PartnerUnitListVO request) throws ChannelPartnerException {
        if (request.getPartnerId().equals(3)) {
            return partnerMenuService.addPartnerOfferForZomato(request);
        } else {
            return partnerMenuService.addPartnerOffer(request);
        }
    }

    @Override
    public void publishInventoryToPartner(UnitPartnerStatusVO request) {
        PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
        partnerActionEvent.setPartner(true);
        partnerActionEvent.setBrandId(request.getBrandId());
        request.getPartnerIds().forEach(partnerId -> partnerActionEvent.getPartnerIds().add(partnerId));
        partnerActionEvent.setEventData(request.getUnitIds());
        partnerActionEvent.setEventType(PartnerActionEventType.INVENTORY_UPDATE);
        publishEventToPartners(request.getPartnerIds(), partnerActionEvent);
    }

    @Override
    public Boolean deactivatePartnerOffer(String partnerOfferId) throws ChannelPartnerException {
        return partnerMenuService.deactivatePartnerOffer(partnerOfferId);
    }

    @Override
    public Boolean activatePartnerOffer(String partnerOfferId) throws ChannelPartnerException {
        return partnerMenuService.activatePartnerOffer(partnerOfferId);
    }

    @Override
    public IdCodeName getZomatoTreatsItem() {
        IdCodeName treatsItem = null;
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty() &&
                partnerMetadataList.get(0).getMetadata().containsKey(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID)) {
            int productId = Integer.parseInt(partnerMetadataList.get(0).getMetadata()
                    .get(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID));
            treatsItem = new IdCodeName(productId, masterDataCache.getProduct(productId).getName(), "");
        }
        return treatsItem;
    }

    @Override
    public boolean setZomatoTreatsItem(Integer productId) throws ChannelPartnerException {
        if (productId != null && masterDataCache.getProduct(productId) != null) {
            Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
            //boolean updated = false;
            if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
                for (PartnerMetadata data : partnerMetadataList) {
                    if (data.getMetadata() == null) {
                        data.setMetadata(new HashMap<>());
                    }
                    if (!data.getMetadata().containsKey(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID) ||
                            !data.getMetadata().get(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID).equalsIgnoreCase(productId.toString())) {
                        data.getMetadata().put(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID, productId.toString());
                        partnerMetadataDao.save(data);
                        //updated = true;
                    }
                }
            } else {
                PartnerMetadata data = new PartnerMetadata();
                data.setKettlePartnerId(kettlePartnerId);
                Map<PartnerMetadataKey, String> metadataMap = new HashMap<>();
                metadataMap.put(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID, productId.toString());
                data.setMetadata(metadataMap);
                data.setPartnerName("ZOMATO");
                partnerMetadataDao.save(data);
                //updated = true;
            }
            // if (updated) {
            //menu update queue push
            //pushMenuUpdateToQueue(kettlePartnerId); //menu will be pushed manually for each unit
            //}
        } else {
            throw new ChannelPartnerException("Product id not valid.");
        }
        return true;
    }

    @Override
    public boolean removeZomatoTreatsItem() {
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList != null) {
            for (PartnerMetadata data : partnerMetadataList) {
                if (data.getMetadata().containsKey(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID)) {
                    data.getMetadata().remove(PartnerMetadataKey.ZOMATO_TREAT_ITEM_ID);
                    partnerMetadataDao.save(data);
                    //Manu will be pushed manually
                    //menu update queue push
                    //pushMenuUpdateToQueue(kettlePartnerId);
                }
            }
        }
        return true;
    }

    @Override
    public IdCodeName getSwiggySuperItem() {
        IdCodeName treatsItem = null;
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            if (partnerMetadataList.get(0).getMetadata().containsKey(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID)) {
                int productId = Integer.parseInt(partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID));
                treatsItem = new IdCodeName(productId, masterDataCache.getProduct(productId).getName(), "");
            }
        }
        return treatsItem;
    }

    @Override
    public boolean setSwiggySuperItem(Integer productId) throws ChannelPartnerException {
        if (productId != null && masterDataCache.getProduct(productId) != null) {
            Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
            List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
            //boolean updated = false;
            if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
                for (PartnerMetadata data : partnerMetadataList) {
                    if (data.getMetadata() == null) {
                        data.setMetadata(new HashMap<>());
                    }
                    if (!data.getMetadata().containsKey(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID) ||
                            !data.getMetadata().get(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID).equalsIgnoreCase(productId.toString())) {
                        data.getMetadata().put(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID, productId.toString());
                        partnerMetadataDao.save(data);
                        //updated = true;
                    }
                }
            } else {
                PartnerMetadata data = new PartnerMetadata();
                data.setKettlePartnerId(kettlePartnerId);
                Map<PartnerMetadataKey, String> metadataMap = new HashMap<>();
                metadataMap.put(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID, productId.toString());
                data.setMetadata(metadataMap);
                data.setPartnerName("SWIGGY");
                partnerMetadataDao.save(data);
                //updated = true;
            }
            //if (updated) {
            //menu update will be pushed manually
            //menu update queue push
            //pushMenuUpdateToQueue(kettlePartnerId); //menu will be pushed manually for each unit
            //}
        } else {
            throw new ChannelPartnerException("Product id not valid.");
        }
        return true;
    }

    @Override
    public boolean removeSwiggySuperItem() {
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList != null) {
            for (PartnerMetadata data : partnerMetadataList) {
                if (data.getMetadata().containsKey(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID)) {
                    data.getMetadata().remove(PartnerMetadataKey.SWIGGY_SUPER_ITEM_ID);
                    partnerMetadataDao.save(data);
                    //Menu update will be pushed manually
                    //menu update queue push
                    //pushMenuUpdateToQueue(kettlePartnerId);
                }
            }
        }
        return true;
    }

    @Override
    public List<Integer> getSwiggyRecommendedProducts() {
        List<Integer> recommended = new ArrayList<>();
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList != null) {
            for (PartnerMetadata data : partnerMetadataList) {
                if (data.getMetadata().containsKey(PartnerMetadataKey.SWIGGY_RECOMMENDED_PRODUCTS)) {
                    String products = data.getMetadata().get(PartnerMetadataKey.SWIGGY_RECOMMENDED_PRODUCTS);
                    if (products != null) {
                        JsonArray productIds = new Gson().fromJson(products, JsonArray.class);
                        if (productIds != null) {
                            for (JsonElement item : productIds) {
                                Integer productId = new Gson().fromJson(item, Integer.class);
                                recommended.add(productId);
                            }
                        }
                    }
                }
            }
        }
        return recommended;
    }

    @Override
    public boolean setSwiggyRecommendedProducts(List<Integer> productIds) {
        Integer kettlePartnerId = channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId();
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(kettlePartnerId);
        if (partnerMetadataList == null || partnerMetadataList.isEmpty()) {
            PartnerMetadata metadata = new PartnerMetadata();
            metadata.setKettlePartnerId(kettlePartnerId);
            metadata.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId).getPartnerName());
            if (metadata.getMetadata() == null) {
                metadata.setMetadata(new HashMap<>());
            }
            metadata.getMetadata().put(PartnerMetadataKey.SWIGGY_RECOMMENDED_PRODUCTS, new Gson().toJson(productIds));
            partnerMetadataDao.save(metadata);
        } else {
            for (PartnerMetadata partnerMetadata : partnerMetadataList) {
                if (partnerMetadata.getKettlePartnerId().equals(kettlePartnerId)) {
                    partnerMetadata.getMetadata().put(PartnerMetadataKey.SWIGGY_RECOMMENDED_PRODUCTS, new Gson().toJson(productIds));
                    partnerMetadataDao.save(partnerMetadata);
                }
            }
        }
        return true;
    }

    @Override
    public MenuTrackResponse getSwiggyMenuStatus(Integer unitId, Integer partnerId, Integer brandId) {
        return swiggyService.trackSwiggyMenuPushStatus(unitId, partnerId, brandId);
    }

    @Override
    public boolean updateCafeDeliveryTimeSwiggy(CafeTimingChangeRequest cafeTimingChangeRequest) {
        return swiggyService.updateCafeDeliveryTimeSwiggy(cafeTimingChangeRequest);
    }

    @Override
    public ZomatoLogisticsStatusResponse getOutletLogisticsStatus(Integer unitId, Integer brandId) throws
            ChannelPartnerException {
        return zomatoService.getOutletLogisticsStatus(unitId, brandId);
    }

    @Override
    public ZomatoNotificationResponse updateOutletLogisticsStatus(ZomatoLogisticsChangeRequest request) throws
            ChannelPartnerException {
        return zomatoService.updateOutletLogisticsStatus(request);
    }

    @Override
    public ZomatoDeliveryStatusResponse getOutletDeliveryStatus(Integer unitId, Integer brandId) throws
            ChannelPartnerException {
        return zomatoService.getOutletDeliveryStatus(unitId, brandId);
    }

    @Override
    public ZomatoNotificationResponse updateOutletDeliveryStatus(ZomatoDeliveryChangeRequest request) throws
            ChannelPartnerException {
        return zomatoService.updateOutletDeliveryStatus(request);
    }

    @Override
    public void updateZomatoOutletStatusByRestaurantId(Integer brandId, ZomatoDeliveryChangeRequest request) {
        zomatoService.updateZomatoOutletStatus(brandId, request);
    }

    @Override
    public ZomatoTakeawayStatusResponse getOutletTakeawayStatus(Integer unitId, Integer brandId) throws
            ChannelPartnerException {
        return zomatoService.getOutletTakeawayStatus(unitId, brandId);
    }

    @Override
    public boolean updateOutletTakeawayStatus(UnitPartnerStatusVO request) {
        if (request.getPartnerIds() != null && !request.getPartnerIds().isEmpty() && request.getUnitIds() != null && !request.getUnitIds().isEmpty()) {
            PartnerActionEvent event = new PartnerActionEvent();
            event.setPartnerIds(request.getPartnerIds());
            event.setPartner(true);
            event.setBrandId(request.getBrandId());
            event.setEventType(PartnerActionEventType.TAKEAWAY_STATUS_UPDATE);
            event.setEventData(request);
            publishEventToPartners(request.getPartnerIds(), event);
        }
        return true;
    }

    @Override
    public boolean updateCafeDeliveryTimeZomato(CafeTimingChangeRequest cafeTimingChangeRequest) {
        return zomatoService.updateCafeDeliveryTimeZomato(cafeTimingChangeRequest);
    }

    @Override
    public List<UnitPartnerLocalityMapping> getPendingLocalityMappings() {
        return unitPartnerLocalityMappingDao.getPendingLocalityMappings();
    }

    @Override
    public boolean updateLocalityMappings(List<UnitPartnerLocalityMapping> mappings) {
        if (mappings != null && !mappings.isEmpty()) {
            for (UnitPartnerLocalityMapping mapping : mappings) {
                Optional<UnitPartnerLocalityMapping> localityMappingData = unitPartnerLocalityMappingDao.findById(mapping.getMappingId());
                if (localityMappingData.isPresent() && localityMappingData.get().getPending()) {
                    localityMappingData.get().setPending(false);
                    localityMappingData.get().setKettleCity(mapping.getKettleCity());
                    localityMappingData.get().setKettleLocality(mapping.getKettleLocality());
                    unitPartnerLocalityMappingDao.save(localityMappingData.get());
                }
            }
        }
        return true;
    }

    private void pushMenuUpdateToQueue(Integer partnerId) {
        List<Integer> partnerIds = new ArrayList<>(1);
        partnerIds.add(partnerId);
        //if(channelPartnerDataCache.getUnitToPartnerMap())
        masterDataCache.getActiveUnitChannelPartnerMapping().forEach(unitChannelPartnerMapping -> {
            if (unitChannelPartnerMapping.getChannelPartner().getId() == partnerId) {
                Unit unit = masterDataCache.getUnit(unitChannelPartnerMapping.getUnit().getId());
                if (unit != null) {
                    pushMenuEventToQueue(partnerIds, unit.getId(), PartnerActionEventType.UPDATE_UNIT_MENU, AppConstants.CHAAYOS_BRAND_ID); //TODO update this
                }
            }
        });
    }

    private void pushMenuEventToQueue(List<Integer> partnerIds, Object data, PartnerActionEventType
            eventType, Integer brandId) {
        PartnerActionEvent event = new PartnerActionEvent();
        event.setPartnerIds(partnerIds);
        event.setEventType(eventType);
        event.setBrandId(brandId);
        event.setEventData(data);
        event.setPartner(true);
        publishEventToPartners(partnerIds, event);
    }

    @Override
    public boolean sendPartnerOffer(PartnerOfferDetail partnerOfferDetail) throws
            ChannelPartnerException, IllegalArgumentException, IllegalAccessException {
        List<PartnerOfferDetail> activePartnerOfferDetails = getActiveOffersForPartnerAndUnits(partnerOfferDetail.getUnitId(), partnerOfferDetail.getPartnerId());
        boolean status = zomatoService.sendMenuOffers(partnerOfferDetail, activePartnerOfferDetails);
        if (status) {
            Optional<PartnerOfferDetail> detailData = partnerOfferDao.findById(partnerOfferDetail.getId());
            if (!detailData.isPresent()) {
                throw new ChannelPartnerException("Partner detail is invalid.");
            }
            PartnerOfferDetail detail = detailData.get();
            //detail.setActive(partnerOfferDetail.getActive() == false ? true : false);
            detail.setActive(Boolean.FALSE.equals(partnerOfferDetail.getActive()));
            detail = partnerOfferDao.save(detail);
            if (Objects.isNull(detail.getId())) {
                LOG.error("error saving partner offer detail");
            }
        }
        return status;

    }

    private List<PartnerOfferDetail> getActiveOffersForPartnerAndUnits(Integer unitId, Integer partnerId) throws
            ChannelPartnerException {
        if (partnerId != null && channelPartnerDataCache.getPartnerCacheById().containsKey(partnerId)) {
            Date time = ChannelPartnerUtils.getCurrentTimestamp();
            return partnerOfferDao.getAllActiveOffersForPartnerAndUnit(partnerId, unitId, time);
        } else {
            throw new ChannelPartnerException("Partner id is invalid.");
        }
    }

    private void publishEventToPartners(List<Integer> partnerIds, PartnerActionEvent event) {
        partnerIds.forEach(partnerId -> {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            if (partnerDetail != null) {
                LOG.info("PUBLISHING REDIS EVENT " + event.getEventType().name() + " topic: " + partnerDetail.getPartnerName());
                String message = new Gson().toJson(event);
                redisPublisher.publish(partnerDetail.getPartnerName(), message);
            } else {
                LOG.error("Error publishing :::{} event for  partner id ::{}", event.getEventType().name(), partnerId + " is not valid.");
            }
        });
    }

    /*@Override
    public boolean addPartnerUnitSingleServeMenu(UnitMenuAddVO request) {
        if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
            if (partnerDetail != null) {
                partnerMenuService.addSingleServeMenuDetail(request);
            }
            return false; //zomatoService.addSingleServeMenu(request);
        }
        return false;
    }*/

    @Override
    public boolean getSwiggyStockVersionStatus() {
        return channelPartnerDataCache.getSwiggyStockVersionStatus();
    }

    @Override
    public boolean updateSwiggyStockVersionStatus(boolean status) {
        try {
            channelPartnerDataCache.setSwiggyStockVersionStatus(status);
            LOG.info("Swiggy Stock Version Status Updated Successfully");
            return true;
        } catch (Exception e) {
            LOG.info("Error While updating Swiggy Stock Version Status");
        }
        return false;
    }

    @Override
    public String addMenuVersionForPartner(UnitMenuAddVO request) throws ChannelPartnerException {
        if (request.getUnitId() != null && masterDataCache.getUnit(request.getUnitId()) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
            if (partnerDetail != null) {
                String currentVersion = partnerMenuService.getCurrentUnitMenuVersion(request, request.getUnitId());
                return partnerMenuService.addUnitMenuMapping(currentVersion, request, request.getUnitId());
            }
        }
        return null;
    }

    @Override
    public List<UnitMenuVersionData> getUnitMenuVersionData(Integer unitId, Integer kettlePartnerId, Integer brandId, String menuType) {
        if (unitId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId);
            if (partnerDetail != null) {
                return partnerMenuService.getUnitMenuVersionList(unitId, kettlePartnerId, brandId, menuType);
            }
        }
        return null;
    }

    @Override
    public List<UnitMenuVersionData> markUnitMenuVersionDef(Integer unitId, Integer kettlePartnerId, Integer brandId,
                                                            String version, String status, String menuType) {
        if (unitId != null && masterDataCache.getUnit(unitId) != null) {
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(kettlePartnerId);
            if (partnerDetail != null) {
                partnerMenuService.markUnitMenuVersionData(unitId, kettlePartnerId, brandId, version, status, menuType);
                return partnerMenuService.getUnitMenuVersionList(unitId, kettlePartnerId, brandId, menuType);
            }
        }
        return null;
    }

    @Override
    public PartnerUnitMenuDetail showVersionMenu(Integer unitId, Integer kettlePartnerId, Integer brandId, String version, String menuType) {
        return partnerMenuService.getUnitVersionMenu(unitId, kettlePartnerId, brandId, version, menuType);
    }

    @Override
    public boolean pushMenuToUnits(UnitMenuAddVO request) throws ChannelPartnerException {
        PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId());
        if (partnerDetail != null) {
            List<Integer> partnerIds = new ArrayList<>(1);
            partnerIds.add(partnerDetail.getKettlePartnerId());
            pushMenuEventToQueue(partnerIds, request, PartnerActionEventType.UPDATE_ALL_UNIT_MENU, request.getBrandId());
        } else {
            throw new ChannelPartnerException("Partner id is not valid!");
        }
        return true;
    }

    @Override
    public PartnerUnitProductStockSnapshot getUnitProductStockSnapshot(UnitPartnerBrandKey key, Integer productId) {
        UnitPartnerBrandMappingData brandMappingData = masterDataCache.getUnitPartnerBrandMappingMetaData().get(key);
        return partnerUnitStockSnapshotDao.findByUnitIdAndPartnerIdAndProductId(brandMappingData.getRestaurantId(), brandMappingData.getPartnerId(), productId);
    }

    @Override
    public List<CafeMenuAutoPush> menuAutoPush(List<CafeMenuAutoPush> data) {
        LOG.info("Storing the Auto menu Push value in database ");
        List<CafeMenuAutoPush> updatedList = new ArrayList<>();
        if (data != null && !data.isEmpty()) {
            for (CafeMenuAutoPush cafeAutoFlagData : data) {
                menuHistoryDataSave(cafeAutoFlagData);
                CafeMenuAutoPush current = cafeMenuAutoPushDao.findByUnitIdAndBrandId(cafeAutoFlagData.getUnitId(), cafeAutoFlagData.getBrandId());
                if (current == null) {
                    current = cafeAutoFlagData;
                } else {
                    current.setZomatoMenu(cafeAutoFlagData.getZomatoMenu());
                    current.setSwiggyMenu(cafeAutoFlagData.getSwiggyMenu());
                }
                current.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimestamp());
                current.setLastUpdatedTimeString(ChannelPartnerUtils.getCurrentTimeISTString());
                current.setEmployeeId(cafeAutoFlagData.getEmployeeId());
                updatedList.add(cafeMenuAutoPushDao.save(current));
            }
        }
        LOG.info("Successfully Updated Auto Push Value in database");
        return updatedList;
    }

    private void menuHistoryDataSave(CafeMenuAutoPush cafeAutoFlagData) {
        LOG.info("Storing the Auto menu Push  value in  History LOG database ");
        CafeMenuAutoPushLog menuHistoryData = new CafeMenuAutoPushLog();
        menuHistoryData.setUnitId(cafeAutoFlagData.getUnitId());
        menuHistoryData.setUnitName(cafeAutoFlagData.getUnitName());
        menuHistoryData.setBrandId(cafeAutoFlagData.getBrandId());
        menuHistoryData.setZomatoMenuFlag(cafeAutoFlagData.getZomatoMenu());
        menuHistoryData.setSwiggyMenuFlag(cafeAutoFlagData.getSwiggyMenu());
        menuHistoryData.setLastUpdatedTime(ChannelPartnerUtils.getCurrentTimestamp());
        menuHistoryData.setLastUpdatedTimeString(ChannelPartnerUtils.getCurrentTimeISTString());
        menuHistoryData.setEmployeeId(cafeAutoFlagData.getEmployeeId());
        cafeMenuAutoPushLogDao.save(menuHistoryData);
        LOG.info("Successfully Updated Auto Push LOG  Value in database");
    }

    @Override
    public List<CafeMenuAutoPush> showMenuData(Integer brandId) {
        return cafeMenuAutoPushDao.findByBrandId(brandId);
    }

    @Override
    public List<CafeMenuAutoPushLog> menuAutoPushHistory(Integer brandId, Integer unitId) {
        return cafeMenuAutoPushLogDao.findTop15ByBrandIdAndUnitIdOrderByLastUpdatedTimeStringDesc(brandId, unitId);
    }

    @Override
    public List<DesiChaiCustomProfiles> getDesiChaiCustomProfiles() {
        return desiChaiCustomProfileDao.findAll();
    }

    @Override
    public DesiChaiCustomProfiles addDesiChaiCustomProfile(DesiChaiCustomProfiles profile) throws ChannelPartnerException {
        if (profile.getProfileName() != null && profile.getProfileType() != null) {
            DesiChaiCustomProfiles customProfiles = desiChaiCustomProfileDao.findByProfileName(profile.getProfileName());
            if (customProfiles != null) {
                throw new ChannelPartnerException("Profile by this name already exists");
            }
            customProfiles = desiChaiCustomProfileDao.save(profile);
            channelPartnerDataCache.loadDesiChaiCustomProfilesMap();
            return customProfiles;
        }
        return null;
    }

    @Override
    public List<DesiChaiCustomProfileMappings> addDesiChaiCustomProfileMappings(List<DesiChaiCustomProfileMappings> mappings) {
        List<Integer> unitIds = new ArrayList<>();
        mappings.forEach(desiChaiCustomProfileMappings ->
                unitIds.add(desiChaiCustomProfileMappings.getUnitId())
        );
        List<Integer> existingMappingUnits = desiChaiCustomProfileMappingsDao.findAllByUnitIdInAndPartnerIdAndBrandIdAndProfileId(unitIds,
                        mappings.get(0).getPartnerId(), mappings.get(0).getBrandId(), mappings.get(0).getProfileId()).stream()
                .map(DesiChaiCustomProfileMappings::getUnitId).collect(Collectors.toList());
        mappings = mappings.stream().filter(desiChaiCustomProfileMappings ->
                !existingMappingUnits.contains(desiChaiCustomProfileMappings.getUnitId())).collect(Collectors.toList());
        return desiChaiCustomProfileMappingsDao.saveAll(mappings);
    }

    @Override
    public List<DesiChaiCustomProfileMappings> getDesiChaiCustomProfileMappings(PartnerUnitListVO request) {
        if (request != null && request.getUnitIds() != null && !request.getUnitIds().isEmpty() &&
                request.getPartnerId() != null && request.getBrandId() != null) {
            return desiChaiCustomProfileMappingsDao.findAllByUnitIdInAndPartnerIdAndBrandId(request.getUnitIds(),
                    request.getPartnerId(), request.getBrandId());
        } else {
            return desiChaiCustomProfileMappingsDao.findAll();
        }
    }

    @Override
    public DesiChaiCustomProfileMappings updateDesiChaiCustomProfileMappingStatus(DesiChaiCustomProfileMappings mapping) throws ChannelPartnerException {
        if (mapping != null) {
            Optional<DesiChaiCustomProfileMappings> profileMappingsData = desiChaiCustomProfileMappingsDao.findById(mapping.getId());
            if (profileMappingsData.isPresent()) {
                DesiChaiCustomProfileMappings profileMappings = profileMappingsData.get();
                profileMappings.setStatus(mapping.getStatus());
                return desiChaiCustomProfileMappingsDao.save(profileMappings);
            } else {
                throw new ChannelPartnerException("Profile mapping is invalid");
            }
        }
        throw new ChannelPartnerException("Please send mapping details");
    }

    @Override
    public List<DesiChaiCustomProfiles> getDesiChaiCustomProfilesForUnit(Integer unitId, Integer partnerId) throws ChannelPartnerException {
        if (unitId != null) {
            List<DesiChaiCustomProfileMappings> profileMappings = desiChaiCustomProfileMappingsDao.findAllByUnitIdAndStatusAndPartnerId(unitId, AppConstants.ACTIVE, partnerId);
            if (profileMappings != null && !profileMappings.isEmpty()) {
                List<String> profiles = profileMappings.stream().map(DesiChaiCustomProfileMappings::getProfileId).collect(Collectors.toList());
                List<DesiChaiCustomProfiles> customProfiles = desiChaiCustomProfileDao.findAllByIdIn(profiles);
                if (!CollectionUtils.isEmpty(customProfiles)) {
                    customProfiles.stream()
                            .filter(profile -> profile.getProfileName().equals(environmentProperties.getDesiChaiProfileNameForSuperCombo()))
                            .forEach(profile -> profile.setSuperComboProduct(true));
                }
                return customProfiles;
            }
            return new ArrayList<>();
        }
        throw new ChannelPartnerException("Please send unit id");
    }

    @Override
    public void setUnitAvailability(String requestId, Integer partnerId, Boolean forceUpdate) {
        if (partnerId.equals(AppConstants.CHANNEL_PARTNER_ZOMATO)) {
            Optional<ZomatoCafeStatusData> zomatoCafeStatusData = zomatoCafeStatusDao.findById(requestId);
            zomatoCafeStatusData.ifPresent(cafeStatusData -> setZomatoUnitAvailability(partnerId, cafeStatusData, forceUpdate));
        } else {
            Optional<SwiggyCafeStatusData> swiggyCafeStatusData = swiggyCafeStatusDataDao.findById(requestId);
            swiggyCafeStatusData.ifPresent(cafeStatusData -> setSwiggyUnitAvailability(partnerId, cafeStatusData, forceUpdate));
        }
    }

    private void setSwiggyUnitAvailability(Integer partnerId, SwiggyCafeStatusData swiggyCafeStatusData, Boolean forceUpdate) {
        if (swiggyCafeStatusData.isUpdationRequest() && forceUpdate.equals(false)) {
            LOG.info("Already Tried To Update Cafe Status for Unit {}", swiggyCafeStatusData.getUnitId());
            return;
        }
        try {
            activateCafeStatus(partnerId, swiggyCafeStatusData.getUnitId(), swiggyCafeStatusData.getBrandId());
            swiggyCafeStatusData.setUpdationRequest(true);
            swiggyCafeStatusDataDao.save(swiggyCafeStatusData);
        } catch (Exception e) {
            LOG.error("Exception Caught While Updating Cafe Status for Unit {}", swiggyCafeStatusData.getUnitId());
        }
    }

    private void setZomatoUnitAvailability(Integer partnerId, ZomatoCafeStatusData zomatoCafeStatusData, Boolean forceUpdate) {
        if (zomatoCafeStatusData.isUpdationRequest() && forceUpdate.equals(false)) {
            LOG.info("Already Tried To Update Cafe Status for Unit {}", zomatoCafeStatusData.getUnitId());
            return;
        }
        try {
            activateCafeStatus(partnerId, zomatoCafeStatusData.getUnitId(), zomatoCafeStatusData.getBrandId());
            zomatoCafeStatusData.setUpdationRequest(true);
            zomatoCafeStatusDao.save(zomatoCafeStatusData);
        } catch (Exception e) {
            LOG.error("Exception Caught While Updating Cafe Status for Unit {}", zomatoCafeStatusData.getUnitId());
        }
    }

    private void activateCafeStatus(Integer partnerId, Integer unitId, Integer brandId) throws ChannelPartnerException {
        LOG.info("Activating Cafe Status for Unit {} PartnerId {}", unitId, partnerId);
        UnitPartnerStatusVO unitPartnerStatusVO = new UnitPartnerStatusVO();
        unitPartnerStatusVO.setPartnerIds(Collections.singletonList(partnerId));
        unitPartnerStatusVO.setBrandId(brandId);
        unitPartnerStatusVO.setUnitIds(Collections.singletonList(unitId));
        unitPartnerStatusVO.setStatus(true);
        //See here
        setUnitAvailability(unitPartnerStatusVO);
    }

    @Override
    public boolean setOffUnitAvailibilityFromKnockApp(UnitAutoSwitchOff request, boolean status) {
        UnitPartnerStatusVO unitPartnerStatusVO = new UnitPartnerStatusVO();
        if (!Objects.equals(request.getUnitId(), "") && !Objects.equals(request.getBrandId(), "")) {
            if (Objects.equals(request.getPartnerName(), AppConstants.BOTH_SWIGGY_ZOMATO)) {
                return setBothSwiggyZomatoOff(request, status);
            }
            unitPartnerStatusVO.setUnitIds(Collections.singletonList(Integer.parseInt(request.getUnitId())));
            unitPartnerStatusVO.setBrandId(Integer.parseInt(request.getBrandId()));
            if (Objects.equals(request.getPartnerName(), AppConstants.SWIGGY)) {
                unitPartnerStatusVO.setPartnerIds(Collections.singletonList(AppConstants.CHANNEL_PARTNER_SWIGGY));
            } else if (Objects.equals(request.getPartnerName(), AppConstants.ZOMATO)) {
                unitPartnerStatusVO.setPartnerIds(Collections.singletonList(AppConstants.CHANNEL_PARTNER_ZOMATO));
            }
            unitPartnerStatusVO.setStatus(status);
            LOG.info("Value of unit Partner Status:{}", new Gson().toJson(unitPartnerStatusVO));
            try {
                return setUnitAvailability(unitPartnerStatusVO);
            } catch (Exception e) {
                LOG.info("Error in Switching off unit", e);
                return false;
            }
        }
        return false;
    }

    public boolean setBothSwiggyZomatoOff(UnitAutoSwitchOff request, boolean status) {
        UnitPartnerStatusVO unitPartnerStatusVOSwiggy = new UnitPartnerStatusVO();
        UnitPartnerStatusVO unitPartnerStatusVOZomato = new UnitPartnerStatusVO();
        unitPartnerStatusVOSwiggy.setUnitIds(Collections.singletonList(Integer.parseInt(request.getUnitId())));
        unitPartnerStatusVOSwiggy.setBrandId(Integer.parseInt(request.getBrandId()));
        unitPartnerStatusVOSwiggy.setPartnerIds(Collections.singletonList(AppConstants.CHANNEL_PARTNER_SWIGGY));
        unitPartnerStatusVOSwiggy.setStatus(status);
        unitPartnerStatusVOZomato.setUnitIds(Collections.singletonList(Integer.parseInt(request.getUnitId())));
        unitPartnerStatusVOZomato.setBrandId(Integer.parseInt(request.getBrandId()));
        unitPartnerStatusVOZomato.setPartnerIds(Collections.singletonList(AppConstants.CHANNEL_PARTNER_ZOMATO));
        unitPartnerStatusVOZomato.setStatus(status);
        try {
            return (setUnitAvailability(unitPartnerStatusVOSwiggy) && setUnitAvailability(unitPartnerStatusVOZomato));
        } catch (Exception e) {
            LOG.info("Error in Switching off unit", e);
            return false;
        }
    }

    @Override
    public boolean activateCafeUnitDashboard(Integer id, Integer partnerId, Integer unitId, Integer brandId) {
        LOG.info("Activating Cafe Status for Unit {} PartnerId {}", unitId, partnerId);
        UnitPartnerStatusVO unitPartnerStatusVO = new UnitPartnerStatusVO();
        unitPartnerStatusVO.setPartnerIds(Collections.singletonList(partnerId));
        unitPartnerStatusVO.setBrandId(brandId);
        unitPartnerStatusVO.setUnitIds(Collections.singletonList(unitId));
        unitPartnerStatusVO.setStatus(true);
        //See here
        try {
            boolean response = setUnitAvailability(unitPartnerStatusVO);

            if (response) {
                cafeStatusChannelPartnerService.deleteActivatedCafe(id);
            }
            return response;
        } catch (Exception e) {
            LOG.info("Error in activateCafeUnitDashboard");
            return false;
        }
    }

    @Override
    public DealOfTheDay getDotdProducts(Integer kettlePartnerId) {
        return dealOfTheDayDao.findByKettlePartnerId(kettlePartnerId);
    }


    public DotdProduct setDotdProductToList(DotdProductsRequest dotdProductsRequest) {
        DotdProduct dotdProduct = new DotdProduct();
        dotdProduct.setProductId(dotdProductsRequest.getProductId());
        dotdProduct.setProductName(dotdProductsRequest.getProductName());
        List<DotdProductDimensions> dotdProductDimensionsList = new ArrayList<>();
        for (DotdProductDimensionsRequest dotdProductDimensionsRequest : dotdProductsRequest.getDotdProductDimensionsList()) {

            DotdProductDimensions dotdProductDimensions = new DotdProductDimensions();
            dotdProductDimensions.setDimensionId(dotdProductDimensionsRequest.getDimensionId());
            dotdProductDimensions.setDimensionName(dotdProductDimensionsRequest.getDimensionName());
            dotdProductDimensions.setDimensionCode(dotdProductDimensionsRequest.getDimensionCode());

            dotdProductDimensionsList.add(dotdProductDimensions);
        }
        dotdProduct.setDotdProductDimensionsList(dotdProductDimensionsList);
        return dotdProduct;
    }

    public List<DotdProduct> settingDOTDProducts(DealOfTheDayRequest dealOfTheDayRequest) {
        List<DotdProduct> dotdProductList = new ArrayList<>();

        for (DotdProductsRequest dotdProductsRequest : dealOfTheDayRequest.getDotdProducts()) {
            dotdProductList.add(setDotdProductToList(dotdProductsRequest));
        }

        return dotdProductList;
    }

    @Override
    public boolean setProductsForDOTD(DealOfTheDayRequest dealOfTheDayRequest) {

        if (dealOfTheDayRequest != null && dealOfTheDayRequest.getKettlePartnerId() != null) {
            DealOfTheDay dealOfTheDay = dealOfTheDayDao.findByKettlePartnerId(dealOfTheDayRequest.getKettlePartnerId());
            if (dealOfTheDay == null) {

                DealOfTheDay dealOfTheDayTemp = new DealOfTheDay();
                dealOfTheDayTemp.setKettlePartnerId(dealOfTheDayRequest.getKettlePartnerId());
                dealOfTheDayTemp.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(dealOfTheDayRequest.getKettlePartnerId()).getPartnerName());

                dealOfTheDayTemp.setDotdProducts(settingDOTDProducts(dealOfTheDayRequest));
                dealOfTheDay = dealOfTheDayTemp;
            } else {
                dealOfTheDay.setDotdProducts(settingDOTDProducts(dealOfTheDayRequest));
            }

            dealOfTheDay.setProductIds(dealOfTheDayRequest.getProductIds());
            dealOfTheDayDao.save(dealOfTheDay);
            LOG.info("Saved DOTD Products");
            return true;
        }
        LOG.info("Unable to Save DOTD Products");
        return false;
    }

    @Override
    public <T> boolean sendRequestBasisRequestType(T reqObj, PartnerRequestType requestType, List<Integer> partnerIds) throws ChannelPartnerException {
        Map<PartnerRequestType, CheckedFunction<UnitMenuAddVO, Boolean>> requestTypeMap = Map.of(
                PartnerRequestType.UPDATE_UNIT_MENU, this::addPartnerUnitMenu,
                PartnerRequestType.UPDATE_MENU, this::addPartnerRegionMenu,
                PartnerRequestType.UPDATE_ALL_UNIT_MENU, this::pushMenuToUnits
        );

        Map<PartnerRequestType, CheckedBiFunction<UnitMenuAddVO, List<Integer>, Boolean>> requestTypeMapWithPartnerIds = Map.of(
                PartnerRequestType.SCHEDULED_MENU_PUSH, this::scheduledMenuPush
        );

        if (requestTypeMap.containsKey(requestType)) {
            CheckedFunction<UnitMenuAddVO, Boolean> methodToCall = requestTypeMap.get(requestType);
            return executeFunction((UnitMenuAddVO) reqObj, methodToCall);
        } else if (requestTypeMapWithPartnerIds.containsKey(requestType)) {
            CheckedBiFunction<UnitMenuAddVO, List<Integer>, Boolean> methodToCall = requestTypeMapWithPartnerIds.get(requestType);
            return executeFunctionWithPartnerIds((UnitMenuAddVO) reqObj, partnerIds, methodToCall);
        } else {
            throw new ChannelPartnerException("No suitable implementation found for the requestType " + requestType.name());
        }
    }

    private boolean executeFunction(UnitMenuAddVO reqObj, CheckedFunction<UnitMenuAddVO, Boolean> function) throws ChannelPartnerException {
        try {
            return function.apply(reqObj);
        } catch (Exception e) {
            throw new ChannelPartnerException("Exception occurred while executing function: " + e.getMessage());
        }
    }

    private boolean executeFunctionWithPartnerIds(UnitMenuAddVO reqObj, List<Integer> partnerIds, CheckedBiFunction<UnitMenuAddVO, List<Integer>, Boolean> function) throws ChannelPartnerException {
        try {
            return function.apply(reqObj, partnerIds);
        } catch (Exception e) {
            throw new ChannelPartnerException("Exception occurred while executing function: " + e.getMessage());
        }
    }

    @Override
    public boolean scheduledMenuPush(UnitMenuAddVO request, List<Integer> partnerIds) {
        try {
            List<Integer> unitIdsToFilter = environmentProperties.getUnitIdsForMenu199();
            List<Integer> unitIdsToFilterForEveningMenu = environmentProperties.getUnitIdsForMenu299();
            LOG.info("Filter Unit Ids : {}" , new Gson().toJson(unitIdsToFilter));
            LOG.info("Filter Unit Ids For Menu 299 : {}" , new Gson().toJson(unitIdsToFilterForEveningMenu));
            List<Integer> originalUnitIds = request.getUnitIdsForMenu();
            if((Objects.isNull(request.getBrandId()) ||  AppConstants.GNT_BRAND_ID != request.getBrandId()) && partnerIds.get(0).equals(3)
                    && (MenuType.DAY_SLOT_BREAKFAST.equals(request.getMenuType()) ||
                    MenuType.DAY_SLOT_OVERNIGHT.equals(request.getMenuType()))){
                List<Integer> unitIds = request.getUnitIdsForMenu().stream().filter(unitId -> !unitIdsToFilter.contains(unitId))
                        .collect(Collectors.toList());
                request.setUnitIdsForMenu(unitIds);
            }
            if(!partnerIds.get(0).equals(-2) && MenuType.DAY_SLOT_EVENING.equals(request.getMenuType())){
                List<Integer> unitIds = request.getUnitIdsForMenu().stream().filter(unitId -> !unitIdsToFilterForEveningMenu.contains(unitId))
                        .collect(Collectors.toList());
                request.setUnitIdsForMenu(unitIds);
            }
            if(partnerIds.get(0).equals(-1)){
                partnerIds = new ArrayList<>(Arrays.asList(3));
            }
            if(partnerIds.get(0).equals(-2)){
                partnerIds = new ArrayList<>(Arrays.asList(3,6));
            }
            pushMenuEventToQueue(partnerIds, request, PartnerActionEventType.SCHEDULED_MENU_PUSH, request.getBrandId());
            request.setUnitIdsForMenu(originalUnitIds);
        } catch (Exception e) {
            LOG.error("Exception occurred while bulk scheduled menu push :::::::::::::::::", e);
            return false; // Return false to indicate failure
        }
        return true;
    }

    @Override
    public List<Integer> getFilteredProductForNoPackagingCharges(Integer partnerId,Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productFilters = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_FILTER_LIST_NO_PACKAGING_CHARGES);
            JsonArray tagsArray = new Gson().fromJson(productFilters, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    try {
                        FilteredProductsVO mapping = new Gson().fromJson(item, FilteredProductsVO.class);
                        if (mapping.getBrandId().equals(brandId)) {
                            return mapping.getProductIds();
                        }
                    } catch (Exception e) {
                        return null;
                    }
                }
            }
        }
        return null;
    }

    public Set<Integer> getProductIdsForSplitDimension(){
        return ChannelPartnerUtils.convertCommaSeparatedStringToList(environmentProperties.getProductIdsForSplitDimension());
    }
}
