package com.stpl.tech.kettle.channelpartner.core.redis.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.EventAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerAbstractFactory;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerActionEventDecorator;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerBuilderFactory;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.ServiceFactory;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


@Component
@Log4j2
public class PartnerActionListenerImpl implements MessageListener {

    private PartnerActionEventDecorator postEventProcessor;
    private PartnerActionEventDecorator postReqToPartnerProcessor;
    private EventAbstractFactory<?, ?> eventAbstractFactory;
    private PartnerAbstractFactory partnerAbstractFactory;

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Autowired
    private PartnerMenuConverterDependency partnerMenuConverterDependency;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    private ChannelTopic channelTopic = new ChannelTopic("COMMON_PARTNER_ACTION_EVENT_CHANNEL");

    @PostConstruct
    public void subscribe() {
        redisMessageListenerContainer.addMessageListener(this, channelTopic);
    }


    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            PartnerActionEvent event = new Gson().fromJson(message.toString(), PartnerActionEvent.class);
            if (event != null) {
                log.info("Processing Partner Action: " + new Gson().toJson(event));
                try {
                    Integer unitId;
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    Class<?> reqObj = null;
                    for (EventType eventType : EventType.values()) {
                        if (eventType.getEvents().contains(event.getEventType().name())) {
                            reqObj = eventType.getAssociatedClass();
                            setPostEventProcessor(eventType.name(), event, reqObj);
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("Error processing Redis event::::::::::::::: {}" ,new Gson().toJson(event), e);
                }
            }
        } catch (Exception e) {
            log.error("error: ", e);
        }
    }

    private void setPostEventProcessor(String eventType, PartnerActionEvent event, Class<?> reqObj) throws ChannelPartnerException {
        for (Integer partnerId : event.getPartnerIds()) {
            final PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            final PartnerMetadataBuilder partnerMetadataBuilder = PartnerBuilderFactory.getInstance(partnerMenuConverterDependency);
            setPartnerAbstractFactory(partnerDetail.getPartnerName(), partnerMetadataBuilder);
            setPartnerAbstractEventFactory(eventType,partnerMetadataBuilder);
            setPostReqToPartnerProcessor(partnerDetail.getPartnerName(), eventType, this.partnerAbstractFactory);
            setPostEventProcessor(eventType, this.partnerAbstractFactory, this.postReqToPartnerProcessor, partnerMetadataBuilder);
            this.postEventProcessor.processRedisEvent(partnerDetail.getPartnerName(), event, reqObj, this.eventAbstractFactory);
        }
    }

    private void setPostEventProcessor(String eventType, PartnerAbstractFactory partnerAbstractFactory, PartnerActionEventDecorator postReqToPartnerProcessor, PartnerMetadataBuilder partnerMetadataBuilder) {
        this.postEventProcessor = ServiceFactory.getPostEventProcessorService(eventType, partnerAbstractFactory,postReqToPartnerProcessor, partnerMetadataBuilder);
    }

    private void setPostReqToPartnerProcessor(String partnerName, String eventType, PartnerAbstractFactory partnerAbstractFactory) {
        this.postReqToPartnerProcessor = ServiceFactory.getPostReqToPartnerService(eventType, partnerAbstractFactory);
    }

    private void setPartnerAbstractEventFactory(String eventType, PartnerMetadataBuilder partnerMetadataBuilder) {
        this.eventAbstractFactory = ServiceFactory.getPartnerEventFactoryService(eventType, partnerMetadataBuilder);
    }

    private void setPartnerAbstractFactory(String partner, PartnerMetadataBuilder partnerMetadataBuilder) {
        this.partnerAbstractFactory = ServiceFactory.getPartnerAbstractFactory(partner, partnerMetadataBuilder);
    }
}
