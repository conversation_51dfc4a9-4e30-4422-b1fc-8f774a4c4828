package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"value",
	"vendorEntityId",
	"kind",
	"unit"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCatPropPropertyVal {
	
	
	@JsonProperty("value")
	private String value;
	@JsonProperty("vendorEntityId")
	private String vendorEntityId;
	@JsonProperty("kind")
	private String kind;
	@JsonProperty("unit")
	private String unit;
	
	@JsonProperty("value")
	public String getValue() {
		return value;
	}
	
	@JsonProperty("value")
	public void setValue(String value) {
		this.value = value;
	}
	
	@JsonProperty("vendorEntityId")
	public String getVendorEntityId() {
		return vendorEntityId;
	}
	
	@JsonProperty("vendorEntityId")
	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}

	@JsonProperty("kind")
	public String getKind() {
		return kind;
	}

	@JsonProperty("kind")
	public void setKind(String kind) {
		this.kind = kind;
	}

	@JsonProperty("unit")
	public String getUnit() {
		return unit;
	}

	@JsonProperty("unit")
	public void setUnit(String unit) {
		this.unit = unit;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoCatPropPropertyVal that = (ZomatoCatPropPropertyVal) o;

		return new EqualsBuilder()
			.append(value, that.value)
			.append(vendorEntityId, that.vendorEntityId)
			.append(kind, that.kind)
			.append(unit, that.unit)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(value)
			.append(vendorEntityId)
			.append(kind)
			.append(unit)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoCatPropPropertyVal{" +
			"value='" + value + '\'' +
			", vendorEntityId='" + vendorEntityId + '\'' +
			", kind='" + kind + '\'' +
			", unit='" + unit + '\'' +
			'}';
	}
}
