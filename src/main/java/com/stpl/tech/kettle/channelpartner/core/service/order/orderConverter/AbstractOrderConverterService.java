package com.stpl.tech.kettle.channelpartner.core.service.order.orderConverter;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.OrderItemConverterService;
import com.stpl.tech.kettle.channelpartner.core.service.order.factory.OrderFactory;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPrimaryData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public abstract class AbstractOrderConverterService {

    protected OrderItemConverterService orderItemConverterService;

    protected EnvironmentProperties environmentProperties;

    protected ChannelPartnerDataCache channelPartnerDataCache;

    protected WebServiceHelper webServiceHelper;

    protected MasterDataCache masterDataCache;

    protected OrderValidationService orderValidationService;


    AbstractOrderConverterService(OrderItemConverterService orderItemConverterService ,
                                  EnvironmentProperties environmentProperties ,
                                  ChannelPartnerDataCache channelPartnerDataCache , WebServiceHelper webServiceHelper
    , MasterDataCache masterDataCache , OrderValidationService orderValidationService){
        this.orderItemConverterService = orderItemConverterService;
        this.environmentProperties  =  environmentProperties;
        this.channelPartnerDataCache  = channelPartnerDataCache;
        this.webServiceHelper = webServiceHelper;
        this.masterDataCache = masterDataCache;
        this.orderValidationService = orderValidationService;

    }

    public abstract <R> Order convertOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual
    , PartnerPrimaryData partnerPrimaryData);
}
