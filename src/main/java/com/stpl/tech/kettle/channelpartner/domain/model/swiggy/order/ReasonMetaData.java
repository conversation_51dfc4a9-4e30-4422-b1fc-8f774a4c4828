package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReasonMetaData {
    @JsonProperty(value = "next_available_time_epoch")
    private long nextAvailableTimeEpoch;

    @JsonProperty(value = "out_of_stock_items")
    private List<StockOutItems> outOfStockItems;

}
