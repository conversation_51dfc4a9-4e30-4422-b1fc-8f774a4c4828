package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"id", "third_party_id", "displayText", "quantity", "amount", "tax", "subItems", "primary_type", "taxCharges", "charges", "taxLiability", "shipmentId"})
public class MagicpinOrderItems {

    @JsonProperty("id")
    private Long id;
    @JsonProperty("third_party_id")
    private String thirdPartyId;
    @JsonProperty("displayText")
    private String displayText;
    @JsonProperty("quantity")
    private Float quantity;
    @JsonProperty("amount")
    private Float amount;
    @JsonProperty("tax")
    private Float tax;

    @JsonProperty("discount")
    private Float discount;
    @JsonProperty("discountedAmount")
    private Float discountedAmount;
    @JsonProperty("discountedTax")
    private Float discountedTax;


    @JsonProperty("baseAmount")
    private Float baseAmount;


    @JsonProperty("baseTax")
    private  Float baseTax;

    @JsonProperty("baseDiscount")
    private Float baseDiscount;
    @JsonProperty("subItems")
    private List<MagicpinOrderItemAddons> magicpinOrderItemAddonsList;
    @JsonProperty("primary_type")
    private String primaryType;
    @JsonProperty("taxCharges")
    private List<MagicpinTaxDetails> taxCharges;
    @JsonProperty("charges")
    private List<MagicpinChargesDetails> magicpinChargesDetails;
    @JsonProperty("taxLiability")
    private String taxLiability;
    @JsonProperty("shipmentId")
    private Long shipmentId;

    //Field For Our Distributed Discount Weightage
    @Builder.Default
    private BigDecimal distributedDiscountWieght = BigDecimal.ZERO;
}
