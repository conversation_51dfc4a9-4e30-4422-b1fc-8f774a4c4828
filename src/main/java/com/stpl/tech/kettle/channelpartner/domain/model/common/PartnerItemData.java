package com.stpl.tech.kettle.channelpartner.domain.model.common;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.concurrent.BackgroundInitializer;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerItemData {
    String itemId;
    String itemName;

    Integer productId;

    Integer originalProductId;

    Integer quantity;

    String dimensionCode;

    BigDecimal unitPrice;

    BigDecimal totalItemPrice;

    Boolean taxDeductedByPartner;
    boolean comboProduct ;

    String taxPayingEntity;
    BigDecimal discount ;
    BigDecimal discountPercent ;

    BigDecimal discountWeight;

    @Builder.Default
    BigDecimal taxRate = BigDecimal.ZERO;

    @Builder.Default
    List<PartnerItemAddonData> paidAddonProducts = new ArrayList<>();

    @Builder.Default
    List<PartnerItemAddonData> recommendedProducts = new ArrayList<>();

    @Builder.Default
    List<PartnerItemAddonData> addonProducts = new ArrayList<>();



}
