package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.menu.builder.PartnerMetadataBuilder;
import com.stpl.tech.kettle.channelpartner.core.service.menu.factory.PartnerMenuConverterDependency;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.EventType;
import com.stpl.tech.kettle.channelpartner.domain.model.StockReqMetadata;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.PartnerEventResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenuRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public abstract class PartnerAbstractFactory {
    protected MasterDataCache masterDataCache;
    protected PartnerMenuService partnerMenuService;
    protected EnvironmentProperties environmentProperties;
    protected WebServiceHelper webServiceHelper;
    protected ChannelPartnerDataCache channelPartnerDataCache;
    protected CommonPartnerEventNotificationService<String, SlackNotification, Boolean,String> commonPartnerEventNotificationService;
    protected PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;
    protected PartnerOrderService partnerOrderService;
    protected OrderValidationService orderValidationService;

    public PartnerAbstractFactory(PartnerMetadataBuilder partnerMetadataBuilder) {
        this.masterDataCache = partnerMetadataBuilder.masterDataCache;
        this.partnerMenuService = partnerMetadataBuilder.partnerMenuService;
        this.environmentProperties = partnerMetadataBuilder.environmentProperties;
        this.webServiceHelper = partnerMetadataBuilder.webServiceHelper;
        this.channelPartnerDataCache = partnerMetadataBuilder.channelPartnerDataCache;
        this.commonPartnerEventNotificationService = partnerMetadataBuilder.commonPartnerEventNotificationService;
        this.partnerUnitStockSnapshotDao = partnerMetadataBuilder.partnerUnitStockSnapshotDao;
        this.partnerOrderService = partnerMetadataBuilder.partnerOrderService;
        this.orderValidationService =partnerMetadataBuilder.orderValidationService;
    }

    public <T, R> R convertToPartnerMenu(T obj, Class<R> returnType) throws ChannelPartnerException {
        return null;
    }


    public <R> R sendMenuPushReqToPartner(UnitMenuAddVO unitMenuAddVO) {
        return null;
    }


    public <T> List<PartnerEventResponse> postEventProcessing(List<T> obj, EventType eventType) throws ChannelPartnerException {
        return new ArrayList<>();
    }

    public <T> T convertToPartnerStockEvent(T obj) throws ChannelPartnerException {
        return null;
    }

    public <R> R sendStockUpdateReqToPartner(StockReqMetadata stockReqMetadata) {
        return null;
    }

    public  <T> T updateStockValueForMenuPush( T menuReq, UnitPartnerBrandKey key){return null;};
}