package com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "startTime", "endTime" })
public class ZomatoRestOfferTimmings {
	
	@JsonProperty("startTime")
	private String startTime;
	@JsonProperty("endTime")
	private Float endTime;
	
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public Float getEndTime() {
		return endTime;
	}
	public void setEndTime(Float endTime) {
		this.endTime = endTime;
	}
	@Override
	public String toString() {
		return "ZomatoRestOfferTimmings [startTime=" + startTime + ", endTime=" + endTime + "]";
	}
	
	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(startTime).append(endTime).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoRestOfferTimmings) == false) {
			return false;
		}
		ZomatoRestOfferTimmings rhs = ((ZomatoRestOfferTimmings) other);
		return new EqualsBuilder().append(startTime, rhs.startTime).append(endTime, rhs.endTime)
				.isEquals();
	}

	
	
	
	

}
