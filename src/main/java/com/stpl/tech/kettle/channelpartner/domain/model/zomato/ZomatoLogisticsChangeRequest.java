package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ZomatoLogisticsChangeRequest {

    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("self_delivery_serviceability_status")
    private Boolean selfDeliveryServiceabilityStatus;
    @JsonProperty("revert_seconds")
    private Integer revertSeconds;
    @JsonProperty("reason")
    private String reason;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("self_delivery_serviceability_status")
    public Boolean getSelfDeliveryServiceabilityStatus() {
        return selfDeliveryServiceabilityStatus;
    }

    @JsonProperty("self_delivery_serviceability_status")
    public void setSelfDeliveryServiceabilityStatus(Boolean selfDeliveryServiceabilityStatus) {
        this.selfDeliveryServiceabilityStatus = selfDeliveryServiceabilityStatus;
    }

    @JsonProperty("revert_seconds")
    public Integer getRevertSeconds() {
        return revertSeconds;
    }

    @JsonProperty("revert_seconds")
    public void setRevertSeconds(Integer revertSeconds) {
        this.revertSeconds = revertSeconds;
    }

    @JsonProperty("reason")
    public String getReason() {
        return reason;
    }

    @JsonProperty("reason")
    public void setReason(String reason) {
        this.reason = reason;
    }
}