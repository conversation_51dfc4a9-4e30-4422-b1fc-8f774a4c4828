package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MagicPinStatusUpdate {

    @JsonProperty("orderId")
    private Long orderId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("thirdPartyOrderId")
    private String thirdPartyOrderId;

    @JsonProperty("cancelReason")
    private String cancelReason;

    @JsonProperty("rejectionId")
    private Integer rejectionId;
}
