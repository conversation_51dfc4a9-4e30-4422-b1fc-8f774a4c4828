package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="ORDER_COMMISSION")
public class OrderComission implements Serializable {
    private static final long serialVersionUID = 3630399315896913057L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "PARTNER_ORDER_ID", nullable = false)
    private String partnerOrderId;
    @Column(name="KETTLE_ORDER_ID")
    private int kettleOrderId;
    @Column(name = "UNIT_ID")
    private int unitId;
    @Column(name = "SUB_TOTAL")
    private BigDecimal subTotal;
    @Column(name = "PACKAGING_CHARGES")
    private BigDecimal packagingCharges;
    @Column(name = "DISCOUNT")
    private BigDecimal discount;
    @Column(name = "NET_AMOUNT")
    private BigDecimal netAmount;
    @Column(name="AOV")
    private BigDecimal Aov;
    @Column(name = "COMMISSION_RATE")
    private Double commissionRate;
    @Column(name = "EXTRA_COMMISSION_RATE")
    private Double extraCommissionRate;
    @Column(name = "COMMISSION_AMOUNT")
    private BigDecimal commissionAmount;
    @Column(name = "GST_RATE")
    private Double gstRate;
    @Column(name = "FINAL_COMMISSION_AMOUNT")
    private BigDecimal finalCommissionAmount;
    @Column(name = "PARTNER_NAME")
    private  String partnerName;
    @Column(name = "BRAND")
    private int brand;
    @Column(name = "UNIT_AGE")
    private long unitAge;
    @Column(name = "UNIT_LIVE_DATE")
    private Date unitLiveDate;
    @Column(name = "SWIGGY_CLOUD_KITCHEN")
    private String swiggyCloudKitchen;

    @Column(name = "RULE_PARAMS")
    private String ruleParams;
    @Column(name="ORDER_DATE")
    private Date orderDate;
    @Column(name="STATUS")
    private String status;
    @Column(name="RESTAURANT_GROSS_BILL")
    private BigDecimal restaurantGrossBill;
}
