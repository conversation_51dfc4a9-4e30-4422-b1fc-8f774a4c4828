
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "group_id",
    "group_name",
    "group_description",
    "group_minimum",
    "group_maximum",
    "group_is_active",
    "items"
})
public class Group {

    @JsonProperty("group_id")
    private String groupId;
    @JsonProperty("group_name")
    private String groupName;
    @JsonProperty("group_description")
    private String groupDescription;
    @JsonProperty("group_minimum")
    private Integer groupMinimum;
    @JsonProperty("group_maximum")
    private Integer groupMaximum;
    @JsonProperty("group_is_active")
    private Integer groupIsActive;
    @JsonProperty("items")
    private List<ZomatoMenuItem> items = null;

    @JsonProperty("group_id")
    public String getGroupId() {
        return groupId;
    }

    @JsonProperty("group_id")
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @JsonProperty("group_name")
    public String getGroupName() {
        return groupName;
    }

    @JsonProperty("group_name")
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @JsonProperty("group_description")
    public String getGroupDescription() {
        return groupDescription;
    }

    @JsonProperty("group_description")
    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }

    @JsonProperty("group_minimum")
    public Integer getGroupMinimum() {
        return groupMinimum;
    }

    @JsonProperty("group_minimum")
    public void setGroupMinimum(Integer groupMinimum) {
        this.groupMinimum = groupMinimum;
    }

    @JsonProperty("group_maximum")
    public Integer getGroupMaximum() {
        return groupMaximum;
    }

    @JsonProperty("group_maximum")
    public void setGroupMaximum(Integer groupMaximum) {
        this.groupMaximum = groupMaximum;
    }

    @JsonProperty("group_is_active")
    public Integer getGroupIsActive() {
        return groupIsActive;
    }

    @JsonProperty("group_is_active")
    public void setGroupIsActive(Integer groupIsActive) {
        this.groupIsActive = groupIsActive;
    }

    @JsonProperty("items")
    public List<ZomatoMenuItem> getItems() {
        if(items == null){
            items = new ArrayList<>();
        }
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<ZomatoMenuItem> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("groupId", groupId).append("groupName", groupName).append("groupDescription", groupDescription).append("groupMinimum", groupMinimum).append("groupMaximum", groupMaximum).append("groupIsActive", groupIsActive).append("items", items).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(groupMaximum).append(groupIsActive).append(groupId).append(groupName).append(groupDescription).append(items).append(groupMinimum).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof Group) == false) {
            return false;
        }
        Group rhs = ((Group) other);
        return new EqualsBuilder().append(groupMaximum, rhs.groupMaximum).append(groupIsActive, rhs.groupIsActive).append(groupId, rhs.groupId).append(groupName, rhs.groupName).append(groupDescription, rhs.groupDescription).append(items, rhs.items).append(groupMinimum, rhs.groupMinimum).isEquals();
    }

}
