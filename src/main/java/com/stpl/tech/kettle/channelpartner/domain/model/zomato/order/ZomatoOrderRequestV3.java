package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.PaymentStatus;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"order_id", "restaurant_id", "restaurant_name", "outlet_id", "order_date_time", "enable_delivery", "cutlery_instructions",
    "net_amount", "gross_amount", "payment_mode", "payment_status", "amount_paid", "amount_balance", "order_type", "order_status",
    "order_instructions", "total_merchant", "dishes", "order_additional_charges", "takeaway_details", "is_special_combo_order",
    "order_discounts", "time_to_penalty_map", "order_messages", "customer_details", "cash_to_be_collected", "merchant_bag_flow", "prep_time_details"})

public class ZomatoOrderRequestV3 implements ZomatoOrderable {

    @JsonProperty("order_id")
    private Long orderId;
    @JsonProperty("restaurant_id")
    private Long restaurantId;
    @JsonProperty("restaurant_name")
    private String restaurantName;
    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("order_date_time")
    private Integer orderDateTime;
    @JsonProperty("enable_delivery")
    private Integer enableDelivery;
    @JsonProperty("cutlery_instructions")
    private String cutleryInstructions;
    @JsonProperty("net_amount")
    private Float netAmount;
    @JsonProperty("gross_amount")
    private Float grossAmount;
    @JsonProperty("payment_mode")
    private String paymentMode;
    @JsonProperty("payment_status")
    private PaymentStatus paymentStatus;
    @JsonProperty("amount_paid")
    private Float amountPaid;
    @JsonProperty("amount_balance")
    private Float amountBalance;
    @JsonProperty("order_type")
    private ZomatoOrderType orderType;
    @JsonProperty("order_instructions")
    private String orderInstructions;
    @JsonProperty("cash_to_be_collected")
    private Float cashToBeCollected = 0f;
    @JsonProperty("total_merchant")
    private Float totalMerchant = 0f;
    @JsonProperty("dishes")
    private List<ZomatoOrderDishesV3> dishes = new ArrayList<>();
    @JsonProperty("order_additional_charges")
    private List<ZomatoOrderChargesV3> orderAdditionalCharges = new ArrayList<>();
    @JsonProperty("takeaway_details")
    private ZomatoOrderTakeAwayDetailsV3 takeAwayDetails;
    @JsonProperty("order_discounts")
    private List<ZomatoOrderDiscountV3> orderDiscounts = new ArrayList<>();
    @JsonProperty("customer_details")
    private ZomatoOrderCustomerDetailsV3 customerDetails;
    @JsonProperty("time_to_penalty_map")
    private ZomatoOrderPenaltyMapV3 timeToPenaltyMap;
    @JsonProperty("order_messages")
    private List<ZomatoOrderMessagesV3> orderMessages = new ArrayList<>();
    @JsonProperty("merchant_bag_flow")
    private boolean merchantBagFlow;
    @JsonProperty("prep_time_details")
    private ZomatoOrderPrepTimeDetails prepTimeDetails;
    @JsonProperty("is_special_combo_order")
    private Integer specialComboOrder;
    @JsonProperty("order_status")
    private String orderStatus;
    @JsonProperty("is_rbt_enabled")
    private Boolean isRbtEnabled;
    @JsonProperty("pickup_time")
    private Float pickupTime;
    @JsonProperty("is_bulk_order")
    private Boolean isBulkOrder;


    @JsonProperty("order_tags")
    private List<ZomatoOrderTag> zomatoOrderTag;


    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("restaurant_id")
    public Long getRestaurantId() {
        return restaurantId;
    }

    @JsonProperty("restaurant_id")
    public void setRestaurantId(Long restaurantId) {
        this.restaurantId = restaurantId;
    }

    @JsonProperty("restaurant_name")
    public String getRestaurantName() {
        return restaurantName;
    }

    @JsonProperty("restaurant_name")
    public void setRestaurantName(String restaurantName) {
        this.restaurantName = restaurantName;
    }

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("order_date_time")
    public Integer getOrderDateTime() {
        return orderDateTime;
    }

    @JsonProperty("order_date_time")
    public void setOrderDateTime(Integer orderDateTime) {
        this.orderDateTime = orderDateTime;
    }

    @JsonProperty("enable_delivery")
    public Integer getEnableDelivery() {
        return enableDelivery;
    }

    @JsonProperty("enable_delivery")
    public void setEnableDelivery(Integer enableDelivery) {
        this.enableDelivery = enableDelivery;
    }

    @JsonProperty("cutlery_instructions")
    public String getCutleryInstructions() {
        return cutleryInstructions;
    }

    @JsonProperty("cutlery_instructions")
    public void setCutleryInstructions(String cutleryInstructions) {
        this.cutleryInstructions = cutleryInstructions;
    }

    @JsonProperty("net_amount")
    public Float getNetAmount() {
        return netAmount;
    }

    @JsonProperty("net_amount")
    public void setNetAmount(Float netAmount) {
        this.netAmount = netAmount;
    }

    @JsonProperty("gross_amount")
    public Float getGrossAmount() {
        return grossAmount;
    }

    @JsonProperty("gross_amount")
    public void setGrossAmount(Float grossAmount) {
        this.grossAmount = grossAmount;
    }

    @JsonProperty("payment_mode")
    public String getPaymentMode() {
        return paymentMode;
    }

    @JsonProperty("payment_mode")
    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @JsonProperty("payment_status")
    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    @JsonProperty("payment_status")
    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @JsonProperty("amount_paid")
    public Float getAmountPaid() {
        return amountPaid;
    }

    @JsonProperty("amount_paid")
    public void setAmountPaid(Float amountPaid) {
        this.amountPaid = amountPaid;
    }

    @JsonProperty("amount_balance")
    public Float getAmountBalance() {
        return amountBalance;
    }

    @JsonProperty("amount_balance")
    public void setAmountBalance(Float amountBalance) {
        this.amountBalance = amountBalance;
    }

    @JsonProperty("order_type")
    public ZomatoOrderType getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(ZomatoOrderType orderType) {
        this.orderType = orderType;
    }

    @JsonProperty("order_instructions")
    public String getOrderInstructions() {
        return orderInstructions;
    }

    @JsonProperty("order_instructions")
    public void setOrderInstructions(String orderInstructions) {
        this.orderInstructions = orderInstructions;
    }

    @JsonProperty("cash_to_be_collected")
    public Float getCashToBeCollected() {
        return cashToBeCollected;
    }

    @JsonProperty("cash_to_be_collected")
    public void setCashToBeCollected(Float cashToBeCollected) {
        this.cashToBeCollected = cashToBeCollected;
    }

    @JsonProperty("total_merchant")
    public Float getTotalMerchant() {
        return totalMerchant;
    }

    @JsonProperty("total_merchant")
    public void setTotalMerchant(Float totalMerchant) {
        this.totalMerchant = totalMerchant;
    }

    @JsonProperty("dishes")
    public List<ZomatoOrderDishesV3> getDishes() {
        return dishes;
    }

    @JsonProperty("dishes")
    public void setDishes(List<ZomatoOrderDishesV3> dishes) {
        this.dishes = dishes;
    }

    @JsonProperty("order_additional_charges")
    public List<ZomatoOrderChargesV3> getOrderAdditionalCharges() {
        return orderAdditionalCharges;
    }

    @JsonProperty("order_additional_charges")
    public void setOrderAdditionalCharges(List<ZomatoOrderChargesV3> orderAdditionalCharges) {
        this.orderAdditionalCharges = orderAdditionalCharges;
    }

    @JsonProperty("takeaway_details")
    public ZomatoOrderTakeAwayDetailsV3 getTakeAwayDetails() {
        return takeAwayDetails;
    }

    @JsonProperty("takeaway_details")
    public void setTakeAwayDetails(ZomatoOrderTakeAwayDetailsV3 takeAwayDetails) {
        this.takeAwayDetails = takeAwayDetails;
    }

    @JsonProperty("order_discounts")
    public List<ZomatoOrderDiscountV3> getOrderDiscounts() {
        return orderDiscounts;
    }

    @JsonProperty("order_discounts")
    public void setOrderDiscounts(List<ZomatoOrderDiscountV3> orderDiscounts) {
        this.orderDiscounts = orderDiscounts;
    }

    @JsonProperty("customer_details")
    public ZomatoOrderCustomerDetailsV3 getCustomerDetails() {
        return customerDetails;
    }

    @JsonProperty("customer_details")
    public void setCustomerDetails(ZomatoOrderCustomerDetailsV3 customerDetails) {
        this.customerDetails = customerDetails;
    }

    @JsonProperty("time_to_penalty_map")
    public ZomatoOrderPenaltyMapV3 getTimeToPenaltyMap() {
        return timeToPenaltyMap;
    }

    @JsonProperty("time_to_penalty_map")
    public void setTimeToPenaltyMap(ZomatoOrderPenaltyMapV3 timeToPenaltyMap) {
        this.timeToPenaltyMap = timeToPenaltyMap;
    }

    @JsonProperty("order_messages")
    public List<ZomatoOrderMessagesV3> getOrderMessages() {
        return orderMessages;
    }

    @JsonProperty("order_messages")
    public void setOrderMessages(List<ZomatoOrderMessagesV3> orderMessages) {
        this.orderMessages = orderMessages;
    }

    @JsonProperty("is_special_combo_order")
    public Integer getSpecialComboOrder() {
        return specialComboOrder;
    }

    @JsonProperty("is_special_combo_order")
    public void setSpecialComboOrder(Integer specialComboOrder) {
        this.specialComboOrder = specialComboOrder;
    }

    @JsonProperty("order_status")
    public String getOrderStatus() {
        return orderStatus;
    }

    @JsonProperty("order_status")
    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    @JsonProperty("merchant_bag_flow")
    public boolean isMerchantBagFlow() {
        return merchantBagFlow;
    }

    @JsonProperty("merchant_bag_flow")
    public void setMerchantBagFlow(boolean merchantBagFlow) {
        this.merchantBagFlow = merchantBagFlow;
    }

    @JsonProperty("prep_time_details")
    public ZomatoOrderPrepTimeDetails getPrepTimeDetails() {
        return prepTimeDetails;
    }

    @JsonProperty("prep_time_details")
    public void setPrepTimeDetails(ZomatoOrderPrepTimeDetails prepTimeDetails) {
        this.prepTimeDetails = prepTimeDetails;
    }

    @JsonProperty("is_rbt_enabled")
    public Boolean getRbtEnabled() {
        return isRbtEnabled;
    }

    @JsonProperty("is_rbt_enabled")
    public void setRbtEnabled(Boolean rbtEnabled) {
        isRbtEnabled = rbtEnabled;
    }

    @JsonProperty("pickup_time")
    public Float getPickupTime() {
        return pickupTime;
    }

    @JsonProperty("pickup_time")
    public void setPickupTime(Float pickupTime) {
        this.pickupTime = pickupTime;
    }

    @JsonProperty("is_bulk_order")
    public Boolean getBulkOrder() {
        return isBulkOrder;
    }

    @JsonProperty("is_bulk_order")
    public void setBulkOrder(Boolean bulkOrder) {
        isBulkOrder = bulkOrder;
    }


    @JsonProperty("order_tags")
    public List<ZomatoOrderTag> getZomatoOrderTag() {
        return zomatoOrderTag;
    }

    @JsonProperty("order_tags")

    public void setZomatoOrderTag(List<ZomatoOrderTag> zomatoOrderTag) {
        this.zomatoOrderTag = zomatoOrderTag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoOrderRequestV3 that = (ZomatoOrderRequestV3) o;

        return new EqualsBuilder()
            .append(merchantBagFlow, that.merchantBagFlow)
            .append(orderId, that.orderId)
            .append(restaurantId, that.restaurantId)
            .append(restaurantName, that.restaurantName)
            .append(outletId, that.outletId)
            .append(orderDateTime, that.orderDateTime)
            .append(enableDelivery, that.enableDelivery)
            .append(cutleryInstructions, that.cutleryInstructions)
            .append(netAmount, that.netAmount)
            .append(grossAmount, that.grossAmount)
            .append(paymentMode, that.paymentMode)
            .append(paymentStatus, that.paymentStatus)
            .append(amountPaid, that.amountPaid)
            .append(amountBalance, that.amountBalance)
            .append(orderType, that.orderType)
            .append(orderInstructions, that.orderInstructions)
            .append(cashToBeCollected, that.cashToBeCollected)
            .append(totalMerchant, that.totalMerchant)
            .append(dishes, that.dishes)
            .append(orderAdditionalCharges, that.orderAdditionalCharges)
            .append(takeAwayDetails, that.takeAwayDetails)
            .append(orderDiscounts, that.orderDiscounts)
            .append(customerDetails, that.customerDetails)
            .append(timeToPenaltyMap, that.timeToPenaltyMap)
            .append(orderMessages, that.orderMessages)
            .append(prepTimeDetails, that.prepTimeDetails)
            .append(specialComboOrder, that.specialComboOrder)
            .append(orderStatus, that.orderStatus)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(orderId)
            .append(restaurantId)
            .append(restaurantName)
            .append(outletId)
            .append(orderDateTime)
            .append(enableDelivery)
            .append(cutleryInstructions)
            .append(netAmount)
            .append(grossAmount)
            .append(paymentMode)
            .append(paymentStatus)
            .append(amountPaid)
            .append(amountBalance)
            .append(orderType)
            .append(orderInstructions)
            .append(cashToBeCollected)
            .append(totalMerchant)
            .append(dishes)
            .append(orderAdditionalCharges)
            .append(takeAwayDetails)
            .append(orderDiscounts)
            .append(customerDetails)
            .append(timeToPenaltyMap)
            .append(orderMessages)
            .append(merchantBagFlow)
            .append(prepTimeDetails)
            .append(specialComboOrder)
            .append(orderStatus)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoOrderRequestV3{" +
            "orderId=" + orderId +
            ", restaurantId=" + restaurantId +
            ", restaurantName='" + restaurantName + '\'' +
            ", outletId='" + outletId + '\'' +
            ", orderDateTime=" + orderDateTime +
            ", enableDelivery=" + enableDelivery +
            ", cutleryInstructions=" + cutleryInstructions +
            ", netAmount=" + netAmount +
            ", grossAmount=" + grossAmount +
            ", paymentMode='" + paymentMode + '\'' +
            ", paymentStatus=" + paymentStatus +
            ", amountPaid=" + amountPaid +
            ", amountBalance=" + amountBalance +
            ", orderType=" + orderType +
            ", orderInstructions='" + orderInstructions + '\'' +
            ", cashToBeCollected=" + cashToBeCollected +
            ", totalMerchant=" + totalMerchant +
            ", dishes=" + dishes +
            ", orderAdditionalCharges=" + orderAdditionalCharges +
            ", takeAwayDetails=" + takeAwayDetails +
            ", orderDiscounts=" + orderDiscounts +
            ", customerDetails=" + customerDetails +
            ", timeToPenaltyMap=" + timeToPenaltyMap +
            ", orderMessages=" + orderMessages +
            ", merchantBagFlow=" + merchantBagFlow +
            ", prepTimeDetails=" + prepTimeDetails +
            ", specialComboOrder=" + specialComboOrder +
            ", orderStatus='" + orderStatus + '\'' +
            '}';
    }
}
