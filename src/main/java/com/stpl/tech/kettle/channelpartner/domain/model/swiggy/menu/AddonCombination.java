
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "addon_group_id",
    "addon_id"
})
public class AddonCombination {

    @JsonProperty("addon_group_id")
    private String addonGroupId;
    @JsonProperty("addon_id")
    private String addonId;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("addon_group_id")
    public String getAddonGroupId() {
        return addonGroupId;
    }

    @JsonProperty("addon_group_id")
    public void setAddonGroupId(String addonGroupId) {
        this.addonGroupId = addonGroupId;
    }

    @JsonProperty("addon_id")
    public String getAddonId() {
        return addonId;
    }

    @JsonProperty("addon_id")
    public void setAddonId(String addonId) {
        this.addonId = addonId;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("addonGroupId", addonGroupId).append("addonId", addonId).append("additionalProperties", additionalProperties).toString();
    }

}
