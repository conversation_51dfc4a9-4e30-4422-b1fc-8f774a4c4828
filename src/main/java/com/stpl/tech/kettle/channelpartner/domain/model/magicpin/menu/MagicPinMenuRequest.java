package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinMenuRequest implements Serializable {
    private static final long serialVersionUID = 5158208123982220012L;
    private MagicPinStoreData store ;
    private String partnerStoreId ; //
    private String outletId ;
    private MagicPinMenu menu ;
    @Builder.Default
    private List<MagicPinTimings> timings= new ArrayList<>();
    @Builder.Default
    private List<MagicPinCharges> charges=new ArrayList<>();
    @Builder.Default
    private List<MagicPinTaxes> taxes= new ArrayList<>();
    @Builder.Default
    private List<String> chargeIds=null ;
    @Builder.Default
    private List<MagicPinModifier> modifiers = new ArrayList<>();
    private MagicPinCallBackData magicPinCallBackData ;
}
