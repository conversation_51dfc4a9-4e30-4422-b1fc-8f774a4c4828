package com.stpl.tech.kettle.channelpartner.core.task;

import com.stpl.tech.kettle.channelpartner.core.service.MagicpinService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.order.partnerOrder.impl.MagicPinOrderManagementService;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class MagicpinOrderTask implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyOrderTask.class);

    private MagicPinOrderManagementService magicpinService;
    private volatile MagicpinOrderRequest magicpinOrderRequest;
    private volatile PartnerOrderDetail partnerOrderDetail;
    private volatile Order order;
    private volatile Boolean isManual;
    private volatile Boolean skipInventoryCheck;
    private volatile String requestId;

    @Override
    public void run() {
        try {
            MDC.put("request.id", requestId);
            magicpinService.placeOrder(partnerOrderDetail, order, isManual, skipInventoryCheck);
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error processing magicpin order task ", ex);
        }
        try {
            MDC.put("request.id", requestId);
            magicpinService.notifyOrder(partnerOrderDetail, isManual);
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error notifying magicpin order ", ex);
        }
    }
}
