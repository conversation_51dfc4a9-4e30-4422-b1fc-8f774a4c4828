package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadata;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerMetadataDao extends MongoRepository<PartnerMetadata, String> {

    List<PartnerMetadata> findAllByKettlePartnerId(Integer kettlePartnerId);
}
