package com.stpl.tech.kettle.channelpartner.core.service.menu.factory;

import com.stpl.tech.kettle.channelpartner.core.service.CommonMetadataValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.CommonPartnerEventNotificationService;
import com.stpl.tech.kettle.channelpartner.core.service.OrderValidationService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.common.factory.CommonPartnerDependency;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitStockSnapshotDao;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PartnerMenuConverterDependency extends CommonPartnerDependency {

    @Autowired
    private PartnerMenuService partnerMenuService;

    @Autowired
    private CommonPartnerEventNotificationService<String, SlackNotification, Boolean,String> commonPartnerEventNotificationService;

    @Autowired
    private PartnerUnitStockSnapshotDao partnerUnitStockSnapshotDao;

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    private CommonMetadataValidationService commonMetadataValidationService;

    @Autowired
    private OrderValidationService orderValidationService ;

    public PartnerMenuService getPartnerMenuService() {
        return partnerMenuService;
    }

    public CommonPartnerEventNotificationService<String, SlackNotification, Boolean, String> getCommonPartnerEventNotificationService() {
        return commonPartnerEventNotificationService;
    }

    public PartnerUnitStockSnapshotDao getPartnerUnitStockSnapshotDao() {
        return partnerUnitStockSnapshotDao;
    }

    public PartnerOrderService getPartnerOrderService() {
        return partnerOrderService;
    }

    public CommonMetadataValidationService getCommonMetadataValidationService() {
        return commonMetadataValidationService;
    }

    public OrderValidationService getOrderValidationService() {
        return orderValidationService;
    }
}
