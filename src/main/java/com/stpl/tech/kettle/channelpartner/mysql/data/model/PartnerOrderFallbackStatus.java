package com.stpl.tech.kettle.channelpartner.mysql.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Cache;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PARTNER_ORDER_FALLBACK_STATUS")
public class PartnerOrderFallbackStatus {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FALLBACK_STATUS_ID")
    private Long id;

    @Column(name = "ORDER_ID", nullable = false, unique = true, length = 50)
    private String orderId;

    @Column(name = "EMPLOYEE_ID", length = 45)
    private String employeeId;

    @Column(name = "FINAL_STATUS", columnDefinition = "enum('RESOLVED','CANCELLED','FAILED','CHECKED','REJECTED','PLACED','NOTIFIED','ACCEPTED','EDIT_CANCEL_REQUESTED','EDIT_CANCELLED','CANCEL_REQUESTED','CANCEL_REQUEST_FAILED')")
    private String finalStatus;

    @Column(name = "ACTION_TIME")
    private Date actionTime;

    @Column(name = "CANCELLATION_REASON")
    private String cancellationReason;

    @Column(name = "CUSTOMER_RESPONSE")
    private String customerResponse;

    @Column(name = "PARTNER_RESPONSE")
    private String partnerResponse;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "partnerOrderFallbackStatusId")
    private Set<PartnerOrderFallbackLog> partnerOrderFallbackLogs;

}
