package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.util.List;

@Document
@XmlRootElement(name = "PartnerUnitMenuDetail")
public class PartnerUnitMenuDetail {

    @Id
    private String id;
    private String partnerName;
    @Indexed
    private Integer partnerId;
    @Indexed
    private Integer unitId;
    private String region;
    private Object menuData;
    private List<Object> charges;
    private List<Object> timings;
    private List<Object> taxes;
    private List<String> chargeIds;
    private List<Object> modifiers;
    private Boolean active = false;
    private Date addTime;
    private String addTimeIST;
    private Integer employeeId;
    private String employeeName;
    @Indexed
    private Boolean isNew;
    private Integer brandId;
    private String version;
    private MenuType menuType;
    private Integer menuSequenceId;
    private String menuSequenceName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Object getMenuData() {
        return menuData;
    }

    public void setMenuData(Object menuData) {
        this.menuData = menuData;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Boolean getNew() {
        return isNew;
    }

    public void setNew(Boolean aNew) {
        isNew = aNew;
    }
    
    public List<Object> getCharges() {
		return charges;
	}

	public void setCharges(List<Object> charges) {
		this.charges = charges;
	}

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public MenuType getMenuType() {
		return menuType;
	}

	public void setMenuType(MenuType menuType) {
		this.menuType = menuType;
	}

    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    public String getMenuSequenceName() {
        return menuSequenceName;
    }

    public void setMenuSequenceName(String menuSequenceName) {
        this.menuSequenceName = menuSequenceName;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }

    public List<Object> getTimings() {
        return timings;
    }

    public void setTimings(List<Object> timings) {
        this.timings = timings;
    }

    public List<Object> getTaxes() {
        return taxes;
    }

    public void setTaxes(List<Object> taxes) {
        this.taxes = taxes;
    }

    public List<String> getChargeIds() {
        return chargeIds;
    }

    public void setChargeIds(List<String> chargeIds) {
        this.chargeIds = chargeIds;
    }

    public List<Object> getModifiers() {
        return modifiers;
    }

    public void setModifiers(List<Object> modifiers) {
        this.modifiers = modifiers;
    }
}
