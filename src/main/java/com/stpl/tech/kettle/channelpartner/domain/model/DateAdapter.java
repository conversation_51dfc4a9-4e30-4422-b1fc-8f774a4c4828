/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.channelpartner.domain.model;

import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateAdapter {

	public static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_TL = new ThreadLocal<SimpleDateFormat>() {

		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		}

	};
	
	public static final ThreadLocal<SimpleDateFormat> TIME_FORMAT_TL = new ThreadLocal<SimpleDateFormat>() {

		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("HH:mm:ss");
		}

	};

	public static Date parseDate(String s) throws ParseException {
		long time = Long.valueOf(s);
		return new Date(time);
	}
	
	public static Time parseTime(String s) throws ParseException {
		long time = Long.valueOf(s);
		return new Time(time);
	}

	public static String printDate(Date dt) {
		return DATE_FORMAT_TL.get().format(dt);
	}
	
	public static String printTime(Time dt) {
		return TIME_FORMAT_TL.get().format(dt);
	}
}