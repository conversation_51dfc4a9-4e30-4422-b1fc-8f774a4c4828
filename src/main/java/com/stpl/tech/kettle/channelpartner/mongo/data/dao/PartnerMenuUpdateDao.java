package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerMenuUpdateDao extends MongoRepository<PartnerMenuDetail, String> {

    public List<PartnerMenuDetail> findAllByActiveAndPartnerIdAndRegion(Boolean active, Integer partnerId, String region);
}
