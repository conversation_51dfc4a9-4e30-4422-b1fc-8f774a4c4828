package com.stpl.tech.kettle.channelpartner.core.service.common.factory;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CommonPartnerDependency {

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;


    @Autowired
    private RedisPublisher redisPublisher;

    public MasterDataCache getMasterDataCache() {
        return masterDataCache;
    }

    public ChannelPartnerDataCache getChannelPartnerDataCache() {
        return channelPartnerDataCache;
    }

    public EnvironmentProperties getEnvironmentProperties() {
        return environmentProperties;
    }

    public WebServiceHelper getWebServiceHelper() {
        return webServiceHelper;
    }

    public RedisPublisher getRedisPublisher() {
        return redisPublisher;
    }
}
