package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "property_id", "property_name", "property_value_id", "property_value" })
public class ZomatoOrderCompositionPropV3 {

	@JsonProperty("property_id")
	private String propertyId;
	@JsonProperty("property_name")
	private String propertyName;
	@JsonProperty("property_value_id")
	private String propertyValueId;
	@JsonProperty("property_value")
	private String propertyValue;

	@JsonProperty("property_id")
	public String getPropertyId() {
		return propertyId;
	}

	@JsonProperty("property_id")
	public void setPropertyId(String propertyId) {
		this.propertyId = propertyId;
	}

	@JsonProperty("property_name")
	public String getPropertyName() {
		return propertyName;
	}

	@JsonProperty("property_name")
	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	@JsonProperty("property_value_id")
	public String getPropertyValueId() {
		return propertyValueId;
	}

	@JsonProperty("property_value_id")
	public void setPropertyValueId(String propertyValueId) {
		this.propertyValueId = propertyValueId;
	}

	@JsonProperty("property_value")
	public String getPropertyValue() {
		return propertyValue;
	}

	@JsonProperty("property_value")
	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

	@Override
	public String toString() {
		return "ZomatoOrderCompositionPropV3 [propertyId=" + propertyId + ", propertyName=" + propertyName
				+ ", propertyValueId=" + propertyValueId + ", propertyValue=" + propertyValue + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(propertyId).append(propertyName).append(propertyValueId)
				.append(propertyValue).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderCompositionPropV3) == false) {
			return false;
		}
		ZomatoOrderCompositionPropV3 rhs = ((ZomatoOrderCompositionPropV3) other);
		return new EqualsBuilder().append(propertyId, rhs.propertyId).append(propertyName, rhs.propertyName)
				.append(propertyValueId, rhs.propertyValueId).append(propertyValue, rhs.propertyValue).isEquals();
	}

}
