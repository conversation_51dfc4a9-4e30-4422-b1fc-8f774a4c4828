package com.stpl.tech.kettle.channelpartner.core.service.order.factory;

import com.stpl.tech.kettle.channelpartner.core.service.order.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.order.orderConverter.OrderConverterService;
import org.apache.commons.lang3.NotImplementedException;

import javax.mail.Part;
import java.util.HashMap;
import java.util.Map;

public class PartnerOrderServiceFactory {
    private static Map<String, PartnerOrderService> orderServiceMap = new HashMap<>();

    private PartnerOrderServiceFactory(){};

    public static PartnerOrderService getInstance(String partnerName ,
                                   PartnerOrderConverterDependency partnerOrderConverterDependency){
        if(!orderServiceMap.containsKey(partnerName)){
            orderServiceMap.put(partnerName,getPartnerOrderServiceInstance(partnerName,partnerOrderConverterDependency));
        }
        return orderServiceMap.get(partnerName);
    }

    private static PartnerOrderService getPartnerOrderServiceInstance(String partnerName,
                                                                     PartnerOrderConverterDependency partnerOrderConverterDependency){
        switch (partnerName){
            case "MAGICPIN":
               return new PartnerOrderService(
                       partnerOrderConverterDependency.getTrackService(),
                       partnerOrderConverterDependency.getChannelPartnerDataCache(),
                       partnerOrderConverterDependency.getPartnerOrderManagementService(partnerName),
                       partnerOrderConverterDependency
               );
            default:
                throw new NotImplementedException("Not Implemented yet For This Partner");
        }
    }
}
