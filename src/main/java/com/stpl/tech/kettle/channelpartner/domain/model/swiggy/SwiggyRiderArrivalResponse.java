package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "statusMessage",
        "statusCode",
        "data"
})
public class SwiggyRiderArrivalResponse {

    @JsonProperty("statusMessage")
    private String statusMessage;
    @JsonProperty("statusCode")
    private int statusCode;
    @JsonProperty("data")
    private OrderTime data;

    @JsonProperty("status_message")
    public String getStatusMessage() {
        return statusMessage;
    }

    @JsonProperty("status_message")
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @JsonProperty("status_code")
    public int getStatusCode() {
        return statusCode;
    }

    @JsonProperty("status_code")
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    @JsonProperty("data")
    public OrderTime getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(OrderTime data) {
        this.data = data;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SwiggyRiderArrivalResponse that = (SwiggyRiderArrivalResponse) o;
        return statusCode == that.statusCode &&
                Objects.equals(statusMessage, that.statusMessage) &&
                Objects.equals(data, that.data);
    }

    @Override
    public int hashCode() {

        return Objects.hash(statusMessage, statusCode, data);
    }

    @Override
    public String toString() {
        return "SwiggyRiderArrivalResponse{" +
                "statusMessage='" + statusMessage + '\'' +
                ", statusCode=" + statusCode +
                ", data=" + data +
                '}';
    }
}