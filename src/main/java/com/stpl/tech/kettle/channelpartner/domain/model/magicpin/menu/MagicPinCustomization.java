package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MagicPinCustomization implements Serializable {

    private static final long serialVersionUID = 8685524725500996497L;
    private String id;
    private String title;
    private Integer minimum;
    private Integer maximum;
    private List<MagicPinOption> options = new ArrayList<MagicPinOption>();
    private Boolean addon;
    private Integer rank ;
}

