package com.stpl.tech.kettle.channelpartner.core.util;

import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

@Component
public class AsyncWebServiceHelper {

    private static final Logger LOG = LoggerFactory.getLogger(AsyncWebServiceHelper.class);

    @Autowired
    WebServiceHelper webServiceHelper;

    @Async
    public <T> Future<T> postWithAuth(String endpoint, String token, Object body, Class<T> clazz) {
        return CompletableFuture.completedFuture(webServiceHelper.postWithAuth(endpoint, token, body, clazz));
    }

    @Async
    public <T> Future<T> callInternalApi(String endpoint, String token, HttpMethod method, Class<T> clazz, Object body,
                                 Map<String, ?> uriVariables) {
        return CompletableFuture.completedFuture(webServiceHelper.callInternalApi(endpoint, token, method, clazz, body, uriVariables));
        //return CompletableFuture.completedFuture(webServiceHelper.callInternalApi(endpoint, token, method, clazz, body, uriVariables));
    }

    @Async
    public <T> Future<T> callSwiggyApi(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz) {
        return CompletableFuture.completedFuture(webServiceHelper.callSwiggyApi(props, endpoint, method, request, clazz));
    }

    @Async
    public <T> Future<T> callZomatoApi(EnvironmentProperties props, ZomatoServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz, Integer brandId) {
        return CompletableFuture.completedFuture(webServiceHelper.callZomatoApi(props, endpoint, method, request, clazz, brandId));
    }
}
