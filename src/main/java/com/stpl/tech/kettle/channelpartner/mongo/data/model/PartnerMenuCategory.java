package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "PartnerMenuCategory")
public class PartnerMenuCategory {

    @Id
    private String id;
    private String name;
    private Integer kettlePartnerId;
    private String partnerName;
    private Boolean status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}
