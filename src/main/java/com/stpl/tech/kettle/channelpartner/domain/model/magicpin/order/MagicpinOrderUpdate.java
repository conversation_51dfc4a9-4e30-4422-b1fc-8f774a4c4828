package com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"orderId","status","rejectionReason","rejectionId","shipmentId","itemChanges","deliverySlots"})
public class MagicpinOrderUpdate {

    @JsonProperty("orderId")
    private Long orderId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("rejectionReason")
    private String rejectionReason;

    @JsonProperty("rejectionId")
    private Integer rejectionId;

    @JsonProperty("shipmentId")
    private Integer shipmentId;

    @JsonProperty("itemChanges")
    private List<ItemChanges> itemChanges;

    @JsonProperty("deliverySlots")
    private List<DeliverySlots> deliverySlots;
}
