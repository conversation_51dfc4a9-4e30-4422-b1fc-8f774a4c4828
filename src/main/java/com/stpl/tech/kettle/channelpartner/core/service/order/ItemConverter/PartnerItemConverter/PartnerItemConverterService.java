package com.stpl.tech.kettle.channelpartner.core.service.order.ItemConverter.PartnerItemConverter;

import com.stpl.tech.kettle.channelpartner.domain.model.common.PartnerItemData;
import com.stpl.tech.kettle.channelpartner.domain.model.magicpin.order.MagicpinOrderRequest;
import com.stpl.tech.kettle.domain.model.OrderItem;

import javax.persistence.criteria.CriteriaBuilder;
import java.math.BigDecimal;
import java.util.List;

public interface PartnerItemConverterService {


    <I> PartnerItemData getPartnerItemData(I item);

    <I> BigDecimal getItemPrice(I item , String itemId);

    <I, R> List<I> getItems(R request);

    <I> PartnerItemData getPackagingCharge(I item);

    public <I> PartnerItemData getConstituentProductPartnerItemData(I item , Integer constituentProductId ,
                                                                    Integer quantity , String dimension ,String name);

    public <R> PartnerItemData getOrderLevelPackagingCharge(R request);


}
