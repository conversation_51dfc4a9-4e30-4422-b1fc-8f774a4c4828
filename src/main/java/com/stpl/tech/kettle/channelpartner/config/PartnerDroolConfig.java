package com.stpl.tech.kettle.channelpartner.config;


import org.kie.api.KieServices;
import org.kie.api.builder.*;
import org.kie.api.io.Resource;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PartnerDroolConfig {
    private static final KieServices kieServices = KieServices.Factory.get();
    private KieContainer kieContainerForCommissionMatrix;
    private static final String DROOLS_FOR_COMMISSION_MATRIX= "drools/commission_matrix/commission_matrix.xls";

    public KieContainer getKieContainerForCommissionMatrix(){
        return kieContainerForCommissionMatrix;
    }

    public void initDroolConfigForCommissionMatrix(){
        Resource dt = ResourceFactory.newUrlResource("file:/data/app/kettle/"+System.getProperty("env.type") + "/" + DROOLS_FOR_COMMISSION_MATRIX);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerForCommissionMatrix = kieServices.newKieContainer(kieModule.getReleaseId());
    }
}
