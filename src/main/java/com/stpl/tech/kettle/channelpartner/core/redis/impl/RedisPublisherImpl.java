package com.stpl.tech.kettle.channelpartner.core.redis.impl;

import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;

public class RedisPublisherImpl implements RedisPublisher {

    private final RedisTemplate<String, Object> template;

    public RedisPublisherImpl(final RedisTemplate<String, Object> template) {
        this.template = template;
    }

    @Override
    public void publish(String topic, Object message) {
        ChannelTopic channel = new ChannelTopic(topic.toString());
        template.convertAndSend(channel.getTopic(), message);
    }
}
