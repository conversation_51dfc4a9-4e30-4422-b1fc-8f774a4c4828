package com.stpl.tech.kettle.channelpartner.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "kettlePartnerId",
        "partnerName",
        "dotdProducts",
        "productIds"
})
public class DealOfTheDayRequest {

    @JsonProperty("kettlePartnerId")
    private Integer kettlePartnerId;

    @JsonProperty("partnerName")
    private String partnerName;

    @JsonProperty("dotdProducts")
    private List<DotdProductsRequest> dotdProducts;

    @JsonProperty("productIds")
    private List<Integer> productIds;
}
