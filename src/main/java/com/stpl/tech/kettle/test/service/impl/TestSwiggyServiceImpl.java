package com.stpl.tech.kettle.test.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.controller.SwiggyOrderAbstractResource;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderResponse;
import com.stpl.tech.kettle.test.domain.TestSwiggyOrderRequest;
import com.stpl.tech.kettle.test.mongo.dao.TestPartnerOrderDao;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import com.stpl.tech.kettle.test.service.TestSwiggyService;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.util.endpoint.Endpoints;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SWIGGY_TEST_ROOT_CONTEXT;

@Slf4j
@Service
public class TestSwiggyServiceImpl extends SwiggyOrderAbstractResource implements TestSwiggyService {
    @Autowired
    private TestPartnerOrderDao testPartnerOrderDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Override
    public List<PartnerOrderDetail> getSwiggyLiveOrders(String partnerName, Integer startOrderId, Integer endOrderId) {
        try {
            List<String> orderIds = getOrderIdsInRange(startOrderId, endOrderId);
            return testPartnerOrderDao.searchAllByPartnerNameAndKettleOrderIdIn(partnerName, orderIds);
        } catch (Exception e) {
            log.error("Error while getting order data in range for partnerId :{}::::::::", partnerName, e);
            return null;
        }
    }

    @Override
    public PartnerOrderDetail getPartnerOrder(String kettleOrderId) {
        try{
            return testPartnerOrderDao.searchByKettleOrderId(kettleOrderId);
        }catch(Exception e ){
            log.error("Error while swiggy order detail for orderId  :{}::::::::",kettleOrderId, e);
            return null;
        }
    }

    private List<String> getOrderIdsInRange(Integer min, Integer max) {
        List<String> orderIds = new ArrayList<>();
        for (int i = min; i <= max; i++) {
            orderIds.add(Integer.toString(i));
        }
        return orderIds;
    }

    @Override
    public TestSwiggyOrderRequest sendRequestToGetLiveSwiggyOrderRequestData(String partnerName, Integer startOrderId, Integer endOrderId) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        log.info("&&&&&&&&&&&&&&&&& STEP 1 : PROCESSING BULK REQUEST TO GET LIVE ORDERS FOR PARTNER ::{} with startOrderID ::{} --------------------------", partnerName, startOrderId);
        String endPoint = environmentProperties.getLiveOrderBaseUrl()+ Endpoints.CHANNELPARTNER_SERVICE_ENTRY_POINT+SWIGGY_TEST_ROOT_CONTEXT+SEPARATOR+"get-live-sw-orders";
        String authorization = environmentProperties.getLiveOrdersToken();
        Map<String,String> urivariables = new HashMap<>();
        urivariables.put("partnerName", partnerName);
        urivariables.put("startOrderId", startOrderId.toString());
        urivariables.put("endOrderId",endOrderId.toString());
//        List<PartnerOrderDetail> partnerOrderDetailList = new ArrayList<>();
        TestSwiggyOrderRequest bulkSwiggyOrderRequest =null;
        try {
            bulkSwiggyOrderRequest = WebServiceHelper.getRequestWithParam(endPoint, authorization, urivariables, TestSwiggyOrderRequest.class);
            /*GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer());
            if (list != null) {
                list.forEach(p -> {
                    Gson gson = gSonBuilder.create();
                    String str = gson.toJson(p);
                    PartnerOrderDetail partnerOrderDetail = gson.fromJson(str, PartnerOrderDetail.class);
                    if (partnerOrderDetail != null) {
                        partnerOrderDetailList.add(partnerOrderDetail);
                    }
                });
            }else{
                //do nothing
            }*/

        } catch (IOException | URISyntaxException e) {
            log.error("Exception thrown while getting live order partner data  for partner ::{}", partnerName);
        }
        log.info("&&&&&&&&&&&&&&&&& COMPLETED BULK REQUEST TO GET LIVE ORDERS FOR PARTNER ::{} with startOrderID ::{} and endOrderId ::{} in :::{} --------------------------", partnerName, startOrderId, endOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return bulkSwiggyOrderRequest;
    }

    @Override
    public boolean addSwiggyBatchedOrders(TestSwiggyOrderRequest bulkSwiggyOrderRequest, Integer startOrderId, Integer endOrderId, String partnerName, Map<String, Object> failedOrderMap) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        log.info("&&&&&&&&&&&&&&&&& STEP 2 : PROCESSING BULK REQUEST TO ADD LIVE ORDERS FOR PARTNER  ON ENV  :::{} --------------------------", environmentProperties.getEnvType());
        ExecutorService taskExecutor = Executors.newFixedThreadPool(100);// or default 100
        AtomicBoolean isSuccess = new AtomicBoolean(true);
        if (Objects.nonNull(bulkSwiggyOrderRequest)&& Objects.nonNull(bulkSwiggyOrderRequest.getSwiggyOrderRequestList()) && !bulkSwiggyOrderRequest.getSwiggyOrderRequestList().isEmpty()) {
            for (PartnerOrderDetail partnerOrderDetail : bulkSwiggyOrderRequest.getSwiggyOrderRequestList()) {
                taskExecutor.execute(() -> {
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    String jsonString = new Gson().toJson(partnerOrderDetail.getPartnerOrder());
                    SwiggyOrderRequest swiggyOrderRequest = null;
                    try {
                        swiggyOrderRequest = mapper.readValue(jsonString, SwiggyOrderRequest.class);
                    } catch (JsonProcessingException e) {
                        log.error("Exception while parsing  order request for partnerOrderId :{}::::::::::::::", partnerOrderDetail.getPartnerOrderId(), e);
                    }
                    isSuccess.set(addSwiggyOrderRequest(swiggyOrderRequest,failedOrderMap));
                });
            }
            taskExecutor.shutdown();
            try {
                taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
            } catch (InterruptedException e) {
                log.error("--------------------INTERUPTION !!!!-------------Error in completion of order threads", e);
                return false;
            }
        }
        log.info("&&&&&&&&&&&&&&&&& COMPLETED BULK REQUEST TO ADD LIVE PARTNER ORDERS ::{} with startOrderID ::{} and endOrderId ::{} in :::{} --------------------------", partnerName, startOrderId, endOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return isSuccess.get();
    }

    @Override
    public boolean addSwiggyOrderRequest(SwiggyOrderRequest swiggyOrderRequest, Map<String, Object> failedOrderMap) {
        SwiggyOrderResponse orderResponse = null ;
        try{
            orderResponse = addSwiggyOrder(swiggyOrderRequest);
            if(Objects.nonNull(orderResponse)){
                log.info("Response on processing  swiggy add order request for partnerOrderId ::{} {}::::::::::::::::", swiggyOrderRequest.getOrderId(), new Gson().toJson(orderResponse));
                if(orderResponse.getStatusCode()!=200){
                    if(Objects.nonNull(failedOrderMap)){
                        failedOrderMap.put(String.valueOf(swiggyOrderRequest.getOrderId()),(Object)orderResponse);
                    }
                    return false;
                }
            }
            return true ;
        }catch (Exception e ){
            log.error("Exception while adding order with orderId :::{}", swiggyOrderRequest.getOrderId(),e);
            return false;
        }
    }

}
