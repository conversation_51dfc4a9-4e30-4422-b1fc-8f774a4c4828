package com.stpl.tech.kettle.test.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddonToggleRequest implements Serializable {

    private static final long serialVersionUID = -2871910394395880804L;
    private String restaurantId ;
    private String externalOrderId ;
    private boolean inStock ;

}
