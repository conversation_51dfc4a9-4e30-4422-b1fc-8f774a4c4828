<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.2</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.stpl.tech.kettle.partner</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>channel-partner</artifactId>
    <version>6.1.144</version>


    <name>channel-partner</name>
    <!-- FIXME change it to the project's website -->
    <url>http://dev.kettle.chaayos.com</url>
    <packaging>war</packaging>
    <properties>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.spring.version>4.2.5.RELEASE</project.spring.version>
        <java.version>17</java.version>
        <slf4j.version>1.7.30</slf4j.version>
        <ch.qos.logback.version>1.2.3</ch.qos.logback.version>
    </properties>
    <distributionManagement>
        <snapshotRepository>
            <id>artifact-registry</id>
            <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos</url>
        </snapshotRepository>
        <repository>
            <id>artifact-registry</id>
            <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos</url>
        </repository>
    </distributionManagement>
    <profiles>
        <profile>
            <id>jenkins</id>
            <repositories>
                <repository>
                    <id>artifact-registry</id>
                    <url>artifactregistry://asia-south1-maven.pkg.dev/titanium-atlas-363108/chaayos
                    </url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <!--    <build>-->
    <!--        <pluginManagement>&lt;!&ndash; lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) &ndash;&gt;-->
    <!--            <plugins>-->
    <!--                <plugin>-->
    <!--                    <groupId>org.apache.maven.plugins</groupId>-->
    <!--                    <artifactId>maven-war-plugin</artifactId>-->
    <!--                    <version>2.6</version>-->
    <!--                    <configuration>-->
    <!--                        <webXml>WebContent/WEB-INF/web.xml</webXml>-->
    <!--                    </configuration>-->
    <!--                </plugin>-->
    <!--                &lt;!&ndash;<plugin>-->
    <!--                    <groupId>org.jvnet.jaxb2.maven2</groupId>-->
    <!--                    <artifactId>maven-jaxb2-plugin</artifactId>-->
    <!--                    <version>0.11.0</version>-->
    <!--                    <executions>-->
    <!--                        <execution>-->
    <!--                            <id>cp-domain-mongo-schema</id>-->
    <!--                            <phase>generate-sources</phase>-->
    <!--                            <goals>-->
    <!--                                <goal>generate</goal>-->
    <!--                            </goals>-->
    <!--                            <configuration>-->
    <!--                                <schemaDirectory>src/main/xsds/mongo</schemaDirectory>-->
    <!--                                <generatePackage>com.stpl.tech.kettle.channelpartner.mongo.data.model</generatePackage>-->
    <!--                                <generateDirectory>${project.build.directory}/generated-sources/xjc-mongo</generateDirectory>-->
    <!--                            </configuration>-->
    <!--                        </execution>-->
    <!--                    </executions>-->
    <!--                </plugin>&ndash;&gt;-->
    <!--            </plugins>-->
    <!--        </pluginManagement>-->
    <!--        <finalName>channel-partner</finalName>-->
    <!--    </build>-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <checkModificationExcludes>
                        <checkModificationExclude>**/pom.xml</checkModificationExclude>
                        <checkModificationExclude>release.properties</checkModificationExclude>
                    </checkModificationExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-install</id>
                        <configuration>
                            <skip>${deployOnly}</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-test</id>
                        <configuration>
                            <skip>${deployOnly}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <skipMain>${deployOnly}</skipMain>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>com.google.cloud.artifactregistry</groupId>
                <artifactId>artifactregistry-maven-wagon</artifactId>
                <version>2.2.0</version>
            </extension>
        </extensions>
        <finalName>${project.artifactId}</finalName>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
            <version>3.1.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-spring</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-core</artifactId>
            <version>[5.0.1,)</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>domain-model</artifactId>
            <version>[5.0.1,)</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.spring</groupId>
            <artifactId>spring-service</artifactId>
            <version>[5.0.1,)</version>
        </dependency>
        <!--<dependency>
            <groupId>com.stpl.tech.kettle</groupId>
            <artifactId>kettle-crm</artifactId>
            <version>3.1.0-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
            <version>1.11.883</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.10</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>2.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.tomcat.embed</groupId>-->
        <!--            <artifactId>tomcat-embed-core</artifactId>-->
        <!--            <version>8.0.32</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-path</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>xml-path</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>2.4.4</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
    </dependencies>
</project>
